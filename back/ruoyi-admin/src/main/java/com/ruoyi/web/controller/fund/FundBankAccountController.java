package com.ruoyi.web.controller.fund;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.dto.FundBankAccountQuery;
import com.ruoyi.system.domain.fund.FundBankAccount;
import com.ruoyi.system.domain.fund.FundBankStation;
import com.ruoyi.system.domain.project.ProjectHqdp;
import com.ruoyi.system.domain.vo.fund.FundBankAccountVo;
import com.ruoyi.system.service.fund.FundBankAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Api(value = "收款账户维护控制器", tags = {"收款账户维护功能接口"})
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@RequestMapping("/fund/bankAccount")
@Validated
public class FundBankAccountController  extends BaseController {

    private final FundBankAccountService fundBankAccountService;


    /**
     * 分页收款账户列表
     *
     * @param condition 条件
     * @return 结果
     */
    @ApiOperation("分页收款账户列表")
    //@PreAuthorize("@ss.hasPermi('fund:bankAccount:list')")
    @GetMapping("/list")
    public TableDataInfo list(FundBankAccountQuery condition) {
        startPage();
        List<FundBankAccountVo> list = fundBankAccountService.listBankAccount(condition);
        return getDataTable(list);
    }

    @ApiOperation("获取收款账户详细信息")
    @PreAuthorize("@ss.hasPermi('fund:bankAccount:list')")
    @GetMapping("/{id}")
    public R<FundBankAccount> getContract(@ApiParam("主键")
                                       @NotNull(message = "主键不能为空")
                                       @PathVariable("id") String id) {
        return R.ok(fundBankAccountService.getById(id));
    }

    @ApiOperation("新增收款账户记录")
    @PreAuthorize("@ss.hasPermi('fund:bankAccount:add')")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    @PostMapping("add")
    public R<Void> add(@RequestBody @Validated FundBankAccount entity) {
        boolean exists= fundBankAccountService.lambdaQuery()
             .eq(FundBankAccount::getCardNumber, entity.getCardNumber()).exists();
         if(exists){
             return R.fail("户(卡号)'"+entity.getCardNumber()+"'已存在，请确认！");
         }
        SysDept currentDept = SecurityUtils.getLoginUser().getUser().getDept();
        entity.setOrgId(currentDept.getDeptId());
        return fundBankAccountService.saveOrUpdate(entity) ? R.ok() : R.fail();
    }

    @ApiOperation("修改收款账户数据")
    @PreAuthorize("@ss.hasPermi('fund:bankAccount:edit')")
    @PostMapping("edit")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    public R<Void> edit(@Validated @RequestBody FundBankAccount entity) {
        boolean exists= fundBankAccountService.lambdaQuery()
                .eq(FundBankAccount::getCardNumber, entity.getCardNumber()).exists();
        if(exists){
            return R.fail("户(卡号)'"+entity.getCardNumber()+"'已存在，请确认！");
        }
        return fundBankAccountService.saveOrUpdate(entity) ? R.ok() : R.fail();
    }

    @ApiOperation("删除收款账户记录")
    @PreAuthorize("@ss.hasPermi('fund:bankAccount:remove')")
    @PostMapping("/remove/{id}")
    public R<Void> remove(@ApiParam("主键串") @NotEmpty(message = "主键不能为空") @PathVariable String id) {
        return fundBankAccountService.removeById(id) ? R.ok() : R.fail();
    }

    @Log(title = "全国银行网点表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @PreAuthorize("@ss.hasPermi('fund:bankstation:import')")
    public AjaxResult importData(@RequestPart("file") MultipartFile file) throws Exception
    {
        ExcelUtil<FundBankStation> util = new ExcelUtil<FundBankStation>(FundBankStation.class);
        if (file.isEmpty()) return AjaxResult.error("File is empty");
        List<FundBankStation> bankList = util.importExcel(file.getInputStream());
        String message = fundBankAccountService.importBank(bankList);
        return success(message);
    }

    @ApiOperation("银行网点列表")
    @PreAuthorize("@ss.hasPermi('fund:bankstation:list')")
    @GetMapping("/banklist")
    public TableDataInfo banklist(FundBankStation condition) {
        startPage();
        List<FundBankStation> list = fundBankAccountService.selectBankList(condition);
        return getDataTable(list);
    }
}
