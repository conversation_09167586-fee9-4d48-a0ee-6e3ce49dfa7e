package com.ruoyi.web.controller.fund;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysAttachment;
import com.ruoyi.system.domain.dto.FundBankAccountQuery;
import com.ruoyi.system.domain.dto.FundContractInfoQuery;
import com.ruoyi.system.domain.dto.FundPaybillDto;
import com.ruoyi.system.domain.dto.FundPaybillQuery;
import com.ruoyi.system.domain.vo.fund.*;
import com.ruoyi.system.domain.vo.project.ProjectInfoVo;
import com.ruoyi.system.domain.vo.project.ProjectManageConditionVo;
import com.ruoyi.system.service.fund.FundBankAccountService;
import com.ruoyi.system.service.fund.FundContractInfoService;
import com.ruoyi.system.service.fund.FundPaybillAuditService;
import com.ruoyi.system.service.fund.FundPaybillService;
import com.ruoyi.system.service.project.ProjectManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 拨款单控制器
 *
 * <AUTHOR>
 */
@Api(value = "拨款单控制器", tags = {"拨款单对象功能接口"})
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RestController
@RequestMapping("/fund/paybill")
@Validated
public class FundPaybillController extends BaseController {

    private final FundPaybillService fundPaybillService;
    private final FundPaybillAuditService fundPaybillAuditService;
    private final ProjectManageService projectManageService;
    private final FundContractInfoService fundContractInfoService;
    private final FundBankAccountService fundBankAccountService;

    /**
     * 分页拨款单申请列表
     *
     * @param condition 条件
     * @return 结果
     */
    @ApiOperation("查询拨款单申请列表")
    @PreAuthorize("@ss.hasPermi('fund:paybill:list')")
    @GetMapping("/list")
    public TableDataInfo list(FundPaybillQuery condition) {
        startPage();
        List<FundPaybillVo> list = fundPaybillService.listPaybill(condition);
        return getDataTable(list);
    }

    @ApiOperation("查询拨款单申请列表的金额统计")
    @PreAuthorize("@ss.hasPermi('fund:paybill:list')")
    @GetMapping("/list/amount")
    public R<FundPaybillAmountSumVo> getPaybillListAmount(FundPaybillQuery condition) {
        FundPaybillAmountSumVo vo = fundPaybillService.getPaybillListAmount(condition);
        return R.ok(vo);
    }

    @ApiOperation("拨款单项目选择列表")
    @PreAuthorize("@ss.hasPermi('fund:paybill:save')")
    @GetMapping("/project/list")
    public TableDataInfo projectList(ProjectManageConditionVo vo) {
        startPage();
        // 未结项
        vo.setIsClosed(CommonConstant.ZeroOrOne.ZERO.getValue());
        // 已入库 或者 已经加入篮子
        vo.setLibReady(CommonConstant.ZeroOrOne.ONE.getValue());
        // 已发布年度资金计划
        vo.setHasEarlyAmount(CommonConstant.ZeroOrOne.ZERO.getValue());
        List<ProjectInfoVo> list = projectManageService.getProjectManageList(vo);
        return getDataTable(list);
    }

    /**
     * 拨款单合同选择列表
     *
     * @param condition 条件
     * @return 结果
     */
    @ApiOperation("拨款单合同选择列表")
    @PreAuthorize("@ss.hasPermi('fund:paybill:save')")
    @GetMapping("/contract/list")
    public TableDataInfo list(FundContractInfoQuery condition) {
        startPage();
        List<FundContractInfoVo> list = fundContractInfoService.listContract(condition);
        return getDataTable(list);
    }

    /**
     * 拨款单收款账户选择列表
     *
     * @param condition 条件
     * @return 结果
     */
    @ApiOperation("拨款单收款账户选择列表")
    @PreAuthorize("@ss.hasPermi('fund:paybill:save')")
    @GetMapping("/bankAccount/list")
    public TableDataInfo list(FundBankAccountQuery condition) {
        startPage();
        List<FundBankAccountVo> list = fundBankAccountService.listBankAccount(condition);
        return getDataTable(list);
    }

    /**
     * 获取拨款单详细信息
     */
    @ApiOperation("获取拨款单详细信息")
    @PreAuthorize("@ss.hasAnyPermi('fund:paybill:query,fund:paybillAudit:query,fund:paybillAuditMonitor:query')")
    @GetMapping("/{id}")
    public R<FundPaybillVo> getPaybill(@ApiParam("主键")
                                       @NotNull(message = "主键不能为空")
                                       @PathVariable("id") String id) {
        return R.ok(fundPaybillService.getPaybill(id));
    }

    /**
     * 获取项目附件(过滤不需要在拨款单显示的项目附件)
     *
     * @param projId 项目id
     * @return 结果
     */
    @ApiOperation("获取项目附件")
    @GetMapping("getBillProjAttachments")
    public R<List<SysAttachment>> getBillProjAttachments(@ApiParam("项目id") @RequestParam String projId) {
        return R.ok(fundPaybillService.getBillProjAttachments(projId));
    }

    /**
     * 查询项目拨款单附件
     *
     * @param projId         项目id
     * @param excludedBillId 排除的拨款单id
     * @return 结果
     */
    @ApiOperation("查询项目拨款单附件")
    @GetMapping("getBillAttachmentsByProjId")
    public R<List<FundPaybillAttachmentVo>> getBillAttachmentsByProjId(@ApiParam("项目id") @RequestParam String projId, @ApiParam("排除的拨款单id") String excludedBillId) {
        List<FundPaybillAttachmentVo> list = fundPaybillService.getBillAttachmentsByProjId(projId, excludedBillId);
        return R.ok(list);
    }

    /**
     * 获取项目拨款单支付信息
     *
     * @param projId 项目id
     * @return 结果
     */
    @ApiOperation("获取项目拨款单支付信息")
    @GetMapping("paidInfo/project")
    public R<List<FundPaybillPaidInfoVo>> getBillPaidInfoByProjId(@ApiParam("项目id") @RequestParam String projId) {
        return R.ok(fundPaybillService.getPaybillPaidInfoByProjId(projId));
    }

    /**
     * 获取合同拨款单支付信息
     *
     * @param contractId 合同id
     * @return 结果
     */
    @ApiOperation("获取合同拨款单支付信息")
    @GetMapping("paidInfo/contract")
    public R<List<FundPaybillPaidInfoVo>> getBillPaidInfoByContractId(@ApiParam("项目id") @RequestParam String contractId) {
        return R.ok(fundPaybillService.getPaybillPaidInfoByContractId(contractId));
    }

    /**
     * 获取高质量辅助json信息
     *
     * @param id 拨款单id
     * @return 结果
     */
    @ApiOperation("获取高质量辅助json信息")
    @GetMapping("hqdpContent")
    public R<FundPaybillHqdpContentVo> getHqdpContent(@ApiParam("拨款单id") @RequestParam String id) {
        return R.ok(fundPaybillService.getHqdpContent(id));
    }

    /**
     * 保存拨款单记录
     */
    @ApiOperation("保存拨款单记录")
    @PreAuthorize("@ss.hasPermi('fund:paybill:save')")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    @Log(title = "保存拨款单", businessType = BusinessType.UPDATE)
    @PostMapping("save")
    public R<Void> save(@RequestBody FundPaybillDto dto) {
        return fundPaybillService.savePaybill(dto) ? R.ok() : R.fail();
    }

    /**
     * 提交拨款单记录
     */
    @ApiOperation("提交拨款单记录")
    @PreAuthorize("@ss.hasPermi('fund:paybill:submit')")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    @Log(title = "提交拨款单", businessType = BusinessType.UPDATE)
    @PostMapping("submit")
    public R<Void> submit(@RequestBody @Validated FundPaybillDto dto, Boolean sendSms) {
        return fundPaybillService.submitPaybill(dto, sendSms) ? R.ok() : R.fail();
    }

    /**
     * 撤回拨款单提交操作
     *
     * @param billId 拨款单id
     * @return 结果
     */
    @ApiOperation("撤回拨款单提交操作")
    @PreAuthorize("@ss.hasPermi('fund:paybill:recall')")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    @Log(title = "错发回收提交的拨款单", businessType = BusinessType.UPDATE)
    @PostMapping("recall/{billId}")
    public R<Void> doRecall(@PathVariable String billId) {
        return fundPaybillAuditService.doRecall(billId) ? R.ok() : R.fail();
    }


    /**
     * 删除拨款单记录
     */
    @ApiOperation("删除拨款单记录")
    @PreAuthorize("@ss.hasPermi('fund:paybill:remove')")
    @RepeatSubmit(interval = 1000, message = "请勿重复提交")
    @PostMapping("remove/{ids}")
    @Log(title = "删除拨款单记录", businessType = BusinessType.DELETE)
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable String[] ids) {
        return fundPaybillService.removePaybills(Arrays.asList(ids)) ? R.ok() : R.fail();
    }

    @ApiOperation("导出拨款单申请列表")
    @Log(title = "导出拨款单申请列表", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('fund:paybill:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, FundPaybillQuery condition){
        List<FundPaybillExportVo> exportVos = fundPaybillService.listPaybillForExport(condition);
        ExcelUtil<FundPaybillExportVo> util = new ExcelUtil<>(FundPaybillExportVo.class);
        util.exportExcel(response, exportVos, "拨款单申请列表");
    }


}
