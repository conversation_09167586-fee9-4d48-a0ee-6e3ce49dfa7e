package com.ruoyi.framework.aspectj;

import com.ruoyi.common.annotation.SignCheck;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.EncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * 签名解码
 */
@Slf4j
@Aspect
@Component
public class SignDecoderAspect {

    private final static String SHA256 = "SHA-256";

    private final static String SM3 = "SM3";

    /**
     * 处理请求前执行
     */
    @Around(value = "@annotation(signCheck)")
    public Object boBefore(ProceedingJoinPoint joinPoint, SignCheck signCheck) throws Throwable
    {
        var request = ServletUtils.getRequest();
        String nonce = request.getHeader("nonce");
        String ts = request.getHeader("ts");
        String appKey = request.getHeader("appKey");
        String signMethod = StringUtils.defaultIfEmpty(request.getHeader("signMethod"), SHA256);
        String signData = request.getHeader("signData");
        if (StringUtils.isEmpty(nonce)) {
            return AjaxResult.error("nonce is null");
        }
        if (StringUtils.isEmpty(ts)) {
            return AjaxResult.error("ts is null");
        }
        if (!isWithinThreeMinutes(Long.parseLong(ts))) {
            return AjaxResult.error("ts 必须要在当前时间的3分钟范围内");
        }
        if (StringUtils.isEmpty(appKey)) {
            return AjaxResult.error("appKey is null");
        }
        if (StringUtils.isEmpty(signMethod)) {
            return AjaxResult.error("signMethod is null");
        }
        if (StringUtils.isEmpty(signData)) {
            return AjaxResult.error("signData is null");
        }

        String bodyData = null;
        try (BufferedReader reader = request.getReader()) {
            StringBuilder sb = new StringBuilder();
//            String line;
            int ch;
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            bodyData = sb.toString();
        } catch (IOException e) {
            log.warn("Failed to read request body", e);
        }

        Map<String, String> map = new TreeMap<>();
        map.put("nonce", nonce);
        map.put("ts", ts);
        map.put("appKey", appKey);
        map.put("signMethod", signMethod);
        request.getParameterNames().asIterator().forEachRemaining(name -> {
            map.put(name, request.getParameter(name));
        });
        if (StringUtils.isNotEmpty(bodyData)) {
            map.put("bodyData", bodyData);
        }

        // 用于存放键值对字符串的列表
        List<String> entries = new ArrayList<>();
        // 遍历排序后的Map，并构建键值对字符串
        for (Map.Entry<String, String> entry : map.entrySet()) {
            entries.add(entry.getKey() + "=" + entry.getValue());
        }

        String appSecret = DictUtils.getDictValue("oa_setting", "appSecret");
        StringBuilder stringBuilder = new StringBuilder(appSecret + ":");
        for (int i = 0; i < entries.size(); i++) {
            stringBuilder.append(entries.get(i));
            if (i < entries.size() - 1) {
                stringBuilder.append("&");
            }
        }
        stringBuilder.append(":" + appSecret);
        if (signMethod.equalsIgnoreCase(SM3)) {
            var sm3 = EncodeUtils.calculateSm3Hex(stringBuilder.toString());
            log.info("加密内容: " + stringBuilder + ", SM3: " + sm3);
            if (!StringUtils.equals(sm3, signData)) {
                return AjaxResult.error("SM3 校验失败，请检查");
            }
        } else {
            var sha256 = EncodeUtils.calculateSha256(stringBuilder.toString());
            log.info("加密内容: " + stringBuilder + ", SHA-256: " + sha256);
            if (!StringUtils.equals(sha256, signData)) {
                return AjaxResult.error("SHA-256 校验失败，请检查");
            }
        }

        return joinPoint.proceed();
    }

    public static boolean isWithinThreeMinutes(long timestamp) {
        // 获取当前系统时间的时间戳
        long now = Instant.now().toEpochMilli();

        // 计算时间差
        long minutesBetween = ChronoUnit.MINUTES.between(Instant.ofEpochMilli(timestamp), Instant.now());

        // 判断是否在一小时以内
        return Math.abs(minutesBetween) <= 3;
    }
}
