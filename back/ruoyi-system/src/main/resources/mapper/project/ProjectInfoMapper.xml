<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.project.ProjectInfoMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.project.ProjectInfo">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="seqid" column="seqid" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="formalName" column="formal_name" jdbcType="VARCHAR"/>
        <result property="applyOrgid" column="apply_orgid" jdbcType="BIGINT"/>
        <result property="applyOrgname" column="apply_orgname" jdbcType="VARCHAR"/>
        <result property="applyTime" column="apply_time" jdbcType="DATE"/>
        <result property="submitOrgid" column="submit_orgid" jdbcType="BIGINT"/>
        <result property="submitTime" column="submit_time" jdbcType="DATE"/>
        <result property="submitOrgname" column="submit_orgname" jdbcType="VARCHAR"/>
        <result property="useOrgname" column="use_orgname" jdbcType="VARCHAR"/>
        <result property="assessOrgid" column="assess_orgid" jdbcType="BIGINT"/>
        <result property="assessOrgname" column="assess_orgname" jdbcType="VARCHAR"/>
        <result property="typeId" column="type_id" jdbcType="VARCHAR"/>
        <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
        <result property="natureCode" column="nature_code" jdbcType="VARCHAR"/>
        <result property="natureName" column="nature_name" jdbcType="VARCHAR"/>
        <result property="leader" column="leader" jdbcType="VARCHAR"/>
        <result property="leaderTel" column="leader_tel" jdbcType="VARCHAR"/>
        <result property="handler" column="handler" jdbcType="VARCHAR"/>
        <result property="handlerTel" column="handler_tel" jdbcType="VARCHAR"/>
        <result property="estAmount" column="est_amount" jdbcType="DECIMAL"/>
        <result property="libAmount" column="lib_amount" jdbcType="DECIMAL"/>
        <result property="checkAmount" column="check_amount" jdbcType="DECIMAL"/>
        <result property="contractAmount" column="contract_amount" jdbcType="DECIMAL"/>
        <result property="libState" column="lib_state" jdbcType="INTEGER"/>
        <result property="libReason" column="lib_reason" jdbcType="VARCHAR"/>
        <result property="checkState" column="check_state" jdbcType="INTEGER"/>
        <result property="checkReason" column="check_reason" jdbcType="VARCHAR"/>
        <result property="necessity" column="necessity" jdbcType="VARCHAR"/>
        <result property="basis" column="basis" jdbcType="VARCHAR"/>
        <result property="mainCnt" column="main_cnt" jdbcType="VARCHAR"/>
        <result property="actionPlan" column="action_plan" jdbcType="VARCHAR"/>
        <result property="buildBegin" column="build_begin" jdbcType="DATE"/>
        <result property="buildEnd" column="build_end" jdbcType="DATE"/>
        <result property="fundBegin" column="fund_begin" jdbcType="DATE"/>
        <result property="fundEnd" column="fund_end" jdbcType="DATE"/>
        <result property="isSzhyq" column="is_szhyq" jdbcType="VARCHAR"/>
        <result property="isJxhgl" column="is_jxhgl" jdbcType="VARCHAR"/>
        <result property="isZfgm" column="is_zfgm" jdbcType="VARCHAR"/>
        <result property="goal" column="goal" jdbcType="VARCHAR"/>
        <result property="basketCode" column="basket_code" jdbcType="INTEGER"/>
        <result property="basketName" column="basket_name" jdbcType="VARCHAR"/>
        <result property="baseProjid" column="base_projid" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="cooperateOrgid" column="cooperate_orgid" jdbcType="BIGINT"/>
        <result property="cooperateOrgname" column="cooperate_orgname" jdbcType="VARCHAR"/>
        <result property="purpose" column="purpose" jdbcType="VARCHAR"/>
        <result property="goalDetail" column="goal_detail" jdbcType="VARCHAR"/>
        <result property="isClosed" column="is_closed" jdbcType="VARCHAR"/>
        <result property="isPk" column="is_pk" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        id,seqid,name,
        formal_name,apply_orgid,apply_orgname,apply_time,
        submit_orgid,submit_orgname,submit_time,use_orgname,
        assess_orgid,assess_orgname,type_id,type_name,
        nature_code,nature_name,leader,
        leader_tel,handler,handler_tel,
        est_amount,lib_amount,check_amount,
        contract_amount,lib_state,lib_reason,
        check_state,check_reason,necessity,
        basis,main_cnt,action_plan,
        build_begin,build_end,fund_begin,
        fund_end,is_szhyq,is_jxhgl,
        is_zfgm,goal,basket_code,
        basket_name,base_projid,del_flag,
        create_time,create_by,update_time,
        update_by,cooperate_orgid,cooperate_orgname,purpose,
        goal_detail,state,is_closed,is_pk
    </sql>

    <resultMap id="BaseProjectVoMap" type="com.ruoyi.system.domain.vo.project.ProjectInfoVo" extends="BaseResultMap">
        <association property="currentYearRelation" javaType="com.ruoyi.system.domain.project.ProjectYearRelation">
            <id property="id" column="y_id" jdbcType="VARCHAR"/>
            <result property="year" column="y_year" jdbcType="INTEGER"/>
            <result property="projId" column="y_proj_id" jdbcType="VARCHAR"/>
            <result property="projType" column="proj_type" jdbcType="INTEGER"/>
            <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="DATE"/>
            <result property="isRecycle" column="is_recycle" jdbcType="VARCHAR"/>
            <result property="isClose" column="is_close" jdbcType="VARCHAR"/>
            <result property="impProj" column="imp_proj" jdbcType="VARCHAR"/>
            <result property="impCode" column="imp_code" jdbcType="INTEGER"/>
            <result property="impName" column="imp_name" jdbcType="VARCHAR"/>
        </association>
        <association property="fundPlan" javaType="com.ruoyi.system.domain.project.ProjectPlan">
            <id property="id" column="p_id" jdbcType="VARCHAR"/>
            <result property="projId" column="p_proj_id" jdbcType="VARCHAR"/>
            <result property="year" column="p_year" jdbcType="INTEGER"/>
            <result property="planState" column="p_plan_state" jdbcType="INTEGER"/>
            <result property="planType" column="p_plan_type" jdbcType="VARCHAR"/>
            <result property="declareAmount" column="p_declare_amount" jdbcType="DECIMAL"/>
            <result property="declareUserid" column="p_declare_userid" jdbcType="VARCHAR"/>
            <result property="declareTime" column="p_declare_time" jdbcType="DATE"/>
            <result property="earlyAmount" column="p_early_amount" jdbcType="DECIMAL"/>
            <result property="formalAmount" column="p_formal_amount" jdbcType="DECIMAL"/>
            <result property="adjustAmount" column="p_adjust_amount" jdbcType="DECIMAL"/>
            <result property="describe" column="p_describe" jdbcType="VARCHAR"/>
            <result property="basketId" column="p_basket_id" jdbcType="INTEGER"/>
            <result property="basketName" column="p_basket_name" jdbcType="VARCHAR"/>
            <result property="applyAdjust" column="p_apply_adjust" jdbcType="DECIMAL"/>
            <result property="applyReason" column="p_apply_reason" jdbcType="VARCHAR"/>
            <result property="applyState" column="p_apply_state" jdbcType="INTEGER"/>
            <result property="applyUserid" column="p_apply_userid" jdbcType="VARCHAR"/>
            <result property="applyTime" column="p_apply_time" jdbcType="DATE"/>
            <result property="applyRefused" column="p_apply_refused" jdbcType="VARCHAR"/>
            <result property="planDetail" column="p_plan_detail" jdbcType="VARCHAR"/>
        </association>
        <association property="projectSn" javaType="com.ruoyi.system.domain.project.ProjectSn">
            <id property="id" column="s_id" jdbcType="VARCHAR"/>
            <result property="projId" column="s_proj_id" jdbcType="VARCHAR"/>
            <result property="tempYear" column="temp_year" jdbcType="INTEGER"/>
            <result property="tempSeqid" column="temp_seqid" jdbcType="INTEGER"/>
            <result property="tempSn" column="temp_sn" jdbcType="VARCHAR"/>
            <result property="tempTime" column="temp_time" jdbcType="DATE"/>
            <result property="formalYear" column="formal_year" jdbcType="INTEGER"/>
            <result property="formalSeqid" column="formal_seqid" jdbcType="INTEGER"/>
            <result property="formalSn" column="formal_sn" jdbcType="VARCHAR"/>
            <result property="formalTime" column="formal_time" jdbcType="DATE"/>
        </association>
    </resultMap>
    <sql id="Project_Base_List">
        a.id y_id,a.year y_year,a.proj_id y_proj_id,
        a.proj_type,a.seq_no,a.update_time,
        a.is_recycle,a.is_close,a.imp_proj,
        a.imp_code,a.imp_name,
        b.id,b.seqid,b.name,
        b.formal_name,b.apply_orgid,b.apply_orgname,b.apply_time,
        b.submit_orgid,b.submit_orgname,b.submit_time,b.use_orgname,
        b.assess_orgid,b.assess_orgname,b.type_id,b.type_name,
        b.nature_code,b.nature_name,b.leader,
        b.leader_tel,b.handler,b.handler_tel,
        b.est_amount,b.lib_amount,b.check_amount,
        b.contract_amount,b.lib_state,b.lib_reason,
        b.check_state,b.check_reason,b.necessity,
        b.basis,b.main_cnt,b.action_plan,
        b.build_begin,b.build_end,b.fund_begin,
        b.fund_end,b.is_szhyq,b.is_jxhgl,
        b.is_zfgm,b.goal,b.basket_code,
        b.basket_name,b.base_projid,b.del_flag,
        b.create_time,b.create_by,b.update_time,
        b.update_by,b.cooperate_orgid,b.cooperate_orgname,b.purpose,
        b.goal_detail,b.state,b.is_closed,b.is_pk,
        c.id p_id,c.proj_id p_proj_id,c.year p_year,
        c.plan_state p_plan_state,c.plan_type p_plan_type,c.declare_amount p_declare_amount,
        c.declare_userid p_declare_userid,c.declare_time p_declare_time,c.early_amount p_early_amount,
        c.formal_amount p_formal_amount,c.adjust_amount p_adjust_amount,c.describe p_describe,
        c.basket_id p_basket_id,c.basket_name p_basket_name,c.apply_adjust p_apply_adjust,
        c.apply_reason p_apply_reason,c.apply_state p_apply_state,c.apply_userid p_apply_userid,
        c.apply_time p_apply_time,c.apply_refused p_apply_refused,c.plan_detail p_plan_detail,
        d.id s_id,d.proj_id s_proj_id,d.temp_year,
        d.temp_seqid,d.temp_sn,d.temp_time,
        d.formal_year,d.formal_seqid,d.formal_sn,
        d.formal_time
    </sql>
    <select id="getProjectInfoList" resultMap="BaseProjectVoMap">
        select
        <include refid="Project_Base_List"/>
        from project_year_relation a
        left join project_info b on a.proj_id = b.id
        left join (select * from project_plan where year = #{year}) c on b.id = c.proj_id
        left join project_sn d on b.id = d.proj_id
        left join sys_user u on u.user_name = b.create_by
        left join sys_dept h on h.dept_id = b.assess_orgid
        where a.year = #{year} and b.del_flag = '0'
        <choose>
            <when test='isNormal == "1"'>
                and a.is_recycle = '0' and a.is_close = '0'
            </when>
            <otherwise>
                and (a.is_recycle = '1' or a.is_close = '1')
            </otherwise>
        </choose>
        <if test="name != null and name != ''">
            and b.name like CONCAT('%',#{name},'%')
        </if>
        <if test='projectUnitsList.size() > 0 and onlyAssess != "1"'>
            and (b.assess_orgid in
            <foreach item="item" collection="projectUnitsList" separator="," open="(" close=")">
                #{item}
            </foreach>
            or b.apply_orgid in
            <foreach item="item" collection="projectUnitsList" separator="," open="(" close=")">
                #{item}
            </foreach>
            or b.submit_orgid in
            <foreach item="item" collection="projectUnitsList" separator="," open="(" close=")">
                #{item}
            </foreach>
            or b.cooperate_orgid in
            <foreach item="item" collection="projectUnitsList" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test='projectUnitsList.size() > 0 and onlyAssess == "1"'>
            and (b.assess_orgid in
            <foreach item="item" collection="projectUnitsList" separator="," open="(" close=")">
                #{item}
            </foreach>
            <if test="projectUnitsList.size() == 1">
                <foreach item="item" collection="projectUnitsList">
                    or (b.apply_orgid = #{item} and b.is_pk = '0')
                    or (b.cooperate_orgid = #{item} and b.is_pk = '1')
                </foreach>
            </if>
            )
        </if>
        <if test="typeId != null and typeId != ''">
            and b.type_id = #{typeId}
        </if>
        <if test="natureCode != null and natureCode != ''">
            and b.nature_code = #{natureCode}
        </if>
        <if test="isSzhyq != null and isSzhyq != ''">
            and b.is_szhyq = #{isSzhyq}
        </if>
        <if test="isJxhgl != null and isJxhgl != ''">
            and b.is_jxhgl = #{isJxhgl}
        </if>
        <if test="isZfgm != null and isZfgm != ''">
            and b.is_zfgm = #{isZfgm}
        </if>
        <if test="basketCode != null and basketCode != '' ">
            and b.basket_code = #{basketCode}
        </if>
        <if test="libState != null and libState != '' ">
            and b.lib_state = #{libState}
        </if>
        <if test="checkState != null and checkState != '' ">
            and b.check_state = #{checkState}
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
          and (
                <foreach collection="purposeIds" item="item" separator="or">
                    b.purpose like CONCAT('%"',#{item},'"%')
                </foreach>
            )
        </if>
        ${params.dataScope}
        order by h.seq_no asc,b.seqid asc
    </select>
    <select id="getProjectManageList" resultMap="BaseProjectVoMap">
        select
        <include refid="Project_Base_List"/>
        from project_year_relation a
        left join project_info b on a.proj_id = b.id
        left join (select * from project_plan where year = #{year}) c on b.id = c.proj_id
        left join project_sn d on b.id = d.proj_id
        left join sys_user u on u.user_name = b.create_by
        left join sys_dept h on h.dept_id = b.assess_orgid
        where a.year = #{year} and a.is_recycle = '0' and a.is_close = '0' and b.del_flag = '0' and b.state &gt;= 1
        <if test="assessOrgid != null and assessOrgid != ''">
            and b.assess_orgid = #{assessOrgid}
        </if>
        <if test="typeId != null and typeId != ''">
            and b.type_id = #{typeId}
        </if>
        <if test="natureCode != null and natureCode != ''">
            and b.nature_code = #{natureCode}
        </if>
        <if test="isSzhyq != null and isSzhyq != ''">
            and b.is_szhyq = #{isSzhyq}
        </if>
        <if test="name != null and name != ''">
            and b.name like CONCAT('%',#{name},'%')
        </if>
        <if test='hasEarlyAmount == "1" and hasAdjustAmount != "1"'>
            and c.plan_state = 9
        </if>
        <if test='hasEarlyAmount != "1" and hasAdjustAmount == "1"'>
            and c.adjust_amount is not null
        </if>
        <if test='hasEarlyAmount == "1" and hasAdjustAmount == "1"'>
            and (c.plan_state = 9 or c.adjust_amount is not null)
        </if>
        <if test="isClosed != null and isClosed != ''">
            and b.is_closed = #{isClosed}
        </if>
        <if test="libReady == '1'">
            and (b.lib_state = 1 or b.basket_code is not null)
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
           and (
                <foreach collection="purposeIds" item="item" separator="or">
                    b.purpose like CONCAT('%"',#{item},'"%')
                </foreach>
            )
        </if>
        ${params.dataScope}
        order by h.seq_no asc, b.seqid asc
    </select>

    <select id="getProjectInfoAndPlanById" resultMap="BaseProjectVoMap">
        select b.id,
               b.seqid,
               b.name,
               b.formal_name,
               b.apply_orgid,
               b.apply_orgname,
               b.apply_time,
               b.submit_orgid,
               b.submit_orgname,
               b.submit_time,
               b.use_orgname,
               b.assess_orgid,
               b.assess_orgname,
               b.type_id,
               b.type_name,
               b.nature_code,
               b.nature_name,
               b.leader,
               b.leader_tel,
               b.handler,
               b.handler_tel,
               b.est_amount,
               b.lib_amount,
               b.check_amount,
               b.contract_amount,
               b.lib_state,
               b.lib_reason,
               b.check_state,
               b.check_reason,
               b.necessity,
               b.basis,
               b.main_cnt,
               b.action_plan,
               b.build_begin,
               b.build_end,
               b.fund_begin,
               b.fund_end,
               b.is_szhyq,
               b.is_jxhgl,
               b.is_zfgm,
               b.goal,
               b.basket_code,
               b.basket_name,
               b.base_projid,
               b.del_flag,
               b.create_time,
               b.create_by,
               b.update_time,
               b.update_by,
               b.cooperate_orgid,
               b.cooperate_orgname,
               b.purpose,
               b.goal_detail,
               b.state,
               b.is_closed,
               b.is_pk,
               c.id             p_id,
               c.proj_id        p_proj_id,
               c.year           p_year,
               c.plan_state     p_plan_state,
               c.plan_type      p_plan_type,
               c.declare_amount p_declare_amount,
               c.declare_userid p_declare_userid,
               c.declare_time   p_declare_time,
               c.early_amount   p_early_amount,
               c.formal_amount  p_formal_amount,
               c.adjust_amount  p_adjust_amount,
               c.describe       p_describe,
               c.basket_id      p_basket_id,
               c.basket_name    p_basket_name,
               c.apply_adjust   p_apply_adjust,
               c.apply_reason   p_apply_reason,
               c.apply_state    p_apply_state,
               c.apply_userid   p_apply_userid,
               c.apply_time     p_apply_time,
               c.apply_refused  p_apply_refused,
               c.plan_detail    p_plan_detail,
               d.id             s_id,
               d.proj_id        s_proj_id,
               d.temp_year,
               d.temp_seqid,
               d.temp_sn,
               d.temp_time,
               d.formal_year,
               d.formal_seqid,
               d.formal_sn,
               d.formal_time
        from project_info b
                 left join (select * from project_plan where year = #{year}) c on b.id = c.proj_id
                 left join project_sn d on b.id = d.proj_id
        where b.id = #{id}
    </select>

    <resultMap id="reproducibleProjectMap" type="com.ruoyi.system.domain.vo.project.ProjectInfoCopyVo">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="handler" column="handler"/>
        <result property="estAmount" column="est_amount"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
    </resultMap>
    <select id="getReproducibleProject" resultMap="reproducibleProjectMap">
        select a.id,a.name,a.handler,a.est_amount,LEAST(a.build_begin ,a.fund_begin) as begin_date,GREAT(a.build_end
        ,a.fund_end) as end_date from project_info a
        left join project_year_relation b on a.id = b.proj_id
        left join sys_user u on u.user_name = a.create_by
        left join sys_dept h on h.dept_id = a.apply_orgid
        where a.basket_code is not null and a.del_flag = '0' and b.is_recycle = '0' and b.is_close = '0'
        <if test="natureCode != null and natureCode != ''">
            and a.nature_code = #{natureCode}
        </if>
        <if test="year != null and year != ''">
            and b.year = #{year}
        </if>
        ${params.dataScope}
        order by h.seq_no , a.seqid
    </select>

    <resultMap id="projectInfoOfPlanMap" type="com.ruoyi.system.domain.vo.project.ProjectInfoOfSelectVo">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="applyOrgid" column="apply_orgid"/>
        <result property="applyOrgname" column="apply_orgname"/>
        <result property="assessOrgid" column="assess_orgid"/>
        <result property="assessOrgname" column="assess_orgname"/>
        <result property="cooperateOrgid" column="cooperate_orgid"/>
        <result property="cooperateOrgname" column="cooperate_Orgname"/>
        <result property="estAmount" column="est_amount"/>
        <result property="beginDate" column="begin_date"/>
        <result property="endDate" column="end_date"/>
    </resultMap>
    <select id="getProjectInfoOfSelect" resultMap="projectInfoOfPlanMap">
        select
        a.id,a.name,a.assess_orgid,a.assess_orgname,a.apply_orgname,a.cooperate_orgid,a.cooperate_orgname,a.est_amount,LEAST(a.build_begin
        ,a.fund_begin) as begin_date,GREAT(a.build_end ,a.fund_end) as end_date from project_info a
        left join project_year_relation b on a.id = b.proj_id
        left join sys_user u on u.user_name = a.create_by
        left join sys_dept h on h.dept_id = a.assess_orgid
        where a.basket_code is not null and a.del_flag = '0' and b.is_recycle = '0' and b.is_close = '0' and a.state &gt;= 1
        <if test="year != null and year != ''">
            and b.year = #{year}
        </if>
        <if test="name != null and name != ''">
            and a.name like CONCAT('%',#{name},'%')
        </if>
        <if test="assessOrgid != null and assessOrgid != ''">
            and a.assess_orgid = #{assessOrgid}
        </if>
        <if test="projectUnitsList != null and projectUnitsList.size() > 0">
            and (a.apply_orgid in
            <foreach item="item" collection="projectUnitsList" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR a.submit_orgid in
            <foreach item="item" collection="projectUnitsList" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR a.cooperate_orgid in
            <foreach item="item" collection="projectUnitsList" separator="," open="(" close=")">
                #{item}
            </foreach>
                )
        </if>
        <if test="typeId != null and typeId != ''">
            and a.type_id = #{typeId}
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
           and (
                <foreach collection="purposeIds" item="item" separator="or">
                    a.purpose like CONCAT('%"',#{item},'"%')
                </foreach>
            )
        </if>
        ${params.dataScope}
        order by h.seq_no asc, a.seqid asc
    </select>

    <resultMap id="ProjectDetailForUsagePlanMapVo" type="com.ruoyi.system.domain.vo.project.ProjectInfoVo"
               extends="BaseResultMap">
        <association property="currentYearRelation" javaType="com.ruoyi.system.domain.project.ProjectYearRelation">
            <id property="id" column="y_id" jdbcType="VARCHAR"/>
            <result property="year" column="y_year" jdbcType="INTEGER"/>
            <result property="projId" column="y_proj_id" jdbcType="VARCHAR"/>
            <result property="projType" column="proj_type" jdbcType="INTEGER"/>
            <result property="seqNo" column="seq_no" jdbcType="INTEGER"/>
            <result property="updateTime" column="update_time" jdbcType="DATE"/>
            <result property="isRecycle" column="is_recycle" jdbcType="VARCHAR"/>
            <result property="isClose" column="is_close" jdbcType="VARCHAR"/>
            <result property="impProj" column="imp_proj" jdbcType="VARCHAR"/>
            <result property="impCode" column="imp_code" jdbcType="INTEGER"/>
            <result property="impName" column="imp_name" jdbcType="VARCHAR"/>
        </association>
        <association property="fundPlan" javaType="com.ruoyi.system.domain.project.ProjectPlan">
            <id property="id" column="p_id" jdbcType="VARCHAR"/>
            <result property="projId" column="p_proj_id" jdbcType="VARCHAR"/>
            <result property="year" column="p_year" jdbcType="INTEGER"/>
            <result property="planState" column="p_plan_state" jdbcType="INTEGER"/>
            <result property="planType" column="p_plan_type" jdbcType="VARCHAR"/>
            <result property="declareAmount" column="p_declare_amount" jdbcType="DECIMAL"/>
            <result property="declareUserid" column="p_declare_userid" jdbcType="VARCHAR"/>
            <result property="declareTime" column="p_declare_time" jdbcType="DATE"/>
            <result property="earlyAmount" column="p_early_amount" jdbcType="DECIMAL"/>
            <result property="formalAmount" column="p_formal_amount" jdbcType="DECIMAL"/>
            <result property="adjustAmount" column="p_adjust_amount" jdbcType="DECIMAL"/>
            <result property="describe" column="p_describe" jdbcType="VARCHAR"/>
            <result property="basketId" column="p_basket_id" jdbcType="INTEGER"/>
            <result property="basketName" column="p_basket_name" jdbcType="VARCHAR"/>
            <result property="applyAdjust" column="p_apply_adjust" jdbcType="DECIMAL"/>
            <result property="applyReason" column="p_apply_reason" jdbcType="VARCHAR"/>
            <result property="applyState" column="p_apply_state" jdbcType="INTEGER"/>
            <result property="applyUserid" column="p_apply_userid" jdbcType="VARCHAR"/>
            <result property="applyTime" column="p_apply_time" jdbcType="DATE"/>
            <result property="applyRefused" column="p_apply_refused" jdbcType="VARCHAR"/>
            <result property="planDetail" column="p_plan_detail" jdbcType="VARCHAR"/>
        </association>
        <association property="prevFundPlan" javaType="com.ruoyi.system.domain.project.ProjectPlan">
            <id property="id" column="pv_id" jdbcType="VARCHAR"/>
            <result property="projId" column="pv_proj_id" jdbcType="VARCHAR"/>
            <result property="year" column="pv_year" jdbcType="INTEGER"/>
            <result property="planState" column="pv_plan_state" jdbcType="INTEGER"/>
            <result property="planType" column="pv_plan_type" jdbcType="VARCHAR"/>
            <result property="declareAmount" column="pv_declare_amount" jdbcType="DECIMAL"/>
            <result property="declareUserid" column="pv_declare_userid" jdbcType="VARCHAR"/>
            <result property="declareTime" column="pv_declare_time" jdbcType="DATE"/>
            <result property="earlyAmount" column="pv_early_amount" jdbcType="DECIMAL"/>
            <result property="formalAmount" column="pv_formal_amount" jdbcType="DECIMAL"/>
            <result property="adjustAmount" column="pv_adjust_amount" jdbcType="DECIMAL"/>
            <result property="describe" column="pv_describe" jdbcType="VARCHAR"/>
            <result property="basketId" column="pv_basket_id" jdbcType="INTEGER"/>
            <result property="basketName" column="pv_basket_name" jdbcType="VARCHAR"/>
            <result property="applyAdjust" column="pv_apply_adjust" jdbcType="DECIMAL"/>
            <result property="applyReason" column="pv_apply_reason" jdbcType="VARCHAR"/>
            <result property="applyState" column="pv_apply_state" jdbcType="INTEGER"/>
            <result property="applyUserid" column="pv_apply_userid" jdbcType="VARCHAR"/>
            <result property="applyTime" column="pv_apply_time" jdbcType="DATE"/>
            <result property="applyRefused" column="pv_apply_refused" jdbcType="VARCHAR"/>
            <result property="planDetail" column="pv_plan_detail" jdbcType="VARCHAR"/>
        </association>
        <association property="projectSn" javaType="com.ruoyi.system.domain.project.ProjectSn">
            <id property="id" column="s_id" jdbcType="VARCHAR"/>
            <result property="projId" column="s_proj_id" jdbcType="VARCHAR"/>
            <result property="tempYear" column="temp_year" jdbcType="INTEGER"/>
            <result property="tempSeqid" column="temp_seqid" jdbcType="INTEGER"/>
            <result property="tempSn" column="temp_sn" jdbcType="VARCHAR"/>
            <result property="tempTime" column="temp_time" jdbcType="DATE"/>
            <result property="formalYear" column="formal_year" jdbcType="INTEGER"/>
            <result property="formalSeqid" column="formal_seqid" jdbcType="INTEGER"/>
            <result property="formalSn" column="formal_sn" jdbcType="VARCHAR"/>
            <result property="formalTime" column="formal_time" jdbcType="DATE"/>
        </association>
    </resultMap>

    <select id="getProjectDetailForUsagePlan" resultMap="ProjectDetailForUsagePlanMapVo">
        select
        a.id y_id,a.year y_year,a.proj_id y_proj_id,
        a.proj_type,a.seq_no,a.update_time,
        a.is_recycle,a.is_close,a.imp_proj,
        a.imp_code,a.imp_name,
        b.id,b.seqid,b.name,
        b.formal_name,b.apply_orgid,b.apply_orgname,b.apply_time,
        b.submit_orgid,b.submit_orgname,b.submit_time,b.use_orgname,
        b.assess_orgid,b.assess_orgname,b.type_id,b.type_name,
        b.nature_code,b.nature_name,b.leader,
        b.leader_tel,b.handler,b.handler_tel,
        b.est_amount,b.lib_amount,b.check_amount,
        b.contract_amount,b.lib_state,b.lib_reason,
        b.check_state,b.check_reason,b.necessity,
        b.basis,b.main_cnt,b.action_plan,
        b.build_begin,b.build_end,b.fund_begin,
        b.fund_end,b.is_szhyq,b.is_jxhgl,
        b.is_zfgm,b.goal,b.basket_code,
        b.basket_name,b.base_projid,b.del_flag,
        b.create_time,b.create_by,b.update_time,
        b.update_by,b.cooperate_orgid,b.cooperate_orgname,
        b.purpose,b.goal_detail,b.state,b.is_closed,b.is_pk,
        c.id p_id,c.proj_id p_proj_id,c.year p_year,
        c.plan_state p_plan_state,c.plan_type p_plan_type,c.declare_amount p_declare_amount,
        c.declare_userid p_declare_userid,c.declare_time p_declare_time,c.early_amount p_early_amount,
        c.formal_amount p_formal_amount,c.adjust_amount p_adjust_amount,c.describe p_describe,
        c.basket_id p_basket_id,c.basket_name p_basket_name,c.apply_adjust p_apply_adjust,
        c.apply_reason p_apply_reason,c.apply_state p_apply_state,c.apply_userid p_apply_userid,
        c.apply_time p_apply_time,c.apply_refused p_apply_refused,c.plan_detail p_plan_detail,
        d.id pv_id,d.proj_id pv_proj_id,d.year pv_year,
        d.plan_state pv_plan_state,d.plan_type pv_plan_type,d.declare_amount pv_declare_amount,
        d.declare_userid pv_declare_userid,d.declare_time pv_declare_time,d.early_amount pv_early_amount,
        d.formal_amount pv_formal_amount,d.adjust_amount pv_adjust_amount,d.describe pv_describe,
        d.basket_id pv_basket_id,d.basket_name pv_basket_name,d.apply_adjust pv_apply_adjust,
        d.apply_reason pv_apply_reason,d.apply_state pv_apply_state,d.apply_userid pv_apply_userid,
        d.apply_time pv_apply_time,d.apply_refused pv_apply_refused,d.plan_detail pv_plan_detail,
        e.id s_id,e.proj_id s_proj_id,e.temp_year,
        e.temp_seqid,e.temp_sn,e.temp_time,
        e.formal_year,e.formal_seqid,e.formal_sn,
        e.formal_time
        from project_info b
        left join (select * from project_year_relation where year = #{year}) a on a.proj_id = b.id
        left join (select * from project_plan where year = #{year}) c on b.id = c.proj_id
        left join (select * from project_plan where year = #{year} - 1 ) d on b.id = d.proj_id
        left join project_sn e on b.id = e.proj_id
        left join sys_dept h on h.dept_id = b.assess_orgid
        <where>
            <if test="pIds != null and pIds.size() > 0">
                b.id in
                <foreach item="id" collection="pIds" separator="," close=")" open="(" index="">
                    #{id}
                </foreach>
            </if>
            <if test="assessId != null and assessId != ''">
                and b.assess_orgid  = #{assessId}
            </if>
            <if test="submitId != null and submitId != ''">
                and b.submit_orgid = #{submitId}
            </if>
        </where>
        order by h.seq_no asc, b.seqid asc
    </select>
    <select id="getProjectListForSetting" resultType="com.ruoyi.system.domain.project.ProjectSet">
        select
        a.id, a.name, a.formal_name, a.apply_orgid,
        a.apply_orgname,a.type_id, a.type_name,
        LEAST(a.build_begin ,a.fund_begin) as begin_date, GREAT(a.build_end ,a.fund_end) as end_date, a.is_szhyq,
        a.is_jxhgl, a.is_zfgm,
        a.basket_code, a.basket_name, a.state, a.purpose
        from project_info a
        left join project_year_relation b on a.id = b.proj_id
        left join sys_user u on u.user_name = a.create_by
        left join sys_dept h on h.dept_id = a.assess_orgid
        where a.del_flag = '0' and b.is_recycle = '0' and b.is_close = '0' and a.is_closed = '0'
        <if test="name != null and name != ''">
            and a.name like concat('%', #{name}, '%')
        </if>
        <if test="applyOrgid != null">
            and a.apply_orgid = #{applyOrgid}
        </if>
        <if test="typeId != null">
            and a.type_id = #{typeId}
        </if>
        <if test="beginYear != null">
            and b.year &gt;= #{beginYear}
        </if>
        <if test="endYear != null">
            and b.year &lt;= #{endYear}
        </if>
        <if test="isSzhyq != null and isSzhyq != ''">
            and a.is_szhyq = #{isSzhyq}
        </if>
        <if test="isJxhgl != null and isJxhgl != ''">
            and a.is_jxhgl = #{isJxhgl}
        </if>
        <if test="basketCode != null">
            and a.basket_code = #{basketCode}
        </if>
        <if test="state != null">
            and a.state = #{state}
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
          and (
                <foreach collection="purposeIds" item="item" separator="or">
                    a.purpose like CONCAT('%"',#{item},'"%')
                </foreach>
            )
        </if>
        ${params.dataScope}
        order by h.seq_no asc, a.seqid asc
    </select>

    <select id="getMaxSeqId" resultType="java.lang.Integer">
        select IFNULL(max(seqid), 0) as maxSeqId from project_info
    </select>

    <select id="getProjectBudgetExecutionList" resultMap="BaseProjectVoMap">
        select
        <include refid="Project_Base_List"/>
        from project_year_relation a
        left join project_info b on a.proj_id = b.id
        left join project_plan c on b.id = c.proj_id and c.year = #{year}
        left join project_sn d on b.id = d.proj_id
        where a.year = #{year} and a.is_recycle = '0' and a.is_close = '0' and b.del_flag = '0' and b.state &gt;= 1 and c.plan_state = 9
        <if test="assessId != null and assessId != ''">
            and b.assess_orgid = #{assessId}
        </if>
        <if test="submitIds != null and submitIds.size() > 0">
            and b.submit_orgid in
            <foreach item="item" collection="submitIds" separator="," close=")" open="(" index="">
                #{item}
            </foreach>
        </if>
        order by b.seqid
    </select>

    <select id="getProjectChecklist" resultType="com.ruoyi.system.domain.api.ProjectChecklist">
        select
        a.id, a.name, a.formal_name,ifnull(c.formal_sn, c.temp_sn) as sn, a.submit_orgname as dept
        from project_info a
        left join project_year_relation b on a.id = b.proj_id and b.year = #{year}
        left join project_sn c on a.id = c.proj_id
        where a.del_flag = '0' and b.is_recycle = '0' and b.is_close = '0' and a.is_closed = '0'
        <if test="name != null and name != ''">
            and a.name like concat('%', #{name}, '%')
        </if>
        <if test="mobile != null and mobile != ''">
            and a.submit_orgid = (select dept_id from sys_user where phonenumber = #{mobile})
        </if>
    </select>

    <select id="getProjectQueryList" resultType="com.ruoyi.system.domain.project.ProjectQueryEntity">
        select
            a.id, a.name, ifnull(b.formal_sn, b.temp_sn) as sn, IF(a.cooperate_orgid IS NOT NULL , a.apply_orgname || '(' || a.cooperate_orgname || ')', a.apply_orgname ) AS org_name,
            a.est_amount,a.lib_amount, a.check_amount, LEAST(a.build_begin ,a.fund_begin) as begin_date, GREAT(a.build_end ,a.fund_end) as end_date,
            a.state as status, a.basket_code, a.basket_name
        from project_year_relation e
        left join project_info a on a.id = e.proj_id
        left join project_sn b on a.id = b.proj_id
        left join sys_user u on u.user_name = a.create_by
        left join sys_dept h on h.dept_id = a.assess_orgid
        where a.del_flag = '0'
        <if test="name != null and name != ''">
            and a.name like concat('%', #{name}, '%')
        </if>
        <if test="applyOrgid != null">
            and a.apply_orgid = #{applyOrgid}
        </if>
        <if test="cooperateOrgid != null">
            and a.cooperate_orgid = #{cooperateOrgid}
        </if>
        <if test="cooperateOrgname != null and cooperateOrgname != ''">
            and a.cooperate_orgname like concat('%', #{cooperateOrgname}, '%')
        </if>
        <if test="natureCode != null">
            and a.nature_code = #{natureCode}
        </if>
        <if test="typeId != null">
            and a.type_id = #{typeId}
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
          and (
                <foreach collection="purposeIds" item="item" separator="or">
                    a.purpose like CONCAT('%"',#{item},'"%')
                </foreach>
            )
        </if>
        <if test="status != null">
            and a.state = #{status}
        </if>
        <if test="beginYear != null">
            and e.year &gt;= #{beginYear}
        </if>
        <if test="endYear != null">
            and e.year &lt;= #{endYear}
        </if>
        <if test="basketCode != null">
            and a.basket_code = #{basketCode}
        </if>
        <if test='zfType != null and zfType == "0"'>
            and a.is_szhyq = '1'
        </if>
        <if test='zfType != null and zfType == "1"'>
            and a.is_jxhgl = '1'
        </if>
        <if test='zfType != null and zfType == "2"'>
            and a.is_zfgm = '1'
        </if>
        ${params.dataScope}
        order by h.seq_no , a.seqid
    </select>

    <select id="getPlanQueryList" resultType="com.ruoyi.system.domain.project.PlanQueryEntity">
        select
            a.id, a.name, ifnull(c.formal_sn, c.temp_sn) as sn, IF(a.cooperate_orgid is not null , a.apply_orgname || '(' || a.cooperate_orgname || ')', a.apply_orgname ) as org_name,
            b.plan_state as status, b.year, b.formal_amount as curr_plan, b.adjust_amount, ifnull(a.contract_amount,  a.check_amount) as check_amount,
        (
        SELECT IFNULL(sum(pay_amount),0)/10000 FROM fund_paybill_skr c
        LEFT JOIN fund_paybill d ON c.bill_id = d.id
        WHERE YEAR(pay_date) = b.year  AND d.proj_id = a.id
        ) as curr_payed, (
        SELECT IFNULL(sum(pay_amount),0)/10000 FROM fund_paybill_skr c
        LEFT JOIN fund_paybill d ON c.bill_id = d.id
        WHERE YEAR(pay_date) &lt; b.year  AND d.proj_id = a.id
        ) as prev_payed
        from project_info a
        join project_plan b on a.id = b.proj_id
        left join project_sn c on a.id = c.proj_id
        left join sys_user u on u.user_name = a.create_by
        left join sys_dept h on h.dept_id = a.assess_orgid
        where a.del_flag = '0'
        <if test="name != null and name != ''">
            and a.name like concat('%', #{name}, '%')
        </if>
        <if test="applyOrgid != null">
            and a.apply_orgid = #{applyOrgid}
        </if>
        <if test="cooperateOrgid != null">
            and a.cooperate_orgid = #{cooperateOrgid}
        </if>
        <if test="cooperateOrgname != null and cooperateOrgname != ''">
            and a.cooperate_orgname like concat('%', #{cooperateOrgname}, '%')
        </if>
        <if test="natureCode != null">
            and a.nature_code = #{natureCode}
        </if>
        <if test="typeId != null">
            and a.type_id = #{typeId}
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
          and (
                <foreach collection="purposeIds" item="item" separator="or">
                     a.purpose like CONCAT('%"',#{item},'"%')
                </foreach>
            )
        </if>
        <if test="status != null">
            and b.plan_state = #{status}
        </if>
        <if test="beginYear != null">
            and b.year &gt;= #{beginYear}
        </if>
        <if test="endYear != null">
            and b.year &lt;= #{endYear}
        </if>
        ${params.dataScope}
        order by h.seq_no , a.seqid
    </select>


    <select id="getPlanAdjustQueryList" resultType="com.ruoyi.system.domain.project.PlanAdjustQueryEntity">
        select
        a.id, a.name,ifnull(c.formal_sn, c.temp_sn) as sn, IF(a.cooperate_orgid is not null , a.apply_orgname || '(' || a.cooperate_orgname || ')', a.apply_orgname ) as org_name,
        b.year, b.formal_amount as curr_plan, b.apply_adjust, b.apply_reason, b.apply_refused, b.apply_state as status, a.purpose
        from project_info a
        join project_plan b on a.id = b.proj_id
        left join project_sn c on a.id = c.proj_id
        left join sys_user u on u.user_name = a.create_by
        left join sys_dept h on h.dept_id = a.assess_orgid
        where a.del_flag = '0'  and b.apply_state is not null
        <if test="name != null and name != ''">
            and a.name like concat('%', #{name}, '%')
        </if>
        <if test="applyOrgid != null">
            and a.apply_orgid = #{applyOrgid}
        </if>
        <if test="cooperateOrgid != null">
            and a.cooperate_orgid = #{cooperateOrgid}
        </if>
        <if test="cooperateOrgname != null and cooperateOrgname != ''">
            and a.cooperate_orgname like concat('%', #{cooperateOrgname}, '%')
        </if>
        <if test="natureCode != null">
            and a.nature_code = #{natureCode}
        </if>
        <if test="typeId != null">
            and a.type_id = #{typeId}
        </if>
        <if test="purposeIds != null and purposeIds.size() > 0">
           and (
                <foreach collection="purposeIds" item="item" separator="or">
                    a.purpose like CONCAT('%"',#{item},'"%')
                </foreach>
            )
        </if>
        <if test="status != null">
            and b.apply_state = #{status}
        </if>
        <if test="beginYear != null">
            and b.year &gt;= #{beginYear}
        </if>
        <if test="endYear != null">
            and b.year &lt;= #{endYear}
        </if>
        ${params.dataScope}
        order by h.seq_no , a.seqid
    </select>

    <select id="getBudgetQueryList" resultType="com.ruoyi.system.domain.fund.BudgetQueryEntity">
        select * from (
            SELECT b.id,b.name,b.apply_orgid,b.cooperate_orgid,b.submit_orgid,b.submit_orgname,b.assess_orgid,b.assess_orgname,b.type_id,b.type_name,e.proj_type,ifnull(b.contract_amount,IFNULL(b.check_amount,IFNULL(b.lib_amount,b.est_amount))) * 10000 AS approved_amount,c.searchYear AS YEAR,
                   ifnull(d.adjust_amount,IFNULL(d.formal_amount,IFNULL(d.early_amount,IFNULL(d.declare_amount,0)))) * 10000 AS arranged_amount,c.pay_amount AS executed_amount,b.create_by,b.seqid,e.proj_type as nature_code,
                   case when ifnull(d.adjust_amount,IFNULL(d.formal_amount,IFNULL(d.early_amount,IFNULL(d.declare_amount,0)))) * 10000 != 0 then ROUND((c.pay_amount / (ifnull(d.adjust_amount,IFNULL(d.formal_amount,IFNULL(d.early_amount,IFNULL(d.declare_amount,0)))) * 10000)) * 100, 2) ELSE 0 END AS executed_rate
            FROM project_info b
            Right JOIN (
                SELECT a.proj_id,YEAR(a.pay_date) AS searchYear,ifnull(sum(pay_amount),0) AS pay_amount FROM (
                    SELECT fpbs.pay_amount,fpb.proj_id,fpbs.pay_date FROM fund_paybill_skr fpbs LEFT JOIN fund_paybill fpb ON fpbs.bill_id = fpb.id
                ) a WHERE a.pay_date &gt;= #{beginDate} AND a.pay_date &lt;= #{endDate} GROUP BY a.proj_id,YEAR(a.pay_date)
            ) c ON c.proj_id = b.id
            LEFT JOIN project_plan d ON d.proj_id = c.proj_id AND d.year = c.searchYear
            LEFT JOIN project_year_relation e ON e.proj_id = c.proj_id AND e.year = c.searchYear
        ) g
       left join sys_user u on u.user_name = g.create_by
       left join sys_dept h on h.dept_id = g.assess_orgid
       where 1 = 1
        <if test="name != null and name != ''">
            and g.name like concat('%', #{name}, '%')
        </if>
        <if test="submitOrgid != null">
            and g.submit_orgid = #{submitOrgid}
        </if>
        <if test="assessOrgid != null">
            and g.assess_orgid = #{assessOrgid}
        </if>
        <if test="typeId != null and typeId != ''">
            and g.type_id = #{typeId}
        </if>
        <if test="natureCode != null">
            and g.nature_code = #{natureCode}
        </if>
        <if test="approvedAmount != null">
            and g.approved_amount = #{approvedAmount}
        </if>
        <if test="minRate != null">
            and g.executed_rate &gt;= #{minRate}
        </if>
        <if test="maxRate != null">
            and g.executed_rate &lt;= #{maxRate}
        </if>
        ${params.dataScope}
        order by h.seq_no , g.seqid
    </select>

</mapper>
