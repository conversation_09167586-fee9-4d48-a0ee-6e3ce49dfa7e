package com.ruoyi.system.domain.fund;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 高质量拨款单辅助信息
 *
 * @TableName fund_paybill_hqdp
 */
@TableName(value = "fund_paybill_hqdp")
@Data
public class FundPaybillHqdp {
    /**
     * 主键;主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 项目编号;项目编号
     */
    private String projCode;

    /**
     * 项目名称;项目名称
     */
    private String projName;

    /**
     * 企业名称;企业名称
     */
    private String companyName;

    /**
     * 审批处室;审批处室
     */
    private String auditOrgname;

    /**
     * 总投资额;总投资额
     */
    private BigDecimal totalAmount;

    /**
     * 大类;大类
     */
    private String typeFirst;

    /**
     * 小类;小类
     */
    private String typeSecond;

    /**
     * 子类;子类
     */
    private String typeThird;

    /**
     * 批次号;批次号
     */
    private Integer batchIndex;

    /**
     * 批次金额;批次金额
     */
    private BigDecimal batchAmount;

    /**
     * 收款户名;收款户名
     */
    private String accountName;

    /**
     * 收款户号;收款户号
     */
    private String accountNumber;

    /**
     * 开户银行名称;开户银行名称
     */
    private String bankName;

    /**
     * 开户网点代号;开户网点代号
     */
    private String stationCode;

    /**
     * 请求IP地址;请求IP地址
     */
    private String reqIp;

    /**
     * 请求时间;请求时间
     */
    private Date reqTime;

    /**
     * 项目描述;项目描述
     */
    private String describe;

    /**
     * 请求内容;请求内容
     */
    private String reqContent;
}