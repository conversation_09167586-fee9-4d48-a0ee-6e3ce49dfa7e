package com.ruoyi.system.service.impl.fund;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysAttachment;
import com.ruoyi.system.domain.dto.FundPaybillDto;
import com.ruoyi.system.domain.dto.FundPaybillQuery;
import com.ruoyi.system.domain.fund.*;
import com.ruoyi.system.domain.project.*;
import com.ruoyi.system.domain.vo.fund.*;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.SysAttachmentService;
import com.ruoyi.system.service.fund.*;
import com.ruoyi.system.mapper.fund.FundPaybillMapper;
import com.ruoyi.system.service.project.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【fund_paybill(资金拨款单)】的数据库操作Service实现
 * @date 2025-06-23 10:19:21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FundPaybillServiceImpl extends MPJBaseServiceImpl<FundPaybillMapper, FundPaybill> implements FundPaybillService {

    private final ProjectInfoService projectInfoService;
    private final ProjectSnService projectSnService;
    private final ProjectChildrenService projectChildrenService;
    private final ProjectChildrenBudgetService projectChildrenBudgetService;
    private final ProjectPlanService projectPlanService;
    private final SysAttachmentService attachmentService;
    private final FundPaybillSkrService fundPaybillSkrService;
    private final FundContractInfoService fundContractInfoService;
    private final ObjectMapper objectMapper;
    private final ISysConfigService configService;
    private final FundPaybillNodeService fundPaybillNodeService;
    private final FundPaybillMapper fundPaybillMapper;
    private final SysAttachmentService sysAttachmentService;
    private final ExecutorService virtualThreadExecutor;
    private final ISysDictDataService dictDataService;
    private final ISysUserService sysUserService;
    private final FundPaybillHqdpService fundPaybillHqdpService;

    @DataScope(deptAlias = "d", userAlias = "u")
    @Override
    public List<FundPaybillVo> listPaybill(FundPaybillQuery condition) {
        SysUser opUser = SecurityUtils.getLoginUser().getUser();
        List<FundPaybillVo> vos = this.baseMapper.selectPaybillList(condition);

        // 为每个拨款单创建异步任务，使用虚拟线程执行
        List<CompletableFuture<Void>> futures = vos.stream()
                .map(item -> CompletableFuture.runAsync(() -> fillItemInfo(item, opUser), virtualThreadExecutor))
                .toList();

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return vos;
    }

    @DataScope(deptAlias = "d", userAlias = "u")
    @Override
    public FundPaybillAmountSumVo getPaybillListAmount(FundPaybillQuery condition) {
        FundPaybillAmountSumVo sumVo = this.baseMapper.selectPaybillAmount(condition);
        if (sumVo == null) {
            return new FundPaybillAmountSumVo(BigDecimal.ZERO, BigDecimal.ZERO);
        }
        return sumVo;
    }

    @DataScope(deptAlias = "d", userAlias = "u")
    @Override
    public List<FundPaybillExportVo> listPaybillForExport(FundPaybillQuery condition) {
        List<FundPaybillVo> vos = this.baseMapper.selectPaybillList(condition);
        List<FundPaybillExportVo> exportVos = BeanUtil.copyToList(vos, FundPaybillExportVo.class);
        List<CompletableFuture<Void>> futures = exportVos.stream()
                .map(item -> CompletableFuture.runAsync(() -> {
                    try{
                        // 状态信息
                        if (!CommonConstant.PaybillStateType.ESCALATED.getValue().equals(item.getStatus())) {
                            item.setStatusInfo(CommonConstant.PaybillStateType.fromValue(item.getStatus()).getName());
                        } else {
                            item.setStatusInfo(item.getNodeName());
                        }
                        // 收款人信息
                        List<FundPaybillSkrVo> skrs = fundPaybillSkrService.listSkrByBillId(item.getId());
                        if (!ObjectUtil.isEmpty(skrs)) {
                            if (skrs.size() == 1) {
                                item.setSkrInfo(skrs.getFirst().getAccountName());
                            } else {
                                String firstAccountName = skrs.getFirst().getAccountName();
                                item.setSkrInfo(firstAccountName + " 等" + skrs.size() + "人 <见附表>");
                            }
                        }
                    } catch (Exception e){
                        log.error("填充拨款单导出信息失败，billId: {}", item.getId(), e);
                        item.setStatusInfo(null);
                        item.setSkrInfo(null);
                    }
                }, virtualThreadExecutor))
                .toList();
        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return exportVos;
    }

    @Override
    public FundPaybillVo getPaybill(String id) {
        SysUser opUser = SecurityUtils.getLoginUser().getUser();
        FundPaybill bill = this.getById(id);
        if (bill == null) {
            throw new ServiceException("拨款单信息不存在");
        }
        FundPaybillVo vo = BeanUtil.toBean(bill, FundPaybillVo.class);
        // 收款人信息
        vo.setSkrs(fundPaybillSkrService.listSkrByBillId(bill.getId()));
        // 项目累计拨款单明细
        vo.setProjPaybillList(parseJson(bill.getProjPaybills()));
        // 合同累计拨款单明细
        vo.setContractPaybillList(parseJson(bill.getContractPaybills()));
        // 相关附件
//        fillRelatedFiles(vo);
        // 是否可以提交
        vo.setCanDoSubmit(canUserSubmit(bill, opUser));
        // 是否可以撤回
        vo.setCanDoRecall(fundPaybillNodeService.canUserRecall(bill, opUser));
        // 是否可以审核
        vo.setCanDoAudit(fundPaybillNodeService.canUserAudit(bill, opUser));
        // 设置已支付合计信息
        List<FundPaybillPaidInfoVo> paidInfos = getPaybillPaidInfoByBillIds(List.of(bill.getId()));
        if(!paidInfos.isEmpty()){
            vo.setPaidInfo(paidInfos.getFirst());
        }
        // 设置合同乙方
        if(StringUtils.isNotBlank(vo.getContractId())){
            FundContractInfo contract = fundContractInfoService.getById(vo.getContractId());
            if(contract != null){
                vo.setContractPartb(contract.getPartb());
            }
        }
        // 设置项目单位信息
        if(StringUtils.isNotBlank(vo.getProjId())){
            ProjectInfo projectInfo = projectInfoService.getById(vo.getProjId());
            if(projectInfo != null){
                vo.setProjApplyOrgid(projectInfo.getApplyOrgid());
                vo.setProjSubmitOrgid(projectInfo.getSubmitOrgid());
                vo.setProjAssessOrgid(projectInfo.getAssessOrgid());
                vo.setProjCooperateOrgid(projectInfo.getCooperateOrgid());
            }
        }
        return vo;
    }

    @Override
    public List<SysAttachment> getBillProjAttachments(String projId) {
        return attachmentService.getListBySourceId(projId)
                .stream().filter(attachment -> !CommonConstant.AttachmentHideFlag.Paybill.getValue().equals(attachment.getHideFlag()))
                .toList();
    }

    @Override
    public List<FundPaybillPaidInfoVo> getPaybillPaidInfoByProjId(String projId) {
        List<String> billIds = getPaybillIdsByProjId(projId);
        return getPaybillPaidInfoByBillIds(billIds);
    }

    @Override
    public List<FundPaybillPaidInfoVo> getPaybillPaidInfoByContractId(String contractId) {
        List<String> billIds = getPaybillIdsByContractId(contractId);
        return getPaybillPaidInfoByBillIds(billIds);
    }

    @Override
    public List<FundPaybillAttachmentVo> getBillAttachmentsByProjId(String projId, String excludedBillId){
        if(StringUtils.isBlank(projId)){
            return Collections.emptyList();
        }
        // 其他拨款单附件、财务监理附件(已审核已支付的拨款单)
        List<FundPaybill> otherBills = getAuditedPaybillsByProjId(projId).stream().filter(a -> !a.getId().equals(excludedBillId)).toList();
        Map<String, FundPaybill> otherBillMap = otherBills.stream().collect(Collectors.toMap(FundPaybill::getId, Function.identity()));
        List<SysAttachment> otherBillFiles = attachmentService.getListBySourceIds(otherBills.stream().map(FundPaybill::getId).toList());
        List<FundPaybillAttachmentVo> vos = BeanUtil.copyToList(otherBillFiles, FundPaybillAttachmentVo.class);
        vos.forEach(item->{
            FundPaybill paybill = otherBillMap.get(item.getSourceId());
            // 优先使用拨款单编号，其次使用申请日期
            item.setSerialNumber(getBillNo(paybill));
        });
        return vos;
    }

    @Override
    public FundPaybillHqdpContentVo getHqdpContent(String id){
        FundPaybill fundPaybill = this.getById(id);
        if(fundPaybill == null){
            return null;
        }
        if(StringUtils.isBlank(fundPaybill.getHqdpId())){
            return null;
        }
        return fundPaybillHqdpService.getContent(fundPaybill.getHqdpId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePaybill(FundPaybillDto dto) {
        String id = dto.getId();
        if (StringUtils.isBlank(id)) {
            throw new ServiceException("拨款单id不能为空");
        }
        // 数据库中的数据
        FundPaybill db = this.getById(id);
        boolean isNew = db == null;
        FundPaybill bill = BeanUtil.toBean(dto, FundPaybill.class);
        // 设置项目信息
        String projId = bill.getProjId();
        // 是否清除合同信息
        boolean clearContract = false;

        if (StringUtils.isNotBlank(projId)) {
            // 1.项目名称
            ProjectInfo project = projectInfoService.getById(projId);
            if (project == null) {
                throw new ServiceException("您选择的项目不存在或已被删除！");
            }
            bill.setProjName(project.getName());
            // 项目类型使用名字存储到支出分类
            String projectType = dictDataService.selectDictLabel("project_type", project.getTypeId());
            // 2.支出分类 -> 项目类型
            bill.setProjType(projectType);

            // 3.项目编号
            ProjectSn sn = projectSnService.getSnByProjId(projId);
            if (sn == null) {
                throw new ServiceException("所选项目编号信息不存在");
            }
            bill.setProjSn(ObjectUtil.defaultIfBlank(sn.getFormalSn(), sn.getTempSn()));

            // 4.子项目信息
            if (StringUtils.isNotBlank(bill.getChildId())) {
                ProjectChildren subProject = projectChildrenService.getById(bill.getChildId());
                if (subProject == null) {
                    throw new ServiceException("您选择的子项目不存在或已被删除！");
                }
                if (!subProject.getProjId().equals(projId)) {
                    throw new ServiceException("子项目不属于该项目");
                }
                bill.setChildName(subProject.getName());
            }

            // 项目库数据乘以倍数
            BigDecimal times = new BigDecimal(10000);
            // 5.项目批准金额 按照优先级获取 合同金额 > 核定金额 > 入库金额 > 项目估算
            BigDecimal projAmount = ObjectUtil.defaultIfNull(project.getContractAmount(),
                    ObjectUtil.defaultIfNull(project.getCheckAmount(),
                            ObjectUtil.defaultIfNull(project.getLibAmount(), project.getEstAmount())));
            bill.setProjAmount(times.multiply(projAmount));

            // 6.当年下达用款计划
            ProjectPlan planInfo = projectPlanService.getPlanInfo(projId, DateUtil.thisYear());
            if (planInfo == null) {
                throw new ServiceException("所选项目本年资金计划不存在");
            }
            // 当年下达用款计划 按照优先级获取 调整计划金额 > 正式计划金额 > 年初计划金额 > 上报计划金额
            BigDecimal planYear = ObjectUtil.defaultIfNull(planInfo.getAdjustAmount(),
                    ObjectUtil.defaultIfNull(planInfo.getFormalAmount(),
                            ObjectUtil.defaultIfNull(planInfo.getEarlyAmount(), planInfo.getDeclareAmount())));
            bill.setPlanYear(times.multiply(planYear));
            // 7.设置项目累计拨款金额、项目累计拨款单 JSON
            calcAndSetProjPaidData(bill);
            // 8.设置合同
            if (StringUtils.isNotBlank(bill.getContractId())) {
                FundContractInfo contract = fundContractInfoService.getById(bill.getContractId());
                if (contract == null) {
                    throw new ServiceException("所选的合同信息不存在");
                }
                if (!contract.getProjId().equals(bill.getProjId())) {
                    throw new ServiceException("所选的合同不属于所选的项目");
                }
                bill.setContractName(contract.getName());
                bill.setContractAmount(contract.getAmount());
                // 设置合同累计拨款金额、合同累计拨款单 JSON
                calcAndSetContractPaidData(bill);
            } else {
                clearContract = true;
            }
        }
        // 未选择项目管理部门，清除
        boolean clearMgrOrgId = StringUtils.isBlank(bill.getMgrOrgid());
        // 核定金额不为空，清除
        boolean clearCheckAmount = ObjectUtil.isNotNull(bill.getCheckAmount());
        // 财务监理意见不为有，清除附件字段
        boolean clearFundFile = !CommonConstant.ZeroOrOne.ONE.getValue().equals(bill.getFundOpinion());
        // 清除子项目
        boolean clearProjectChild = StringUtils.isBlank(bill.getChildId());
        if (isNew) {
            // 设置为草稿
            bill.setStatus(CommonConstant.PaybillStateType.DRAFT.getValue());
        } else {
            // 否则从已存数据中获取状态
            bill.setStatus(db.getStatus());
        }
        // 草稿状态每次保存更新申请信息
        if (bill.getStatus().equals(CommonConstant.PaybillStateType.DRAFT.getValue())) {
            // 高质量接口推送的数据根据接口设置申请人
            if(StringUtils.isNotBlank(bill.getHqdpId())){
                if(StringUtils.isAnyBlank(bill.getApplyUserid(),
                        bill.getApplyUsername(),
                        bill.getApplyOrgid(),
                        bill.getApplyOrgname())){
                    throw new ServiceException("申请人信息不能为空！", HttpStatus.ERROR);
                }
                // 设置创建人和更新人为申请人（不使用当前用户自动填充）
                bill.setCreateBy(bill.getApplyUsername());
                bill.setUpdateBy(bill.getApplyUsername());
            } else {
                // 设置申请人信息
                SysUser currentUser = SecurityUtils.getLoginUser().getUser();
                SysDept currentDept = currentUser.getDept();
                bill.setApplyOrgid(currentDept.getDeptId().toString());
                bill.setApplyOrgname(currentDept.getDeptName());
                bill.setApplyUserid(currentUser.getUserId().toString());
                bill.setApplyUsername(currentUser.getUserName());
            }
            bill.setApplyDate(new Date());
        }
        // 保存拨款单
        saveOrUpdate(bill);
        // 没有选择合同，清除合同信息
        if (clearContract || clearMgrOrgId || clearCheckAmount || clearFundFile || clearProjectChild) {
            if (clearFundFile && StringUtils.isNotBlank(bill.getFundFile())) {
                sysAttachmentService.removeById(bill.getFundFile());
            }
            this.lambdaUpdate()
                    .set(clearContract, FundPaybill::getContractId, null)
                    .set(clearContract, FundPaybill::getContractName, null)
                    .set(clearContract, FundPaybill::getContractAmount, null)
                    .set(clearContract, FundPaybill::getContractPayed, null)
                    .set(clearContract, FundPaybill::getContractPaybills, null)
                    .set(clearMgrOrgId, FundPaybill::getMgrOrgid, null)
                    .set(clearCheckAmount, FundPaybill::getCheckAmount, null)
                    .set(clearFundFile, FundPaybill::getFundFile, null)
                    .set(clearProjectChild, FundPaybill::getChildId, null)
                    .set(clearProjectChild, FundPaybill::getChildName, null)
                    .eq(FundPaybill::getId, bill.getId())
                    .update();
        }
        // 保存收款人信息，skrs 为空则会清空
        fundPaybillSkrService.saveSkrListForBill(bill.getId(), dto.getSkrs());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitPaybill(FundPaybillDto dto, Boolean sendSms) {
        savePaybill(dto);
        FundPaybillVo vo = getPaybill(dto.getId());
        checkBeforeSubmit(vo);
        SysUser opUser = sysUserService.selectUserById(Long.valueOf(vo.getApplyUserid()));
        if(opUser == null){
            throw new ServiceException("申请人不存在");
        }
        this.lambdaUpdate()
                .set(FundPaybill::getStatus, CommonConstant.PaybillStateType.ESCALATED.getValue())
                .eq(FundPaybill::getId, dto.getId())
                .update();
        // 提交审核流程
        fundPaybillNodeService.advanceApprovalFlow(dto.getId(), opUser, null, sendSms);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removePaybills(List<String> ids) {
        if (ids.isEmpty()) {
            return false;
        }
        // 草稿状态的记录
        List<FundPaybill> draftBills = this.lambdaQuery()
                .in(FundPaybill::getId, ids)
                .eq(FundPaybill::getStatus, CommonConstant.PaybillStateType.DRAFT.getValue()).list();
        if (draftBills.size() != ids.size()) {
            throw new ServiceException("选择的部分拨款单无法删除");
        }
        // 删除拨款单
        this.lambdaUpdate()
                .in(FundPaybill::getId, ids)
                .remove();
        // 删除收款人信息
        fundPaybillSkrService.removeSkrListByBillIds(ids);
        // 删除附件
        sysAttachmentService.removeAttachmentsBySourceIds(ids);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean doCheckAmountChange(String billId, BigDecimal checkAmount) {
        FundPaybillVo vo = getPaybill(billId);
        if (checkAmount == null || checkAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("核定金额必须大于0");
        }
        List<FundPaybillSimpleVo> projPaybillList = vo.getProjPaybillList();
        // 重新计算项目累计拨付金额
        BigDecimal projPayed = checkAmount.add(projPaybillList.stream().map(FundPaybillSimpleVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal contractPayed = null;
        if (StringUtils.isNotBlank(vo.getContractId())) {
            // 重新计算合同累计拨付金额
            List<FundPaybillSimpleVo> contrctPaybillList = vo.getContractPaybillList();
            contractPayed = checkAmount.add(contrctPaybillList.stream().map(FundPaybillSimpleVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        this.lambdaUpdate()
                .set(FundPaybill::getCheckAmount, checkAmount)
                .set(FundPaybill::getProjPayed, projPayed)
                .set(FundPaybill::getContractPayed, contractPayed)
                .eq(FundPaybill::getId, billId)
                .update();
        return true;
    }

    /**
     * 填充拨款单项目信息
     *
     * @param item   拨款单视图对象
     * @param opUser 操作用户
     */
    private void fillItemInfo(FundPaybillVo item, SysUser opUser) {
        try {
            // 1. 设置收款人
            List<FundPaybillSkrVo> skrs = fundPaybillSkrService.listSkrByBillId(item.getId());
            item.setSkrs(skrs);

            // 2. 判断是否可以撤回
            FundPaybill bill = BeanUtil.toBean(item, FundPaybill.class);
            boolean canRecall = fundPaybillNodeService.canUserRecall(bill, opUser);
            item.setCanDoRecall(canRecall);

            // 3. 判断是否可以提交
            boolean canSubmit = canUserSubmit(bill, opUser);
            item.setCanDoSubmit(canSubmit);

        } catch (Exception e) {
            log.error("填充拨款单信息失败，billId: {}", item.getId(), e);
            // 发生异常时设置默认值
            item.setSkrs(Collections.emptyList());
            item.setCanDoRecall(false);
            item.setCanDoSubmit(false);
        }
    }

    /**
     * 提交前检查
     */
    private void checkBeforeSubmit(FundPaybillVo vo) {
        Integer status = vo.getStatus();
        // 允许提交的状态
        Integer[] allowedStatus = {CommonConstant.PaybillStateType.DRAFT.getValue(), CommonConstant.PaybillStateType.BACK.getValue()};
        if (!Arrays.asList(allowedStatus).contains(status)) {
            throw new ServiceException("当前拨款单状态不允许提交！");
        }
        ProjectInfo project = projectInfoService.getById(vo.getProjId());
        if (!CommonConstant.ZeroOrOne.ONE.getValue().equals(project.getLibState().toString()) && project.getBasketCode() == null) {
            throw new ServiceException("您选择的项目尚未入库！");
        }
        String isFundApplyOpen = ObjectUtil.defaultIfBlank(configService.selectConfigByKey("sys.fund.bill.apply"), CommonConstant.ZeroOrOne.ONE.getValue());
        // 草稿状态如果拨款单申请通道关闭，则不允许提交
        if (!CommonConstant.ZeroOrOne.ONE.getValue().equals(isFundApplyOpen) && status.equals(CommonConstant.PaybillStateType.DRAFT.getValue())) {
            throw new ServiceException("拨款单提交通道已关闭，请联系计财处！");
        }
        if (CommonConstant.ZeroOrOne.ONE.getValue().equals(project.getIsClosed())) {
            throw new ServiceException("项目已标记结项，无法申请资金！");
        }
        // 收款人金额合计
        BigDecimal skrTotalAmount = vo.getSkrs().stream().map(FundPaybillSkrVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (skrTotalAmount.compareTo(vo.getApplyAmount()) != 0) {
            throw new ServiceException("申请拨款总额与附表分项合计不一致！");
        }


        // 允许误差
        BigDecimal marginOfError = new BigDecimal("100");
        // 当年累计申请拨款总额检查
        BigDecimal totalAppyAmountThisYear = getProjTotalBillApplyAmount(vo.getProjId(), true);
        // 草稿状态的需要加上本次申请金额
        if (status.equals(CommonConstant.PaybillStateType.DRAFT.getValue())) {
            totalAppyAmountThisYear = totalAppyAmountThisYear.add(vo.getApplyAmount());
        }
        if (totalAppyAmountThisYear.compareTo(vo.getPlanYear().add(marginOfError)) > 0) {
            String msg = String.format("当年累计申请拨款总额达%s元，已超过项目当年下达用款计划%s元！", totalAppyAmountThisYear.stripTrailingZeros().toPlainString(), vo.getPlanYear().stripTrailingZeros().toPlainString());
            throw new ServiceException(msg);
        }
        // 累计申请拨款总额检查
        BigDecimal totalBillApplyAmount = getProjTotalBillApplyAmount(vo.getProjId(), false);
        // 草稿状态的需要加上本次申请金额
        if (status.equals(CommonConstant.PaybillStateType.DRAFT.getValue())) {
            totalBillApplyAmount = totalBillApplyAmount.add(vo.getApplyAmount());
        }
        if (totalBillApplyAmount.compareTo(vo.getProjAmount().add(marginOfError)) > 0) {
            String msg = String.format("累计申请拨款总额达%s元，已超过项目入库总金额%s元！", totalBillApplyAmount.stripTrailingZeros().toPlainString(), vo.getProjAmount().stripTrailingZeros().toPlainString());
            throw new ServiceException(msg);
        }
        // 子项目申请拨款总额检查
        if (StringUtils.isNotBlank(vo.getChildId())) {
            // 设置了当年子项目预算金额，比较当年申请金额
            ProjectChildrenBudget childrenBudget = projectChildrenBudgetService.lambdaQuery()
                    .eq(ProjectChildrenBudget::getChildId, vo.getChildId())
                    .eq(ProjectChildrenBudget::getYear, DateUtil.thisYear()).last("limit 1").one();
            if(childrenBudget != null && childrenBudget.getAmount() != null){
                BigDecimal childProjTotalBillApplyAmountThisYear = getChildProjTotalBillApplyAmount(vo.getChildId(),true);
                if (status.equals(CommonConstant.PaybillStateType.DRAFT.getValue())) {
                    childProjTotalBillApplyAmountThisYear = childProjTotalBillApplyAmountThisYear.add(vo.getApplyAmount());
                }
                if (childProjTotalBillApplyAmountThisYear.compareTo(childrenBudget.getAmount()) > 0) {
                    String msg = String.format("当年累计申请拨款总额达%s元，已超过子项目当年预算金额%s元！", childProjTotalBillApplyAmountThisYear.stripTrailingZeros().toPlainString(), childrenBudget.getAmount().stripTrailingZeros().toPlainString());
                    throw new ServiceException(msg);
                }
            }
            // 子项目累计申请拨款总额检查
            ProjectChildren projChild = projectChildrenService.getById(vo.getChildId());
            // 子项目金额
            BigDecimal childProjTotalBillApplyAmount = getChildProjTotalBillApplyAmount(vo.getChildId(),false);
            // 草稿状态的需要加上本次申请金额
            if (status.equals(CommonConstant.PaybillStateType.DRAFT.getValue())) {
                childProjTotalBillApplyAmount = childProjTotalBillApplyAmount.add(vo.getApplyAmount());
            }
            if (childProjTotalBillApplyAmount.compareTo(projChild.getAmount()) > 0) {
                String msg = String.format("累计申请拨款总额达%s元，已超过子项目总金额%s元！", childProjTotalBillApplyAmount.stripTrailingZeros().toPlainString(), projChild.getAmount().stripTrailingZeros().toPlainString());
                throw new ServiceException(msg);
            }
        }
        // 合同金额检查
        if (StringUtils.isNotBlank(vo.getContractId())) {
            BigDecimal contractTotalBillApplyAmount = getContractTotalBillApplyAmount(vo.getContractId());
            // 草稿状态的需要加上本次申请金额
            if (status.equals(CommonConstant.PaybillStateType.DRAFT.getValue())) {
                contractTotalBillApplyAmount = contractTotalBillApplyAmount.add(vo.getApplyAmount());
            }
            FundContractInfo contract = fundContractInfoService.getById(vo.getContractId());
            if (contractTotalBillApplyAmount.compareTo(contract.getAmount()) > 0) {
                String msg = String.format("累计申请拨款总额达%s元，已超过合同总金额%s元！", contractTotalBillApplyAmount.stripTrailingZeros().toPlainString(), contract.getAmount().stripTrailingZeros().toPlainString());
                throw new ServiceException(msg);
            }
        }
        // 财务监理意见附件检查
        if (vo.getFundOpinion().equals(CommonConstant.ZeroOrOne.ONE.getValue()) && StringUtils.isBlank(vo.getFundFile())) {
            throw new ServiceException("需要财务监理意见相关附件");
        }
    }


    /**
     * 检查拨款单的状态以及流程状态能否提交（控制是否显示提交按钮）
     * 检查拨款单状态以及流程状态，不看拨款单表单数据
     *
     * @param bill   拨款单
     * @param opUser 操作人
     * @return 结果
     */
    private boolean canUserSubmit(FundPaybill bill, SysUser opUser) {
        if (bill == null) {
            return false;
        }
        // 草稿都能提交
        if (bill.getStatus().equals(CommonConstant.PaybillStateType.DRAFT.getValue())) {
            return true;
        }
        // 退回需要看当前发起流程节点人是不是当前操作人
        if (bill.getStatus().equals(CommonConstant.PaybillStateType.BACK.getValue())) {
            List<FundPaybillNode> pendingNodes = fundPaybillNodeService.getPendingNodesBySeq(bill.getId(), bill.getNodeSeq());
            // 找到当前操作人的节点
            FundPaybillNode opNode = pendingNodes.stream()
                    .filter(n -> n.getOperatorId().equals(opUser.getUserId().toString()))
                    .findFirst()
                    .orElse(null);
            return opNode != null;
        }
        return false;
    }

    /**
     * 项目累计申请的拨款金额（只统计非草稿的拨款单）
     *
     * @param projId   项目id
     * @param thisYear 是否只统计本年
     * @return 结果
     */
    private BigDecimal getProjTotalBillApplyAmount(String projId, boolean thisYear) {
        // 非草稿的
        List<FundPaybill> bills = this.lambdaQuery().eq(FundPaybill::getProjId, projId)
                .ne(FundPaybill::getStatus, CommonConstant.PaybillStateType.DRAFT.getValue())
                .apply(thisYear, "YEAR(apply_date) = {0}", DateUtil.thisYear())
                .list();
        BigDecimal total = BigDecimal.ZERO;
        for (FundPaybill bill : bills) {
            // 优先看核定金额，其次看申请金额
            total = total.add(ObjectUtil.defaultIfNull(bill.getCheckAmount(), bill.getApplyAmount()));
        }
        return total;
    }

    /**
     * 子项目累计申请的拨款金额（只统计非草稿的拨款单）
     *
     * @param childId 子项目id
     * @param thisYear 是否只统计本年
     * @return 结果
     */
    private BigDecimal getChildProjTotalBillApplyAmount(String childId, boolean thisYear) {
        // 非草稿的
        List<FundPaybill> bills = this.lambdaQuery().eq(FundPaybill::getChildId, childId)
                .ne(FundPaybill::getStatus, CommonConstant.PaybillStateType.DRAFT.getValue())
                .apply(thisYear, "YEAR(apply_date) = {0}", DateUtil.thisYear())
                .list();
        BigDecimal total = BigDecimal.ZERO;
        for (FundPaybill bill : bills) {
            // 优先看核定金额，其次看申请金额
            total = total.add(ObjectUtil.defaultIfNull(bill.getCheckAmount(), bill.getApplyAmount()));
        }
        return total;
    }

    /**
     * 合同累计申请的拨款金额（只统计非草稿的拨款单）
     *
     * @param contractId 合同id
     * @return 结果
     */
    private BigDecimal getContractTotalBillApplyAmount(String contractId) {
        // 非草稿的
        List<FundPaybill> bills = this.lambdaQuery().eq(FundPaybill::getContractId, contractId)
                .ne(FundPaybill::getStatus, CommonConstant.PaybillStateType.DRAFT.getValue())
                .list();
        BigDecimal total = BigDecimal.ZERO;
        for (FundPaybill bill : bills) {
            // 优先看核定金额，其次看申请金额
            total = total.add(ObjectUtil.defaultIfNull(bill.getCheckAmount(), bill.getApplyAmount()));
        }
        return total;
    }

    /**
     * 根据拨款单ids列表过滤出已支付的拨款单支付信息
     *
     * @param billIds 拨款单id列表
     * @return 结果
     */
    private List<FundPaybillPaidInfoVo> getPaybillPaidInfoByBillIds(List<String> billIds) {
        if (billIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 已经支付的收款信息
        List<FundPaybillSkr> skrs = fundPaybillSkrService.lambdaQuery()
                .in(FundPaybillSkr::getBillId, billIds)
                .isNotNull(FundPaybillSkr::getPayAmount)
                .orderByDesc(FundPaybillSkr::getSeqNo)
                .list();
        if (skrs.isEmpty()) {
            return Collections.emptyList();
        }
        // 收款信息按照拨款单分组
        Map<String, List<FundPaybillSkr>> billGroupMap = skrs.stream()
                .collect(Collectors.groupingBy(FundPaybillSkr::getBillId));

        // 支付过的拨款单
        List<String> paidBillIds = skrs.stream().map(FundPaybillSkr::getBillId).distinct().toList();
        return this.listByIds(paidBillIds).stream().map(bill -> {
            FundPaybillPaidInfoVo vo = BeanUtil.toBean(bill, FundPaybillPaidInfoVo.class);
            List<FundPaybillSkr> group = billGroupMap.get(bill.getId());
            vo.setPayAmount(group.stream().map(FundPaybillSkr::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            vo.setPayDate(group.getFirst().getPayDate());
            vo.setPayVoucher(group.getFirst().getPayVoucher());
            vo.setSerialNumber(getBillNo(bill));
            return vo;
        }).sorted(Comparator.comparing(FundPaybillPaidInfoVo::getPayDate).reversed()).toList();
    }

    /**
     * 获取拨款单编号
     * 优先使用拨款单编号，其次使用申请日期
     *
     * @param bill 拨款单
     * @return 拨款单编号
     */
    private String getBillNo(FundPaybill bill){
        return StringUtils.defaultIfBlank(bill.getSerialNumber(), DateUtil.format(bill.getApplyDate(), DatePattern.PURE_DATETIME_PATTERN));
    }

    /**
     * 设置项目累计拨款金额、项目累计拨款单 JSON
     *
     * @param bill 拨款单
     */
    private void calcAndSetProjPaidData(FundPaybill bill) {
        PaidDataParams params = new PaidDataParams(
                bill.getProjId(),
                bill.getId(),
                "projId",
                bill.getApplyAmount(),
                null
        );

        PaidDataResult result = calculatePaidData(params);
        bill.setProjPayed(result.totalPaidAmount());
        if (result.paybillsJson() != null) {
            bill.setProjPaybills(result.paybillsJson());
        }
    }

    /**
     * 设置合同累计拨款金额、合同累计拨款单 JSON
     *
     * @param bill 拨款单
     */
    private void calcAndSetContractPaidData(FundPaybill bill) {
        if (StringUtils.isBlank(bill.getContractId())) {
            return;
        }

        // 获取合同信息用于2019年前金额特殊处理
        FundContractInfo contract = fundContractInfoService.getById(bill.getContractId());

        PaidDataParams params = new PaidDataParams(
                bill.getContractId(),
                bill.getId(),
                "contractId",
                bill.getApplyAmount(),
                contract
        );

        PaidDataResult result = calculatePaidData(params);
        bill.setContractPayed(result.totalPaidAmount());
        if (result.paybillsJson() != null) {
            bill.setContractPaybills(result.paybillsJson());
        }
    }


    /**
     * 解析json数据
     *
     * @param json
     * @return
     */
    @SneakyThrows
    private List<FundPaybillSimpleVo> parseJson(String json) {
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }
        // 解析后的数据
        List<FundPaybillSimpleVo> vos = objectMapper.readValue(json, new TypeReference<>() {
        });
        if (vos.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, FundPaybillSkr> skrMap = new HashMap<>();
        List<String> skrIds = vos.stream().flatMap(vo -> vo.getDetails().stream()).map(FundPaybillSimpleVo.FundPaybillSkrSimpleVo::getId).distinct().toList();
        if (!skrIds.isEmpty()) {
            skrMap = fundPaybillSkrService.listByIds(skrIds).stream().collect(Collectors.toMap(FundPaybillSkr::getId, Function.identity()));
        }
        List<String> billIds = vos.stream().map(FundPaybillSimpleVo::getId).toList();
        Map<String, FundPaybill> billMap = this.listByIds(billIds).stream().collect(Collectors.toMap(FundPaybill::getId, Function.identity()));
        for (FundPaybillSimpleVo vo : vos) {
            // 处理合同2019年前拨付金额明细是特殊构造的数据，没有对应的拨款单记录
            if (vo.getId().equals("0")) {
                vo.setSerialNumber("2019年前拨付");
                continue;
            }
            FundPaybill paybill = billMap.get(vo.getId());
            vo.setSerialNumber(paybill.getSerialNumber());
            for (FundPaybillSimpleVo.FundPaybillSkrSimpleVo detail : vo.getDetails()) {
                FundPaybillSkr skr = skrMap.get(detail.getId());
                detail.setAccountName(skr.getAccountName());
                detail.setPayDate(skr.getPayDate());
                detail.setPayVoucher(skr.getPayVoucher());
            }
        }
        return vos;
    }

    // 填充拨款单相关附件
    private void fillRelatedFiles(FundPaybillVo vo) {
        // 当前拨款单附件、财务监理附件
        List<SysAttachment> billFiles = attachmentService.getListBySourceId(vo.getId());
        vo.setBillFiles(billFiles);
        // 关联项目附件
        List<SysAttachment> projFiles = attachmentService.getListBySourceId(vo.getProjId());
        // 隐藏项目附件
        projFiles = projFiles.stream().filter(sysAttachment -> !CommonConstant.AttachmentHideFlag.Paybill.getValue().equals(sysAttachment.getHideFlag())).toList();
        vo.setProjFiles(projFiles);
        // 合同附件
        if (StringUtils.isNotBlank(vo.getContractId())) {
            List<SysAttachment> contractFiles = attachmentService.getListBySourceId(vo.getContractId());
            vo.setContractFiles(contractFiles);
        } else {
            vo.setContractFiles(Collections.emptyList());
        }
        // 项目其他拨款单附件
//        List<FundPaybillAttachmentVo> otherBillFiles = getBillAttachmentsByProjId(vo.getProjId(), vo.getId());
//        vo.setOtherBillFiles(otherBillFiles);
    }

    /**
     * 获得项目所有拨款单ids（非草稿的）
     *
     * @param projId 项目id
     * @return 结果
     */
    public List<String> getPaybillIdsByProjId(String projId) {
        return this.lambdaQuery()
                .ne(FundPaybill::getStatus, CommonConstant.PaybillStateType.DRAFT.getValue())
                .eq(FundPaybill::getProjId, projId).list().stream().map(FundPaybill::getId).toList();
    }


    @Override
    public List<FundPaybill> getAuditedPaybillsByProjId(String projId) {
        return this.lambdaQuery()
                .in(FundPaybill::getStatus, CommonConstant.PaybillStateType.AUDITED.getValue(), CommonConstant.PaybillStateType.PAYED.getValue())
                .eq(FundPaybill::getProjId, projId).list();
    }

    /**
     * 获得合同所有拨款单ids（非草稿的）
     *
     * @param contractId 合同id
     * @return 结果
     */
    public List<String> getPaybillIdsByContractId(String contractId) {
        return this.lambdaQuery()
                .ne(FundPaybill::getStatus, CommonConstant.PaybillStateType.DRAFT.getValue())
                .eq(FundPaybill::getContractId, contractId).list().stream().map(FundPaybill::getId).toList();
    }

    /**
     * 累计拨款数据计算参数
     */
    private record PaidDataParams(String queryId, String excludedBillId, String queryField,
                                  BigDecimal currentAmount, FundContractInfo contract) {
    }

    /**
     * 累计拨款数据计算结果
     */
    private record PaidDataResult(BigDecimal totalPaidAmount, String paybillsJson) {
    }

    /**
     * 通用的累计拨款数据计算方法
     *
     * @param params 计算参数
     * @return 计算结果
     */
    private PaidDataResult calculatePaidData(PaidDataParams params) {
        // 查询其他拨款单ID (排除当前的)
        List<String> billIds;
        if ("projId".equals(params.queryField())) {
            billIds = getPaybillIdsByProjId(params.queryId())
                    .stream().filter(id -> !id.equals(params.excludedBillId())).toList();
        } else if ("contractId".equals(params.queryField())) {
            billIds = getPaybillIdsByContractId(params.queryId)
                    .stream().filter(id -> !id.equals(params.excludedBillId())).toList();
        } else {
            return new PaidDataResult(params.currentAmount(), null);
        }

        if (billIds.isEmpty()) {
            return new PaidDataResult(params.currentAmount(), null);
        }

        // 已经支付的收款信息
        List<FundPaybillSkr> skrs = fundPaybillSkrService.lambdaQuery()
                .in(FundPaybillSkr::getBillId, billIds)
                .isNotNull(FundPaybillSkr::getPayAmount)
                .orderByDesc(FundPaybillSkr::getSeqNo)
                .list();

        if (skrs.isEmpty()) {
            return new PaidDataResult(params.currentAmount(), null);
        }

        // 收款信息按照拨款单分组
        Map<String, List<FundPaybillSkr>> billGroupMap = skrs.stream()
                .collect(Collectors.groupingBy(FundPaybillSkr::getBillId));

        // 支付过的拨款单
        List<String> paidBillIds = skrs.stream().map(FundPaybillSkr::getBillId).distinct().toList();
        List<FundPaybillSimpleVo> paidBillSimpleVos = BeanUtil.copyToList(
                this.listByIds(paidBillIds),
                FundPaybillSimpleVo.class,
                CopyOptions.create().setIgnoreProperties(FundPaybillSimpleVo::getSerialNumber));

        // 计算其他拨款单累计拨款金额
        BigDecimal otherPaidAmount = BigDecimal.ZERO;
        for (FundPaybillSimpleVo item : paidBillSimpleVos) {
            List<FundPaybillSkr> group = billGroupMap.get(item.getId());
            List<FundPaybillSimpleVo.FundPaybillSkrSimpleVo> details = BeanUtil.copyToList(
                    group,
                    FundPaybillSimpleVo.FundPaybillSkrSimpleVo.class,
                    CopyOptions.create().setIgnoreProperties(
                            FundPaybillSimpleVo.FundPaybillSkrSimpleVo::getAccountName,
                            FundPaybillSimpleVo.FundPaybillSkrSimpleVo::getPayDate,
                            FundPaybillSimpleVo.FundPaybillSkrSimpleVo::getPayVoucher)
            );
            item.setDetails(details);
            BigDecimal amount = details.stream()
                    .map(FundPaybillSimpleVo.FundPaybillSkrSimpleVo::getPayAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            item.setAmount(amount);
            otherPaidAmount = otherPaidAmount.add(amount);
        }

        // 2019年前合同拨付金额特殊处理（仅合同查询时）
        if ("contractId".equals(params.queryField()) && params.contract() != null &&
                params.contract().getAmount2019() != null && params.contract().getAmount2019().compareTo(BigDecimal.ZERO) > 0) {
            FundPaybillSimpleVo special = new FundPaybillSimpleVo();
            special.setId("0");
            special.setAmount(params.contract().getAmount2019());
            paidBillSimpleVos.add(special);
            otherPaidAmount = otherPaidAmount.add(params.contract().getAmount2019());
        }

        try {
            // 序列化为json存储
            String paybillsJson = objectMapper.writeValueAsString(paidBillSimpleVos);
            BigDecimal totalPaidAmount = params.currentAmount().add(otherPaidAmount);
            return new PaidDataResult(totalPaidAmount, paybillsJson);
        } catch (JsonProcessingException e) {
            log.error("序列化拨款单JSON数据失败: {}", e.getMessage());
            return new PaidDataResult(params.currentAmount().add(otherPaidAmount), null);
        }
    }


}




