package com.ruoyi.system.service.impl.fund;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysFlowNode;
import com.ruoyi.system.domain.dto.SmsSendDto;
import com.ruoyi.system.domain.fund.FundPaybill;
import com.ruoyi.system.domain.fund.FundPaybillNode;
import com.ruoyi.system.domain.vo.SysFlowNodeResultVo;
import com.ruoyi.system.domain.vo.fund.FundPaybillNodeVo;
import com.ruoyi.system.mapper.fund.FundPaybillMapper;
import com.ruoyi.system.mapper.fund.FundPaybillNodeMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.SmsService;
import com.ruoyi.system.service.SysFlowNodeService;
import com.ruoyi.system.service.fund.FundPaybillNodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 拨款单审核节点表实现（待办）
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FundPaybillNodeServiceImpl extends MPJBaseServiceImpl<FundPaybillNodeMapper, FundPaybillNode> implements FundPaybillNodeService {

    private final FundPaybillMapper fundPaybillMapper;
    private final SysFlowNodeService sysFlowNodeService;
    private final ISysUserService sysUserService;
    private final SmsService smsService;

    // 短信模板
    @Value("${sms.template.paybillAuditMessage}")
    private String paybillAuditMessage;
    @Value("${sms.template.paybillBackMessage}")
    private String paybillBackMessage;
    @Value("${sms.template.paybillPassMessage}")
    private String paybillPassMessage;


    @Override
    public String findOnePendingNodeBillId(SysUser opUser) {
        FundPaybillNode node = this.lambdaQuery()
                .eq(FundPaybillNode::getOperatorId, opUser.getUserId().toString())
                .isNull(FundPaybillNode::getOperateTime)
                .isNull(FundPaybillNode::getOperateResult)
                .apply("exists(select 1 from fund_paybill b where b.id = fund_paybill_node.paybill_id and b.status = 1 and b.node_seq = fund_paybill_node.node_seq)")
                .orderByAsc(FundPaybillNode::getCreateTime)
                .last("limit 1")
                .one();
        return node == null ? null : node.getPaybillId();
    }

    @Override
    public boolean canUserAudit(FundPaybill bill, SysUser opUser) {
        if (bill == null) {
            return false;
        }
        // 不是上报状态无法审核
        if (!bill.getStatus().equals(CommonConstant.PaybillStateType.ESCALATED.getValue())) {
            return false;
        }
        List<FundPaybillNode> unhandledNodes = getPendingNodesBySeq(bill.getId(), bill.getNodeSeq());
        // 找到当前操作人的节点
        FundPaybillNode opNode = unhandledNodes.stream()
                .filter(n -> n.getOperatorId().equals(opUser.getUserId().toString()))
                .findFirst()
                .orElse(null);
        return opNode != null;
    }

    @Override
    public boolean canUserRecall(FundPaybill bill, SysUser opUser) {
        if (bill == null) {
            return false;
        }
        // 不是上报状态无法撤回
        if (!bill.getStatus().equals(CommonConstant.PaybillStateType.ESCALATED.getValue())) {
            return false;
        }
        // 找到未处理的节点
        List<FundPaybillNode> pendingNodes = getPendingNodesBySeq(bill.getId(), bill.getNodeSeq());
        if (pendingNodes.isEmpty()) {
            return false;
        }
        FundPaybillNode currentNode = pendingNodes.getFirst();
        // 找到最近的由审核人处理的节点
        FundPaybillNode latestHandledNode = findLastApprovedNode(bill.getId(), currentNode.getPrevNode());
        if (latestHandledNode == null) {
            return false;
        }
        // 判断是否是操作人自己
        return latestHandledNode.getOperatorId().equals(opUser.getUserId().toString());
    }

    private List<FundPaybillNode> getPaybillNodes(String billId) {
        return this.lambdaQuery()
                .eq(FundPaybillNode::getPaybillId, billId)
                .orderByAsc(FundPaybillNode::getCreateTime)
                .list();
    }

    @Override
    public List<FundPaybillNodeVo> getPaybillNodesVo(String billId) {
        FundPaybill bill = fundPaybillMapper.selectById(billId);
        List<FundPaybillNode> nodes = getPaybillNodes(billId);
        List<FundPaybillNodeVo> vos = BeanUtil.copyToList(nodes, FundPaybillNodeVo.class);
        if (vos.isEmpty()) {
            return Collections.emptyList();
        }
        // 节点设置多人抢办标记
        List<FundPaybillNode> pendingNodes = getPendingNodesBySeq(billId, bill.getNodeSeq());
        if (pendingNodes.size() > 1) {
            vos.stream().filter(n -> pendingNodes.stream().anyMatch(p -> p.getId().equals(n.getId()))).forEach(n -> n.setIsMultiClaim(true));
        }
        List<Long> userIds = vos.stream().map(FundPaybillNodeVo::getOperatorId).filter(StringUtils::isNotBlank).map(Long::parseLong).distinct().toList();
        Map<Long, SysUser> userMap = sysUserService.selectUsersByIds(userIds).stream().collect(Collectors.toMap(SysUser::getUserId, Function.identity()));
        vos.stream().filter(v -> StringUtils.isNotBlank(v.getOperatorId())).forEach(n -> n.setOperatorUserName(userMap.get(Long.valueOf(n.getOperatorId())).getUserName()));
        return vos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean advanceApprovalFlow(String billId, SysUser opUser, String operateReason, Boolean sendSms) {
        FundPaybill bill = fundPaybillMapper.selectById(billId);
        validateBillStatus(bill.getStatus());

        // 下个节点序号
        Integer nextSeq = null;
        // 上个节点序号（最后一个有审批人的节点序号）
        Integer prevSeq = null;

        // 首次提交
        if (getPaybillNodes(billId).isEmpty()) {
            // 创建第一个节点（提交节点）
            SysFlowNode flowNode = sysFlowNodeService.getStartNode(CommonConstant.SysFlowId.F01.getValue());
            createInitialApprovalNode(billId, opUser, flowNode);
            prevSeq = flowNode.getNodeSeq();
            nextSeq = flowNode.getNodeNext();
            updateBillNodeInfo(bill, flowNode.getNodeSeq(), flowNode.getNodeName());
        } else {
            // 完成当前节点
            FundPaybillNode opNode = completeApprovalNode(bill, opUser, CommonConstant.NodeOpResult.CONFIRM.getValue(), operateReason);
            // 记录完成节点序号为上一个节点
            prevSeq = opNode.getNodeSeq();
            // 获取下一个节点序号
            SysFlowNode flowNode = sysFlowNodeService.getNode(CommonConstant.SysFlowId.F01.getValue(), opNode.getNodeSeq());
            if (flowNode != null) {
                nextSeq = flowNode.getNodeNext();
            }
        }
        // 处理后续节点（包括无审批人节点的自动推进）
        return advanceToNextApprover(bill, nextSeq, prevSeq, sendSms, opUser);
    }

    /**
     * 处理后续节点，自动跳过无审批人的节点
     *
     * @param bill            拨款单
     * @param startSeq        开始处理的节点序号
     * @param lastApprovalSeq 最后一个有审批人的节点序号
     * @param sendSms         是否发送短信
     * @param opUser          当前操作人
     * @return 是否完成了审核流程
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean advanceToNextApprover(FundPaybill bill, Integer startSeq, Integer lastApprovalSeq, Boolean sendSms, SysUser opUser) {
        Integer currentSeq = startSeq;
        Integer prevSeq = lastApprovalSeq;
        String billId = bill.getId();
        Map<String, Object> params = Map.of("business_id", billId);

        // 循环处理节点，直到找到有审批人的节点或到达流程末尾
        while (currentSeq != null) {
            SysFlowNodeResultVo nodeResult = sysFlowNodeService.getNodeResult(
                    CommonConstant.SysFlowId.F01.getValue(), currentSeq, params);
            // 没有找到节点
            if (nodeResult == null) {
                if(sendSms){
                    sendPassNoticeSms(bill);
                }
                return true;
            }
            // 是结束节点
            if (nodeResult.getEndFlag()) {
                // 更新拨款单存储的节点信息
                updateBillNodeInfo(bill, nodeResult.getNodeSeq(), nodeResult.getNodeName());
                if(sendSms){
                    sendPassNoticeSms(bill);
                }
                return true;
            }
            if (nodeResult.getOperateUsers() == null || nodeResult.getOperateUsers().isEmpty()) {
                // 无审批人节点，自动创建完成状态的节点记录
                createAutoApprovedNode(billId, nodeResult, prevSeq);
                updateBillNodeInfo(bill, nodeResult.getNodeSeq(), nodeResult.getNodeName());
                // 继续处理下一个节点
                currentSeq = nodeResult.getNodeNext();
            } else {
                // 找到有审批人的节点，创建待处理节点
                List<FundPaybillNode> pendingApprovalNodes = createPendingApprovalNodes(billId, nodeResult, prevSeq);
                // 更新拨款单存储的节点信息
                updateBillNodeInfo(bill, nodeResult.getNodeSeq(), nodeResult.getNodeName());
                // 待处理节点中包含当前审批人，自动完成继续流转
                if (pendingApprovalNodes.stream().anyMatch(n -> n.getOperatorId().equals(opUser.getUserId().toString()))) {
                    // bill对象节点信息在updateBillNodeInfo已经变更可以继续使用
                    FundPaybillNode opNode = completeApprovalNode(bill, opUser, CommonConstant.NodeOpResult.CONFIRM.getValue(), null);
                    // 获取下一个节点序号
                    SysFlowNode flowNode = sysFlowNodeService.getNode(CommonConstant.SysFlowId.F01.getValue(), opNode.getNodeSeq());
                    if (flowNode != null) {
                        currentSeq = flowNode.getNodeNext();
                    } else {
                        currentSeq = null;
                    }
                } else {
                    // 发送短信提醒
                    if (sendSms) {
                        sendAuditNoticeSms(bill, nodeResult.getOperateUsers());
                    }
                    return false;
                }
            }
        }
        if(sendSms){
            sendPassNoticeSms(bill);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean rejectToPreviousNode(String billId, SysUser opUser, String operateReason, Boolean sendSms) {
        FundPaybill bill = fundPaybillMapper.selectById(billId);
        validateBillStatus(bill.getStatus());
        // 完成当前节点
        FundPaybillNode opNode = completeApprovalNode(bill, opUser, CommonConstant.NodeOpResult.REJECT.getValue(), operateReason);
        // 按照路径找到上个节点
        FundPaybillNode prevNode = findLastApprovedNode(billId, opNode.getPrevNode());
        // 创建退回目标节点的待处理记录
        if (prevNode != null) {
            // 流程第一个节点seq
            SysFlowNode startNode = sysFlowNodeService.getStartNode(CommonConstant.SysFlowId.F01.getValue());
            // 是否到达第一个节点
            boolean toInitial = startNode.getNodeSeq().equals(prevNode.getNodeSeq());
            prevNode.setId(null);
            prevNode.setOperateTime(null);
            prevNode.setOperateResult(null);
            prevNode.setOperateReason(null);
            prevNode.setCreateTime(null);
            this.save(prevNode);
            updateBillNodeInfo(bill, prevNode.getNodeSeq(), prevNode.getNodeName());
            if (sendSms) {
                if (toInitial) {
                    sendBackNoticeSms(bill);
                } else {
                    String operatorId = prevNode.getOperatorId();
                    SysUser prevUser = sysUserService.selectUserById(Long.valueOf(operatorId));
                    sendAuditNoticeSms(bill, List.of(prevUser));
                }
            }
            return toInitial;
        }
        if (sendSms) {
            sendBackNoticeSms(bill);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean recallApprovalNode(String billId, SysUser opUser) {
        FundPaybill bill = fundPaybillMapper.selectById(billId);
        if (!bill.getStatus().equals(CommonConstant.PaybillStateType.ESCALATED.getValue())) {
            throw new ServiceException("当前拨款单状态不允许操作！");
        }
        // 找到未处理的节点
        List<FundPaybillNode> pendingNodes = getPendingNodesBySeq(billId, bill.getNodeSeq());
        if (pendingNodes.isEmpty()) {
            throw new ServiceException("您已无法回退当前拨款单的审核操作！");
        }
        FundPaybillNode currentNode = pendingNodes.getFirst();
        // 找到最近的有审核人处理的节点
        FundPaybillNode lastApprovedNode = findLastApprovedNode(billId, currentNode.getPrevNode());
        if (lastApprovedNode == null) {
            throw new ServiceException("您已无法回退当前拨款单的审核操作！");
        }
        // 判断是否是操作人自己
        if (!lastApprovedNode.getOperatorId().equals(opUser.getUserId().toString())) {
            throw new ServiceException("您已无法回退当前拨款单的审核操作！");
        }
        // 重置节点状态
        this.lambdaUpdate()
                .set(FundPaybillNode::getOperateResult, null)
                .set(FundPaybillNode::getOperateReason, null)
                .set(FundPaybillNode::getOperateTime, null)
                .eq(FundPaybillNode::getId, lastApprovedNode.getId())
                .update();
        // 更新拨款单存储的节点信息
        updateBillNodeInfo(bill, lastApprovedNode.getNodeSeq(), lastApprovedNode.getNodeName());
        // 删除未处理的节点
        this.removeBatchByIds(pendingNodes.stream().map(FundPaybillNode::getId).collect(Collectors.toList()));
        // 需要删除可能的无审核人节点 删除创建日期大于当前节点的数据
        this.lambdaUpdate()
                .eq(FundPaybillNode::getPaybillId, billId)
                .isNull(FundPaybillNode::getOperatorId)
                .gt(FundPaybillNode::getCreateTime, lastApprovedNode.getCreateTime())
                .remove();
        // 流程第一个节点seq
        SysFlowNode startNode = sysFlowNodeService.getStartNode(CommonConstant.SysFlowId.F01.getValue());
        // 找所有节点
        List<FundPaybillNode> nodes = getPaybillNodes(billId);
        // 是发起节点
        if (startNode.getNodeSeq().equals(lastApprovedNode.getNodeSeq())) {
            // 如果回退后只有一个节点（发起节点），需要退出审批流程到草稿状态
            if (nodes.size() == 1) {
                // 删除发起节点
                this.removeById(lastApprovedNode.getId());
                LambdaUpdateWrapper<FundPaybill> wrapper = Wrappers.lambdaUpdate(FundPaybill.class)
                        .set(FundPaybill::getStatus, CommonConstant.PaybillStateType.DRAFT.getValue())
                        .set(FundPaybill::getCheckAmount, null)
                        .set(FundPaybill::getNodeSeq, null)
                        .set(FundPaybill::getNodeName, null)
                        .set(FundPaybill::getFlowId, null)
                        .eq(FundPaybill::getId, billId);
                fundPaybillMapper.update(wrapper);
            }
            // 如果撤回后还有其他节点，设置拨款单状态为退回
            if (nodes.size() > 1) {
                LambdaUpdateWrapper<FundPaybill> wrapper = Wrappers.lambdaUpdate(FundPaybill.class)
                        .set(FundPaybill::getStatus, CommonConstant.PaybillStateType.BACK.getValue())
                        .eq(FundPaybill::getId, billId);
                fundPaybillMapper.update(wrapper);
            }
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public FundPaybillNode completeApprovalNode(FundPaybill bill, SysUser opUser, Integer operateResult, String operateReason) {
        List<FundPaybillNode> unhandledNodes = getPendingNodesBySeq(bill.getId(), bill.getNodeSeq());
        // 找到当前操作人的节点
        FundPaybillNode opNode = unhandledNodes.stream()
                .filter(n -> n.getOperatorId().equals(opUser.getUserId().toString()))
                .findFirst()
                .orElse(null);
        if (opNode == null) {
            throw new ServiceException("您目前无法操作当前拨款单！");
        }

        // 删除其他未处理的节点
        List<String> excludeNodeIds = unhandledNodes.stream()
                .map(FundPaybillNode::getId)
                .filter(id -> !id.equals(opNode.getId()))
                .collect(Collectors.toList());
        if (!excludeNodeIds.isEmpty()) {
            this.removeBatchByIds(excludeNodeIds);
        }

        // 更新当前操作节点状态
        opNode.setOperateTime(new Date());
        opNode.setOperateReason(operateReason);
        opNode.setOperateResult(operateResult);
        this.updateById(opNode);
        return opNode;
    }


    /**
     * 查找指定 seq 最后一个由审核人处理的节点(不包含无审核人跳过的节点)
     *
     * @param billId 拨款单ID
     * @param seq    需要查找的节点编号
     * @return 退回目标节点序号，如果未找到返回 null
     */
    private FundPaybillNode findLastApprovedNode(String billId, Integer seq) {
        // 获取当前拨款单的所有已处理节点，按创建时间倒序
        List<FundPaybillNode> processedNodes = this.lambdaQuery()
                .eq(FundPaybillNode::getPaybillId, billId)
                .eq(FundPaybillNode::getNodeSeq, seq)
                .isNotNull(FundPaybillNode::getOperatorId)
                .isNotNull(FundPaybillNode::getOperateTime)
                .isNotNull(FundPaybillNode::getOperateResult)
                .orderByDesc(FundPaybillNode::getCreateTime)
                .list();

        if (processedNodes.isEmpty()) {
            return null;
        }
        return processedNodes.getFirst();
    }

    /**
     * 更新拨款单存储的节点信息
     *
     * @param bill     拨款单
     * @param nodeSeq  节点序号
     * @param nodeName 节点名称
     */
    private void updateBillNodeInfo(FundPaybill bill, Integer nodeSeq, String nodeName) {
        bill.setNodeSeq(nodeSeq);
        bill.setNodeName(nodeName);
        bill.setFlowId(CommonConstant.SysFlowId.F01.getValue());
        fundPaybillMapper.updateById(bill);
    }

    /**
     * 根据节点操作人生成拨款单审核节点
     *
     * @param billId     拨款单id
     * @param nodeResult 节点操作人信息
     * @param prevSeq    上个节点序号
     * @return 生成的节点
     */
    private List<FundPaybillNode> createPendingApprovalNodes(String billId, SysFlowNodeResultVo nodeResult, Integer prevSeq) {
        List<SysUser> users = nodeResult.getOperateUsers();
        List<FundPaybillNode> pendingNodes = new ArrayList<>();
        for (SysUser user : users) {
            FundPaybillNode node = new FundPaybillNode();
            node.setPaybillId(billId);
            node.setNodeSeq(nodeResult.getNodeSeq());
            node.setNodeName(nodeResult.getNodeName());
            node.setPrevNode(prevSeq);
            node.setOperatorId(user.getUserId().toString());
            node.setOperatorName(user.getNickName());
            this.save(node);
            pendingNodes.add(node);
        }
        return pendingNodes;
    }

    /**
     * 创建无审批人跳过记录
     *
     * @param billId     拨款单ID
     * @param nodeResult 节点信息
     * @param prevSeq    上个节点序号
     */
    private void createAutoApprovedNode(String billId, SysFlowNodeResultVo nodeResult, Integer prevSeq) {
        FundPaybillNode node = new FundPaybillNode();
        node.setPaybillId(billId);
        node.setNodeSeq(nodeResult.getNodeSeq());
        node.setNodeName(nodeResult.getNodeName());
        node.setPrevNode(prevSeq);
        node.setOperateResult(CommonConstant.NodeOpResult.CONFIRM.getValue());
        node.setOperateTime(new Date());
        node.setOperateReason(nodeResult.getSkipText());
        this.save(node);
    }

    /**
     * 创建第一个节点
     *
     * @param billId    拨款单id
     * @param opUser    操作人
     * @param firstNode 首个节点
     */
    private void createInitialApprovalNode(String billId, SysUser opUser, SysFlowNode firstNode) {
        FundPaybillNode node = new FundPaybillNode();
        node.setPaybillId(billId);
        node.setNodeSeq(firstNode.getNodeSeq());
        node.setNodeName(firstNode.getNodeName());
        node.setOperatorId(opUser.getUserId().toString());
        node.setOperatorName(opUser.getNickName());
        node.setOperateResult(CommonConstant.NodeOpResult.CONFIRM.getValue());
        node.setOperateTime(new Date());
        this.save(node);
    }

    private void validateBillStatus(Integer status) {
        Integer[] allowedStatus = {CommonConstant.PaybillStateType.ESCALATED.getValue(), CommonConstant.PaybillStateType.BACK.getValue()};
        if (!Arrays.asList(allowedStatus).contains(status)) {
            throw new ServiceException("当前拨款单状态不允许操作！");
        }
    }


    /**
     * 获得拨款单的第一个节点
     *
     * @param billId 拨款单id
     * @return 结果
     */
//    private FundPaybillNode getInitialApprovalNode(String billId) {
//        return this.lambdaQuery()
//                .eq(FundPaybillNode::getPaybillId, billId)
//                .isNull(FundPaybillNode::getPrevNode)
//                .orderByAsc(FundPaybillNode::getCreateTime)
//                .last("limit 1")
//                .one();
//
//    }

    /**
     * 获得当前拨款单未处理的节点
     *
     * @param billId 拨款单id
     * @param seq    节点序号
     * @return 结果
     */
    @Override
    public List<FundPaybillNode> getPendingNodesBySeq(String billId, Integer seq) {
        return this.lambdaQuery()
                .eq(FundPaybillNode::getPaybillId, billId)
                .isNull(FundPaybillNode::getOperateTime)
                .isNull(FundPaybillNode::getOperateResult)
                .eq(FundPaybillNode::getNodeSeq, seq)
                .orderByAsc(FundPaybillNode::getCreateTime)
                .list();
    }

    /**
     * 发送审核提醒短信
     *
     * @param bill        拨款单
     * @param targetUsers 目标用户列表
     */
    private void sendAuditNoticeSms(FundPaybill bill, List<SysUser> targetUsers) {
        for (SysUser targetUser : targetUsers) {
            SmsSendDto sendDto = SmsSendDto.builder()
                    .sourceId(bill.getId())
                    .sourceType(CommonConstant.SourceType.FUND_PAYBILL.getName())
                    .businessName("审核提醒")
                    .toMobile(targetUser.getPhonenumber())
                    .toName(targetUser.getNickName())
                    .template(paybillAuditMessage)
                    .templateArgs(List.of(bill.getApplyOrgname(), bill.getProjName()))
                    .build();
            smsService.recordAndSendSms(sendDto);
        }
    }

    /**
     * 发送退回提醒短信
     *
     * @param bill 拨款单
     */
    private void sendBackNoticeSms(FundPaybill bill) {
        String userid = bill.getApplyUserid();
        SysUser sysUser = sysUserService.selectUserById(Long.valueOf(userid));
        SmsSendDto sendDto = SmsSendDto.builder()
                .sourceId(bill.getId())
                .sourceType(CommonConstant.SourceType.FUND_PAYBILL.getName())
                .businessName("退回提醒")
                .toMobile(sysUser.getPhonenumber())
                .toName(sysUser.getNickName())
                .template(paybillBackMessage)
                .templateArgs(List.of(bill.getProjName()))
                .build();
        smsService.recordAndSendSms(sendDto);
    }

    /**
     * 发送通过提醒短信
     *
     * @param bill 拨款单
     */
    private void sendPassNoticeSms(FundPaybill bill) {
        String userid = bill.getApplyUserid();
        SysUser sysUser = sysUserService.selectUserById(Long.valueOf(userid));
        SmsSendDto sendDto = SmsSendDto.builder()
                .sourceId(bill.getId())
                .sourceType(CommonConstant.SourceType.FUND_PAYBILL.getName())
                .businessName("通过提醒")
                .toMobile(sysUser.getPhonenumber())
                .toName(sysUser.getNickName())
                .template(paybillPassMessage)
                .templateArgs(List.of(bill.getProjName(), bill.getCheckAmount().stripTrailingZeros().toPlainString()))
                .build();
        smsService.recordAndSendSms(sendDto);
    }
}
