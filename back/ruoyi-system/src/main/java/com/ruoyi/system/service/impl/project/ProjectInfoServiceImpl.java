package com.ruoyi.system.service.impl.project;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.handler.CustomCellStyleHandler;
import com.ruoyi.common.core.handler.ExcelStringValueConverter;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.BigDecimalUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.system.domain.SysAttachment;
import com.ruoyi.system.domain.dto.AdjustPlanDto;
import com.ruoyi.system.domain.dto.ProjectSummaryDto;
import com.ruoyi.system.domain.dto.SmsSendDto;
import com.ruoyi.system.domain.event.HistoryEvent;
import com.ruoyi.system.domain.event.OperateEvent;
import com.ruoyi.system.domain.excel.ProjectInfoExcel;
import com.ruoyi.system.domain.excel.ProjectInfoMainExcel;
import com.ruoyi.system.domain.excel.ProjectInfoTotalExcel;
import com.ruoyi.system.domain.fund.FundPaybill;
import com.ruoyi.system.domain.fund.FundPaybillSkr;
import com.ruoyi.system.domain.project.*;
import com.ruoyi.system.domain.vo.project.*;
import com.ruoyi.system.mapper.project.ProjectInfoMapper;
import com.ruoyi.system.mapper.project.ProjectYearRelationMapper;
import com.ruoyi.system.service.*;
import com.ruoyi.system.service.project.*;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.spi.ValidationProvider;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【project_info(项目信息表;)】的数据库操作Service实现
 * @date 2025-06-03 15:28:40
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class ProjectInfoServiceImpl extends ServiceImpl<ProjectInfoMapper, ProjectInfo> implements ProjectInfoService {

    private final ProjectYearRelationService projectYearRelationService;
    private final ProjectYearRelationMapper projectYearRelationMapper;
    private final ProjectPlanService projectPlanService;
    private final ProjectSnService projectSnService;
    private final ISysDeptService sysDeptService;
    private final ProjectPerformanceService projectPerformanceService;
    private final SmsService smsService;
    private final ISysUserService sysUserService;
    private final Validator validator;
    private final ISysConfigService configService;

    @Value("${sms.template.reportProjectMessage}")
    private String reportProjectMessage;
    @Value("${sms.template.backProjectMessage}")
    private String backProjectMessage;


    @Override
    public List<ProjectInfoVo> getInfoList(ProjectQueryConditionVo vo) {
        List<ProjectInfoVo> list = getProjectInfoVos(vo);
        List<String> ids = list.stream().map(ProjectInfoVo::getId).toList();
        Integer year = vo.getYear();
        if (!ids.isEmpty()) {
            Map<String, List<FundPaybillSkr>> skrMap = getPayBillSkrMap(ids, year);
            for (ProjectInfoVo projectInfoVo : list) {
                List<FundPaybillSkr> skrList = skrMap.get(projectInfoVo.getId());
                setPayAmount(projectInfoVo, skrList, year);
            }
        }
        return list;
    }

    /**
     * 获取项目信息
     *
     * @param vo 查询条件
     * @return 项目信息
     */
    @DataScope(deptAlias = "b", userAlias = "u", isProjectData = true)
    private List<ProjectInfoVo> getProjectInfoVos(ProjectQueryConditionVo vo) {

        List<Long> projectUnitsList = new ArrayList<>();
        Long projectUnits = vo.getProjectUnits();
        if (Objects.nonNull(projectUnits)) {
            projectUnitsList.add(projectUnits);
            List<SysDept> childList = sysDeptService.getChildList(projectUnits);
            if (childList != null && !childList.isEmpty() && !"1".equals(vo.getOnlyAssess())) {
                projectUnitsList.addAll(childList.stream().map(SysDept::getDeptId).toList());
            }
        }
        vo.setProjectUnitsList(projectUnitsList);
        List<ProjectInfoVo> projectInfos = baseMapper.getProjectInfoList(vo);
        // 获取所有项目计划信息
        MPJLambdaWrapper<ProjectUsagePlanItem> wrapper = JoinWrappers.lambda(ProjectUsagePlanItem.class)
                .selectAll(ProjectUsagePlanItem.class)
                .join("inner join", ProjectUsagePlan.class, ProjectUsagePlan::getId, ProjectUsagePlanItem::getFundId)
                .eq(ProjectUsagePlan::getYear, vo.getYear())
                .eq(ProjectUsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value);
        List<ProjectUsagePlanItem> projectUsagePlanItems = wrapper.list();
        projectInfos.forEach(e -> projectUsagePlanItems.stream().filter(item -> e.getId().equals(item.getProjId())).findAny().ifPresent(e::setUsagePlanItem));
        return projectInfos;
    }

    @Override
    public ProjectInfoVo getProjectDetail(String id, Integer year) {
        ProjectInfo byId = this.getById(id);
        ProjectInfoVo projectInfoVo = BeanUtil.copyProperties(byId, ProjectInfoVo.class);
        //设置资金计划
        projectInfoVo.setFundPlan(projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, id)
                .eq(ProjectPlan::getYear, year)
                .one());
        //设置附件
        List<SysAttachment> attachmentList = JoinWrappers.lambda(SysAttachment.class)
                .selectAll(SysAttachment.class)
                .leftJoin(SysUser.class, SysUser::getUserName, SysAttachment::getCreateBy)
                .selectAs(SysUser::getNickName, SysAttachment::getCreator)
                .eq(SysAttachment::getSourceId, id)
                .orderByDesc(SysAttachment::getPrimaryType)
                .list();
        projectInfoVo.setSysAttachments(attachmentList);
        //设计年度信息
        projectInfoVo.setCurrentYearRelation(projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getProjId, id)
                .eq(ProjectYearRelation::getYear, year)
                .one());
        //设置项目编号信息
        projectInfoVo.setProjectSn(projectSnService.lambdaQuery()
                .eq(ProjectSn::getProjId, id)
                .one());
        //设置项目绩效信息
        projectInfoVo.setPerformance(projectPerformanceService.lambdaQuery()
                .eq(ProjectPerformance::getProjId, id)
                .list());
        //上年已执行
        List<FundPaybillSkr> innerJoin = JoinWrappers.lambda(FundPaybillSkr.class)
                .selectAll(FundPaybillSkr.class)
                .select(FundPaybill::getProjId)
                .join("inner join", FundPaybill.class, FundPaybill::getId, FundPaybillSkr::getBillId)
                .in(FundPaybill::getProjId, id)
                .apply("YEAR(pay_date) < {0}", year)
                .list();
        BigDecimal reduce = innerJoin.stream().map(e -> e.getPayAmount() == null ? BigDecimal.ZERO : e.getPayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        projectInfoVo.setPrevPayed(reduce.divide(new BigDecimal("10000")));
        return projectInfoVo;
    }

    @Override
    public List<ProjectInfoVo> getProjectDetailForUsagePlan(Integer year) {
        return getProjectDetailForUsagePlan(year, null, null);
    }

    @Override
    public List<ProjectInfoVo> getProjectDetailForUsagePlan(Integer year, Long assessId, Long submitId) {
        List<ProjectInfoVo> list = new ArrayList<>();
        List<ProjectUsagePlanItem> projectUsagePlanItems = JoinWrappers.lambda(ProjectUsagePlanItem.class)
                .selectAll(ProjectUsagePlanItem.class)
                .join("inner join", ProjectUsagePlan.class, ProjectUsagePlan::getId, ProjectUsagePlanItem::getFundId)
                .eq(ProjectUsagePlan::getYear, year)
                .eq(ProjectUsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .list();
        List<String> ids = projectUsagePlanItems.stream().map(ProjectUsagePlanItem::getProjId).filter(Objects::nonNull).toList();

        if (!ids.isEmpty()) {
            list = this.getBaseMapper().getProjectDetailForUsagePlan(ids, year, assessId, submitId);
            //获取资金拨付信息
            Map<String, List<FundPaybillSkr>> skrMap = getPayBillSkrMap(ids, year);
            for (ProjectInfoVo projectInfoVo : list) {
                List<FundPaybillSkr> skrList = skrMap.get(projectInfoVo.getId());
                if (skrList != null && !skrList.isEmpty()) {
                    BigDecimal prevPayed = skrList.stream().filter(item -> {
                        LocalDate localDate = item.getPayDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                        return localDate.getYear() == year - 1;
                    }).map(item -> item.getPayAmount() == null ? BigDecimal.ZERO : item.getPayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
                    projectInfoVo.setPrevPayed(prevPayed.divide(new BigDecimal("10000")));
                    setPayAmount(projectInfoVo, skrList, year);
                }
            }

        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> saveProjectInfo(ProjectInfoVo vo) {
        ProjectInfo projectInfo = BeanUtil.copyProperties(vo, ProjectInfo.class);
        ProjectInfo one = this.lambdaQuery().eq(ProjectInfo::getName, projectInfo.getName()).one();
        if (Objects.nonNull(one)) {
            throw new ServiceException("项目名称已存在");
        }
        //新增项目信息
        Map<String, String> map = new HashMap<>();
        Integer maxSeqId = this.getBaseMapper().getMaxSeqId();
        projectInfo.setSeqid(++maxSeqId);
        projectInfo.setApplyOrgname(SecurityUtils.getLoginUser().getUser().getDept().getDeptName());
        projectInfo.setState(CommonConstant.StateType.DRAFT.value);

        //设置考核部门
        setAssessOrg(projectInfo, vo.getIsPk());

        //保存项目信息表
        this.saveOrUpdate(projectInfo);

        //新增项目编号记录
        ProjectSn sn = this.generateSn(projectInfo.getId(), projectInfo.getSeqid(), null, vo.getYear());

        //新增项目年度信息
        ProjectYearRelation projectYearRelation = new ProjectYearRelation();
        projectYearRelation.setProjId(projectInfo.getId());
        projectYearRelation.setProjType(Integer.valueOf(vo.getProjType()));
        projectYearRelation.setYear(vo.getYear());
        Integer maxSeqNo = projectYearRelationMapper.getMaxSeqNo();
        projectYearRelation.setSeqNo(++maxSeqNo);
        projectYearRelationService.save(projectYearRelation);

        //新增项目(计划）操作记录表
        publishOperateEvent(projectInfo.getId(), "新增");

        map.put("id", projectInfo.getId());
        map.put("tempSn", sn.getTempSn());
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateProjectInfo(ProjectInfo projectInfo) {
        List<ProjectInfo> list = this.lambdaQuery()
                .eq(ProjectInfo::getName, projectInfo.getName())
                .ne(ProjectInfo::getId, projectInfo.getId())
                .list();
        if (!list.isEmpty()) {
            throw new ServiceException("项目名称已存在");
        }
        //设置考核部门
        setAssessOrg(projectInfo, projectInfo.getIsPk());

        ProjectInfo byId = this.getById(projectInfo.getId());
        String jsonData = JSONUtil.toJsonStr(byId);
        boolean update = this.updateById(projectInfo);

        Long cOrgId = projectInfo.getCooperateOrgid();
        if (cOrgId == null) {
            this.lambdaUpdate()
                    .eq(ProjectInfo::getId, projectInfo.getId())
                    .set(ProjectInfo::getCooperateOrgid, null)
                    .set(ProjectInfo::getCooperateOrgname, null)
                    .update();
        }
        //新增项目(计划）操作记录表
        publishOperateEvent(projectInfo.getId(), "修改");
        publishHistoryEvent(projectInfo.getId(), CommonConstant.ProjectOrPlan.PROJECT, "修改项目信息", jsonData);
        return update;
    }

    /**
     * 设置考核部门
     *
     * @param projectInfo 项目信息
     * @param isPK        是否勾选配合单位为考核主体
     */
    private void setAssessOrg(ProjectInfo projectInfo, String isPK) {
        Long deptId;
        if (CommonConstant.ZeroOrOne.ONE.value.equals(isPK)) {
            //如果勾选配合单位为考核主体，即配合单位为考核主体
            deptId = projectInfo.getCooperateOrgid();
            if (deptId == null) {
                throw new ServiceException("勾选配合单位为考核主体, 请填写配合单位");
            }
        } else {
            //如果未勾选配合单位为考核主体的，即申报单位为考核主体
            deptId = projectInfo.getApplyOrgid();
        }
        SysDept dept = sysDeptService.selectDeptById(deptId);
        Long parentId = dept.getParentId();
        if (Objects.equals(parentId, 100L) || Objects.equals(parentId, 200L) || Objects.equals(parentId, 0L)) {
            // 部门的parent_id in (100,200,0)，则考核主体为自己
            projectInfo.setAssessOrgid(dept.getDeptId());
            projectInfo.setAssessOrgname(dept.getDeptName());

        } else {
            // 部门parent_id not in (100,200,0)，则考核主体为上级部门
            SysDept sysDept = sysDeptService.selectDeptById(parentId);
            projectInfo.setAssessOrgid(sysDept.getDeptId());
            projectInfo.setAssessOrgname(sysDept.getDeptName());
        }
    }

    @Override
    @DataScope(deptAlias = "a", userAlias = "u", isProjectData = true)
    public List<ProjectInfoCopyVo> getReproducibleProject(ProjectQueryConditionVo vo) {
        return this.getBaseMapper().getReproducibleProject(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyProject(String[] ids, Integer year) {
        for (String id : ids) {
            //复制项目信息
            ProjectInfo projectInfo = this.getById(id);
            projectInfo.setId(null);
            Integer maxSeqId = this.getBaseMapper().getMaxSeqId();
            projectInfo.setSeqid(++maxSeqId);
            projectInfo.setNatureCode("JC");
            projectInfo.setNatureName("经常性项目");
            projectInfo.setName(projectInfo.getName() + "[复制]");
            projectInfo.setApplyOrgid(SecurityUtils.getDeptId());
            projectInfo.setApplyOrgname(SecurityUtils.getLoginUser().getUser().getDept().getDeptName());
            projectInfo.setApplyTime(new Date());
            projectInfo.setBuildBegin(updateDate(true));
            projectInfo.setBuildEnd(updateDate(false));
            projectInfo.setFundBegin(updateDate(true));
            projectInfo.setFundEnd(updateDate(false));
            projectInfo.setState(CommonConstant.StateType.DRAFT.value);
            projectInfo.setLibAmount(null);
            projectInfo.setContractAmount(null);
            projectInfo.setLibState(0);
            projectInfo.setLibReason(null);
            projectInfo.setCheckState(0);
            projectInfo.setCheckReason(null);
            projectInfo.setBasketCode(null);
            projectInfo.setBasketName(null);
            projectInfo.setBaseProjid(id);
            projectInfo.setCreateBy(null);
            projectInfo.setCreateTime(null);
            projectInfo.setUpdateBy(null);
            projectInfo.setUpdateTime(null);
            this.save(projectInfo);

            //新增编号信息
            this.generateSn(projectInfo.getId(), projectInfo.getSeqid(), projectInfo.getApplyOrgid(), year);

            //生成年度信息
            ProjectYearRelation yearRelation = new ProjectYearRelation();
            yearRelation.setYear(year);
            yearRelation.setProjId(projectInfo.getId());
            yearRelation.setProjType(CommonConstant.ProjectType.RECURRENT_PROJECT.value);
            Integer maxSeqNo = projectYearRelationMapper.getMaxSeqNo();
            yearRelation.setSeqNo(++maxSeqNo);
//            yearRelation.setUpdatedTime(new Date());
            projectYearRelationService.save(yearRelation);

            //复制绩效信息
            List<ProjectPerformance> performanceList = projectPerformanceService.lambdaQuery()
                    .eq(ProjectPerformance::getProjId, id)
                    .list();
            for (ProjectPerformance performance : performanceList) {
                performance.setId(null);
                performance.setProjId(projectInfo.getId());
                performance.setCreateBy(null);
                performance.setCreateTime(null);
                performance.setUpdateBy(null);
                performance.setUpdateTime(null);
                projectPerformanceService.save(performance);
            }
        }
        return true;
    }

    @Override
    public Boolean updateProjectSummary(ProjectSummaryDto dto) {

        String projId = dto.getProjId();
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, projId)
                .set(ProjectInfo::getNecessity, dto.getNecessity())
                .set(ProjectInfo::getBasis, dto.getBasis())
                .set(ProjectInfo::getMainCnt, dto.getMainCnt())
                .set(ProjectInfo::getActionPlan, dto.getActionPlan())
                .update();

        //新增项目(计划）操作记录表
        ProjectInfo byId = this.getById(dto.getProjId());
        String jsonData = JSONUtil.toJsonStr(byId);
        publishOperateEvent(dto.getProjId(), "修改");
        publishHistoryEvent(dto.getProjId(), CommonConstant.ProjectOrPlan.PROJECT, "修改项目概述信息", jsonData);

        return update;
    }

    @Override
    public Boolean moveNextYear(String pid, Integer year) {

        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .set(ProjectYearRelation::getYear, year + 1)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "移动一下年");

        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reportProject(String pid, Integer year) {
        ProjectInfo info = this.lambdaQuery().eq(ProjectInfo::getId, pid).one();
        //验证项目信息数据
        ProjectInfoValidate infoValidate = BeanUtil.copyProperties(info, ProjectInfoValidate.class);
        try {
            BeanValidators.validateWithException(validator, infoValidate);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        // 获取项目绩效信息
        if (info.getEstAmount().compareTo(new BigDecimal("100")) >= 0) {
            // 获取项目绩效信息
            List<ProjectPerformance> performanceList = projectPerformanceService.lambdaQuery()
                    .eq(ProjectPerformance::getProjId, pid)
                    .list();
            if (performanceList.isEmpty()) {
                throw new ServiceException("[绩效目标]绩效分解目标必须填写");
            }
        }
        ProjectPlan projectPlan = projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .one();

        if (projectPlan == null) {
            throw new ServiceException("[资金计划]资金计划必须填写");
        }
        if (info.getEstAmount().compareTo(projectPlan.getDeclareAmount()) < 0) {
            throw new ServiceException("[资金计划]上报计划总额超限");
        }
        String period = configService.selectConfigByKey("sys.plan.submit.period");
        if (StringUtils.isBlank(period)){
            throw new ServiceException("请配置年初计划上报时间规则");
        }
        String[] split = period.split(",");
        if (split.length != 2) {
            throw new ServiceException("请配置正确的年初计划上报时间规则");
        }
        LocalDate startDate = null;
        LocalDate endDate = null;
        for (int i = 0; i < split.length; i++) {
            String dateStr = "1970-01-01";
            String[] dateSplit = split[i].split(":");
            if (dateSplit.length != 2 && ("c".equals(dateSplit[0]) || "p".equals(dateSplit[0]))) {
                throw new ServiceException("请配置正确的年初计划上报时间规则");
            }
            if ("c".equals(dateSplit[0])) {
                dateStr = year + "-" + dateSplit[1];
            }
            if ("p".equals(dateSplit[0])) {
                dateStr = (year - 1) + "-" + dateSplit[1];
            }
            if (i == 0){
                startDate = LocalDate.parse(dateStr);
            } else {
                endDate = LocalDate.parse(dateStr);
            }
        }
        String planType = "调整计划";
        if (!LocalDate.now().isBefore(startDate) && !LocalDate.now().isAfter(endDate)) {
            planType = "初步计划";
        }
        //如果计划类型为调整计划，上报同时修改计划表中的调整计划信息
        boolean isAdjustPlan = "调整计划".equals(planType);
        projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getDeclareUserid, SecurityUtils.getUserId())
                .set(ProjectPlan::getDeclareTime, new Date())
                .set(ProjectPlan::getPlanType, planType)
                .set(isAdjustPlan, ProjectPlan::getFormalAmount, BigDecimal.ZERO)
                .set(isAdjustPlan, ProjectPlan::getDeclareAmount, BigDecimal.ZERO)
                .set(isAdjustPlan, ProjectPlan::getPlanState, CommonConstant.StateType.RELEASED.value)
                .set(isAdjustPlan, ProjectPlan::getApplyAdjust, projectPlan.getDeclareAmount())
                .set(isAdjustPlan, ProjectPlan::getApplyReason, "直接提交调整计划")
                .set(isAdjustPlan, ProjectPlan::getApplyUserid, SecurityUtils.getUserId())
                .set(isAdjustPlan, ProjectPlan::getApplyTime, new Date())
                .set(isAdjustPlan, ProjectPlan::getApplyState, CommonConstant.ApplyStateType.ESCALATED.value)
                .set(!isAdjustPlan, ProjectPlan::getFormalAmount, null)
                .set(!isAdjustPlan, ProjectPlan::getDeclareAmount, projectPlan.getDeclareAmount())
                .set(!isAdjustPlan, ProjectPlan::getPlanState, CommonConstant.StateType.ESCALATED.value)
                .set(!isAdjustPlan, ProjectPlan::getApplyAdjust, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyReason, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyUserid, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyTime, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyState, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyRefused, null)
                .update();

        //修改项目上报信息，状态改为已上报
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getSubmitOrgid, SecurityUtils.getLoginUser().getDeptId())
                .set(ProjectInfo::getSubmitOrgname, SecurityUtils.getUsername())
                .set(ProjectInfo::getSubmitTime, new Date())
                .set(ProjectInfo::getState, CommonConstant.StateType.ESCALATED.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "上报");
        publishOperateEvent(pid, year, CommonConstant.ProjectOrPlan.PLAN, "上报");

        //发送消息
        sendReportProjectSms(info);

        return update;
    }

    @Override
    public Boolean onlyReportProject(String pid) {
        //根据项目id查询项目信息
        ProjectInfo info = this.getById(pid);
        //验证项目信息数据
        ProjectInfoValidate infoValidate = BeanUtil.copyProperties(info, ProjectInfoValidate.class);
        try {
            BeanValidators.validateWithException(validator, infoValidate);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        // 获取项目绩效信息
        if (info.getEstAmount().compareTo(new BigDecimal("100")) >= 0) {
            // 获取项目绩效信息
            List<ProjectPerformance> performanceList = projectPerformanceService.lambdaQuery()
                    .eq(ProjectPerformance::getProjId, pid)
                    .list();
            if (performanceList.isEmpty()) {
                throw new ServiceException("[绩效目标]绩效分解目标必须填写");
            }
        }
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getSubmitOrgid, SecurityUtils.getLoginUser().getDeptId())
                .set(ProjectInfo::getSubmitTime, new Date())
                .set(ProjectInfo::getState, CommonConstant.StateType.ESCALATED.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "上报");

        //发送消息
        sendReportProjectSms(info);

        return update;
    }

    /**
     * 发送项目上报消息
     *
     * @param info 项目信息
     */
    @NotNull
    private void sendReportProjectSms(ProjectInfo info) {
        String reporterStr = SecurityUtils.getLoginUser().getUser().getNickName() + "-" + SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
        List<SysUser> jfc = sysUserService.selectUserByPostCode("JFC_ZHGL");
        List<String> senders = new ArrayList<>();
        if (jfc != null && !jfc.isEmpty()) {
            List<String> list = jfc.stream().map(SysUser::getPhonenumber).filter(StringUtils::isNotBlank).distinct().toList();
            senders.addAll(list);
        }
        if (!senders.isEmpty()) {
            String toMobile = String.join(",", senders);
            SmsSendDto sendDto = SmsSendDto.builder()
                    .sourceId(info.getId())
                    .sourceType(CommonConstant.SourceType.PROJECT.getName())
                    .businessName("上报项目")
                    .toMobile(toMobile)
                    .toName("经发处综合管理员")
                    .template(reportProjectMessage)
                    .templateArgs(List.of(info.getName(), reporterStr))
                    .build();
            smsService.recordAndSendSms(sendDto);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean withdrawProject(String pid, Integer year) {

        projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getDeclareUserid, null)
                .set(ProjectPlan::getDeclareTime, null)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.DRAFT.value)
                .update();

        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getSubmitOrgid, null)
                .set(ProjectInfo::getSubmitOrgname, null)
                .set(ProjectInfo::getSubmitTime, null)
                .set(ProjectInfo::getState, CommonConstant.StateType.DRAFT.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "撤销上报");
        publishOperateEvent(pid, year, CommonConstant.ProjectOrPlan.PLAN, "撤销上报");


        return update;
    }

    @Override
    public Boolean onlyWithdrawProject(String pid) {
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getSubmitOrgid, null)
                .set(ProjectInfo::getSubmitOrgname, null)
                .set(ProjectInfo::getSubmitTime, null)
                .set(ProjectInfo::getState, CommonConstant.StateType.DRAFT.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "撤销上报");

        return update;
    }

    @Override
    public Boolean putTrash(String pid, Integer year) {
        ProjectYearRelation yearRelation = projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .one();

        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getId, yearRelation.getId())
                .set(CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(yearRelation.getProjType()), ProjectYearRelation::getIsClose, CommonConstant.ZeroOrOne.ONE.value)
                .set(!CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(yearRelation.getProjType()), ProjectYearRelation::getIsRecycle, CommonConstant.ZeroOrOne.ONE.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "放入回收站");
        return update;
    }

    @Override
    public Boolean recoveryProject(String pid, Integer year) {
        ProjectYearRelation yearRelation = projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .one();
        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getId, yearRelation.getId())
                .set(CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(yearRelation.getProjType()), ProjectYearRelation::getIsClose, CommonConstant.ZeroOrOne.ZERO.value)
                .set(!CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(yearRelation.getProjType()), ProjectYearRelation::getIsRecycle, CommonConstant.ZeroOrOne.ZERO.value)
                .update();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "回收站恢复");
        return update;
    }

    @Override
    public Boolean removeYearRelationByPid(String pid, Integer year) {
        boolean remove = this.projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .remove();

        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "删除" + year + "年项目");
        return remove;
    }

    @Override
    public Boolean removeProjectById(String pid) {
        boolean b = this.removeById(pid);
        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "删除项目");
        return b;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean backProject(String pid, Integer year) {
        ProjectInfo info = this.getById(pid);
        Integer basketCode = info.getBasketCode();
        String dictValue = DictUtils.getDictValue("project_basket_type", "实施库");
        if (basketCode != null && dictValue.equals(basketCode.toString())) {
            throw new ServiceException("该项目已成功发布过资金计划（或归入实施库）无法退回");
        }
        ProjectPlan plan = projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .one();
        if (plan != null && CommonConstant.StateType.RELEASED.value.equals(plan.getPlanState())) {
            throw new ServiceException("该项目已成功发布过资金计划（或归入实施库）无法退回");
        }
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getState, CommonConstant.StateType.BACK.value)
                .set(ProjectInfo::getBasketCode, null)
                .set(ProjectInfo::getBasketName, null)
                .set(ProjectInfo::getLibAmount, null)
                .set(ProjectInfo::getLibState, 0)
                .set(ProjectInfo::getLibReason, null)
                .set(ProjectInfo::getCheckAmount, null)
                .set(ProjectInfo::getCheckState, 0)
                .set(ProjectInfo::getCheckReason, null)
                .update();

        projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.BACK.value)
                .set(ProjectPlan::getDeclareUserid, null)
                .set(ProjectPlan::getDeclareTime, null)
                .set(ProjectPlan::getFormalAmount, null)
                .set(ProjectPlan::getAdjustAmount, null)
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.BACK.value)
                .update();
        //新增项目(计划）操作记录表
        publishOperateEvent(pid, "退回申请人");
        //发送短信
        List<String> mobileList = new ArrayList<>();
        mobileList.add(info.getHandlerTel());
        List<String> nameList = new ArrayList<>();
        nameList.add(info.getHandler());
        SysUser sysUser = sysUserService.selectUserByUserName(info.getCreateBy());
        if (sysUser != null && StringUtils.isNotEmpty(sysUser.getPhonenumber()) && !sysUser.getPhonenumber().equals(info.getHandlerTel())) {
            mobileList.add(sysUser.getPhonenumber());
            nameList.add(sysUser.getNickName());
        }
        String toMobile = String.join(",", mobileList);
        String toName = String.join(",", nameList);
        SmsSendDto sendDto = SmsSendDto.builder()
                .sourceId(info.getId())
                .sourceType(CommonConstant.SourceType.PROJECT.getName())
                .businessName("上报项目")
                .toMobile(toMobile)
                .toName(toName)
                .template(backProjectMessage)
                .templateArgs(List.of(info.getName()))
                .build();
        smsService.recordAndSendSms(sendDto);
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustPlan(AdjustPlanDto dto) {

        // 查询项目历年已执行支付金额
        List<FundPaybillSkr> skrList = JoinWrappers.lambda(FundPaybillSkr.class)
                .selectAll(FundPaybillSkr.class)
                .select(FundPaybill::getProjId)
                .join("inner join", FundPaybill.class, FundPaybill::getId, FundPaybillSkr::getBillId)
                .in(FundPaybill::getProjId, dto.getProjId())
                .apply("YEAR(pay_date) < {0}", dto.getYear())
                .list();
        BigDecimal payAmount = BigDecimalUtils.sumIfAnyNonNull(skrList, FundPaybillSkr::getPayAmount);
        if (payAmount != null) {
            payAmount = payAmount.divide(new BigDecimal("10000"));
        }
        // 历年已执行+当年调整计划
        BigDecimal sumAmount = BigDecimalUtils.sumAllowNull(dto.getApplyAdjust(), payAmount);

        ProjectInfo info = this.getById(dto.getProjId());
        BigDecimal amount = info.getContractAmount() != null ? info.getContractAmount() :
                info.getCheckAmount() != null ? info.getCheckAmount() :
                        info.getLibAmount() != null ? info.getLibAmount() :
                                info.getEstAmount();

        assert sumAmount != null;
        if (sumAmount.compareTo(amount) > 0) {
            throw new ServiceException("历年已执行+当年调整计划已达" + sumAmount.stripTrailingZeros().toPlainString() + "万元，超过项目核定/入库金额");
        }

        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, dto.getProjId())
                .eq(ProjectPlan::getYear, dto.getYear())
                .set(ProjectPlan::getApplyAdjust, dto.getApplyAdjust())
                .set(ProjectPlan::getApplyReason, dto.getApplyReason())
                .set(ProjectPlan::getApplyUserid, SecurityUtils.getUserId())
                .set(ProjectPlan::getApplyTime, new Date())
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.ESCALATED.value)
                .update();
        //新增项目(计划）操作记录表
        publishOperateEvent(dto.getProjId(), dto.getYear(), CommonConstant.ProjectOrPlan.PLAN, "计划调整");
        return update;
    }

    @Override
    public Boolean withdrawAdjustPlan(String pid, Integer year) {
        ProjectPlan plan = projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .one();
        boolean isAdjustPlan = "调整计划".equals(plan.getPlanType());
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.DRAFT.value)
                .set(ProjectPlan::getDeclareAmount, BigDecimal.ZERO)
                .set(ProjectPlan::getFormalAmount, null)
                .set(isAdjustPlan, ProjectPlan::getApplyAdjust, null)
                .set(isAdjustPlan, ProjectPlan::getApplyReason, null)
                .set(isAdjustPlan, ProjectPlan::getApplyState, null)
                .set(isAdjustPlan, ProjectPlan::getApplyUserid, null)
                .set(isAdjustPlan, ProjectPlan::getApplyTime, null)
                .set(isAdjustPlan, ProjectPlan::getApplyRefused, null)
                .update();
        //新增项目(计划）操作记录表
        publishOperateEvent(pid, year, CommonConstant.ProjectOrPlan.PLAN, "撤销计划调整");
        return update;
    }

    @Override
    @DataScope(deptAlias = "a", userAlias = "u", isProjectData = true)
    public List<ProjectInfoOfSelectVo> getProjectInfoOfPlan(ProjectSelectQueryConditionVo vo) {
        //项目单位重新处理，获取项目单位及子部门
        Long projectUnits = vo.getProjectUnits();
        List<Long> projectUnitsList = vo.getProjectUnitsList();
        if (projectUnits != null) {
            projectUnitsList.add(projectUnits);
            List<SysDept> childList = sysDeptService.getChildList(projectUnits);
            if (childList != null && !childList.isEmpty()) {
                projectUnitsList.addAll(childList.stream().map(SysDept::getDeptId).toList());
            }
        }
        vo.setProjectUnitsList(projectUnitsList);
        List<ProjectInfoOfSelectVo> planVoList = this.getBaseMapper().getProjectInfoOfSelect(vo);
        List<ProjectUsagePlanItem> itemList = JoinWrappers.lambda(ProjectUsagePlanItem.class)
                .selectAll(ProjectUsagePlanItem.class)
                .join("inner join", ProjectUsagePlan.class, ProjectUsagePlan::getId, ProjectUsagePlanItem::getFundId)
                .eq(ProjectUsagePlan::getYear, vo.getYear())
                .eq(ProjectUsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .list();
        if (Objects.nonNull(itemList) && !itemList.isEmpty()) {
            List<String> pidList = itemList.stream().map(ProjectUsagePlanItem::getProjId).toList();
            planVoList.removeIf(planVo -> pidList.contains(planVo.getId()));
        }
        return planVoList;
    }

    @Override
    public ProjectInfoVo getProjectInfoAndPlanById(String id, Integer year) {
        return this.getBaseMapper().getProjectInfoAndPlanById(id, year);
    }

    @Override
    public ProjectInfoMainExcel getProjectInfoMainExcel(ProjectQueryConditionVo vo) {
        List<ProjectInfoVo> abnormalList = getInfoList(vo);
        ProjectInfoMainExcel mainExcel = new ProjectInfoMainExcel();
        ProjectInfoTotalExcel jzTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel jcTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel xzTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel total = new ProjectInfoTotalExcel();
        List<ProjectInfoExcel> jzList = new ArrayList<>();
        List<ProjectInfoExcel> jcList = new ArrayList<>();
        List<ProjectInfoExcel> xzList = new ArrayList<>();
        for (ProjectInfoVo info : abnormalList) {
            ProjectYearRelation currentYearRelation = info.getCurrentYearRelation();
            ProjectInfoExcel projectInfoExcel = new ProjectInfoExcel();
            projectInfoExcel.setName(info.getName());
            projectInfoExcel.setOrgName(info.getApplyOrgname());
            projectInfoExcel.setHandler(info.getHandler());
            projectInfoExcel.setEstAmount(bigDecimalToString(info.getEstAmount()));
            String projectStatus;
            Integer state = info.getState();
            if (Objects.equals(state, CommonConstant.StateType.DRAFT.value) || Objects.equals(state, CommonConstant.StateType.BACK.value)) {
                projectStatus = "未上报";
            } else if (StringUtils.isBlank(info.getBasketName())) {
                projectStatus = "已上报";
            } else {
                projectStatus = info.getBasketName();
            }

            projectInfoExcel.setProjectStatus(projectStatus);
            projectInfoExcel.setLibAmount(bigDecimalToString(info.getLibAmount()));
            projectInfoExcel.setCheckAmount(bigDecimalToString(info.getCheckAmount()));
            projectInfoExcel.setHistoricalExecutedAmounts(bigDecimalToString(info.getPrevAmount()));
            projectInfoExcel.setCurrYearExecutedAmounts(bigDecimalToString(info.getCurrPayed()));
            projectInfoExcel.setExecutedAmountsTotal(bigDecimalToString(BigDecimalUtils.sumAllowNull(info.getPrevAmount(), info.getCurrPayed())));

            ProjectPlan projectPlan = info.getFundPlan();
            if (Objects.nonNull(projectPlan)) {
                if (CommonConstant.StateType.BACK.value.equals(projectPlan.getPlanState()) || CommonConstant.StateType.DRAFT.value.equals(projectPlan.getPlanState())) {
                    projectInfoExcel.setDeclareAmount("未上报");
                } else {
                    projectInfoExcel.setDeclareAmount(bigDecimalToString(projectPlan.getDeclareAmount()));
                }
                projectInfoExcel.setFormalAmount(bigDecimalToString(projectPlan.getFormalAmount()));
                Integer applyState = projectPlan.getApplyState();
                if (CommonConstant.ApplyStateType.ESCALATED.value.equals(applyState) || CommonConstant.ApplyStateType.CONFIRM.value.equals(applyState)) {
                    projectInfoExcel.setApplyAdjust(bigDecimalToString(projectPlan.getApplyAdjust()));
                } else {
                    projectInfoExcel.setApplyAdjust(null);
                }
                projectInfoExcel.setAdjustAmount(bigDecimalToString(projectPlan.getAdjustAmount()));
                projectInfoExcel.setRemark(projectPlan.getApplyReason());
            } else {
                projectInfoExcel.setDeclareAmount("未上报");
            }
            if (CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(jzList, jzTotal, projectInfoExcel);
            } else if (CommonConstant.ProjectType.RECURRENT_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(jcList, jcTotal, projectInfoExcel);
            } else if (CommonConstant.ProjectType.NEW_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(xzList, xzTotal, projectInfoExcel);
            }
        }
        //计算总合计
        List<ProjectInfoTotalExcel> list = Arrays.asList(jzTotal, jcTotal, xzTotal);
        total.setEstAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getEstAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setLibAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getLibAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setCheckAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getCheckAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setHistoricalExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getHistoricalExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setCurrYearExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getCurrYearExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setDeclareAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getDeclareAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setFormalAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getFormalAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setApplyAdjustTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getApplyAdjustTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setAdjustAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getAdjustAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));

        SysDept sysDept = sysDeptService.selectDeptById(vo.getProjectUnits());

        mainExcel.setYear(vo.getYear())
                .setDeptName(sysDept.getDeptName())
                .setJzList(jzList)
                .setJzTotal(jzTotal)
                .setJzCount(jzList.size())
                .setJcList(jcList)
                .setJcTotal(jcTotal)
                .setJcCount(jcList.size())
                .setXzList(xzList)
                .setXzTotal(xzTotal)
                .setXzCount(xzList.size())
                .setHjCount(jzList.size() + jcList.size() + xzList.size())
                .setTotal(total);

        return mainExcel;
    }

    @Override
    public void export(ProjectQueryConditionVo vo, HttpServletResponse response) throws IOException {
        vo.setIsNormal("1");
        ProjectInfoMainExcel mainExcel = this.getProjectInfoMainExcel(vo);
        List<ProjectInfoExcel> jzList = mainExcel.getJzList();
        ProjectInfoTotalExcel jzTotal = mainExcel.getJzTotal();
        List<ProjectInfoExcel> jcList = mainExcel.getJcList();
        ProjectInfoTotalExcel jcTotal = mainExcel.getJcTotal();
        List<ProjectInfoExcel> xzList = mainExcel.getXzList();
        ProjectInfoTotalExcel xzTotal = mainExcel.getXzTotal();
        ProjectInfoTotalExcel total = mainExcel.getTotal();

        String encodedFilename = URLEncoder.encode("项目(计划)申报.xlsx", StandardCharsets.UTF_8);
        // 设置响应头
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"");
        // 定义模板位置
        InputStream template = getClass().getClassLoader().getResourceAsStream("excel/申报模板.xlsx");

        if (template == null) {
            throw new ServerException("导出模板文件不存在");
        }
        Workbook workbook = WorkbookFactory.create(template);
        Sheet sheet = workbook.getSheetAt(0);

        // Excel索引从0开始
        if (xzList.isEmpty() && sheet.getRow(10) != null) {
            removeRow(sheet, 10);
        }
        if (jcList.isEmpty() && sheet.getRow(7) != null) {
            removeRow(sheet, 7);
        }
        if (jzList.isEmpty() && sheet.getRow(4) != null) {
            removeRow(sheet, 4);
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        template.close();
        workbook.close();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(outputStream.toByteArray());
        // 使用EasyExcel写出
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellStyleHandler())
                // 注册字符串数值转换器
                .registerConverter(new ExcelStringValueConverter())
                .withTemplate(byteArrayInputStream)
                .build();
        byteArrayInputStream.close();
        WriteSheet writeSheet = EasyExcel.writerSheet()
                .build();

        FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();
        // 填充数据
        excelWriter.fill(mainExcel, writeSheet);

        if (!xzList.isEmpty()) {
            excelWriter.fill(new FillWrapper("xzList", xzList), fillConfig, writeSheet);
        }
        if (!jzList.isEmpty()) {
            excelWriter.fill(new FillWrapper("jzList", jzList), fillConfig, writeSheet);
        }
        if (!jcList.isEmpty()) {
            excelWriter.fill(new FillWrapper("jcList", jcList), fillConfig, writeSheet);
        }
        excelWriter.fill(new FillWrapper("jzTotal", List.of(jzTotal)), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("jcTotal", List.of(jcTotal)), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("xzTotal", List.of(xzTotal)), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("total", List.of(total)), fillConfig, writeSheet);
        excelWriter.finish();
    }

    /**
     * 计算不同类别的合计以及存储
     *
     * @param infoList         存储项目信息excel对象列表
     * @param infoTotal        存储项目信息excel对象合计对象
     * @param projectInfoExcel 单个项目信息excel对象
     */
    private void computeTotal(List<ProjectInfoExcel> infoList, ProjectInfoTotalExcel infoTotal, ProjectInfoExcel projectInfoExcel) {
        projectInfoExcel.setIndex(infoList.size() + 1);
        infoList.add(projectInfoExcel);

        if (Objects.nonNull(projectInfoExcel.getEstAmount())) {
            infoTotal.setEstAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getEstAmountTotal()).add(new BigDecimal(projectInfoExcel.getEstAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getLibAmount())) {
            infoTotal.setLibAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getLibAmountTotal()).add(new BigDecimal(projectInfoExcel.getLibAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getCheckAmount())) {
            infoTotal.setCheckAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getCheckAmountTotal()).add(new BigDecimal(projectInfoExcel.getCheckAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getHistoricalExecutedAmounts())) {
            infoTotal.setHistoricalExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getHistoricalExecutedAmountsTotal()).add(new BigDecimal(projectInfoExcel.getHistoricalExecutedAmounts()))));
        }

        if (Objects.nonNull(projectInfoExcel.getCurrYearExecutedAmounts())) {
            infoTotal.setCurrYearExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getCurrYearExecutedAmountsTotal()).add(new BigDecimal(projectInfoExcel.getCurrYearExecutedAmounts()))));
        }
        infoTotal.setExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getHistoricalExecutedAmountsTotal()).add(new BigDecimal(infoTotal.getCurrYearExecutedAmountsTotal()))));

        if (Objects.nonNull(projectInfoExcel.getDeclareAmount()) && !"未上报".equals(projectInfoExcel.getDeclareAmount())) {
            infoTotal.setDeclareAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getDeclareAmountTotal()).add(new BigDecimal(projectInfoExcel.getDeclareAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getFormalAmount())) {
            infoTotal.setFormalAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getFormalAmountTotal()).add(new BigDecimal(projectInfoExcel.getFormalAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getApplyAdjust())) {
            infoTotal.setApplyAdjustTotal(bigDecimalToString(new BigDecimal(infoTotal.getApplyAdjustTotal()).add(new BigDecimal(projectInfoExcel.getApplyAdjust()))));
        }
        if (Objects.nonNull(projectInfoExcel.getAdjustAmount())) {
            infoTotal.setAdjustAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getAdjustAmountTotal()).add(new BigDecimal(projectInfoExcel.getAdjustAmount()))));
        }
    }

    /**
     * 生成项目编号信息
     *
     * @param pid   项目id
     * @param seqId 项目序号
     * @return 项目编号信息对象
     */
    private ProjectSn generateSn(String pid, Integer seqId, Long deptId, Integer year) {
//        int year = LocalDate.now().getYear();
        ProjectSn projectSn = new ProjectSn();
        projectSn.setProjId(pid);
        projectSn.setTempYear(year);
        projectSn.setTempSeqid(seqId);
        if (Objects.isNull(deptId)) {
            deptId = SecurityUtils.getDeptId();
        }
        SysDept sysDept = sysDeptService.selectDeptById(deptId);
        String dSeqNo = Objects.nonNull(sysDept) ? sysDept.getSeqNo() : "0";
        if (String.valueOf(seqId).length() < 4) {
            projectSn.setTempSn(String.format("临-%d-%s-%04d", year, dSeqNo, seqId));
        } else {
            projectSn.setTempSn(String.format("临-%d-%s-%d", year, dSeqNo, seqId));
        }
        projectSn.setTempTime(new Date());
        projectSnService.save(projectSn);
        return projectSn;
    }

    /**
     * 复制操作部分日期年度跟新
     *
     * @param isBegin 是否是一年的开始，不是则设置一年的最后一天
     * @return 更新后的日期
     */
    private Date updateDate(boolean isBegin) {
        int currentYear = LocalDate.now().getYear();
        Instant instant;
        if (isBegin) {
            instant = LocalDate.of(currentYear, 1, 1).atStartOfDay(ZoneId.systemDefault()).toInstant();
        } else {
            instant = LocalDate.of(currentYear, 12, 31).atStartOfDay(ZoneId.systemDefault()).toInstant();
        }
        return Date.from(instant);
    }


    private void publishOperateEvent(String pid, String operateType) {
        publishOperateEvent(pid, null, CommonConstant.ProjectOrPlan.PROJECT, operateType);
    }

    /**
     * 发布项目操作事件
     *
     * @param pid         项目id（必填）
     * @param year        年度
     * @param type        项目类型
     * @param operateType 操作类型
     */
    private void publishOperateEvent(String pid, Integer year, CommonConstant.ProjectOrPlan type, String operateType) {
        OperateEvent event = new OperateEvent()
                .setProjId(pid)
                .setType(type)
                .setOperateType(operateType)
                .setYear(year);
        SpringUtil.getApplicationContext().publishEvent(event);
    }

    /**
     * 发布历史操作事件
     *
     * @param pid          项目id（必填）
     * @param updateReason 操作类型
     */
    private void publishHistoryEvent(String pid, CommonConstant.ProjectOrPlan type, String updateReason, String dataJson) {
        HistoryEvent event = new HistoryEvent()
                .setProjId(pid)
                .setType(type)
                .setUpdateReason(updateReason)
                .setDataJson(dataJson);
        SpringUtil.getApplicationContext().publishEvent(event);
    }

    private Map<String, List<FundPaybillSkr>> getPayBillSkrMap(List<String> ids, Integer year) {
        //获取资金拨付信息
        List<FundPaybillSkr> innerJoin = JoinWrappers.lambda(FundPaybillSkr.class)
                .selectAll(FundPaybillSkr.class)
                .select(FundPaybill::getProjId)
                .join("inner join", FundPaybill.class, FundPaybill::getId, FundPaybillSkr::getBillId)
                .in(FundPaybill::getProjId, ids)
                .apply("YEAR(pay_date) <= {0}", year)
                .list();
        return innerJoin.stream().collect(Collectors.groupingBy(FundPaybillSkr::getProjId));
    }

    private void setPayAmount(ProjectInfoVo projectInfoVo, List<FundPaybillSkr> skrList, Integer year) {
        if (Objects.isNull(skrList)) {
            return;
        }
        BigDecimal prevAmount = skrList.stream().filter(item -> {
            LocalDate localDate = item.getPayDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            return localDate.getYear() < year;
        }).map(item -> item.getPayAmount() == null ? BigDecimal.ZERO : item.getPayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        projectInfoVo.setPrevAmount(prevAmount.divide(new BigDecimal("10000")));
        BigDecimal currPayed = skrList.stream().filter(item -> {
            LocalDate localDate = item.getPayDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            return localDate.getYear() == year;
        }).map(item -> item.getPayAmount() == null ? BigDecimal.ZERO : item.getPayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        projectInfoVo.setCurrPayed(currPayed.divide(new BigDecimal("10000")));
    }

    private String bigDecimalToString(BigDecimal val) {
        return val == null ? null : val.stripTrailingZeros().toPlainString();
    }

    // 删除指定行并上移后续行
    private static void removeRow(Sheet sheet, int rowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        if (rowIndex >= 0 && rowIndex < lastRowNum) {
            sheet.shiftRows(rowIndex + 1, lastRowNum, -1);
        } else if (rowIndex == lastRowNum) {
            Row removingRow = sheet.getRow(rowIndex);
            if (removingRow != null) {
                sheet.removeRow(removingRow);
            }
        }
    }
}




