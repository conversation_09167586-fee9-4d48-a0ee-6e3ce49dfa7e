package com.ruoyi.system.domain.project;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 项目(计划）操作记录表
 *
 * <AUTHOR>
 * @TableName project_plan_operate
 */
@TableName(value ="project_plan_operate")
@Data
public class ProjectPlanOperate {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 类型（项目，计划）;类型（项目，计划）
     */
    private String type;

    /**
     * 项目ID;项目ID（即使是计划，也需存项目ID）
     */
    private String projId;

    /**
     * 计划ID;计划ID
     */
    private String planId;

    /**
     * 操作人;操作人ID
     */
    private String operateUserid;

    /**
     * 操作时间;操作时间
     */
    private Date operateTime;

    /**
     * 操作类型;操作类型（提交、受理、分送、审批等）
     */
    private String operateType;

    /**
     * 操作意见;操作意见（同意、不同意、具体哪个篮子）
     */
    private String operateOpinion;

    /**
     * 操作理由;操作理由
     */
    private String operateReason;

    /**
     * 关联待办ID;关联待办ID
     */
    private String todoId;

}