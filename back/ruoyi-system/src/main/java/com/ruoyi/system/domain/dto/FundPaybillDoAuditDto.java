package com.ruoyi.system.domain.dto;

import com.ruoyi.common.constant.CommonConstant;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 拨款单审核操作dto
 */
@Data
public class FundPaybillDoAuditDto {

    @NotBlank(message = "拨款单id不能为空")
    private String billId;

    @NotNull(message = "操作结果不能为空")
    private CommonConstant.NodeOpResult operateResult;

    private String operateReason;

    @NotNull(message = "核定金额不能为空")
    @Positive(message = "核定金额必须大于0")
    private BigDecimal checkAmount;

    /**
     * 是否发送短信通知
     */
    private Boolean sendSms;

    /**
     * 是否审核下一个
     */
    private Boolean auditNext;
}
