package com.ruoyi.system.service.impl.project;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.UpdateJoinWrapper;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.handler.ExcelStringValueConverter;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.*;
import com.ruoyi.system.domain.dto.ProjectBasketDto;
import com.ruoyi.system.domain.dto.ProjectAmountDto;
import com.ruoyi.system.domain.dto.SmsSendDto;
import com.ruoyi.system.domain.event.OperateEvent;
import com.ruoyi.system.domain.excel.ProjectInfoExcel;
import com.ruoyi.system.domain.excel.ProjectInfoMainExcel;
import com.ruoyi.system.domain.excel.ProjectInfoTotalExcel;
import com.ruoyi.system.domain.fund.FundPaybill;
import com.ruoyi.system.domain.fund.FundPaybillSkr;
import com.ruoyi.system.domain.project.*;
import com.ruoyi.system.domain.vo.project.ProjectInfoVo;
import com.ruoyi.system.domain.vo.project.ProjectManageConditionVo;
import com.ruoyi.system.mapper.project.ProjectInfoMapper;
import com.ruoyi.system.mapper.project.ProjectUsagePlanItemMapper;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.SmsService;
import com.ruoyi.system.service.project.*;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 针对表【project_info(项目信息表;)】的数据库操作Service实现，通过项目管理模块进行管理
 * @date 2025/6/10 13:56
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class ProjectManageServiceImpl extends ServiceImpl<ProjectInfoMapper, ProjectInfo> implements ProjectManageService {

    public static final String PROJECT_BASKET_DICT_TYPE = "project_basket_type";
    public static final String IMPLEMENT_LIBRARY = "实施库";
    public static final String RESERVE_VAULTS = "储备库";

    @Value("${sms.template.addBasketMessage}")
    private String addBasketTemplate;
    @Value("${sms.template.libMessage}")
    private String libMessageTemplate;
    @Value("${sms.template.checkMessage}")
    private String checkMessageTemplate;

    private final ProjectYearRelationService projectYearRelationService;
    private final ProjectPlanService projectPlanService;
    private final ProjectUsagePlanItemMapper projectUsagePlanItemMapper;
    private final ProjectSnService projectSnService;
    private final ISysDeptService sysDeptService;
    private final ISysUserService sysUserService;
    private final SmsService smsService;
    private final ISysConfigService configService;

    @Override
    @DataScope(deptAlias = "b", userAlias = "u", isProjectData = true)
    public List<ProjectInfoVo> getProjectManageList(ProjectManageConditionVo vo) {
        return this.getProjectInfoNoPermissioList(vo);
    }

    @Override
    public List<ProjectInfoVo> getProjectInfoNoPermissioList(ProjectManageConditionVo vo) {
        Integer year = vo.getYear();
        List<ProjectInfoVo> infos = this.getBaseMapper().getProjectManageList(vo);
        List<String> ids = infos.stream().map(ProjectInfoVo::getId).toList();
        // 获取所有项目用款计划信息
        List<ProjectUsagePlanItem> projectUsagePlanItems = JoinWrappers.lambda(ProjectUsagePlanItem.class)
                .selectAll(ProjectUsagePlanItem.class)
                .join("inner join", ProjectUsagePlan.class, ProjectUsagePlan::getId, ProjectUsagePlanItem::getFundId)
                .eq(ProjectUsagePlan::getYear, vo.getYear())
                .eq(ProjectUsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .list();

        //获取资金拨付信息
        Map<String, List<FundPaybillSkr>> skrMap = new HashMap<>();
        if (!ids.isEmpty()) {
            List<FundPaybillSkr> innerJoin = JoinWrappers.lambda(FundPaybillSkr.class)
                    .selectAll(FundPaybillSkr.class)
                    .select(FundPaybill::getProjId)
                    .join("inner join", FundPaybill.class, FundPaybill::getId, FundPaybillSkr::getBillId)
                    .in(FundPaybill::getProjId, ids)
                    .apply("YEAR(pay_date) <= {0}", year)
                    .list();
            skrMap = innerJoin.stream().collect(Collectors.groupingBy(FundPaybillSkr::getProjId));
        }
        for (ProjectInfoVo info : infos) {
            projectUsagePlanItems.stream().filter(item -> info.getId().equals(item.getProjId())).findAny().ifPresent(info::setUsagePlanItem);
            List<FundPaybillSkr> skrList = skrMap.get(info.getId());
            if (Objects.nonNull(skrList)) {
                info.setPrevAmount(skrList.stream().filter(item -> {
                    LocalDate localDate = item.getPayDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    return localDate.getYear() < year;
                }).map(item -> item.getPayAmount() == null ? BigDecimal.ZERO : item.getPayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal("10000")));
                info.setCurrPayed(skrList.stream().filter(item -> {
                    LocalDate localDate = item.getPayDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    return localDate.getYear() == year;
                }).map(item -> item.getPayAmount() == null ? BigDecimal.ZERO : item.getPayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal("10000")));
            }
        }
        return infos;
    }

    @Override
    public ProjectInfo getProjectBasicInfo(String pid) {
        return this.getById(pid);
    }

    @Override
    public Boolean addImportantProject(String pid, Integer year) {
        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .set(ProjectYearRelation::getImpProj, CommonConstant.ZeroOrOne.ONE.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "添加重点项目");
        return update;
    }

    @Override
    public Boolean cancelImportantProject(String pid, Integer year) {
        boolean update = projectYearRelationService.lambdaUpdate()
                .eq(ProjectYearRelation::getProjId, pid)
                .eq(ProjectYearRelation::getYear, year)
                .set(ProjectYearRelation::getImpProj, CommonConstant.ZeroOrOne.ZERO.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消重点项目");
        return update;
    }

    @Override
    public Boolean addProjectBasket(ProjectBasketDto dto) {
        //在项目中添加篮子信息
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, dto.getProjId())
                .set(ProjectInfo::getBasketCode, dto.getBasketCode())
                .set(ProjectInfo::getBasketName, dto.getBasketName())
                .set(ProjectInfo::getLibAmount, dto.getLibAmount())
                .update();

        // 判断是否发送短信，如果是则发送短信
        if (CommonConstant.ZeroOrOne.ONE.value.equals(dto.getIsSendSms())) {
            ProjectInfo info = this.getById(dto.getProjId());
            String createBy = info.getCreateBy();
            SysUser sysUser = sysUserService.selectUserByUserName(createBy);
            String phoneNumber = sysUser.getPhonenumber();
            String toMobile = info.getHandlerTel();
            String toName = info.getHandler();
            // 如果手机号不一致，则发短信给项目经办人和创建人手机号
            if (StringUtils.isNotEmpty(phoneNumber) && !toMobile.equals(phoneNumber)) {
                toMobile = toMobile + "," + phoneNumber;
                toName = toName + "," + sysUser.getNickName();
            }
            SmsSendDto sendDto = SmsSendDto.builder()
                    .sourceId(dto.getProjId())
                    .sourceType(CommonConstant.SourceType.PROJECT.getName())
                    .businessName("添加篮子")
                    .toMobile(toMobile)
                    .toName(toName)
                    .template(addBasketTemplate)
                    .templateArgs(List.of(info.getName(), dto.getBasketName(), dto.getLibAmount().toPlainString()))
                    .build();
            //发送短信
            smsService.recordAndSendSms(sendDto);
        }
        //新增项目(计划)操作记录表
        this.publishOperateEvent(dto.getProjId(), "添加篮子");
        return update;
    }

    @Override
    public Boolean updateLibInfo(ProjectAmountDto dto) {
        String dictValue = DictUtils.getDictValue(PROJECT_BASKET_DICT_TYPE, IMPLEMENT_LIBRARY);
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, dto.getProjId())
                .set(ProjectInfo::getBasketCode, dictValue)
                .set(ProjectInfo::getBasketName, IMPLEMENT_LIBRARY)
                .set(ProjectInfo::getLibAmount, dto.getAmount())
                .set(ProjectInfo::getLibReason, dto.getReason())
                .set(ProjectInfo::getLibState, CommonConstant.ZeroOrOne.ONE.value)
                .update();

        // 判断是否发送短信，如果是则发送短信
        if (CommonConstant.ZeroOrOne.ONE.value.equals(dto.getIsSendSms())) {
            ProjectInfo info = this.getById(dto.getProjId());
            SysUser sysUser = sysUserService.selectUserByUserName(info.getCreateBy());
            String phoneNumber = sysUser.getPhonenumber();
            String toName = info.getHandler();
            String toMobile = info.getHandlerTel();
            // 如果手机号不一致，则发短信给项目经办人和创建人手机号
            if (StringUtils.isNotEmpty(phoneNumber) && !toMobile.equals(phoneNumber)) {
                toMobile = toMobile + "," + phoneNumber;
                toName = toName + "," + sysUser.getNickName();
            }
            SmsSendDto sendDto = SmsSendDto.builder()
                    .sourceId(dto.getProjId())
                    .sourceType(CommonConstant.SourceType.PROJECT.getName())
                    .businessName("入库")
                    .toMobile(toMobile)
                    .toName(toName)
                    .template(libMessageTemplate)
                    .templateArgs(List.of(info.getName(), info.getName(), info.getLibAmount().toPlainString()))
                    .build();
            smsService.recordAndSendSms(sendDto);
        }

        //新增项目(计划)操作记录表
        this.publishOperateEvent(dto.getProjId(), "更新入库信息");
        return update;
    }

    @Override
    public Boolean cancelLib(String pid) {
        String dictValue = DictUtils.getDictValue(PROJECT_BASKET_DICT_TYPE, RESERVE_VAULTS);
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getBasketCode, dictValue)
                .set(ProjectInfo::getBasketName, RESERVE_VAULTS)
                .set(ProjectInfo::getLibState, CommonConstant.ZeroOrOne.ZERO.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消入库");
        return update;
    }

    @Override
    public Boolean updateCheckInfo(ProjectAmountDto dto) {
        String dictValue = DictUtils.getDictValue(PROJECT_BASKET_DICT_TYPE, IMPLEMENT_LIBRARY);
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, dto.getProjId())
                .set(ProjectInfo::getBasketCode, dictValue)
                .set(ProjectInfo::getBasketName, IMPLEMENT_LIBRARY)
                .set(ProjectInfo::getLibAmount, dto.getAmount())
                .set(ProjectInfo::getCheckAmount, dto.getAmount())
                .set(ProjectInfo::getCheckReason, dto.getReason())
                .set(ProjectInfo::getCheckState, CommonConstant.ZeroOrOne.ONE.value)
                .update();

        if (CommonConstant.ZeroOrOne.ONE.value.equals(dto.getIsSendSms())) {
            ProjectInfo info = this.getById(dto.getProjId());
            String checkUser = SecurityUtils.getLoginUser().getUser().getNickName();
            SmsSendDto sendDto = SmsSendDto.builder()
                    .sourceId(dto.getProjId())
                    .sourceType(CommonConstant.SourceType.PROJECT.getName())
                    .businessName("更新核定信息")
                    .toMobile(info.getHandlerTel())
                    .toName(info.getHandler())
                    .template(checkMessageTemplate)
                    .templateArgs(List.of(info.getName(), checkUser, info.getCheckAmount().toPlainString()))
                    .build();
            smsService.recordAndSendSms(sendDto);
        }
        //新增项目(计划)操作记录表
        this.publishOperateEvent(dto.getProjId(), "更新核定信息");
        return update;
    }

    @Override
    public Boolean cancelCheck(String pid) {
        String dictValue = DictUtils.getDictValue(PROJECT_BASKET_DICT_TYPE, RESERVE_VAULTS);
        boolean update = this.lambdaUpdate()
                .eq(ProjectInfo::getId, pid)
                .set(ProjectInfo::getCheckAmount, null)
                .set(ProjectInfo::getBasketCode, dictValue)
                .set(ProjectInfo::getBasketName, RESERVE_VAULTS)
                .set(ProjectInfo::getCheckState, CommonConstant.ZeroOrOne.ZERO.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消核定");
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean releasePlan(String pid, Integer year, BigDecimal amount) {
        // 生成正式编号
        generateFormalSn(pid, year);

        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.RELEASED.value)
                .set(ProjectPlan::getFormalAmount, amount)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "下达计划");
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean doubleClickRelease(String pid, Integer year, BigDecimal amount) {
        String flag = configService.selectConfigByKey("sys.click.releasePlan");
        if (StringUtils.isBlank(flag) || !(CommonConstant.ZeroOrOne.ONE.value.equals(flag) || CommonConstant.ZeroOrOne.ZERO.value.equals(flag))) {
            throw new ServiceException("请联系管理员配置正确的参数！");
        }
        UpdateJoinWrapper<ProjectUsagePlanItem> wrapper = JoinWrappers.update(ProjectUsagePlanItem.class);
        ProjectUsagePlanItem projectUsagePlanItem = new ProjectUsagePlanItem();
        if (CommonConstant.ZeroOrOne.ZERO.value.equals(flag)) {
            projectPlanService.updateEarlyAmount(pid, year, amount);
            projectUsagePlanItem.setCurrEarly(amount);
        }
        if (CommonConstant.ZeroOrOne.ONE.value.equals(flag)) {
            projectUsagePlanItem.setCurrFormal(amount);
        }
        wrapper.leftJoin(ProjectUsagePlan.class, ProjectUsagePlan::getId, ProjectUsagePlanItem::getFundId)
                .eq(ProjectUsagePlan::getYear, year)
                .eq(ProjectUsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .eq(ProjectUsagePlanItem::getProjId, pid);
        int i = projectUsagePlanItemMapper.updateJoin(projectUsagePlanItem, wrapper);
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "双击下达计划");
        return i == 1;
    }

    @Override
    public Boolean cancelRelease(String pid, Integer year) {
        ProjectPlan plan = projectPlanService.lambdaQuery()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .one();
        if (CommonConstant.ApplyStateType.ESCALATED.value.equals(plan.getApplyState()) || CommonConstant.ApplyStateType.CONFIRM.value.equals(plan.getApplyState())) {
            throw new ServiceException("项目计划已经进行过调整，操作失败");
        }
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.ESCALATED.value)
                .set(ProjectPlan::getFormalAmount, null)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消下达计划");
        return update;
    }

    @Override
    public Boolean adjustPlan(String pid, Integer year, BigDecimal amount) {
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getAdjustAmount, amount)
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.CONFIRM.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "调整下达");
        return update;
    }

    @Override
    public Boolean doubleClickAdjust(String pid, Integer year, BigDecimal amount) {
        UpdateJoinWrapper<ProjectUsagePlanItem> wrapper = JoinWrappers.update(ProjectUsagePlanItem.class);
        ProjectUsagePlanItem projectUsagePlanItem = new ProjectUsagePlanItem();
        projectUsagePlanItem.setCurrAdjust(amount);
        wrapper.leftJoin(ProjectUsagePlan.class, ProjectUsagePlan::getId, ProjectUsagePlanItem::getFundId)
                .eq(ProjectUsagePlan::getYear, year)
                .eq(ProjectUsagePlan::getIsSnap, CommonConstant.ZeroOrOne.ZERO.value)
                .eq(ProjectUsagePlanItem::getProjId, pid);
        int i = projectUsagePlanItemMapper.updateJoin(projectUsagePlanItem, wrapper);
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "双击调整下达");
        return i == 1;
    }

    @Override
    public Boolean cancelAdjust(String pid, Integer year) {
        boolean update = projectPlanService.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getAdjustAmount, null)
                .set(ProjectPlan::getApplyState, CommonConstant.ApplyStateType.BACK.value)
                .update();
        //新增项目(计划)操作记录表
        this.publishOperateEvent(pid, "取消调整下达");
        return update;

    }

    @Override
    public ProjectInfoMainExcel getProjectInfoMainExcel(ProjectManageConditionVo vo) {
        // 读取系统配置，用于后面用款计划读取正式金额的取值逻辑
        String flag = configService.selectConfigByKey("sys.click.releasePlan");

        List<ProjectInfoVo> abnormalList = getProjectManageList(vo);
        ProjectInfoMainExcel mainExcel = new ProjectInfoMainExcel();
        List<ProjectInfoExcel> jzList = new ArrayList<>();
        List<ProjectInfoExcel> jcList = new ArrayList<>();
        List<ProjectInfoExcel> xzList = new ArrayList<>();
        ProjectInfoTotalExcel jzTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel jcTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel xzTotal = new ProjectInfoTotalExcel();
        ProjectInfoTotalExcel total = new ProjectInfoTotalExcel();
        for (ProjectInfoVo info : abnormalList) {
            ProjectYearRelation currentYearRelation = info.getCurrentYearRelation();

            ProjectInfoExcel projectInfoExcel = new ProjectInfoExcel();
            projectInfoExcel.setName(info.getName());
            ProjectSn projectSn = info.getProjectSn();
            String sn = null;
            if (Objects.nonNull(projectSn)) {
                sn = projectSn.getFormalSn();
                if (StringUtils.isBlank(sn)) {
                    sn = projectSn.getTempSn();
                }
            }
            projectInfoExcel.setSn(sn);
            projectInfoExcel.setOrgName(info.getAssessOrgname());
            projectInfoExcel.setHandler(info.getHandler());
            projectInfoExcel.setEstAmount(bigDecimalToString(info.getEstAmount()));
            Integer state = info.getState();
            String projectStatus;
            if (Objects.equals(state, CommonConstant.StateType.DRAFT.value) || Objects.equals(state, CommonConstant.StateType.BACK.value)) {
                projectStatus = "未上报";
            } else if (StringUtils.isBlank(info.getBasketName())) {
                projectStatus = "已上报";
            } else {
                projectStatus = info.getBasketName();
            }

            projectInfoExcel.setProjectStatus(projectStatus);
            projectInfoExcel.setLibAmount(bigDecimalToString(info.getLibAmount()));
            projectInfoExcel.setCheckAmount(bigDecimalToString(info.getCheckAmount()));
            projectInfoExcel.setHistoricalExecutedAmounts(bigDecimalToString(info.getPrevAmount()));
            projectInfoExcel.setCurrYearExecutedAmounts(bigDecimalToString(info.getCurrPayed()));
            projectInfoExcel.setExecutedAmountsTotal(bigDecimalToString(BigDecimalUtils.sumAllowNull(info.getPrevAmount(), info.getCurrPayed())));
            ProjectPlan projectPlan = info.getFundPlan();
            if (Objects.nonNull(projectPlan)) {
                if (CommonConstant.StateType.BACK.value.equals(projectPlan.getPlanState()) || CommonConstant.StateType.DRAFT.value.equals(projectPlan.getPlanState())) {
                    projectInfoExcel.setDeclareAmount("未上报");
                } else {
                    projectInfoExcel.setDeclareAmount(bigDecimalToString(projectPlan.getDeclareAmount()));
                }
                //获取下达计划
                ProjectUsagePlanItem projectUsagePlanItem = info.getUsagePlanItem();
                BigDecimal currAmount = null;
                if (projectUsagePlanItem != null) {
                    //根据配置参数，获取金额的值
                    currAmount = CommonConstant.ZeroOrOne.ONE.value.equals(flag) ? projectUsagePlanItem.getCurrFormal() : projectUsagePlanItem.getCurrEarly();
                }
                BigDecimal formalAmount = projectPlan.getFormalAmount();
                if (!CommonConstant.StateType.DRAFT.value.equals(projectPlan.getPlanState()) && !CommonConstant.StateType.BACK.value.equals(projectPlan.getPlanState())) {
                    if (Objects.nonNull(formalAmount)) {
                        // 如果项目计划为已上报且formalAmount不为null，则formalAmount为 formalAmount
                        projectInfoExcel.setFormalAmount(bigDecimalToString(formalAmount));
                    } else if (currAmount != null) {
                        // 如果项目计划为已上报且formalAmount为null，查询用款计划表当年的正式计划或当年初步计划是否为空，如果存在一个不为null，则formalAmount为该不为null的值
                        // 标记这个值是从用款计划表中带出来的，用于导出excel时标红
                        projectInfoExcel.setIsFormalFlag("1");
                        projectInfoExcel.setFormalAmount(bigDecimalToString(currAmount));
                    }
                }

                //获取调整计划。第一查看是否已经调整计划，是取调整计划的金额，否取下达计划的金额
                Integer applyState = projectPlan.getApplyState();
                if (CommonConstant.ApplyStateType.ESCALATED.value.equals(applyState) || CommonConstant.ApplyStateType.CONFIRM.value.equals(applyState)) {
                    projectInfoExcel.setApplyAdjust(bigDecimalToString(projectPlan.getApplyAdjust()));
                } else if (Objects.nonNull(projectPlan.getFormalAmount())) {
                    projectInfoExcel.setApplyAdjust(bigDecimalToString(projectPlan.getFormalAmount()));
                    projectInfoExcel.setIsApplyAdjustFlag("2");
                } else {
                    projectInfoExcel.setApplyAdjust(null);
                }
                BigDecimal adjustAmount;
                if (CommonConstant.ApplyStateType.ESCALATED.value.equals(applyState)) {
                    projectInfoExcel.setIsAdjustAmountFlag("1");
                    if (Objects.nonNull(projectUsagePlanItem) && Objects.nonNull(projectUsagePlanItem.getCurrAdjust())) {
                        adjustAmount = projectUsagePlanItem.getCurrAdjust();
                    } else {
                        adjustAmount = projectPlan.getApplyAdjust();
                    }
                } else if (CommonConstant.ApplyStateType.BACK.value.equals(applyState) || CommonConstant.ApplyStateType.DRAFT.value.equals(applyState) || applyState == null){
                    projectInfoExcel.setIsAdjustAmountFlag("2");
                    adjustAmount = projectPlan.getFormalAmount();
                } else {
                    adjustAmount = projectPlan.getAdjustAmount();
                }
                projectInfoExcel.setAdjustAmount(bigDecimalToString(adjustAmount));
                projectInfoExcel.setRemark(projectPlan.getApplyReason());
            } else {
                projectInfoExcel.setDeclareAmount("未上报");
            }
            if (CommonConstant.ProjectType.CARRYOVER_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(jzList, jzTotal, projectInfoExcel);
            } else if (CommonConstant.ProjectType.RECURRENT_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(jcList, jcTotal, projectInfoExcel);
            } else if (CommonConstant.ProjectType.NEW_PROJECT.value.equals(currentYearRelation.getProjType())) {
                computeTotal(xzList, xzTotal, projectInfoExcel);
            }
        }
        jzTotal.setFormalAmountTotal(jzTotal.getFormalAmount1() + "\n" + jzTotal.getFormalAmount2());
        jzTotal.setAdjustAmountTotal(jzTotal.getAdjustAmount1() + "\n" + jzTotal.getAdjustAmount2());
        jcTotal.setFormalAmountTotal(jcTotal.getFormalAmount1() + "\n" + jcTotal.getFormalAmount2());
        jcTotal.setAdjustAmountTotal(jcTotal.getAdjustAmount1() + "\n" + jcTotal.getAdjustAmount2());
        xzTotal.setFormalAmountTotal(xzTotal.getFormalAmount1() + "\n" + xzTotal.getFormalAmount2());
        xzTotal.setAdjustAmountTotal(xzTotal.getAdjustAmount1() + "\n" + xzTotal.getAdjustAmount2());

        //计算总合计
        List<ProjectInfoTotalExcel> list = Arrays.asList(jzTotal, jcTotal, xzTotal);
        total.setEstAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getEstAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setLibAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getLibAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setCheckAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getCheckAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setHistoricalExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getHistoricalExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setCurrYearExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getCurrYearExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setExecutedAmountsTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getExecutedAmountsTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setDeclareAmountTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getDeclareAmountTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setFormalAmount1(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getFormalAmount1())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setFormalAmount2(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getFormalAmount2())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setApplyAdjustTotal(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getApplyAdjustTotal())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setAdjustAmount1(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getAdjustAmount1())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setAdjustAmount2(bigDecimalToString(list.stream().map(e -> new BigDecimal(e.getAdjustAmount2())).reduce(BigDecimal.ZERO, BigDecimal::add)));
        total.setFormalAmountTotal(total.getFormalAmount1() + "\n" + total.getFormalAmount2());
        total.setAdjustAmountTotal(total.getAdjustAmount1() + "\n" + total.getAdjustAmount2());

        mainExcel.setYear(vo.getYear())
                .setJzList(jzList)
                .setJzTotal(jzTotal)
                .setJzCount(jzList.size())
                .setJcList(jcList)
                .setJcTotal(jcTotal)
                .setJcCount(jcList.size())
                .setXzList(xzList)
                .setXzTotal(xzTotal)
                .setHjCount(jzList.size() + jcList.size() + xzList.size())
                .setXzCount(xzList.size())
                .setTotal(total);

        return mainExcel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void carryOver(Integer year) {

        //获取建设范围包含year年的项目列表
        List<ProjectInfo> infoList = this.lambdaQuery()
                .eq(ProjectInfo::getIsClosed, CommonConstant.ZeroOrOne.ZERO.value)
                .apply("YEAR(LEAST(build_begin ,fund_begin)) <= {0}", year)
                .apply("{0} <= YEAR(GREAT(build_end ,fund_end))", year)
                .list();
        if (infoList.isEmpty()) {
            infoList = new ArrayList<>();
        }
        //获取建设范围包含year年的项目列表id列表
        List<String> ids = infoList.stream().map(ProjectInfo::getId).toList();
        if (ids.isEmpty()){
            ids = new ArrayList<>();
        }
        //查询指定year的所有年度项目关系
        List<ProjectYearRelation> yearRelations = projectYearRelationService.lambdaQuery()
                .eq(ProjectYearRelation::getYear, year)
                .ne(ProjectYearRelation::getIsClose, CommonConstant.ZeroOrOne.ONE.value)
                .ne(ProjectYearRelation::getIsRecycle, CommonConstant.ZeroOrOne.ONE.value)
                .list();
        //将符合建设范围的ids和已经存在关联的项目id比较查出差异关联信息
        List<String> finalIds = ids;
        List<String> diffRelationIds = yearRelations.stream().filter(item -> !finalIds.contains(item.getProjId())).map(ProjectYearRelation::getId).toList();
        //删除差异关联信息
        if (!diffRelationIds.isEmpty()) {
            projectYearRelationService.removeBatchByIds(diffRelationIds);
        }
        //将原有的年度项目关系进行过滤
        yearRelations.removeIf(item -> diffRelationIds.contains(item.getId()));
        //将指定year的所有年度项目关系转换成map，key为项目id，value为项目关系
        Map<String, ProjectYearRelation> yearRelationMap = yearRelations.stream().collect(Collectors.toMap(ProjectYearRelation::getProjId, item -> item));
        //获取指定项目id的在year之前的用款金额明细
        List<FundPaybillSkr> skrList = JoinWrappers.lambda(FundPaybillSkr.class)
                .selectAll(FundPaybillSkr.class)
                .select(FundPaybill::getProjId)
                .join("inner join", FundPaybill.class, FundPaybill::getId, FundPaybillSkr::getBillId)
                .in(FundPaybill::getProjId, ids)
                .apply("YEAR(pay_date) < {0}", year)
                .list();
        //根据项目id进行对用款明细进行分组
        Map<String, List<FundPaybillSkr>> skrMap = skrList.stream().collect(Collectors.groupingBy(FundPaybillSkr::getProjId));
        //遍历符合要求的项目，重新构建年度项目关系
        for (ProjectInfo info : infoList) {
            List<FundPaybillSkr> infoSkrList = skrMap.get(info.getId());
            //获取项目的拨款金额
            BigDecimal amount = info.getContractAmount() != null ? info.getContractAmount() :
                    info.getCheckAmount() != null ? info.getCheckAmount() :
                            info.getLibAmount() != null ? info.getLibAmount() : info.getEstAmount();

            //查询当前项目在year中是否已经存在年度项目关系
            ProjectYearRelation yearRelation = yearRelationMap.get(info.getId());
            boolean exists = yearRelation != null;

            //判断是否有使用金额明细
            if (infoSkrList != null) {
                BigDecimal useAmount = infoSkrList.stream().map(e -> e.getPayAmount() == null ? BigDecimal.ZERO : e.getPayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal("10000"));
                if (useAmount.compareTo(BigDecimal.ZERO) > 0 && amount.compareTo(useAmount) != 0) {
                    // 结转项目
                    // 判断是否已经存在项目年度关联关系，如果存在则更新项目类型，否则新增
                    if (exists) {
                        yearRelation.setProjType(CommonConstant.ProjectType.CARRYOVER_PROJECT.value);
                        projectYearRelationService.updateById(yearRelation);
                    } else {
                        saveProjectYearRelation(info.getId(), year, CommonConstant.ProjectType.CARRYOVER_PROJECT.value);
                    }
                }
            } else {
                // 经常/新增项目
                // 判断项目性质是否为经常性项目，设置项目年度关系表中项目类型proj_type字段
                int projType = "JC".equals(info.getNatureCode())
                        ? CommonConstant.ProjectType.RECURRENT_PROJECT.value
                        : CommonConstant.ProjectType.NEW_PROJECT.value;
                // 判断是否已经存在项目年度关联关系，如果存在则更新项目类型，否则新增
                if (exists) {
                    yearRelation.setProjType(projType);
                    projectYearRelationService.updateById(yearRelation);
                } else {
                    saveProjectYearRelation(info.getId(), year, projType);
                }
            }
        }
    }

    @Override
    public void export(ProjectManageConditionVo vo, HttpServletResponse response) throws IOException {
        ProjectInfoMainExcel mainExcel = this.getProjectInfoMainExcel(vo);
        List<ProjectInfoExcel> jzList = mainExcel.getJzList();
        List<ProjectInfoExcel> jcList = mainExcel.getJcList();
        List<ProjectInfoExcel> xzList = mainExcel.getXzList();
        ProjectInfoTotalExcel jzTotal = mainExcel.getJzTotal();
        ProjectInfoTotalExcel jcTotal = mainExcel.getJcTotal();
        ProjectInfoTotalExcel xzTotal = mainExcel.getXzTotal();
        ProjectInfoTotalExcel total = mainExcel.getTotal();

        String encodedFilename = URLEncoder.encode("项目(计划)管理.xlsx", StandardCharsets.UTF_8);
        // 设置响应头
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"");
        // 定义模板位置
        InputStream template = getClass().getClassLoader().getResourceAsStream("excel/管理模板.xlsx");
        if (template == null) {
            throw new ServerException("导出模板文件不存在");
        }
        Workbook workbook = WorkbookFactory.create(template);
        Sheet sheet = workbook.getSheetAt(0);
        // Excel索引从0开始
        if (xzList.isEmpty() && sheet.getRow(10) != null) {
            removeRow(sheet, 10);
        }
        if (jcList.isEmpty() && sheet.getRow(7) != null) {
            removeRow(sheet, 7);
        }
        if (jzList.isEmpty() && sheet.getRow(4) != null) {
            removeRow(sheet, 4);
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        template.close();
        workbook.close();
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(outputStream.toByteArray());
        // 使用EasyExcel写出
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new CellWriteHandler() {
                    @Override
                    public void afterCellDispose(CellWriteHandlerContext context) {
                        Object currentObject = FillObjectHolder.get();
                        Map<Integer, ProjectInfoExcel> map = null;
                        if (currentObject instanceof List<?> objectList) {
                            if (!objectList.isEmpty() && objectList.getFirst() instanceof ProjectInfoExcel) {
                                List<ProjectInfoExcel> projectInfoExcelList = (List<ProjectInfoExcel>) objectList;
                                // 使用 projectInfoExcelList 进行后续操作
                                map = projectInfoExcelList.stream()
                                        .filter(e -> Objects.nonNull(e.getIsFormalFlag()) || Objects.nonNull(e.getIsApplyAdjustFlag()) || Objects.nonNull(e.getIsAdjustAmountFlag()))
                                        .collect(Collectors.toMap(ProjectInfoExcel::getIndex, e -> e));
                            }
                        }
                        // 获取填充的值
                        Object cellValue = context.getOriginalValue();
                        WriteCellData<?> cellData = context.getFirstCellData();
                        WriteCellStyle cellStyle = cellData.getOrCreateStyle();
                        int columnIndex = context.getColumnIndex();

                        // 示例2：“未上报”列为红色字体
                        if (columnIndex == 12 && cellValue instanceof String val) {
                            if (val.contains("未上报")) {
                                WriteFont font = new WriteFont();
                                font.setColor(IndexedColors.RED.getIndex());
                                cellStyle.setWriteFont(font);
                            }
                        }
                        Cell cell = context.getRow().getCell(0);
                        if (cell != null && map != null && cell.getCellType() == CellType.NUMERIC) {
                            double numericCellValue = cell.getNumericCellValue();
                            Set<Integer> integers = map.keySet();
                            boolean b = integers.stream().anyMatch(index -> index.doubleValue() == numericCellValue);
                            if (b) {
                                int index = (int) numericCellValue;
                                ProjectInfoExcel projectInfoExcel = map.get(index);
                                String isFormalFlag = projectInfoExcel.getIsFormalFlag();
                                String isApplyAdjustFlag = projectInfoExcel.getIsApplyAdjustFlag();
                                String isAdjustAmountFlag = projectInfoExcel.getIsAdjustAmountFlag();
                                if ("1".equals(isFormalFlag) && columnIndex == 13) {
                                    WriteFont font = new WriteFont();
                                    font.setColor(IndexedColors.RED.getIndex());
                                    cellStyle.setWriteFont(font);
                                }
                                if ("2".equals(isApplyAdjustFlag) && columnIndex == 14) {
                                    WriteFont font = new WriteFont();
                                    font.setColor(IndexedColors.GREY_25_PERCENT.getIndex());
                                    cellStyle.setWriteFont(font);
                                }

                                if ("1".equals(isAdjustAmountFlag) && columnIndex == 15) {
                                    WriteFont font = new WriteFont();
                                    font.setColor(IndexedColors.RED.getIndex());
                                    cellStyle.setWriteFont(font);
                                } else if ("2".equals(isAdjustAmountFlag) && columnIndex == 15) {
                                    WriteFont font = new WriteFont();
                                    font.setColor(IndexedColors.GREY_25_PERCENT.getIndex());
                                    cellStyle.setWriteFont(font);
                                }
                            }
                        }
                        cellData.setWriteCellStyle(cellStyle);
                    }
                })
                // 注册字符串数值转换器
                .registerConverter(new ExcelStringValueConverter())
                .withTemplate(byteArrayInputStream)
                .build();

        byteArrayInputStream.close();
        WriteSheet writeSheet = EasyExcel.writerSheet()
                .build();

        FillConfig fillConfig = FillConfig.builder().forceNewRow(true).build();
        // 填充数据
        excelWriter.fill(mainExcel, writeSheet);

        if (!xzList.isEmpty()) {
            FillObjectHolder.set(xzList);
            excelWriter.fill(new FillWrapper("xzList", xzList), fillConfig, writeSheet);
        }
        if (!jzList.isEmpty()) {
            FillObjectHolder.set(jzList);
            excelWriter.fill(new FillWrapper("jzList", jzList), fillConfig, writeSheet);
        }
        if (!jcList.isEmpty()) {
            FillObjectHolder.set(jcList);
            excelWriter.fill(new FillWrapper("jcList", jcList), fillConfig, writeSheet);
        }
        excelWriter.fill(new FillWrapper("jzTotal", List.of(jzTotal)), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("jcTotal", List.of(jcTotal)), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("xzTotal", List.of(xzTotal)), fillConfig, writeSheet);
        excelWriter.fill(new FillWrapper("total", List.of(total)), fillConfig, writeSheet);
        excelWriter.finish();
    }

    @Override
    public List<ProjectInfoVo> getProjectBudgetExecutionList(Integer year, Long assessId, List<Long> submitIds) {
        //获取指定年份的项目信息列表
        List<ProjectInfoVo> infos = this.getBaseMapper().getProjectBudgetExecutionList(year, assessId, submitIds);
        List<String> ids = infos.stream().map(ProjectInfoVo::getId).toList();
        //获取资金拨付信息
        Map<String, List<FundPaybillSkr>> skrMap = new HashMap<>();
        if (!ids.isEmpty()) {
            List<FundPaybillSkr> innerJoin = JoinWrappers.lambda(FundPaybillSkr.class)
                    .selectAll(FundPaybillSkr.class)
                    .select(FundPaybill::getProjId)
                    .join("inner join", FundPaybill.class, FundPaybill::getId, FundPaybillSkr::getBillId)
                    .in(FundPaybill::getProjId, ids)
                    .apply("YEAR(pay_date) = {0}", year)
                    .list();
            skrMap = innerJoin.stream().collect(Collectors.groupingBy(FundPaybillSkr::getProjId));
        }
        // 设置当年已执行金额
        for (ProjectInfoVo info : infos) {
            List<FundPaybillSkr> skrList = skrMap.get(info.getId());
            if (Objects.nonNull(skrList)) {
                info.setCurrPayed(skrList.stream().map(item -> item.getPayAmount() == null ? BigDecimal.ZERO : item.getPayAmount()).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal("10000")));
            }
        }
        return infos;
    }

    private void saveProjectYearRelation(String projectId, Integer year, int projType) {
        ProjectYearRelation relation = new ProjectYearRelation();
        relation.setProjId(projectId);
        relation.setYear(year);
        relation.setProjType(projType);
        Integer maxSeqNo = projectYearRelationService.getMaxSeqNo();
        relation.setSeqNo(++maxSeqNo);
        projectYearRelationService.save(relation);
    }

    /**
     * 计算不同类别的合计以及存储
     *
     * @param infoList         存储项目信息excel对象列表
     * @param infoTotal        存储项目信息excel对象合计对象
     * @param projectInfoExcel 单个项目信息excel对象
     */
    private void computeTotal(List<ProjectInfoExcel> infoList, ProjectInfoTotalExcel infoTotal, ProjectInfoExcel projectInfoExcel) {
        projectInfoExcel.setIndex(infoList.size() + 1);
        infoList.add(projectInfoExcel);

        if (Objects.nonNull(projectInfoExcel.getEstAmount())) {
            infoTotal.setEstAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getEstAmountTotal()).add(new BigDecimal(projectInfoExcel.getEstAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getLibAmount())) {
            infoTotal.setLibAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getLibAmountTotal()).add(new BigDecimal(projectInfoExcel.getLibAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getCheckAmount())) {
            infoTotal.setCheckAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getCheckAmountTotal()).add(new BigDecimal(projectInfoExcel.getCheckAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getHistoricalExecutedAmounts())) {
            infoTotal.setHistoricalExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getHistoricalExecutedAmountsTotal()).add(new BigDecimal(projectInfoExcel.getHistoricalExecutedAmounts()))));
        }

        if (Objects.nonNull(projectInfoExcel.getCurrYearExecutedAmounts())) {
            infoTotal.setCurrYearExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getCurrYearExecutedAmountsTotal()).add(new BigDecimal(projectInfoExcel.getCurrYearExecutedAmounts()))));
        }
        infoTotal.setExecutedAmountsTotal(bigDecimalToString(new BigDecimal(infoTotal.getHistoricalExecutedAmountsTotal()).add(new BigDecimal(infoTotal.getCurrYearExecutedAmountsTotal()))));

        if (Objects.nonNull(projectInfoExcel.getDeclareAmount()) && !"未上报".equals(projectInfoExcel.getDeclareAmount())) {
            infoTotal.setDeclareAmountTotal(bigDecimalToString(new BigDecimal(infoTotal.getDeclareAmountTotal()).add(new BigDecimal(projectInfoExcel.getDeclareAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getFormalAmount())) {
            if (!CommonConstant.ZeroOrOne.ONE.value.equals(projectInfoExcel.getIsFormalFlag())) {
                infoTotal.setFormalAmount1(bigDecimalToString(new BigDecimal(infoTotal.getFormalAmount1()).add(new BigDecimal(projectInfoExcel.getFormalAmount()))));
            }
            infoTotal.setFormalAmount2(bigDecimalToString(new BigDecimal(infoTotal.getFormalAmount2()).add(new BigDecimal(projectInfoExcel.getFormalAmount()))));
        }
        if (Objects.nonNull(projectInfoExcel.getApplyAdjust())) {
            infoTotal.setApplyAdjustTotal(bigDecimalToString(new BigDecimal(infoTotal.getApplyAdjustTotal()).add(new BigDecimal(projectInfoExcel.getApplyAdjust()))));
        }
        if (Objects.nonNull(projectInfoExcel.getAdjustAmount())) {
            if (!CommonConstant.ZeroOrOne.ONE.value.equals(projectInfoExcel.getIsAdjustAmountFlag())) {
                infoTotal.setAdjustAmount1(bigDecimalToString(new BigDecimal(infoTotal.getAdjustAmount1()).add(new BigDecimal(projectInfoExcel.getAdjustAmount()))));
            }
            infoTotal.setAdjustAmount2(bigDecimalToString(new BigDecimal(infoTotal.getAdjustAmount2()).add(new BigDecimal(projectInfoExcel.getAdjustAmount()))));
        }
    }

    /**
     * 生成正式项目编号
     *
     * @param pid  项目id
     * @param year 计划年份
     */
    private void generateFormalSn(String pid, Integer year) {
        ProjectSn projectSn = projectSnService.lambdaQuery().eq(ProjectSn::getProjId, pid).one();
        if (StringUtils.isBlank(projectSn.getFormalSn())) {
            ProjectInfo projectInfo = this.getById(pid);
            List<ProjectSn> list = projectSnService.lambdaQuery()
                    .eq(ProjectSn::getFormalYear, year)
                    .list();
            int formalSeqId = list.size() + 1;
            SysDept sysDept = sysDeptService.selectDeptById(SecurityUtils.getDeptId());
            String dSeqNo = Objects.nonNull(sysDept.getSeqNo()) ? sysDept.getSeqNo() : "0";
            String formalSn;
            if (String.valueOf(formalSeqId).length() < 3) {
                formalSn = String.format("%d-%s-%s-%s-%03d", year, dSeqNo, projectInfo.getTypeId(), projectInfo.getNatureCode(), formalSeqId);
            } else {
                formalSn = String.format("%d-%s-%s-%s-%d", year, dSeqNo, projectInfo.getTypeId(), projectInfo.getNatureCode(), formalSeqId);
            }
            projectSnService.lambdaUpdate()
                    .eq(ProjectSn::getProjId, pid)
                    .set(ProjectSn::getFormalYear, year)
                    .set(ProjectSn::getFormalSeqid, formalSeqId)
                    .set(ProjectSn::getFormalSn, formalSn)
                    .set(ProjectSn::getFormalTime, new Date())
                    .update();

        }
    }

    /**
     * 发布项目操作事件
     *
     * @param pid         项目id（必填）
     * @param operateType 操作类型
     */
    private void publishOperateEvent(String pid, String operateType) {
        OperateEvent event = new OperateEvent()
                .setProjId(pid)
                .setType(CommonConstant.ProjectOrPlan.PROJECT)
                .setOperateType(operateType);
        SpringUtil.getApplicationContext().publishEvent(event);
    }

    private String bigDecimalToString(BigDecimal val) {
        return val == null ? null : val.stripTrailingZeros().toPlainString();
    }

    // 删除指定行并上移后续行
    private static void removeRow(Sheet sheet, int rowIndex) {
        int lastRowNum = sheet.getLastRowNum();
        if (rowIndex >= 0 && rowIndex < lastRowNum) {
            sheet.shiftRows(rowIndex + 1, lastRowNum, -1);
        } else if (rowIndex == lastRowNum) {
            Row removingRow = sheet.getRow(rowIndex);
            if (removingRow != null) {
                sheet.removeRow(removingRow);
            }
        }
    }
}
