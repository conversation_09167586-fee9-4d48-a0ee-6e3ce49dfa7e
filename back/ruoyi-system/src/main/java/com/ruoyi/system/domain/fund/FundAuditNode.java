package com.ruoyi.system.domain.fund;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 项目监督（审计）流程记录表
 */
@Data
@TableName("fund_audit_node")
public class FundAuditNode {

    /** 主键；确保有序 */
    @TableId
    private String id;

    /** 审计ID；fund_audit表外键 */
    private String auditId;

    /** 节点序号 */
    private Integer nodeSeq;

    /** 节点名称 */
    private String nodeName;

    /** 上一节点；用于流程溯源 */
    private Integer prevNode;

    /** 处理人ID或用户名 */
    private String operatorId;

    /** 处理人姓名 */
    private String operatorName;

    /** 处理时间 */
    private Date operateTime;

    /** 处理结果（1提交） */
    private Integer operateResult;

    /** 处理理由 */
    private String operateReason;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
