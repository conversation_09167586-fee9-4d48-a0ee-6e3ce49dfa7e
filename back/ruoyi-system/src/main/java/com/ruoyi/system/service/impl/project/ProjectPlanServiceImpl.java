package com.ruoyi.system.service.impl.project;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.dto.SmsSendDto;
import com.ruoyi.system.domain.event.HistoryEvent;
import com.ruoyi.system.domain.event.OperateEvent;
import com.ruoyi.system.domain.project.ProjectInfo;
import com.ruoyi.system.domain.project.ProjectPlan;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.SmsService;
import com.ruoyi.system.service.project.ProjectInfoService;
import com.ruoyi.system.service.project.ProjectPlanService;
import com.ruoyi.system.mapper.project.ProjectPlanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【project_plan(资金计划表)】的数据库操作Service实现
 * @date 2025-06-03 14:52:19
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy, @Autowired})
public class ProjectPlanServiceImpl extends ServiceImpl<ProjectPlanMapper, ProjectPlan> implements ProjectPlanService {

    private final ProjectInfoService projectInfoService;
    private final ISysUserService sysUserService;
    private final SmsService smsService;
    private final ISysConfigService configService;


    @Value("${sms.template.reportPlanMessage}")
    private String reportPlanMessage;

    @Override
    public ProjectPlan getPlanInfo(String pid, Integer year) {
        return this.lambdaQuery()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .one();
    }

    @Override
    public ProjectPlan saveOrUpdatePlan(ProjectPlan entity) {
        if (StringUtils.isBlank(entity.getId())) {
            entity.setPlanState(CommonConstant.StateType.DRAFT.value);
        }
        if (StringUtils.isNotBlank(entity.getId())){
            ProjectPlan plan = this.getById(entity.getId());
            String jsonStr = JSONUtil.toJsonStr(plan);
            //新增项目(计划）历史操作事件
            publishHistoryEvent(plan.getProjId(),plan.getId(),"修改上报计划", jsonStr);

        }
        this.saveOrUpdate(entity);
        return entity;
    }

    @Override
    public Boolean reportPlan(String pid, Integer year) {
        ProjectInfo info = projectInfoService.lambdaQuery().eq(ProjectInfo::getId, pid).one();
        ProjectPlan projectPlan = this.lambdaQuery()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .one();

        if (projectPlan == null) {
            throw new ServiceException("资金计划必须填写");
        }
        if (info.getEstAmount().compareTo(projectPlan.getDeclareAmount()) < 0) {
            throw new ServiceException("[资金计划]上报计划总额超限");
        }
        //判断当前时间是否在计划上报时间段内
        String period = configService.selectConfigByKey("sys.plan.submit.period");
        if (StringUtils.isBlank(period)){
            throw new ServiceException("请配置年初计划上报时间规则");
        }
        String[] split = period.split(",");
        if (split.length != 2) {
            throw new ServiceException("请配置正确的年初计划上报时间规则");
        }
        LocalDate startDate = null;
        LocalDate endDate = null;
        for (int i = 0; i < split.length; i++) {
            String dateStr = "1970-01-01";
            String[] dateSplit = split[i].split(":");
            if (dateSplit.length != 2 && ("c".equals(dateSplit[0]) || "p".equals(dateSplit[0]))) {
                throw new ServiceException("请配置正确的年初计划上报时间规则");
            }
            if ("c".equals(dateSplit[0])) {
                dateStr = year + "-" + dateSplit[1];
            }
            if ("p".equals(dateSplit[0])) {
                dateStr = (year - 1) + "-" + dateSplit[1];
            }
            if (i == 0){
                startDate = LocalDate.parse(dateStr);
            } else {
                endDate = LocalDate.parse(dateStr);
            }
        }
        String planType = "调整计划";
        if (!LocalDate.now().isBefore(startDate) && !LocalDate.now().isAfter(endDate)) {
            planType = "初步计划";
        }
        //如果计划类型为调整计划，上报同时修改计划表中的调整计划信息
        boolean isAdjustPlan = "调整计划".equals(planType);
        // 修改项目计划信息
        boolean update = this.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getDeclareUserid, SecurityUtils.getUserId())
                .set(ProjectPlan::getDeclareTime, new Date())
                .set(ProjectPlan::getPlanType, planType)
                .set(isAdjustPlan, ProjectPlan::getFormalAmount, BigDecimal.ZERO)
                .set(isAdjustPlan, ProjectPlan::getDeclareAmount, BigDecimal.ZERO)
                .set(isAdjustPlan, ProjectPlan::getPlanState, CommonConstant.StateType.RELEASED.value)
                .set(isAdjustPlan, ProjectPlan::getApplyAdjust, projectPlan.getDeclareAmount())
                .set(isAdjustPlan, ProjectPlan::getApplyReason, "直接提交调整计划")
                .set(isAdjustPlan, ProjectPlan::getApplyUserid, SecurityUtils.getUserId())
                .set(isAdjustPlan, ProjectPlan::getApplyTime, new Date())
                .set(isAdjustPlan, ProjectPlan::getApplyState, CommonConstant.ApplyStateType.ESCALATED.value)
                .set(!isAdjustPlan, ProjectPlan::getFormalAmount, null)
                .set(!isAdjustPlan, ProjectPlan::getDeclareAmount, projectPlan.getDeclareAmount())
                .set(!isAdjustPlan, ProjectPlan::getPlanState, CommonConstant.StateType.ESCALATED.value)
                .set(!isAdjustPlan, ProjectPlan::getApplyAdjust, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyReason, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyUserid, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyTime, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyState, null)
                .set(!isAdjustPlan, ProjectPlan::getApplyRefused, null)
                .update();

        //新增项目(计划）操作记录表
        this.publishOperateEvent(pid, year, "上报计划");
        this.publishHistoryEvent(pid, projectPlan.getId(), "上报计划", JSONUtil.toJsonStr(projectPlan));

        //发送短信
        List<SysUser> jfc = sysUserService.selectUserByPostCode("JFC_ZHGL");
        String reporterStr = SecurityUtils.getLoginUser().getUser().getNickName() + "-" + SecurityUtils.getLoginUser().getUser().getDept().getDeptName();
        List<String> senders = new ArrayList<>();
        if (jfc != null && !jfc.isEmpty()) {
            senders.addAll(jfc.stream().map(SysUser::getPhonenumber).filter(StringUtils::isNotBlank).distinct().toList());
        }
        if (!senders.isEmpty()) {
            String toMobile = String.join(",", senders);
            SmsSendDto sendDto = SmsSendDto.builder()
                    .sourceId(info.getId())
                    .sourceType(CommonConstant.SourceType.PROJECT.getName())
                    .businessName("上报计划")
                    .toMobile(toMobile)
                    .toName("经发处综合管理员")
                    .template(reportPlanMessage)
                    .templateArgs(List.of(info.getName(), String.valueOf(year), reporterStr))
                    .build();
            smsService.recordAndSendSms(sendDto);
        }
        return update;
    }

    @Override
    public Boolean withdrawPlan(String pid, Integer year) {
        boolean update = this.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getDeclareUserid, null)
                .set(ProjectPlan::getDeclareTime, null)
                .set(ProjectPlan::getPlanState, CommonConstant.StateType.DRAFT.value)
                .update();
        //新增项目(计划）操作记录表
        this.publishOperateEvent(pid, year, "撤回计划");
        return update;
    }

    @Override
    public Boolean updateEarlyAmount(String pid, Integer year, BigDecimal earlyAmount) {
        boolean update = this.lambdaUpdate()
                .eq(ProjectPlan::getProjId, pid)
                .eq(ProjectPlan::getYear, year)
                .set(ProjectPlan::getEarlyAmount, earlyAmount)
                .update();

        //新增项目(计划）操作记录表
        this.publishOperateEvent(pid, year, "修改年初计划金额");
        return update;
    }

    /**
     * 发布计划操作事件
     *
     * @param pid         项目id（必填）
     * @param year        年度
     * @param operateType 操作类型
     */
    private void publishOperateEvent(String pid, Integer year, String operateType) {
        OperateEvent event = new OperateEvent()
                .setProjId(pid)
                .setType(CommonConstant.ProjectOrPlan.PLAN)
                .setOperateType(operateType)
                .setYear(year);
        SpringUtil.getApplicationContext().publishEvent(event);
    }

    /**
     * 发布历史操作事件
     *
     * @param pid          项目id（必填）
     * @param updateReason 操作类型
     */
    private void publishHistoryEvent(String pid, String planId, String updateReason, String dataJson) {
        HistoryEvent event = new HistoryEvent()
                .setProjId(pid)
                .setPlanId(planId)
                .setType(CommonConstant.ProjectOrPlan.PLAN)
                .setUpdateReason(updateReason)
                .setDataJson(dataJson);
        SpringUtil.getApplicationContext().publishEvent(event);
    }
}




