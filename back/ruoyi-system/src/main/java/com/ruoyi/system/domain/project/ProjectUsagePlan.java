package com.ruoyi.system.domain.project;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.ibatis.type.Alias;

/**
 * 用款计划表
 *
 * <AUTHOR>
 * @TableName usage_plan
 */
@Data
@Alias("UPlan")
@TableName(value = "project_usage_plan")
public class ProjectUsagePlan {
    /**
     * 主键;主键，确保有序
     */
    @TableId(value = "id")
    private String id;

    /**
     * 年度;年度
     */
    private Integer year;

    /**
     * 标题;标题
     */
    private String title;

    /**
     * 表格单位;表格单位
     */
    private String unit;

    /**
     * 是否为快照;是否为快照（0否，1是）
     */
    private String isSnap;

    /**
     * 快照类型（一上、二上、调整、正式）
     */
    private String snapType;

    /**
     * 删除标记;删除标记（0否，1是）
     */
    @TableLogic
    private String delFlag;

    /**
     * 创建人;创建人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间;创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人;更新人，存储用户名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间;更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     *
     */
    private String snapName;

    /**
     * 列定义;列定义，使用json存储，定义列的显示和表头名称，[{"sort":1,"code":"xmmc","title":"项目名称","display":true},{"sort":2,"code":"xmlz","title":"项目篮子","display":true},{"sort":3,"code":"khzt","title":"考核主体","display":true}]
     */
    private String columns;
}