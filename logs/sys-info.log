13:56:43.970 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
13:56:44.071 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 88851 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
13:56:44.073 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "prod"
13:56:48.291 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
13:56:49.382 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8082"]
13:56:49.384 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:56:49.384 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
13:56:49.436 [restartedMain] INFO  o.a.c.c.C.[.[.[/sdfundapi] - [log,173] - Initializing Spring embedded WebApplicationContext
13:56:50.083 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
13:56:55.310 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
13:56:55.337 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
