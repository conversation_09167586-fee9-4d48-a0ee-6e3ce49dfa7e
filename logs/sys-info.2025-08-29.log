10:44:11.826 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
10:44:11.868 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 29878 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
10:44:11.869 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
10:44:14.130 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:44:15.077 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
10:44:15.079 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:44:15.079 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
10:44:15.129 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:44:15.835 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
10:44:16.660 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
10:44:16.815 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
10:44:24.336 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:44:24.347 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:44:24.347 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:44:24.348 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:44:24.348 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:44:24.349 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:44:24.349 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:44:24.349 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4d80211a
10:44:24.605 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@307a7904, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@11181bdf
10:44:26.014 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
10:44:26.025 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:44:26.034 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 14.56 seconds (process running for 15.613)
10:52:47.395 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:52:47.605 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:52:47.605 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:52:47.606 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:52:47.607 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:52:47.625 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
10:52:47.635 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
10:52:50.992 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
10:52:51.027 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 35758 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
10:52:51.028 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
10:52:53.337 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:52:54.356 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
10:52:54.358 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:52:54.358 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
10:52:54.408 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:52:55.110 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
10:52:55.812 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
10:52:56.010 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
10:52:56.018 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
10:52:56.021 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
10:52:56.035 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
10:55:41.564 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
10:55:41.599 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 37719 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
10:55:41.600 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
10:55:43.878 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:55:44.775 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
10:55:44.777 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:55:44.777 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
10:55:44.827 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:55:45.499 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
10:55:46.243 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
10:55:46.456 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
10:55:53.944 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:55:53.957 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:55:53.957 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:55:53.958 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:55:53.959 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:55:53.959 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:55:53.959 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:55:53.959 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7e4f587e
10:55:54.213 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@21c3bb93, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@1404bda7
10:55:55.726 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
10:55:55.733 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:55:55.741 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 14.51 seconds (process running for 16.433)
10:56:23.812 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:56:23.967 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:56:23.968 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:56:23.968 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:56:23.969 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:56:23.984 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
10:56:23.987 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
10:56:27.787 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
10:56:27.820 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 38214 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
10:56:27.821 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
10:56:29.912 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:56:30.813 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
10:56:30.814 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:56:30.814 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
10:56:30.862 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:56:31.498 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
10:56:32.213 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
10:56:32.430 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
10:56:39.757 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:56:39.769 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:56:39.769 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:56:39.769 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:56:39.770 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:56:39.770 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:56:39.770 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:56:39.770 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@13e6663
10:56:39.990 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@232d42df, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@4dd9502f
10:56:41.288 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
10:56:41.295 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:56:41.302 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 13.833 seconds (process running for 15.342)
10:56:55.090 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:56:55.234 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
10:56:55.234 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
10:56:55.234 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
10:56:55.235 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
10:56:55.244 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
10:56:55.246 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
10:56:58.405 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
10:56:58.439 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 38550 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
10:56:58.440 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
10:57:00.503 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
10:57:01.371 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
10:57:01.373 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:57:01.373 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
10:57:01.422 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:57:01.976 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
10:57:02.629 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
10:57:02.817 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
10:57:09.844 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:57:09.854 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:57:09.854 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:57:09.855 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:57:09.856 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:57:09.856 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:57:09.856 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:57:09.856 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@31e08ec0
10:57:10.111 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@2489dceb, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@6583a981
10:57:11.408 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
10:57:11.413 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:57:11.420 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 13.424 seconds (process running for 14.714)
10:58:48.854 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:58:49.248 [tomcat-handler-2] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到MASTER数据源
10:58:56.971 [tomcat-handler-3] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到MASTER数据源
10:59:09.376 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[zby][Success][登录成功]
11:06:07.733 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:06:07.906 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:06:07.907 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:06:07.907 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:06:07.908 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:06:07.916 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
11:06:07.921 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
11:06:11.565 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
11:06:11.594 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 44481 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
11:06:11.595 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
11:06:13.562 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
11:06:14.425 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
11:06:14.426 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:06:14.427 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
11:06:14.475 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:06:15.058 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
11:06:16.075 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
11:06:16.275 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
11:06:23.795 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:06:23.807 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:06:23.808 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:06:23.808 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:06:23.809 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:06:23.809 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:06:23.809 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:06:23.809 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@77139e8b
11:06:24.055 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@48abc4d1, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@d0a703d
11:06:25.423 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
11:06:25.428 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:06:25.436 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 14.208 seconds (process running for 15.596)
11:06:35.028 [tomcat-handler-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:07:12.649 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:07:12.784 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
11:07:12.784 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
11:07:12.784 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
11:07:12.785 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
11:07:12.795 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
11:07:12.797 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
11:15:10.854 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
11:15:10.890 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 50402 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
11:15:10.891 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
11:15:13.334 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
11:15:14.235 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
11:15:14.237 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:15:14.237 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
11:15:14.285 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:15:14.949 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
11:15:15.685 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
11:15:15.846 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
11:15:23.041 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:15:23.055 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:15:23.055 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:15:23.055 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
11:15:23.056 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

11:15:23.056 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
11:15:23.056 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:15:23.056 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@412499e8
11:15:23.312 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@38633d9c, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@69bb645f
11:15:24.752 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
11:15:24.761 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
11:15:24.768 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 14.31 seconds (process running for 15.451)
11:15:49.753 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:15:54.180 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[zby][Success][登录成功]
11:36:41.007 [tomcat-handler-21] INFO  c.r.f.d.DynamicDataSourceContextHolder - [setDataSourceType,26] - 切换到MASTER数据源
13:28:04.940 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:35:36.730 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
17:35:36.764 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 32088 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
17:35:36.765 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:35:39.045 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:35:39.991 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
17:35:39.993 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:35:39.993 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
17:35:40.041 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:35:40.710 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
17:35:41.816 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
17:35:41.980 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
17:35:49.668 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:35:49.680 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:35:49.680 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:35:49.681 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:35:49.681 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:35:49.681 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:35:49.682 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:35:49.682 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@197d4a6e
17:35:49.961 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@3c08837f, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@73d0ed4c
17:35:51.396 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
17:35:51.406 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:35:51.413 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 15.1 seconds (process running for 16.349)
17:37:02.670 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:37:08.848 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[zby][Success][登录成功]
17:40:21.032 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:40:21.209 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
17:40:21.209 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:40:21.209 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
17:40:21.210 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
17:40:21.219 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
17:40:21.222 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
17:40:26.007 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
17:40:26.043 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 35203 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
17:40:26.045 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:40:28.433 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:40:29.338 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
17:40:29.340 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:40:29.340 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
17:40:29.386 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:40:30.007 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
17:40:30.650 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
17:40:30.809 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
17:40:37.776 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:40:37.789 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:40:37.789 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:40:37.790 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:40:37.790 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:40:37.790 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:40:37.790 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:40:37.791 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6ee554c6
17:40:37.994 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@39eb4c15, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@70612c60
17:40:39.329 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
17:40:39.335 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:40:39.341 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 13.651 seconds (process running for 14.54)
17:40:50.549 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:49:08.538 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:49:08.760 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
17:49:08.760 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:49:08.761 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
17:49:08.763 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
17:49:08.774 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
17:49:08.787 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
17:49:13.163 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
17:49:13.207 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 40843 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
17:49:13.209 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:49:15.500 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:49:16.424 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
17:49:16.425 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:49:16.426 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
17:49:16.472 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:49:17.212 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
17:49:17.895 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
17:49:18.056 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
17:49:25.283 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:49:25.295 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:49:25.296 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:49:25.297 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:49:25.297 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:49:25.298 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:49:25.298 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:49:25.298 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5d20c190
17:49:25.540 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@27c082b3, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@79930333
17:49:26.905 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
17:49:26.911 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:49:26.917 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 14.087 seconds (process running for 14.945)
17:49:40.736 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:49:40.876 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
17:49:40.876 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:49:40.876 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
17:49:40.876 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
17:49:40.885 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
17:49:40.887 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
17:49:47.988 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
17:49:48.020 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 41174 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
17:49:48.021 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:49:50.502 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:49:51.529 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
17:49:51.531 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:49:51.532 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
17:49:51.625 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:49:52.563 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
17:49:53.200 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
17:49:53.380 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
17:50:00.706 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:50:00.718 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:50:00.719 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:50:00.719 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:50:00.720 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:50:00.720 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:50:00.720 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:50:00.720 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7796623a
17:50:00.954 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@504c0c74, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@17476e09
17:50:02.300 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
17:50:02.306 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:50:02.314 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 14.719 seconds (process running for 19.524)
17:50:35.088 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:51:52.291 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:52:07.690 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
17:52:07.722 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 42676 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
17:52:07.723 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:52:09.830 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:52:10.826 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
17:52:10.827 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:52:10.828 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
17:52:10.882 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:52:11.522 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
17:52:12.177 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
17:52:12.339 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
17:52:19.570 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:52:19.582 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:52:19.582 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:52:19.583 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:52:19.583 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:52:19.584 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:52:19.584 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:52:19.584 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4a30aa03
17:52:19.812 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@109e9a3, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@3cc90b26
17:52:21.162 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
17:52:21.170 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:52:21.179 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 13.841 seconds (process running for 15.211)
17:53:16.159 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:54:29.224 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:54:29.399 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
17:54:29.399 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
17:54:29.399 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
17:54:29.399 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
17:54:29.408 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2031] - {dataSource-1} closing ...
17:54:29.416 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
17:54:34.692 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 8.0.1.Final
17:54:34.728 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,50] - Starting RuoYiApplication using Java 21.0.2 with PID 44280 (/Users/<USER>/workspace/project2025/化工区项目/back/ruoyi-admin/target/classes started by fz in /Users/<USER>/workspace/project2025/化工区项目)
17:54:34.729 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,660] - The following 1 profile is active: "dev"
17:54:37.120 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
17:54:38.033 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-8199"]
17:54:38.035 [restartedMain] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:54:38.035 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/10.1.20]
17:54:38.086 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:54:38.766 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join properties config complete
17:54:39.457 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,824] - {dataSource-1} inited
17:54:39.639 [restartedMain] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - [info,141] - mybatis plus join SqlInjector init
17:54:47.167 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:54:47.180 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:54:47.180 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:54:47.181 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
17:54:47.181 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

17:54:47.181 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
17:54:47.181 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:54:47.182 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5d6ceed4
17:54:47.404 [restartedMain] INFO  org.ktorm.database - [invoke,103] - Connected to jdbc:dm://192.168.2.53:5236?schema=FUND_PROJECT_UPGRADE, productName: DM DBMS, productVersion: 8.1.3.62, logger: org.ktorm.logging.Slf4jLoggerAdapter@58a57e0c, dialect: org.ktorm.database.SqlDialectKt$detectDialectImplementation$1@42dccc58
17:54:48.774 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-8199"]
17:54:48.780 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
17:54:48.787 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,56] - Started RuoYiApplication in 14.468 seconds (process running for 16.068)
17:55:03.169 [tomcat-handler-0] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:03:32.214 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
