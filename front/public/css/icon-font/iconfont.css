@font-face {
  font-family: "iconfont"; /* Project id 4986900 */
  src: url('iconfont.woff2?t=1754296155726') format('woff2'),
       url('iconfont.woff?t=1754296155726') format('woff'),
       url('iconfont.ttf?t=1754296155726') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-star:before {
  content: "\e7df";
}

.icon-star-fill:before {
  content: "\e86a";
}

.icon-pdf:before {
  content: "\e740";
}

.icon-word-full:before {
  content: "\ea47";
}

.icon-jpg:before {
  content: "\e6c2";
}

.icon-wenjian:before {
  content: "\e61d";
}

.icon-wenjianjia:before {
  content: "\e62e";
}

.icon-RARtubiao:before {
  content: "\e641";
}

.icon-ZIPtubiao:before {
  content: "\e644";
}

.icon-text:before {
  content: "\e791";
}

.icon-excel-full:before {
  content: "\ea45";
}

