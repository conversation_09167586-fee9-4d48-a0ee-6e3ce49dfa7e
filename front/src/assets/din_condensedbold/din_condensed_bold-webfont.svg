<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="din_condensedbold" horiz-adv-x="757" >
<font-face units-per-em="2048" ascent="1458" descent="-590" />
<missing-glyph horiz-adv-x="380" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="380" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="0" />
<glyph unicode=" "  horiz-adv-x="380" />
<glyph unicode="&#x09;" horiz-adv-x="380" />
<glyph unicode="&#xa0;" horiz-adv-x="380" />
<glyph unicode="!" horiz-adv-x="491" d="M115 1458h262l-80 -1098h-102zM141 0v209h209v-209h-209z" />
<glyph unicode="&#x22;" horiz-adv-x="569" d="M68 1098v360h163v-360h-163zM338 1098v360h164v-360h-164z" />
<glyph unicode="#" horiz-adv-x="761" d="M12 440v123h121l45 332h-102v123h119l59 440h139l-59 -440h172l59 440h140l-60 -440h105v-123h-121l-45 -332h102v-123h-119l-59 -440h-139l59 440h-172l-59 -440h-140l60 440h-105zM272 563h172l45 332h-172z" />
<glyph unicode="$" horiz-adv-x="761" d="M23 330v82h208v-74q0 -47 22.5 -88t76.5 -53v471l-70 28q-135 55 -181 146.5t-46 228.5q0 156 73.5 267.5t223.5 131.5v154h104v-154q70 -8 126 -37.5t97 -76.5t61.5 -102.5t20.5 -112.5v-103h-209v47q0 66 -21.5 113t-74.5 64v-412l86 -35q70 -27 113 -62.5t67 -82.5 q23 -49 31 -111.5t8 -142.5q0 -88 -15 -163t-50 -130q-37 -57 -95.5 -92t-144.5 -43v-172h-104v172q-68 8 -124 36.5t-97 73.5q-86 92 -86 232zM242 1073q0 -74 14 -111.5t74 -66.5v367q-53 -20 -70.5 -71.5t-17.5 -117.5zM434 197q66 16 81 70.5t15 146.5q0 51 -3 86 t-13 57q-23 43 -80 66v-426z" />
<glyph unicode="%" horiz-adv-x="720" d="M18 1169v134q0 78 47.5 122.5t127.5 44.5t127 -45t47 -122v-134q0 -70 -47 -112.5t-127 -42.5t-127.5 43t-47.5 112zM43 0l500 1458h135l-500 -1458h-135zM141 1190q0 -23 16 -38t36 -15t35.5 15t15.5 38v104q0 23 -15.5 38.5t-35.5 15.5t-36 -15.5t-16 -38.5v-104z M354 143v133q0 78 47.5 123t126.5 45q80 0 127 -45t47 -123v-133q0 -70 -47 -112.5t-127 -42.5t-127 43t-47 112zM477 164q0 -23 15.5 -38t35.5 -15t36 15t16 38v104q0 23 -15.5 38.5t-36.5 15.5q-20 0 -35.5 -15.5t-15.5 -38.5v-104z" />
<glyph unicode="&#x26;" horiz-adv-x="985" d="M74 326q0 121 57 211t143 168q-55 78 -91 160.5t-36 174.5q0 57 18.5 109.5t53.5 91.5q35 37 88 59.5t125 22.5q68 0 119 -21.5t86 -56.5q72 -76 72 -186q0 -104 -63.5 -196.5t-149.5 -170.5q20 -29 41.5 -65.5t48.5 -77.5q51 -88 102 -162l113 154l129 -90l-156 -201 l158 -250h-209l-70 111q-61 -61 -126.5 -92t-151.5 -31q-61 0 -116.5 24.5t-96.5 69.5q-41 43 -64.5 105.5t-23.5 138.5zM258 336q0 -76 44 -130t112 -54q47 0 90 29.5t65 58.5l-200 319q-47 -47 -79 -101t-32 -122zM332 1036q0 -47 22.5 -101t57.5 -110q18 18 40.5 42 t41.5 53q18 29 30.5 62.5t12.5 72.5t-27 71.5t-74 32.5t-75.5 -38t-28.5 -85z" />
<glyph unicode="'" horiz-adv-x="380" d="M109 1098v360h163v-360h-163z" />
<glyph unicode="(" horiz-adv-x="454" d="M102 627q0 104 16.5 214.5t49.5 221.5q61 219 178 395h70q-59 -211 -82 -436t-23 -453q0 -211 28 -402.5t77 -371.5h-70q-61 88 -107 191.5t-75 214.5q-31 111 -46.5 219t-15.5 207z" />
<glyph unicode=")" horiz-adv-x="454" d="M39 -205q59 211 81.5 436.5t22.5 452.5q0 211 -27.5 402.5t-76.5 371.5h70q61 -92 107 -193.5t77 -211.5q29 -111 44 -219.5t15 -206.5q0 -104 -16.5 -215t-46.5 -222q-66 -221 -180 -395h-70z" />
<glyph unicode="*" d="M63 1010l205 112l-205 115l58 106l201 -122l-9 237h131l-8 -237l201 122l57 -106l-205 -115l205 -112l-57 -109l-201 123l8 -238h-131l9 238l-201 -123z" />
<glyph unicode="+" horiz-adv-x="1228" d="M96 436v164h436v436h164v-436h437v-164h-437v-436h-164v436h-436z" />
<glyph unicode="," horiz-adv-x="380" d="M86 -152v361h209v-209z" />
<glyph unicode="-" horiz-adv-x="796" d="M90 463v209h617v-209h-617z" />
<glyph unicode="." horiz-adv-x="380" d="M86 0v209h209v-209h-209z" />
<glyph unicode="/" horiz-adv-x="454" d="M10 -12l271 1482h163l-270 -1482h-164z" />
<glyph unicode="0" horiz-adv-x="761" d="M70 301v856q0 74 25.5 131.5t68.5 97.5q43 41 99 62.5t118 21.5q61 0 117.5 -21.5t99.5 -62.5t68.5 -98t25.5 -131v-856q0 -74 -25.5 -131t-68.5 -98t-99.5 -62.5t-117.5 -21.5t-117.5 21.5t-99.5 62.5t-68.5 98t-25.5 131zM279 301q0 -45 28.5 -74.5t73.5 -29.5 t73.5 29.5t28.5 74.5v856q0 45 -28.5 75t-73.5 30t-73.5 -30t-28.5 -75v-856z" />
<glyph unicode="1" horiz-adv-x="761" d="M109 1083v222l208 153h209v-1458h-209v1237z" />
<glyph unicode="2" horiz-adv-x="761" d="M70 0v197l376 710q29 53 33 93t4 100q0 27 -1 56.5t-11 51.5q-10 23 -30.5 38.5t-59.5 15.5q-47 0 -74.5 -27t-27.5 -78v-119h-209v115q0 66 24.5 123t67.5 100q41 43 98 68.5t123 25.5q82 0 140.5 -29.5t97.5 -80.5q37 -51 54 -116.5t17 -139.5q0 -53 -2 -89t-8 -69 t-20.5 -65.5t-38.5 -79.5l-308 -592h377v-209h-622z" />
<glyph unicode="3" horiz-adv-x="761" d="M70 299v119h209v-109q0 -49 27.5 -80.5t74.5 -31.5t74.5 31.5t27.5 84.5v209q0 86 -32 113q-18 16 -50 20t-84 4v185q94 0 130 22.5t36 104.5v188q0 45 -27.5 74t-74.5 29q-55 0 -78.5 -35t-23.5 -68v-119h-209v121q0 63 24.5 120.5t67.5 98.5t100 65.5t123 24.5 q86 0 142.5 -33.5t84.5 -68.5q20 -25 36 -49.5t26 -58.5q18 -68 18 -201q0 -76 -3 -123t-15 -78q-14 -33 -40 -55.5t-71 -48.5q49 -31 75 -57.5t38 -61.5q12 -37 14 -87t2 -128q0 -72 -3 -119t-9 -78q-6 -33 -16.5 -55.5t-26.5 -46.5q-39 -57 -99.5 -95t-158.5 -38 q-49 0 -104.5 16t-100.5 53t-74.5 96.5t-29.5 145.5z" />
<glyph unicode="4" horiz-adv-x="761" d="M18 217v197l301 1044h222l-314 -1044h207v415h209v-415h100v-197h-100v-217h-209v217h-416z" />
<glyph unicode="5" horiz-adv-x="761" d="M70 301v61h209v-53q0 -53 29.5 -82.5t76.5 -29.5t72.5 28.5t25.5 79.5v342q0 43 -27.5 74t-70.5 31q-27 0 -45 -9.5t-31 -21.5q-12 -12 -18 -27q-10 -18 -12 -22h-185v786h598v-196h-413v-394q29 29 73.5 48.5t100.5 19.5q106 0 172.5 -64.5t66.5 -193.5v-377 q0 -74 -25.5 -131t-68.5 -98t-99.5 -62.5t-117.5 -21.5t-117.5 21.5t-99.5 62.5t-68.5 98t-25.5 131z" />
<glyph unicode="6" horiz-adv-x="761" d="M70 426q0 68 2 112t6 76q4 31 12 56.5t19 54.5l274 733h233l-237 -612l4 -4q10 8 31.5 13t56.5 5q55 0 102.5 -26.5t73.5 -67.5q14 -23 22.5 -45.5t14.5 -58.5q4 -37 6 -93.5t2 -142.5q0 -72 -2 -118t-6 -77q-6 -33 -15.5 -55t-23.5 -47q-41 -68 -109.5 -104.5 t-154.5 -36.5t-153.5 37.5t-108.5 103.5q-16 25 -25.5 47.5t-13.5 54.5q-6 31 -8 77t-2 118zM279 301q0 -47 30.5 -75.5t71.5 -28.5t71.5 28.5t30.5 75.5v270q0 47 -30.5 76t-71.5 29t-71.5 -29t-30.5 -76v-270z" />
<glyph unicode="7" horiz-adv-x="761" d="M70 1094v364h622v-209l-354 -1249h-221l366 1262h-229v-168h-184z" />
<glyph unicode="8" horiz-adv-x="761" d="M70 408q0 82 2 138t12 95t31.5 67.5t58.5 57.5q-37 31 -58.5 58.5t-31.5 62.5t-12 79.5t-2 110.5q0 59 4 101.5t14 72.5q10 31 23.5 55.5t33.5 51.5q41 53 102.5 82.5t133.5 29.5t133 -29.5t102 -82.5q20 -27 35 -51.5t25 -55.5t13 -72.5t3 -101.5q0 -66 -2 -110.5 t-12 -79.5t-31.5 -62.5t-58.5 -58.5q37 -29 58.5 -57.5t31.5 -67.5t12 -95.5t2 -137.5q0 -68 -3 -112t-9 -77t-17.5 -55.5t-27.5 -46.5q-35 -53 -98.5 -91t-155.5 -38t-155.5 37.5t-98.5 91.5q-16 25 -27.5 47t-17.5 55t-9 77t-3 112zM279 299q0 -43 30.5 -72.5t71.5 -29.5 t71.5 29.5t30.5 72.5v287q0 43 -30.5 72.5t-71.5 29.5t-71.5 -29.5t-30.5 -72.5v-287zM279 946q0 -43 30.5 -72.5t71.5 -29.5t71.5 29.5t30.5 72.5v213q0 43 -30.5 73t-71.5 30t-71.5 -30t-30.5 -73v-213z" />
<glyph unicode="9" horiz-adv-x="761" d="M70 1032q0 72 2 118t8 79q4 31 13 53.5t24 46.5q41 68 109.5 104.5t154.5 36.5t153.5 -37.5t108.5 -103.5q14 -25 24.5 -47t16.5 -53q4 -33 6 -79t2 -118q0 -68 -2 -111.5t-6 -74.5q-6 -33 -13 -58.5t-18 -54.5l-274 -733h-234l238 612l-4 4q-10 -8 -31.5 -13t-56.5 -5 q-55 0 -102.5 26.5t-73.5 67.5q-14 20 -22.5 44t-12.5 61q-6 37 -8 93t-2 142zM279 887q0 -47 30.5 -76t71.5 -29t71.5 29t30.5 76v270q0 47 -30.5 76t-71.5 29t-71.5 -29t-30.5 -76v-270z" />
<glyph unicode=":" horiz-adv-x="380" d="M86 0v209h209v-209h-209zM86 465v209h209v-209h-209z" />
<glyph unicode=";" horiz-adv-x="380" d="M86 -152v361h209v-209zM86 465v209h209v-209h-209z" />
<glyph unicode="&#x3c;" horiz-adv-x="1228" d="M96 457v123l1037 473v-164l-814 -371l814 -371v-163z" />
<glyph unicode="=" horiz-adv-x="1228" d="M96 231v164h1037v-164h-1037zM96 641v164h1037v-164h-1037z" />
<glyph unicode="&#x3e;" horiz-adv-x="1228" d="M96 -16v163l813 371l-813 371v164l1037 -473v-123z" />
<glyph unicode="?" d="M66 1038v121q0 63 24.5 120.5t67.5 100.5q43 41 100 65.5t123 24.5q76 0 138.5 -30.5t103.5 -85.5q20 -29 33.5 -52.5t21.5 -52.5q14 -51 14 -145q0 -53 -1 -89t-5 -63t-10 -46t-17 -36l-141 -241q-14 -25 -24.5 -50.5t-10.5 -54.5v-164h-209v191q0 45 14.5 86t37.5 80 l131 213q16 27 21 57.5t5 61.5v108q0 43 -29.5 74t-72.5 31q-37 0 -72 -27t-35 -78v-119h-208zM274 0v209h209v-209h-209z" />
<glyph unicode="@" horiz-adv-x="1638" d="M111 733q0 154 61 288t168 234q104 100 244.5 157.5t300.5 57.5q143 0 272 -45t227 -127t153.5 -193.5t55.5 -246.5q0 -129 -48 -234.5t-120 -179.5q-72 -76 -152.5 -117.5t-146.5 -41.5q-70 0 -110.5 31.5t-48.5 97.5h-4q-23 -25 -46.5 -47.5t-52.5 -40.5 q-31 -18 -67.5 -29.5t-83.5 -11.5q-66 0 -120 24.5t-93 67.5q-80 90 -80 219q0 100 34.5 200.5t98.5 180.5q61 80 148 130t198 50q72 0 138.5 -37t97.5 -123h4l41 125h129l-179 -563q-10 -29 -15 -53.5t-5 -38.5q0 -27 10 -43t37 -16q49 0 98.5 40.5t90.5 106.5t65.5 142.5 t24.5 150.5q0 106 -45 196.5t-121 155.5q-76 66 -174.5 100.5t-208.5 34.5q-129 0 -240.5 -50t-197.5 -136t-133.5 -196.5t-47.5 -229.5q0 -125 48.5 -234.5t134.5 -189.5q86 -82 198.5 -129t247.5 -47q74 0 147.5 17.5t139.5 45.5q66 29 120 69t91 83h157 q-49 -82 -121.5 -147.5t-158.5 -110.5q-88 -45 -187.5 -68.5t-203.5 -23.5q-160 0 -300.5 56t-244.5 157q-104 98 -164.5 235t-60.5 297zM584 610q0 -86 46 -132t124 -46q57 0 111.5 41t95.5 102q41 59 65.5 131.5t24.5 133.5q0 72 -39 121t-113 49q-68 0 -125 -38t-98 -98 q-43 -59 -67.5 -129.5t-24.5 -134.5z" />
<glyph unicode="A" horiz-adv-x="833" d="M4 0l326 1458h174l325 -1458h-208l-62 313h-285l-61 -313h-209zM311 510h209l-102 528h-4z" />
<glyph unicode="B" horiz-adv-x="872" d="M98 0v1458h305q100 0 171 -30.5t116 -81.5t64.5 -118t19.5 -138v-54q0 -59 -9 -100t-28 -72q-35 -57 -106 -98q74 -35 108.5 -102.5t34.5 -184.5v-82q0 -193 -93 -295t-298 -102h-285zM307 209h90q63 0 99 18.5t55 51.5q18 33 22.5 77.5t4.5 98.5q0 55 -6.5 96t-24.5 70 q-20 29 -55 42t-93 13h-92v-467zM307 860h94q104 0 140.5 52.5t36.5 152.5q0 98 -40 147.5t-145 49.5h-86v-402z" />
<glyph unicode="C" horiz-adv-x="833" d="M86 332v803q0 72 24.5 133t69.5 106t107.5 70.5t138.5 25.5q147 0 240 -96q45 -47 70.5 -111.5t25.5 -140.5v-82h-209v70q0 61 -35 106.5t-94 45.5q-78 0 -103.5 -48.5t-25.5 -121.5v-746q0 -63 27.5 -106t99.5 -43q20 0 43.5 7t44.5 23q18 16 30.5 45t12.5 72v72h209 v-90q0 -68 -25.5 -128.5t-70.5 -107.5t-105.5 -74.5t-130.5 -27.5q-59 0 -120.5 16t-110.5 57t-81 105.5t-32 165.5z" />
<glyph unicode="D" horiz-adv-x="872" d="M98 0v1458h310q180 0 273 -98t93 -279v-682q0 -205 -99.5 -302t-285.5 -97h-291zM307 197h96q88 0 125 44t37 138v702q0 86 -34.5 133.5t-127.5 47.5h-96v-1065z" />
<glyph unicode="E" d="M98 0v1458h623v-196h-414v-428h361v-197h-361v-428h414v-209h-623z" />
<glyph unicode="F" d="M98 0v1458h623v-196h-414v-443h361v-196h-361v-623h-209z" />
<glyph unicode="G" horiz-adv-x="872" d="M86 344v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5t126 -22.5t114 -65.5q49 -45 79.5 -112.5t30.5 -155.5v-74h-208v74q0 76 -42 112t-100 36q-57 0 -99 -36t-42 -112v-770q0 -76 42 -111.5t99 -35.5t99.5 35.5t42.5 111.5v274h-166v185h374v-459 q0 -92 -30.5 -157.5t-79.5 -108.5q-51 -45 -113.5 -67.5t-126.5 -22.5q-63 0 -125.5 22.5t-111.5 67.5q-51 43 -82 108.5t-31 157.5z" />
<glyph unicode="H" horiz-adv-x="872" d="M98 0v1458h209v-624h258v624h209v-1458h-209v649h-258v-649h-209z" />
<glyph unicode="I" horiz-adv-x="417" d="M104 0v1458h209v-1458h-209z" />
<glyph unicode="J" horiz-adv-x="681" d="M-45 119l164 131q23 -23 52.5 -38t61.5 -15q18 0 43 7t48 27q23 18 37 52t14 90v1085h209v-1114q0 -150 -95 -250q-47 -49 -111.5 -77.5t-150.5 -28.5q-100 0 -166.5 44t-105.5 87z" />
<glyph unicode="K" horiz-adv-x="872" d="M98 0v1458h209v-665h4l305 665h209l-284 -584l338 -874h-222l-237 647l-113 -211v-436h-209z" />
<glyph unicode="L" d="M98 0v1458h209v-1249h414v-209h-623z" />
<glyph unicode="M" horiz-adv-x="1138" d="M98 0v1458h201l268 -772h4l267 772h202v-1458h-209v887h-4l-206 -627h-105l-205 627h-4v-887h-209z" />
<glyph unicode="N" horiz-adv-x="909" d="M90 0v1458h201l315 -878h4v878h209v-1458h-196l-320 877h-4v-877h-209z" />
<glyph unicode="O" horiz-adv-x="872" d="M86 344v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5t126 -22.5t114 -65.5q49 -45 79.5 -112.5t30.5 -155.5v-770q0 -92 -30.5 -157.5t-79.5 -108.5q-51 -45 -113.5 -67.5t-126.5 -22.5q-63 0 -125.5 22.5t-111.5 67.5q-51 43 -82 108.5t-31 157.5z M295 344q0 -76 42 -111.5t99 -35.5t99.5 35.5t42.5 111.5v770q0 76 -42 112t-100 36q-57 0 -99 -36t-42 -112v-770z" />
<glyph unicode="P" horiz-adv-x="833" d="M98 0v1458h314q86 0 151.5 -22.5t118.5 -79.5t73.5 -134t20.5 -208q0 -98 -11 -166t-48 -127q-43 -72 -115 -112t-188 -40h-107v-569h-209zM307 766h101q63 0 98 18.5t51 51.5q16 31 19.5 75.5t3.5 100.5q0 51 -2.5 97t-18.5 81t-49 53.5t-94 18.5h-109v-496z" />
<glyph unicode="Q" horiz-adv-x="872" d="M86 344v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5t126 -22.5t114 -65.5q49 -45 79.5 -112.5t30.5 -155.5v-770q0 -86 -30 -149l129 -105l-107 -127l-123 100q-93 -75 -216 -75h-3q-63 0 -125.5 22.5t-111.5 67.5q-51 43 -82 108.5t-31 157.5zM295 344 q0 -76 42 -111.5t99 -35.5q29 0 45 8l-94 76l107 127l84 -68v4v770q0 76 -42 112t-100 36q-57 0 -99 -36t-42 -112v-770z" />
<glyph unicode="R" horiz-adv-x="872" d="M98 0v1458h336q369 0 369 -428q0 -129 -40 -219t-140 -145l225 -666h-221l-195 623h-125v-623h-209zM307 807h119q55 0 88 15.5t51 43.5q16 29 22.5 71t6.5 97t-6 97.5t-25 72.5q-39 57 -147 58h-109v-455z" />
<glyph unicode="S" horiz-adv-x="831" d="M57 334v78h209v-66q0 -57 34 -103t114 -46q53 0 82.5 15t46.5 44q16 29 19 69t3 89q0 57 -4 94t-16 59q-14 23 -38 37t-63 31l-149 61q-135 55 -181 146.5t-46 228.5q0 82 22.5 155.5t67.5 127.5q43 53 109.5 84.5t158.5 31.5q78 0 142.5 -28.5t111.5 -75.5 q94 -98 94 -225v-103h-209v47q0 72 -33.5 124.5t-113.5 52.5q-43 0 -69.5 -16.5t-43.5 -41.5q-16 -27 -22.5 -60.5t-6.5 -70.5q0 -43 3.5 -71.5t15.5 -51.5t35.5 -39t64.5 -32l160 -64q70 -27 113 -62.5t67 -82.5q23 -49 31 -111.5t8 -142.5q0 -92 -18.5 -171t-59.5 -134 q-43 -57 -112.5 -90t-169.5 -33q-76 0 -141.5 26.5t-112.5 73.5t-75 109.5t-28 136.5z" />
<glyph unicode="T" horiz-adv-x="679" d="M-6 1262v196h692v-196h-242v-1262h-208v1262h-242z" />
<glyph unicode="U" horiz-adv-x="872" d="M98 328v1130h209v-1110q0 -78 37 -114.5t92 -36.5t92 36.5t37 114.5v1110h209v-1130q0 -72 -25.5 -132.5t-72.5 -107.5t-108.5 -73.5t-131.5 -26.5t-130 26.5t-107 73.5t-74 107.5t-27 132.5z" />
<glyph unicode="V" horiz-adv-x="833" d="M27 1458h221l166 -1018h4l168 1018h221l-299 -1458h-184z" />
<glyph unicode="W" horiz-adv-x="1214" d="M33 1458h221l104 -926h4l164 926h160l170 -948h4l101 948h221l-217 -1458h-197l-160 946h-4l-158 -946h-196z" />
<glyph unicode="X" horiz-adv-x="796" d="M-4 0l295 764l-273 694h222l157 -432l160 432h221l-278 -694l301 -764h-221l-183 492l-180 -492h-221z" />
<glyph unicode="Y" horiz-adv-x="794" d="M6 1458h221l168 -579h4l168 579h221l-286 -842v-616h-209v616z" />
<glyph unicode="Z" horiz-adv-x="681" d="M31 0v184l393 1078h-373v196h600v-166l-401 -1095h401v-197h-620z" />
<glyph unicode="[" horiz-adv-x="454" d="M119 -205v1663h319v-123h-135v-1417h135v-123h-319z" />
<glyph unicode="\" horiz-adv-x="454" d="M10 1470h164l270 -1482h-163z" />
<glyph unicode="]" horiz-adv-x="454" d="M16 -82h136v1417h-136v123h320v-1663h-320v123z" />
<glyph unicode="^" horiz-adv-x="1228" d="M102 580l445 878h135l444 -878h-163l-349 694l-348 -694h-164z" />
<glyph unicode="_" horiz-adv-x="1024" d="M0 -154h1024v-102h-1024v102z" />
<glyph unicode="`" horiz-adv-x="380" d="M-82 1464h221l185 -288h-136z" />
<glyph unicode="a" d="M51 264q0 88 14.5 154.5t53.5 111.5q37 43 100.5 64.5t161.5 21.5h22.5t22.5 -2t24.5 -2t30.5 -2v109q0 53 -22.5 88t-81.5 35q-41 0 -75 -27t-44 -78h-203q8 139 95 226q41 41 98 64.5t129 23.5q66 0 123 -20.5t100 -61.5q41 -41 65.5 -102.5t24.5 -143.5v-723h-209v106 h-4q-41 -57 -83 -87.5t-118 -30.5q-37 0 -74.5 11t-72.5 42q-35 29 -56.5 83t-21.5 140zM260 305q0 -59 28.5 -96t80.5 -37q53 0 82.5 35t29.5 98v141q-35 6 -57 7q-68 0 -116 -32t-48 -116z" />
<glyph unicode="b" horiz-adv-x="796" d="M86 0v1458h209v-514h4q35 51 83 79t110 28q76 0 117.5 -27t68.5 -68q20 -33 27 -86q6 -53 6 -165v-396q0 -86 -8.5 -137t-26.5 -86q-53 -98 -182 -98q-78 0 -119 30.5t-80 75.5v-94h-209zM295 326q0 -57 28.5 -93t75.5 -36q53 0 78 38.5t25 102.5v381q0 49 -21.5 86 t-81.5 37q-45 0 -74.5 -32t-29.5 -81v-403z" />
<glyph unicode="c" d="M98 348v342q0 63 8.5 116.5t30.5 94.5q37 68 105.5 109t167.5 41q72 0 130 -25t99 -68q82 -92 82 -239h-209q0 63 -27.5 93t-74.5 30t-75 -30t-28 -89v-424q0 -49 27.5 -75.5t75.5 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -84 -221q-41 -43 -98.5 -67.5t-128.5 -24.5 q-98 0 -167 41t-106 108q-23 41 -31 94.5t-8 116.5z" />
<glyph unicode="d" horiz-adv-x="796" d="M86 309v396q0 113 6 165q12 102 96 154q39 27 117 27q61 0 109.5 -28t83.5 -79h4v514h209v-1458h-209v94q-20 -23 -39 -42t-41 -34q-23 -14 -51.5 -22t-67.5 -8q-131 0 -182 98q-18 35 -26.5 86t-8.5 137zM295 338q0 -63 24.5 -102t77.5 -39q47 0 76 35.5t29 93.5v403 q0 49 -30 81t-75 32q-59 0 -80.5 -37t-21.5 -86v-381z" />
<glyph unicode="e" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41q72 0 130 -25t99 -68q82 -92 82 -231v-287h-414v-141q0 -49 28 -75.5t75 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -84 -221q-41 -43 -98 -67.5t-129 -24.5q-98 0 -167 41t-106 108q-23 41 -30.5 94.5 t-7.5 116.5zM276 604h205v119q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-119z" />
<glyph unicode="f" horiz-adv-x="454" d="M10 874v164h103v142q0 76 22.5 128t61.5 87q37 35 85 49t99 14h96v-196h-94q-61 0 -61 -72v-152h155v-164h-155v-874h-209v874h-103z" />
<glyph unicode="g" horiz-adv-x="796" d="M86 -121h209q2 -37 29.5 -69.5t81.5 -32.5q51 0 73.5 31.5t22.5 80.5v205h-4q-35 -51 -83 -78.5t-112 -27.5q-131 0 -182 98q-18 35 -26.5 86t-8.5 137v396q0 113 6 165q12 102 96 154q39 27 117 27q39 0 66.5 -8.5t50.5 -22.5t41 -33.5t39 -42.5v94h209v-1165 q0 -53 -17.5 -107.5t-56.5 -97.5q-39 -45 -98.5 -72.5t-141.5 -27.5q-47 0 -98 15.5t-94 51.5q-45 35 -77 93.5t-42 150.5zM295 319q0 -49 21.5 -85.5t80.5 -36.5q45 0 75 31.5t30 80.5v404q0 57 -29 93t-76 36q-53 0 -77.5 -39t-24.5 -103v-381z" />
<glyph unicode="h" horiz-adv-x="796" d="M86 0v1458h209v-520h4q33 49 79 81t118 32q39 0 77.5 -13.5t69.5 -44.5t49.5 -79t18.5 -117v-797h-209v719q0 57 -26.5 90t-76.5 33q-59 0 -81.5 -37t-22.5 -107v-698h-209z" />
<glyph unicode="i" horiz-adv-x="380" d="M86 0v1038h209v-1038h-209zM86 1249v209h209v-209h-209z" />
<glyph unicode="j" horiz-adv-x="380" d="M-45 -223q76 0 104 39q16 23 21.5 58.5t5.5 92.5v1071h209v-1149q0 -154 -96.5 -231.5t-243.5 -77.5v197zM86 1249v209h209v-209h-209z" />
<glyph unicode="k" horiz-adv-x="796" d="M86 0v1458h209v-872h4l252 452h209l-248 -422l299 -616h-227l-189 449l-100 -158v-291h-209z" />
<glyph unicode="l" horiz-adv-x="380" d="M74 279v1179h209v-1165q0 -59 21.5 -81t80.5 -28v-196q-72 0 -129 11t-98 44q-41 31 -62.5 87t-21.5 149z" />
<glyph unicode="m" horiz-adv-x="1212" d="M86 0v1038h209v-100h4q33 49 79 81t118 32q68 0 112.5 -33t73.5 -72q39 43 86 74t131 31q39 0 80 -13.5t74 -44.5t53 -79t20 -117v-797h-208v719q0 57 -27 90t-76 33q-59 0 -81.5 -37t-22.5 -107v-698h-209v719q0 57 -26.5 90t-76.5 33q-59 0 -81.5 -37t-22.5 -107v-698 h-209z" />
<glyph unicode="n" horiz-adv-x="796" d="M86 0v1038h209v-100h4q33 49 79 81t118 32q39 0 77.5 -13.5t69.5 -44.5t49.5 -79t18.5 -117v-797h-209v719q0 57 -26.5 90t-76.5 33q-59 0 -81.5 -37t-22.5 -107v-698h-209z" />
<glyph unicode="o" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41t166.5 -41t105.5 -109q23 -41 31 -94t8 -117v-342q0 -63 -8 -116.5t-31 -94.5q-37 -68 -105.5 -108.5t-166.5 -40.5t-167 41t-106 108q-23 41 -30.5 94.5t-7.5 116.5zM276 315q0 -59 28 -88.5t75 -29.5t74.5 29.5 t27.5 88.5v408q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-408z" />
<glyph unicode="p" horiz-adv-x="796" d="M86 -420v1458h209v-94q39 45 80 76t119 31q129 0 182 -99q18 -35 26.5 -86t8.5 -137v-395q0 -115 -6 -166q-12 -102 -95 -154q-40 -26 -113 -26h-5q-61 0 -109.5 27.5t-83.5 78.5h-4v-514h-209zM295 309q0 -49 29.5 -80.5t74.5 -31.5q59 0 81 36.5t22 85.5v381 q0 63 -24.5 102.5t-78.5 39.5q-47 0 -75.5 -36t-28.5 -93v-404z" />
<glyph unicode="q" horiz-adv-x="796" d="M86 334v395q0 86 8 137.5t27 85.5q51 98 182 99q39 0 67.5 -8.5t51.5 -22.5t41 -33.5t39 -42.5v94h209v-1458h-209v514h-4q-35 -51 -83 -78.5t-110 -27.5q-76 0 -118 26.5t-68 67.5q-20 33 -27 86q-6 51 -6 166zM295 319q0 -49 21.5 -85.5t80.5 -36.5q45 0 75 31.5 t30 80.5v404q0 57 -29 93t-76 36q-53 0 -77.5 -39t-24.5 -103v-381z" />
<glyph unicode="r" horiz-adv-x="569" d="M86 0v1038h209v-110q63 57 124.5 90t151.5 33v-222q-31 12 -63 13q-33 0 -71 -11.5t-68 -37.5q-33 -27 -53.5 -70t-20.5 -107v-616h-209z" />
<glyph unicode="s" horiz-adv-x="720" d="M49 301h197q6 -43 29.5 -80t82.5 -37q45 0 81 30t36 83q0 43 -22.5 73.5t-81.5 53.5l-101 37q-98 37 -154.5 109.5t-56.5 177.5q0 66 25 122t68 97q41 41 96 62.5t119 21.5q63 0 117.5 -22.5t93.5 -63.5t61.5 -97.5t22.5 -119.5h-197q-2 53 -34 79.5t-73 26.5 q-49 0 -75.5 -31.5t-26.5 -72.5q0 -29 14.5 -56.5t73.5 -50.5l123 -49q117 -45 161 -123t44 -170q0 -66 -23.5 -122t-64.5 -99t-98.5 -67.5t-125.5 -24.5q-127 0 -213 80q-43 39 -69.5 97t-28.5 136z" />
<glyph unicode="t" horiz-adv-x="417" d="M-18 874v164h104v316h209v-316h129v-164h-129v-575q0 -37 7 -57.5t24 -30.5q16 -10 39.5 -12t58.5 -2v-197h-86q-72 0 -120 22.5t-77 57.5t-42 79t-13 85v630h-104z" />
<glyph unicode="u" horiz-adv-x="796" d="M86 242v796h209v-719q0 -57 26.5 -89.5t75.5 -32.5q59 0 82 36.5t23 106.5v698h209v-1038h-209v100h-4q-33 -49 -79 -80.5t-118 -31.5q-39 0 -78 13t-69 44q-31 31 -49.5 79t-18.5 118z" />
<glyph unicode="v" horiz-adv-x="720" d="M-2 1038h221l139 -663h4l140 663h221l-270 -1038h-185z" />
<glyph unicode="w" horiz-adv-x="1097" d="M2 1038h221l127 -645h4l117 645h164l110 -645h5l124 645h222l-246 -1038h-184l-115 616h-4l-115 -616h-184z" />
<glyph unicode="x" horiz-adv-x="679" d="M-23 0l256 530l-243 508h221l129 -295l129 295h221l-248 -508l260 -530h-221l-141 317l-141 -317h-222z" />
<glyph unicode="y" horiz-adv-x="720" d="M-2 1038h221l143 -647h5l141 647h215l-299 -1218q-16 -70 -46 -115t-69 -74t-83 -40t-91 -11h-57v197h47q31 0 53.5 8t40.5 37q16 29 30.5 81t33.5 144z" />
<glyph unicode="z" horiz-adv-x="645" d="M35 0v166l338 688h-312v184h549v-166l-338 -675h338v-197h-575z" />
<glyph unicode="{" horiz-adv-x="454" d="M6 567v123q57 0 94 64.5t37 165.5v311q0 111 59.5 169t141.5 58h111v-123h-37q-49 0 -69.5 -35.5t-20.5 -99.5v-309q0 -68 -14.5 -116t-35.5 -79q-20 -31 -45.5 -47t-46.5 -20v-4q20 -4 46 -19.5t46 -46.5t35 -80t15 -119v-305q0 -66 20 -101.5t70 -35.5h37v-123h-111 q-82 0 -141.5 58.5t-59.5 169.5v309q0 104 -37 169.5t-94 65.5z" />
<glyph unicode="|" horiz-adv-x="454" d="M145 -512v2048h164v-2048h-164z" />
<glyph unicode="}" horiz-adv-x="454" d="M6 -82h37q49 0 69.5 36t20.5 101v305q0 70 14.5 119t34.5 80t45 46.5t47 19.5v4q-23 4 -47 20.5t-45 46.5q-20 31 -34.5 79t-14.5 116v309q0 63 -20.5 99t-69.5 36h-37v123h111q82 0 141 -58.5t59 -168.5v-311q0 -100 37 -165t95 -65v-123q-57 0 -94.5 -65.5 t-37.5 -169.5v-309q0 -111 -59 -169.5t-141 -58.5h-111v123z" />
<glyph unicode="~" horiz-adv-x="1228" d="M141 492q16 33 40 64.5t55 57.5q31 25 70.5 40.5t90.5 15.5q53 0 110.5 -19.5t117.5 -48.5q29 -12 56.5 -24.5t55.5 -24.5q55 -25 97 -25q31 0 56 13.5t48 33.5q23 23 39 48q10 14 18.5 26.5t18.5 26.5l73 -133q-16 -33 -39.5 -65t-54.5 -56q-31 -27 -70.5 -42t-91.5 -15 q-53 0 -110 19t-117 48q-29 12 -56.5 24.5t-55.5 24.5q-55 25 -97 25q-31 0 -55.5 -13.5t-46.5 -33.5q-23 -20 -41.5 -46t-36.5 -53z" />
<glyph unicode="&#xa1;" horiz-adv-x="491" d="M115 -420l80 1098h102l80 -1098h-262zM141 829v209h209v-209h-209z" />
<glyph unicode="&#xa2;" horiz-adv-x="761" d="M70 342v352q0 63 8 116.5t31 94.5q33 59 91 98t136 48v153h84v-153q129 -10 200.5 -100.5t71.5 -231.5h-209q0 53 -16 82.5t-47 40.5v-645q35 12 47 41.5t16 62.5h209q0 -68 -21.5 -126t-62.5 -99q-35 -37 -82 -60.5t-106 -27.5v-172h-84v172q-78 6 -136.5 45t-90.5 98 q-23 41 -31 94.5t-8 116.5zM279 291q0 -74 57 -94v645q-25 -8 -41 -38t-16 -73v-440z" />
<glyph unicode="&#xa3;" horiz-adv-x="761" d="M27 610v146h112q-39 72 -58.5 139.5t-19.5 171.5q0 80 18.5 153.5t59.5 129.5q41 55 103.5 87.5t150.5 32.5q51 0 104.5 -19t98.5 -60q43 -41 71.5 -103.5t28.5 -150.5v-93h-196v82q0 55 -23.5 101.5t-89.5 46.5q-72 0 -100.5 -61.5t-28.5 -147.5q0 -80 20.5 -162 t45.5 -147h237v-146h-190q14 -39 25.5 -76.5t11.5 -103.5q0 -61 -18.5 -116.5t-47.5 -106.5q33 -4 80 -12q23 -4 44 -7.5t44 -3.5q35 0 66.5 10.5t68.5 55.5l139 -141q-47 -66 -101 -93.5t-126 -27.5q-29 0 -74 6t-94 14q-49 6 -97 12.5t-83 6.5q-45 0 -93 -11.5t-69 -21.5 v176q16 10 56 23.5t75 17.5q27 39 41 85t14 93q0 45 -12 105.5t-35 115.5h-159z" />
<glyph unicode="&#xa4;" horiz-adv-x="761" d="M8 438l86 86q-33 35 -52 89.5t-19 115.5t18 115.5t51 89.5l-84 86l84 84l84 -86q35 31 89 50t116 19q61 0 115.5 -18t91.5 -51l84 86l82 -84l-84 -84q33 -37 51 -91t18 -116q0 -63 -19.5 -116.5t-49.5 -88.5l84 -84l-84 -84l-84 84q-35 -33 -89.5 -51t-115.5 -18 t-115.5 19.5t-89.5 51.5l-84 -86zM158 729q0 -92 65 -158q31 -31 71 -48t87 -17q45 0 86 17.5t72 47.5q66 66 65 158q0 45 -17.5 86t-47.5 72q-66 66 -158 65q-92 0 -158 -65q-31 -31 -48 -72t-17 -86z" />
<glyph unicode="&#xa5;" horiz-adv-x="761" d="M8 1458h209l162 -579h4l162 579h209l-242 -735h201v-135h-234v-101h234v-135h-234v-352h-196v352h-234v135h234v101h-234v135h201z" />
<glyph unicode="&#xa6;" horiz-adv-x="454" d="M145 -358v716h164v-716h-164zM145 666v716h164v-716h-164z" />
<glyph unicode="&#xa7;" d="M68 530q0 88 8 147.5t30 98.5q23 39 58 62.5t92 42.5q-51 25 -78.5 51t-44.5 59q-16 31 -20 71t-4 93q0 80 22.5 139.5t61.5 98.5q37 39 88 58t108 19q51 0 100.5 -17t88.5 -52t63.5 -88t24.5 -123v-94h-174v86q0 47 -26 86t-77 39q-66 0 -86 -44t-20 -106q0 -37 2 -60.5 t12 -39.5q10 -18 28.5 -34.5t53.5 -37.5l199 -129q37 -25 59 -48.5t35 -57.5q10 -35 14 -87.5t4 -132.5q0 -74 -4 -124t-22 -87t-54.5 -63.5t-97.5 -54.5q51 -27 82 -51.5t47 -57.5t20.5 -76t4.5 -104q0 -86 -10.5 -137t-41.5 -97q-35 -51 -99 -80.5t-134 -29.5 q-86 0 -140.5 28.5t-88.5 73.5q-35 43 -49.5 94.5t-14.5 96.5v86h174v-84q0 -53 32 -92t81 -39q63 0 90 39t27 120q0 39 -3.5 64t-9.5 43q-8 16 -20.5 28.5t-32.5 24.5l-242 148q-78 47 -98 118q-12 39 -15 90.5t-3 122.5zM276 436q0 -45 30 -76.5t73 -31.5q41 0 71.5 31.5 t30.5 76.5v203q0 45 -30.5 77t-71.5 32q-43 0 -73 -32t-30 -77v-203z" />
<glyph unicode="&#xa8;" horiz-adv-x="380" d="M-68 1200v209h185v-209h-185zM264 1200v209h185v-209h-185z" />
<glyph unicode="&#xa9;" horiz-adv-x="1638" d="M78 729q0 154 58.5 289t158.5 235t235.5 158.5t288.5 58.5q154 0 289 -58t235 -159q100 -100 159 -235t59 -289t-58.5 -289t-159.5 -235q-100 -100 -235 -158.5t-289 -58.5t-289 58t-235 159q-100 100 -158.5 235t-58.5 289zM225 729q0 -125 47 -235.5t127 -192.5 t188.5 -130t231.5 -48t231.5 48t188.5 130t127 192.5t47 235.5t-47 235.5t-127 192.5t-188.5 130t-231.5 48t-231.5 -48t-188.5 -130t-127 -192.5t-47 -235.5zM408 729q0 100 29.5 185t84.5 147q55 61 134 96t184 35q141 0 243 -80t125 -229h-135q-14 74 -80 125 q-66 49 -151 49q-68 0 -121 -25.5t-90 -68.5q-76 -92 -76 -230q0 -131 80 -227q37 -47 91 -75t120 -28q88 0 150.5 53.5t76.5 131.5h135q-29 -145 -127 -232q-100 -88 -235 -88q-100 0 -181 36t-139 99q-57 61 -87.5 144.5t-30.5 181.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="491" d="M29 999q0 106 47 162t174 56q14 0 28.5 -1.5t36.5 -3.5v50q0 31 -15 52t-52 21q-27 0 -49.5 -13t-26.5 -48h-135q6 92 62.5 144t148.5 52q90 0 151 -49q29 -25 46.5 -61.5t17.5 -85.5v-434h-148v63h-4q-27 -35 -53.5 -53.5t-75.5 -18.5q-25 0 -51.5 6.5t-48.5 24.5 q-23 18 -38 51t-15 86zM176 1022q0 -59 66 -59q35 0 54 20.5t19 48.5v76q-10 2 -19 3t-17 1q-45 0 -74 -19.5t-29 -70.5z" />
<glyph unicode="&#xab;" horiz-adv-x="872" d="M20 567l416 414v-209l-207 -205l207 -205v-208zM399 567l416 414v-209l-207 -205l207 -205v-208z" />
<glyph unicode="&#xac;" horiz-adv-x="1228" d="M96 641v164h1037v-574h-164v410h-873z" />
<glyph unicode="&#xad;" horiz-adv-x="796" d="M90 463v209h617v-209h-617z" />
<glyph unicode="&#xae;" horiz-adv-x="1638" d="M78 729q0 154 58.5 289t158.5 235t235.5 158.5t288.5 58.5q154 0 289 -58t235 -159q100 -100 159 -235t59 -289t-58.5 -289t-159.5 -235q-100 -100 -235 -158.5t-289 -58.5t-289 58t-235 159q-100 100 -158.5 235t-58.5 289zM225 729q0 -125 47 -235.5t127 -192.5 t188.5 -130t231.5 -48t231.5 48t188.5 130t127 192.5t47 235.5t-47 235.5t-127 192.5t-188.5 130t-231.5 48t-231.5 -48t-188.5 -130t-127 -192.5t-47 -235.5zM539 297v864h327q297 0 297 -243q0 -61 -17.5 -104.5t-47.5 -72.5q-31 -29 -71 -44t-85 -21l248 -379h-164 l-248 375h-104v-375h-135zM674 795h153q100 0 150.5 29.5t50.5 99.5q0 59 -44 86.5t-114 27.5h-196v-243z" />
<glyph unicode="&#xaf;" horiz-adv-x="380" d="M-90 1243v123h561v-123h-561z" />
<glyph unicode="&#xb0;" horiz-adv-x="532" d="M37 1241q0 47 18.5 89t48.5 73q31 31 73 49t89 18t89 -18t73 -49t49.5 -73t18.5 -89t-18.5 -89t-49.5 -73t-73 -49t-89 -18t-89 18.5t-73 48.5q-31 31 -49 73t-18 89zM141 1241q0 -53 36 -89t89 -36t89 36t36 89t-36 89t-89 36t-89 -36t-36 -89z" />
<glyph unicode="&#xb1;" horiz-adv-x="1228" d="M96 0v164h1037v-164h-1037zM96 543v164h436v329h164v-329h437v-164h-437v-330h-164v330h-436z" />
<glyph unicode="&#xb2;" horiz-adv-x="495" d="M23 588v110l262 435q10 18 17 37.5t7 41.5v48q0 31 -20.5 47t-42.5 16q-23 0 -41.5 -16.5t-18.5 -46.5v-64h-163v74q0 88 65 143q31 27 72 42t88 15q45 0 86 -14t72 -39t49 -58.5t18 -74.5v-67q0 -53 -12 -86t-35 -68l-195 -328h242v-147h-450z" />
<glyph unicode="&#xb3;" horiz-adv-x="495" d="M23 772v70h163v-47q0 -31 16.5 -49.5t45.5 -18.5q27 0 44 19.5t17 41.5v107q0 47 -20.5 61.5t-83.5 14.5v135q59 0 80.5 13.5t23.5 56.5v86q0 27 -16.5 44t-44.5 17q-33 0 -47.5 -21.5t-14.5 -39.5v-54h-163v66q0 86 62 141t167 55q57 0 98 -17t70 -46q29 -31 41 -67.5 t12 -73.5v-84q0 -45 -14.5 -79t-69.5 -63q49 -29 66.5 -63.5t17.5 -85.5v-105q0 -37 -11 -74.5t-40 -66.5t-72 -47t-102 -18q-41 0 -82 10t-72 33q-33 23 -52 59.5t-19 89.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="380" d="M57 1176l185 288h221l-270 -288h-136z" />
<glyph unicode="&#xb5;" horiz-adv-x="796" d="M86 -420v1458h209v-719q0 -57 26.5 -89.5t75.5 -32.5q59 0 82 36.5t23 106.5v698h209v-1038h-209v100h-4q-14 -29 -31.5 -50t-34.5 -36q-33 -27 -70 -26q-43 0 -63 20h-4v-428h-209z" />
<glyph unicode="&#xb6;" horiz-adv-x="909" d="M16 1065q0 100 33 174t92 123t140 72.5t182 23.5h352v-1878h-147v1774h-148v-1774h-147v1104q-68 0 -131.5 29.5t-112.5 81.5q-51 51 -82 120.5t-31 149.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="380" d="M59 610q0 53 39 92t92 39t92.5 -38.5t39.5 -92.5q0 -53 -39 -92t-93 -39q-53 0 -92 39t-39 92z" />
<glyph unicode="&#xb8;" horiz-adv-x="380" d="M-31 -420l35 86q33 -12 65.5 -21.5t67.5 -9.5q16 0 36 3.5t36 13.5q39 20 39 67q0 35 -29 56.5t-63 21.5q-45 0 -80 -14l-35 41l133 176h94l-94 -123q16 2 31.5 3t32.5 1q51 0 83.5 -14t53.5 -37q20 -25 28.5 -51.5t8.5 -50.5q0 -49 -23.5 -85t-60.5 -59t-81 -33t-87 -10 q-59 0 -110.5 14.5t-80.5 24.5z" />
<glyph unicode="&#xb9;" horiz-adv-x="495" d="M55 1223v147l135 88h164v-870h-164v723z" />
<glyph unicode="&#xba;" horiz-adv-x="491" d="M37 1034v234q0 88 55 145t154 57q98 0 153.5 -57t55.5 -145v-234q0 -88 -55.5 -145.5t-153.5 -57.5t-153.5 57.5t-55.5 145.5zM184 1036q0 -70 62 -69q61 0 61 69v228q0 72 -61 71q-61 0 -62 -71v-228z" />
<glyph unicode="&#xbb;" horiz-adv-x="872" d="M57 154v208l207 205l-207 205v209l416 -414zM436 154v208l207 205l-207 205v209l416 -414z" />
<glyph unicode="&#xbc;" horiz-adv-x="1136" d="M23 1223v147l135 88h164v-870h-164v723zM37 -35l895 1528h123l-895 -1528h-123zM596 127v127l201 620h164l-193 -612h121v236h164v-236h61v-135h-61v-127h-164v127h-293z" />
<glyph unicode="&#xbd;" horiz-adv-x="1136" d="M23 1223v147l135 88h164v-870h-164v723zM37 -35l895 1528h123l-895 -1528h-123zM659 0v111l267 434q10 18 17 37.5t7 42.5v47q0 31 -20.5 47t-42.5 16q-27 0 -45.5 -16.5t-18.5 -46.5v-64h-164v74q0 86 66 143.5t164 57.5q45 0 86 -14.5t72 -39.5t49 -58.5t18 -74.5v-67 q0 -53 -12 -86t-35 -68l-195 -328h242v-147h-455z" />
<glyph unicode="&#xbe;" horiz-adv-x="1136" d="M23 772v70h163v-47q0 -31 16.5 -49.5t45.5 -18.5q27 0 44 19.5t17 41.5v107q0 47 -20.5 61.5t-83.5 14.5v135q59 0 80.5 13.5t23.5 56.5v86q0 27 -16.5 44t-44.5 17q-33 0 -47.5 -21.5t-14.5 -39.5v-54h-163v66q0 86 62 141t167 55q57 0 98 -17t70 -46q29 -31 41 -67.5 t12 -73.5v-84q0 -45 -14.5 -79t-69.5 -63q49 -29 66.5 -63.5t17.5 -85.5v-105q0 -37 -11 -74.5t-40 -66.5t-72 -47t-102 -18q-41 0 -82 10t-72 33q-33 23 -52 59.5t-19 89.5zM76 -35l895 1528h123l-895 -1528h-123zM596 127v127l201 620h164l-193 -612h121v236h164v-236h61 v-135h-61v-127h-164v127h-293z" />
<glyph unicode="&#xbf;" d="M66 -66q0 51 1 88t5 64t10 46.5t16 35.5l142 242q14 25 24 50t10 54v164h209v-191q0 -45 -14 -86t-37 -79l-131 -213q-16 -27 -21.5 -58t-5.5 -61v-109q0 -43 30 -73.5t73 -30.5q37 0 71.5 26.5t34.5 77.5v119h209v-121q0 -66 -24.5 -122t-67.5 -97q-43 -43 -100.5 -67.5 t-122.5 -24.5q-76 0 -138.5 30.5t-103.5 86.5q-41 53 -55 106q-14 51 -14 143zM274 829v209h209v-209h-209z" />
<glyph unicode="&#xc0;" horiz-adv-x="833" d="M4 0l326 1458h174l325 -1458h-208l-62 313h-285l-61 -313h-209zM145 1812h222l184 -288h-135zM311 510h209l-102 528h-4z" />
<glyph unicode="&#xc1;" horiz-adv-x="833" d="M4 0l326 1458h174l325 -1458h-208l-62 313h-285l-61 -313h-209zM285 1524l184 288h221l-270 -288h-135zM311 510h209l-102 528h-4z" />
<glyph unicode="&#xc2;" horiz-adv-x="833" d="M4 0l326 1458h174l325 -1458h-208l-62 313h-285l-61 -313h-209zM121 1524l201 288h192l201 -288h-160l-137 194l-137 -194h-160zM311 510h209l-102 528h-4z" />
<glyph unicode="&#xc3;" horiz-adv-x="833" d="M4 0l326 1458h174l325 -1458h-208l-62 313h-285l-61 -313h-209zM70 1542q16 100 63 160q25 31 59.5 49t83.5 18q25 0 67 -12t87 -28t85 -28.5t56 -12.5q43 0 65.5 25.5t25.5 66.5h104q-10 -47 -24.5 -88t-39.5 -72t-59.5 -49.5t-83.5 -18.5q-25 0 -66.5 12.5t-86.5 28.5 q-23 8 -43.5 14.5t-40.5 14.5q-41 12 -58 12q-43 0 -65.5 -25.5t-24.5 -66.5h-104zM311 510h209l-102 528h-4z" />
<glyph unicode="&#xc4;" horiz-adv-x="833" d="M4 0l326 1458h174l325 -1458h-208l-62 313h-285l-61 -313h-209zM160 1548v209h184v-209h-184zM311 510h209l-102 528h-4zM492 1548v209h184v-209h-184z" />
<glyph unicode="&#xc5;" horiz-adv-x="833" d="M4 0l326 1458h174l325 -1458h-208l-62 313h-285l-61 -313h-209zM209 1710q0 86 61 148q61 61 148 61q86 0 147 -61t62 -148q0 -86 -62 -147q-61 -61 -147 -62q-86 0 -148 62q-61 61 -61 147zM293 1710q0 -53 36 -89t89 -36t89 36t36 89t-36 89t-89 36t-89 -36t-36 -89z M311 510h209l-102 528h-4z" />
<glyph unicode="&#xc6;" horiz-adv-x="1251" d="M4 0l414 1458h796v-196h-413v-428h360v-197h-360v-428h413v-209h-622v313h-287l-80 -313h-221zM354 510h238v752h-49z" />
<glyph unicode="&#xc7;" horiz-adv-x="833" d="M86 332v803q0 72 24.5 133t69.5 106t107.5 70.5t138.5 25.5q147 0 240 -96q45 -47 70.5 -111.5t25.5 -140.5v-82h-209v70q0 61 -35 106.5t-94 45.5q-78 0 -103.5 -48.5t-25.5 -121.5v-746q0 -63 27.5 -106t99.5 -43q20 0 43.5 7t44.5 23q18 16 30.5 45t12.5 72v72h209 v-90q0 -59 -20.5 -114.5t-55.5 -100.5q-37 -45 -87 -76t-110 -43l-88 -115q16 2 32 3t32 1q51 0 84 -14t53 -37q20 -25 28.5 -51.5t8.5 -50.5q0 -49 -23.5 -85t-60.5 -59t-81 -33t-87 -10q-59 0 -110.5 14.5t-79.5 24.5l34 86q33 -12 66 -21.5t68 -9.5q16 0 35.5 3.5 t35.5 13.5q39 20 39 67q0 35 -28.5 56.5t-63.5 21.5q-45 0 -80 -14l-35 41l125 164q-55 4 -110.5 24.5t-98.5 61.5q-45 41 -71.5 103.5t-26.5 154.5z" />
<glyph unicode="&#xc8;" d="M98 0v1458h623v-196h-414v-428h361v-197h-361v-428h414v-209h-623zM127 1812h221l184 -288h-135z" />
<glyph unicode="&#xc9;" d="M98 0v1458h623v-196h-414v-428h361v-197h-361v-428h414v-209h-623zM266 1524l185 288h221l-271 -288h-135z" />
<glyph unicode="&#xca;" d="M98 0v1458h623v-196h-414v-428h361v-197h-361v-428h414v-209h-623zM102 1524l201 288h193l200 -288h-159l-138 194l-137 -194h-160z" />
<glyph unicode="&#xcb;" d="M98 0v1458h623v-196h-414v-428h361v-197h-361v-428h414v-209h-623zM141 1548v209h185v-209h-185zM473 1548v209h184v-209h-184z" />
<glyph unicode="&#xcc;" horiz-adv-x="417" d="M-63 1812h221l184 -288h-135zM104 0v1458h209v-1458h-209z" />
<glyph unicode="&#xcd;" horiz-adv-x="417" d="M76 1524l184 288h221l-270 -288h-135zM104 0v1458h209v-1458h-209z" />
<glyph unicode="&#xce;" horiz-adv-x="417" d="M-88 1524l201 288h192l201 -288h-160l-137 194l-137 -194h-160zM104 0v1458h209v-1458h-209z" />
<glyph unicode="&#xcf;" horiz-adv-x="417" d="M-49 1548v209h184v-209h-184zM104 0v1458h209v-1458h-209zM283 1548v209h184v-209h-184z" />
<glyph unicode="&#xd0;" horiz-adv-x="872" d="M0 717v135h98v606h310q180 0 273 -98t93 -279v-682q0 -205 -99.5 -302t-285.5 -97h-291v717h-98zM307 197h96q88 0 125 44t37 138v702q0 86 -34.5 133.5t-127.5 47.5h-96v-410h144v-135h-144v-520z" />
<glyph unicode="&#xd1;" horiz-adv-x="909" d="M90 0v1458h201l315 -878h4v878h209v-1458h-196l-320 877h-4v-877h-209zM106 1542q16 100 64 160q25 31 59.5 49t83.5 18q25 0 67 -12t87 -28t85 -28.5t56 -12.5q43 0 65.5 25.5t24.5 66.5h105q-10 -47 -24.5 -88t-39.5 -72t-59.5 -49.5t-83.5 -18.5q-25 0 -67 12.5 t-87 28.5q-23 8 -43 14.5t-41 14.5q-41 12 -57 12q-43 0 -65.5 -25.5t-24.5 -66.5h-105z" />
<glyph unicode="&#xd2;" horiz-adv-x="872" d="M86 344v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5t126 -22.5t114 -65.5q49 -45 79.5 -112.5t30.5 -155.5v-770q0 -92 -30.5 -157.5t-79.5 -108.5q-51 -45 -113.5 -67.5t-126.5 -22.5q-63 0 -125.5 22.5t-111.5 67.5q-51 43 -82 108.5t-31 157.5z M164 1812h221l184 -288h-135zM295 344q0 -76 42 -111.5t99 -35.5t99.5 35.5t42.5 111.5v770q0 76 -42 112t-100 36q-57 0 -99 -36t-42 -112v-770z" />
<glyph unicode="&#xd3;" horiz-adv-x="872" d="M86 344v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5t126 -22.5t114 -65.5q49 -45 79.5 -112.5t30.5 -155.5v-770q0 -92 -30.5 -157.5t-79.5 -108.5q-51 -45 -113.5 -67.5t-126.5 -22.5q-63 0 -125.5 22.5t-111.5 67.5q-51 43 -82 108.5t-31 157.5z M295 344q0 -76 42 -111.5t99 -35.5t99.5 35.5t42.5 111.5v770q0 76 -42 112t-100 36q-57 0 -99 -36t-42 -112v-770zM303 1524l184 288h222l-271 -288h-135z" />
<glyph unicode="&#xd4;" horiz-adv-x="872" d="M86 344v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5t126 -22.5t114 -65.5q49 -45 79.5 -112.5t30.5 -155.5v-770q0 -92 -30.5 -157.5t-79.5 -108.5q-51 -45 -113.5 -67.5t-126.5 -22.5q-63 0 -125.5 22.5t-111.5 67.5q-51 43 -82 108.5t-31 157.5z M139 1524l201 288h192l201 -288h-160l-137 194l-137 -194h-160zM295 344q0 -76 42 -111.5t99 -35.5t99.5 35.5t42.5 111.5v770q0 76 -42 112t-100 36q-57 0 -99 -36t-42 -112v-770z" />
<glyph unicode="&#xd5;" horiz-adv-x="872" d="M86 344v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5t126 -22.5t114 -65.5q49 -45 79.5 -112.5t30.5 -155.5v-770q0 -92 -30.5 -157.5t-79.5 -108.5q-51 -45 -113.5 -67.5t-126.5 -22.5q-63 0 -125.5 22.5t-111.5 67.5q-51 43 -82 108.5t-31 157.5z M88 1542q16 100 64 160q25 31 59.5 49t83.5 18q25 0 66.5 -12t87.5 -28q45 -16 84.5 -28.5t56.5 -12.5q43 0 65.5 25.5t24.5 66.5h104q-10 -47 -24.5 -88t-38.5 -72q-25 -31 -59.5 -49.5t-83.5 -18.5q-25 0 -67 12.5t-87 28.5q-23 8 -43.5 14.5t-40.5 14.5q-41 12 -57 12 q-43 0 -65.5 -25.5t-24.5 -66.5h-105zM295 344q0 -76 42 -111.5t99 -35.5t99.5 35.5t42.5 111.5v770q0 76 -42 112t-100 36q-57 0 -99 -36t-42 -112v-770z" />
<glyph unicode="&#xd6;" horiz-adv-x="872" d="M86 344v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5t126 -22.5t114 -65.5q49 -45 79.5 -112.5t30.5 -155.5v-770q0 -92 -30.5 -157.5t-79.5 -108.5q-51 -45 -113.5 -67.5t-126.5 -22.5q-63 0 -125.5 22.5t-111.5 67.5q-51 43 -82 108.5t-31 157.5z M178 1548v209h184v-209h-184zM295 344q0 -76 42 -111.5t99 -35.5t99.5 35.5t42.5 111.5v770q0 76 -42 112t-100 36q-57 0 -99 -36t-42 -112v-770zM510 1548v209h184v-209h-184z" />
<glyph unicode="&#xd7;" horiz-adv-x="1228" d="M137 158l363 360l-363 363l117 114l360 -360l363 360l115 -114l-361 -363l361 -360l-115 -117l-363 362l-360 -362z" />
<glyph unicode="&#xd8;" horiz-adv-x="872" d="M20 -33l103 211q-37 70 -37 166v770q0 88 30.5 155.5t82.5 112.5q49 43 111.5 65.5t125.5 22.5q70 0 136.5 -26.5t119.5 -79.5l82 172l78 -45l-102 -211q16 -33 26 -75t10 -91v-770q0 -92 -30.5 -157.5t-79.5 -108.5q-51 -45 -113.5 -67.5t-126.5 -22.5 q-72 0 -139.5 26.5t-116.5 79.5l-82 -172zM295 334q4 -70 45 -103.5t96 -33.5q57 0 99.5 35.5t42.5 111.5v576h-5zM295 537h4l279 587q-4 72 -46.5 105t-95.5 33q-57 0 -99 -36t-42 -112v-577z" />
<glyph unicode="&#xd9;" horiz-adv-x="872" d="M98 328v1130h209v-1110q0 -78 37 -114.5t92 -36.5t92 36.5t37 114.5v1110h209v-1130q0 -72 -25.5 -132.5t-72.5 -107.5t-108.5 -73.5t-131.5 -26.5t-130 26.5t-107 73.5t-74 107.5t-27 132.5zM164 1812h221l184 -288h-135z" />
<glyph unicode="&#xda;" horiz-adv-x="872" d="M98 328v1130h209v-1110q0 -78 37 -114.5t92 -36.5t92 36.5t37 114.5v1110h209v-1130q0 -72 -25.5 -132.5t-72.5 -107.5t-108.5 -73.5t-131.5 -26.5t-130 26.5t-107 73.5t-74 107.5t-27 132.5zM303 1524l184 288h222l-271 -288h-135z" />
<glyph unicode="&#xdb;" horiz-adv-x="872" d="M98 328v1130h209v-1110q0 -78 37 -114.5t92 -36.5t92 36.5t37 114.5v1110h209v-1130q0 -72 -25.5 -132.5t-72.5 -107.5t-108.5 -73.5t-131.5 -26.5t-130 26.5t-107 73.5t-74 107.5t-27 132.5zM139 1524l201 288h192l201 -288h-160l-137 194l-137 -194h-160z" />
<glyph unicode="&#xdc;" horiz-adv-x="872" d="M98 328v1130h209v-1110q0 -78 37 -114.5t92 -36.5t92 36.5t37 114.5v1110h209v-1130q0 -72 -25.5 -132.5t-72.5 -107.5t-108.5 -73.5t-131.5 -26.5t-130 26.5t-107 73.5t-74 107.5t-27 132.5zM178 1548v209h184v-209h-184zM510 1548v209h184v-209h-184z" />
<glyph unicode="&#xdd;" horiz-adv-x="794" d="M6 1458h221l168 -579h4l168 579h221l-286 -842v-616h-209v616zM264 1524l185 288h221l-271 -288h-135z" />
<glyph unicode="&#xde;" horiz-adv-x="833" d="M98 0v1458h209v-254h105q86 0 151.5 -22.5t118.5 -79.5t73.5 -134t20.5 -208q0 -98 -11 -166t-48 -127q-43 -72 -115 -112t-188 -40h-107v-315h-209zM307 512h101q63 0 98 18.5t51 51.5q16 31 19.5 75.5t3.5 100.5q0 51 -2.5 97t-18.5 81t-49 53.5t-94 18.5h-109v-496z " />
<glyph unicode="&#xdf;" horiz-adv-x="796" d="M86 0v1130q0 68 18.5 129.5t57.5 108.5q37 47 95 74.5t138 27.5q88 0 148.5 -28.5t97.5 -77.5q37 -51 53.5 -119.5t16.5 -146.5q0 -55 -5.5 -92t-17.5 -66q-14 -29 -37.5 -50.5t-60.5 -45.5q59 -33 90 -82t31 -127v-361q0 -98 -26 -153t-67 -84q-41 -31 -95 -40t-111 -9 v196q45 0 67.5 19.5t22.5 85.5v389q0 41 -23.5 54t-66.5 13v185q47 0 68.5 15.5t21.5 64.5v164q0 43 -19.5 71.5t-79.5 28.5q-59 0 -83.5 -38t-24.5 -142v-1094h-209z" />
<glyph unicode="&#xe0;" d="M51 264q0 88 14.5 154.5t53.5 111.5q37 43 100.5 64.5t161.5 21.5h22.5t22.5 -2t24.5 -2t30.5 -2v109q0 53 -22.5 88t-81.5 35q-41 0 -75 -27t-44 -78h-203q8 139 95 226q41 41 98 64.5t129 23.5q66 0 123 -20.5t100 -61.5q41 -41 65.5 -102.5t24.5 -143.5v-723h-209v106 h-4q-41 -57 -83 -87.5t-118 -30.5q-37 0 -74.5 11t-72.5 42q-35 29 -56.5 83t-21.5 140zM106 1464h222l184 -288h-135zM260 305q0 -59 28.5 -96t80.5 -37q53 0 82.5 35t29.5 98v141q-35 6 -57 7q-68 0 -116 -32t-48 -116z" />
<glyph unicode="&#xe1;" d="M51 264q0 88 14.5 154.5t53.5 111.5q37 43 100.5 64.5t161.5 21.5h22.5t22.5 -2t24.5 -2t30.5 -2v109q0 53 -22.5 88t-81.5 35q-41 0 -75 -27t-44 -78h-203q8 139 95 226q41 41 98 64.5t129 23.5q66 0 123 -20.5t100 -61.5q41 -41 65.5 -102.5t24.5 -143.5v-723h-209v106 h-4q-41 -57 -83 -87.5t-118 -30.5q-37 0 -74.5 11t-72.5 42q-35 29 -56.5 83t-21.5 140zM246 1176l184 288h221l-270 -288h-135zM260 305q0 -59 28.5 -96t80.5 -37q53 0 82.5 35t29.5 98v141q-35 6 -57 7q-68 0 -116 -32t-48 -116z" />
<glyph unicode="&#xe2;" d="M51 264q0 88 14.5 154.5t53.5 111.5q37 43 100.5 64.5t161.5 21.5h22.5t22.5 -2t24.5 -2t30.5 -2v109q0 53 -22.5 88t-81.5 35q-41 0 -75 -27t-44 -78h-203q8 139 95 226q41 41 98 64.5t129 23.5q66 0 123 -20.5t100 -61.5q41 -41 65.5 -102.5t24.5 -143.5v-723h-209v106 h-4q-41 -57 -83 -87.5t-118 -30.5q-37 0 -74.5 11t-72.5 42q-35 29 -56.5 83t-21.5 140zM82 1176l201 288h192l201 -288h-160l-137 194l-137 -194h-160zM260 305q0 -59 28.5 -96t80.5 -37q53 0 82.5 35t29.5 98v141q-35 6 -57 7q-68 0 -116 -32t-48 -116z" />
<glyph unicode="&#xe3;" d="M31 1194q16 100 63 160q25 31 59.5 49t84.5 18q25 0 66.5 -12t86.5 -29q45 -16 85 -28.5t56 -12.5q43 0 66 26t25 67h104q-10 -47 -24.5 -88t-38.5 -72q-25 -31 -60 -49.5t-84 -18.5q-25 0 -66.5 12.5t-86.5 28.5q-23 8 -43.5 14.5t-40.5 14.5q-41 12 -58 12 q-43 0 -65.5 -25.5t-24.5 -66.5h-104zM51 264q0 88 14.5 154.5t53.5 111.5q37 43 100.5 64.5t161.5 21.5h22.5t22.5 -2t24.5 -2t30.5 -2v109q0 53 -22.5 88t-81.5 35q-41 0 -75 -27t-44 -78h-203q8 139 95 226q41 41 98 64.5t129 23.5q66 0 123 -20.5t100 -61.5 q41 -41 65.5 -102.5t24.5 -143.5v-723h-209v106h-4q-41 -57 -83 -87.5t-118 -30.5q-37 0 -74.5 11t-72.5 42q-35 29 -56.5 83t-21.5 140zM260 305q0 -59 28.5 -96t80.5 -37q53 0 82.5 35t29.5 98v141q-35 6 -57 7q-68 0 -116 -32t-48 -116z" />
<glyph unicode="&#xe4;" d="M51 264q0 88 14.5 154.5t53.5 111.5q37 43 100.5 64.5t161.5 21.5h22.5t22.5 -2t24.5 -2t30.5 -2v109q0 53 -22.5 88t-81.5 35q-41 0 -75 -27t-44 -78h-203q8 139 95 226q41 41 98 64.5t129 23.5q66 0 123 -20.5t100 -61.5q41 -41 65.5 -102.5t24.5 -143.5v-723h-209v106 h-4q-41 -57 -83 -87.5t-118 -30.5q-37 0 -74.5 11t-72.5 42q-35 29 -56.5 83t-21.5 140zM121 1200v209h184v-209h-184zM260 305q0 -59 28.5 -96t80.5 -37q53 0 82.5 35t29.5 98v141q-35 6 -57 7q-68 0 -116 -32t-48 -116zM453 1200v209h184v-209h-184z" />
<glyph unicode="&#xe5;" d="M51 264q0 88 14.5 154.5t53.5 111.5q37 43 100.5 64.5t161.5 21.5h22.5t22.5 -2t24.5 -2t30.5 -2v109q0 53 -22.5 88t-81.5 35q-41 0 -75 -27t-44 -78h-203q8 139 95 226q41 41 98 64.5t129 23.5q66 0 123 -20.5t100 -61.5q41 -41 65.5 -102.5t24.5 -143.5v-723h-209v106 h-4q-41 -57 -83 -87.5t-118 -30.5q-37 0 -74.5 11t-72.5 42q-35 29 -56.5 83t-21.5 140zM170 1352q0 86 61 147t148 62q86 0 147 -62q61 -61 62 -147q0 -86 -62 -148q-61 -61 -147 -61t-148 61q-61 62 -61 148zM254 1352q0 -53 36 -89t89 -36t89 35.5t36 89.5q0 53 -36 89 t-89 36t-89 -36t-36 -89zM260 305q0 -59 28.5 -96t80.5 -37q53 0 82.5 35t29.5 98v141q-35 6 -57 7q-68 0 -116 -32t-48 -116z" />
<glyph unicode="&#xe6;" horiz-adv-x="1171" d="M51 264q0 88 14.5 154.5t53.5 111.5q37 43 100.5 64.5t161.5 21.5h22.5t22.5 -2t24.5 -2t30.5 -2v109q0 53 -22.5 88t-81.5 35q-41 0 -75 -27t-44 -78h-203q10 141 95 226q41 41 98 64.5t129 23.5q66 0 116 -19.5t91 -54.5q39 35 94 54.5t115 19.5q72 0 130 -25t99 -68 q82 -92 82 -231v-287h-414v-141q0 -49 27.5 -75.5t75.5 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -86 -223q-43 -41 -100.5 -65.5t-124.5 -24.5q-104 -2 -173 43t-100 104q-37 -74 -101.5 -110.5t-142.5 -36.5q-37 0 -74.5 11t-72.5 42q-35 29 -56.5 83t-21.5 140z M260 305q0 -59 28.5 -96t80.5 -37q53 0 82.5 35t29.5 98v141q-35 6 -57 7q-68 0 -116 -32t-48 -116zM690 604h205v119q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-119z" />
<glyph unicode="&#xe7;" d="M98 348v342q0 63 8.5 116.5t30.5 94.5q37 68 105.5 109t167.5 41q72 0 130 -25t99 -68q82 -92 82 -239h-209q0 63 -27.5 93t-74.5 30t-75 -30t-28 -89v-424q0 -49 27.5 -75.5t75.5 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -84 -221q-74 -76 -186 -88l-89 -115 q16 2 32 3t32 1q51 0 84 -14t53 -37q20 -25 28.5 -51.5t8.5 -50.5q0 -49 -23.5 -85t-60.5 -59t-81 -33t-87 -10q-59 0 -110.5 14.5t-79.5 24.5l35 86q33 -12 65.5 -21.5t67.5 -9.5q16 0 35.5 3.5t35.5 13.5q39 20 39 67q0 35 -28.5 56.5t-63.5 21.5q-45 0 -80 -14l-35 41 l129 168q-78 10 -134 49t-87 96q-23 41 -31 94.5t-8 116.5z" />
<glyph unicode="&#xe8;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41q72 0 130 -25t99 -68q82 -92 82 -231v-287h-414v-141q0 -49 28 -75.5t75 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -84 -221q-41 -43 -98 -67.5t-129 -24.5q-98 0 -167 41t-106 108q-23 41 -30.5 94.5 t-7.5 116.5zM106 1464h222l184 -288h-135zM276 604h205v119q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-119z" />
<glyph unicode="&#xe9;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41q72 0 130 -25t99 -68q82 -92 82 -231v-287h-414v-141q0 -49 28 -75.5t75 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -84 -221q-41 -43 -98 -67.5t-129 -24.5q-98 0 -167 41t-106 108q-23 41 -30.5 94.5 t-7.5 116.5zM246 1176l184 288h221l-270 -288h-135zM276 604h205v119q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-119z" />
<glyph unicode="&#xea;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41q72 0 130 -25t99 -68q82 -92 82 -231v-287h-414v-141q0 -49 28 -75.5t75 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -84 -221q-41 -43 -98 -67.5t-129 -24.5q-98 0 -167 41t-106 108q-23 41 -30.5 94.5 t-7.5 116.5zM82 1176l201 288h192l201 -288h-160l-137 194l-137 -194h-160zM276 604h205v119q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-119z" />
<glyph unicode="&#xeb;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41q72 0 130 -25t99 -68q82 -92 82 -231v-287h-414v-141q0 -49 28 -75.5t75 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -84 -221q-41 -43 -98 -67.5t-129 -24.5q-98 0 -167 41t-106 108q-23 41 -30.5 94.5 t-7.5 116.5zM121 1200v209h184v-209h-184zM276 604h205v119q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-119zM453 1200v209h184v-209h-184z" />
<glyph unicode="&#xec;" horiz-adv-x="380" d="M-82 1464h221l185 -288h-136zM86 0v1038h209v-1038h-209z" />
<glyph unicode="&#xed;" horiz-adv-x="380" d="M57 1176l185 288h221l-270 -288h-136zM86 0v1038h209v-1038h-209z" />
<glyph unicode="&#xee;" horiz-adv-x="380" d="M-106 1176l200 288h193l200 -288h-159l-138 194l-137 -194h-159zM86 0v1038h209v-1038h-209z" />
<glyph unicode="&#xef;" horiz-adv-x="380" d="M-68 1200v209h185v-209h-185zM86 0v1038h209v-1038h-209zM264 1200v209h185v-209h-185z" />
<glyph unicode="&#xf0;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41q29 0 55.5 -13.5t42.5 -33.5l4 4q-16 72 -38.5 128t-57.5 99l-152 -72l-57 82l141 66q-55 47 -157 88l170 90q96 -35 164 -96l139 65l57 -82l-129 -59q39 -51 63.5 -109.5t39.5 -122.5q14 -63 20 -126.5t6 -122.5 v-488q0 -63 -8 -116.5t-31 -94.5q-37 -68 -105.5 -108.5t-166.5 -40.5t-167 41t-106 108q-23 41 -30.5 94.5t-7.5 116.5zM276 315q0 -59 28 -88.5t75 -29.5t74.5 29.5t27.5 88.5v408q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-408z" />
<glyph unicode="&#xf1;" horiz-adv-x="796" d="M51 1194q16 100 64 160q25 31 59.5 49t83.5 18q25 0 67 -12t87 -29q45 -16 85 -28.5t56 -12.5q43 0 65.5 26t24.5 67h105q-10 -47 -24.5 -88t-39.5 -72t-59.5 -49.5t-83.5 -18.5q-25 0 -67 12.5t-87 28.5q-23 8 -43 14.5t-41 14.5q-41 12 -57 12q-43 0 -65.5 -25.5 t-24.5 -66.5h-105zM86 0v1038h209v-100h4q33 49 79 81t118 32q39 0 77.5 -13.5t69.5 -44.5t49.5 -79t18.5 -117v-797h-209v719q0 57 -26.5 90t-76.5 33q-59 0 -81.5 -37t-22.5 -107v-698h-209z" />
<glyph unicode="&#xf2;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41t166.5 -41t105.5 -109q23 -41 31 -94t8 -117v-342q0 -63 -8 -116.5t-31 -94.5q-37 -68 -105.5 -108.5t-166.5 -40.5t-167 41t-106 108q-23 41 -30.5 94.5t-7.5 116.5zM106 1464h222l184 -288h-135zM276 315 q0 -59 28 -88.5t75 -29.5t74.5 29.5t27.5 88.5v408q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-408z" />
<glyph unicode="&#xf3;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41t166.5 -41t105.5 -109q23 -41 31 -94t8 -117v-342q0 -63 -8 -116.5t-31 -94.5q-37 -68 -105.5 -108.5t-166.5 -40.5t-167 41t-106 108q-23 41 -30.5 94.5t-7.5 116.5zM246 1176l184 288h221l-270 -288h-135z M276 315q0 -59 28 -88.5t75 -29.5t74.5 29.5t27.5 88.5v408q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-408z" />
<glyph unicode="&#xf4;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41t166.5 -41t105.5 -109q23 -41 31 -94t8 -117v-342q0 -63 -8 -116.5t-31 -94.5q-37 -68 -105.5 -108.5t-166.5 -40.5t-167 41t-106 108q-23 41 -30.5 94.5t-7.5 116.5zM82 1176l201 288h192l201 -288h-160l-137 194 l-137 -194h-160zM276 315q0 -59 28 -88.5t75 -29.5t74.5 29.5t27.5 88.5v408q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-408z" />
<glyph unicode="&#xf5;" d="M31 1194q16 100 63 160q25 31 59.5 49t84.5 18q25 0 66.5 -12t86.5 -29q45 -16 85 -28.5t56 -12.5q43 0 66 26t25 67h104q-10 -47 -24.5 -88t-38.5 -72q-25 -31 -60 -49.5t-84 -18.5q-25 0 -66.5 12.5t-86.5 28.5q-23 8 -43.5 14.5t-40.5 14.5q-41 12 -58 12 q-43 0 -65.5 -25.5t-24.5 -66.5h-104zM68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41t166.5 -41t105.5 -109q23 -41 31 -94t8 -117v-342q0 -63 -8 -116.5t-31 -94.5q-37 -68 -105.5 -108.5t-166.5 -40.5t-167 41t-106 108q-23 41 -30.5 94.5t-7.5 116.5zM276 315 q0 -59 28 -88.5t75 -29.5t74.5 29.5t27.5 88.5v408q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-408z" />
<glyph unicode="&#xf6;" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41t166.5 -41t105.5 -109q23 -41 31 -94t8 -117v-342q0 -63 -8 -116.5t-31 -94.5q-37 -68 -105.5 -108.5t-166.5 -40.5t-167 41t-106 108q-23 41 -30.5 94.5t-7.5 116.5zM121 1200v209h184v-209h-184zM276 315 q0 -59 28 -88.5t75 -29.5t74.5 29.5t27.5 88.5v408q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-408zM453 1200v209h184v-209h-184z" />
<glyph unicode="&#xf7;" horiz-adv-x="1228" d="M96 436v164h1037v-164h-1037zM483 94q0 53 39 92t92 39t92 -39t39 -92t-38.5 -92t-92.5 -39q-53 0 -92 39t-39 92zM483 942q0 53 39 92t92 39t92 -39t39 -92t-38.5 -92t-92.5 -39q-53 0 -92 39t-39 92z" />
<glyph unicode="&#xf8;" d="M8 -25l101 170q-41 86 -41 179v391q0 74 21.5 135t62.5 106q41 43 98 69t129 26q133 0 217 -86l84 141l70 -43l-101 -170q41 -86 41 -178v-391q0 -152 -82 -240q-41 -45 -99 -70.5t-130 -25.5q-135 0 -217 86l-84 -142zM276 428h5l194 334q-12 80 -96 80q-47 0 -75 -30 t-28 -89v-295zM283 276q12 -80 96 -79q47 0 74.5 29.5t27.5 88.5v295h-4z" />
<glyph unicode="&#xf9;" horiz-adv-x="796" d="M86 242v796h209v-719q0 -57 26.5 -89.5t75.5 -32.5q59 0 82 36.5t23 106.5v698h209v-1038h-209v100h-4q-33 -49 -79 -80.5t-118 -31.5q-39 0 -78 13t-69 44q-31 31 -49.5 79t-18.5 118zM127 1464h221l184 -288h-135z" />
<glyph unicode="&#xfa;" horiz-adv-x="796" d="M86 242v796h209v-719q0 -57 26.5 -89.5t75.5 -32.5q59 0 82 36.5t23 106.5v698h209v-1038h-209v100h-4q-33 -49 -79 -80.5t-118 -31.5q-39 0 -78 13t-69 44q-31 31 -49.5 79t-18.5 118zM266 1176l185 288h221l-271 -288h-135z" />
<glyph unicode="&#xfb;" horiz-adv-x="796" d="M86 242v796h209v-719q0 -57 26.5 -89.5t75.5 -32.5q59 0 82 36.5t23 106.5v698h209v-1038h-209v100h-4q-33 -49 -79 -80.5t-118 -31.5q-39 0 -78 13t-69 44q-31 31 -49.5 79t-18.5 118zM102 1176l201 288h193l200 -288h-159l-138 194l-137 -194h-160z" />
<glyph unicode="&#xfc;" horiz-adv-x="796" d="M86 242v796h209v-719q0 -57 26.5 -89.5t75.5 -32.5q59 0 82 36.5t23 106.5v698h209v-1038h-209v100h-4q-33 -49 -79 -80.5t-118 -31.5q-39 0 -78 13t-69 44q-31 31 -49.5 79t-18.5 118zM141 1200v209h185v-209h-185zM473 1200v209h184v-209h-184z" />
<glyph unicode="&#xfd;" horiz-adv-x="720" d="M-2 1038h221l143 -647h5l141 647h215l-299 -1218q-16 -70 -46 -115t-69 -74t-83 -40t-91 -11h-57v197h47q31 0 53.5 8t40.5 37q16 29 30.5 81t33.5 144zM227 1176l185 288h221l-271 -288h-135z" />
<glyph unicode="&#xfe;" horiz-adv-x="796" d="M86 -420v1878h209v-514q39 45 80 76t119 31q129 0 182 -99q18 -35 26.5 -86t8.5 -137v-395q0 -115 -6 -166q-12 -102 -95 -154q-41 -27 -118 -26q-61 0 -109.5 27.5t-83.5 78.5h-4v-514h-209zM295 309q0 -49 29.5 -80.5t74.5 -31.5q59 0 81 36.5t22 85.5v381 q0 63 -24.5 102.5t-78.5 39.5q-47 0 -75.5 -36t-28.5 -93v-404z" />
<glyph unicode="&#xff;" horiz-adv-x="720" d="M-2 1038h221l143 -647h5l141 647h215l-299 -1218q-16 -70 -46 -115t-69 -74t-83 -40t-91 -11h-57v197h47q31 0 53.5 8t40.5 37q16 29 30.5 81t33.5 144zM102 1200v209h185v-209h-185zM434 1200v209h184v-209h-184z" />
<glyph unicode="&#x152;" horiz-adv-x="1249" d="M98 334v790q0 70 21.5 132.5t62.5 109.5t98.5 75.5t131.5 28.5q68 0 109.5 -25.5t64.5 -60.5h4v74h622v-196h-413v-428h360v-197h-360v-428h413v-209h-622v70h-4q-27 -35 -75 -58.5t-99 -23.5q-74 0 -131.5 27.5t-98.5 76.5q-41 47 -62.5 109.5t-21.5 132.5zM307 324 q0 -61 38 -106.5t104 -45.5t103.5 45t37.5 107v811q0 61 -38 106t-103 45q-66 0 -104 -45t-38 -106v-811z" />
<glyph unicode="&#x153;" horiz-adv-x="1171" d="M68 348v342q0 63 8 116.5t30 94.5q37 68 106 109t167 41q66 0 119 -20.5t88 -49.5q82 70 207 70q72 0 130 -25t99 -68q82 -92 82 -231v-287h-414v-141q0 -49 27.5 -75.5t75.5 -26.5q59 0 78.5 34.5t23.5 69.5h209q0 -137 -84 -221q-41 -43 -98.5 -67.5t-128.5 -24.5 q-57 0 -119 17.5t-96 53.5q-35 -33 -87.5 -52t-111.5 -19q-98 0 -167 41t-106 108q-23 41 -30.5 94.5t-7.5 116.5zM276 315q0 -59 28 -88.5t75 -29.5t74.5 29.5t27.5 88.5v408q0 59 -27.5 89t-74.5 30t-75 -30t-28 -89v-408zM690 604h205v119q0 59 -27.5 89t-74.5 30 t-75 -30t-28 -89v-119z" />
<glyph unicode="&#x178;" horiz-adv-x="794" d="M6 1458h221l168 -579h4l168 579h221l-286 -842v-616h-209v616zM139 1548v209h185v-209h-185zM471 1548v209h184v-209h-184z" />
<glyph unicode="&#x2c6;" horiz-adv-x="380" d="M-106 1176l200 288h193l200 -288h-159l-138 194l-137 -194h-159z" />
<glyph unicode="&#x2dc;" horiz-adv-x="380" d="M-158 1194q16 100 64 160q25 31 59.5 49t83.5 18q25 0 67 -12t87 -29q45 -16 85 -28.5t56 -12.5q43 0 65.5 26t24.5 67h105q-10 -47 -24.5 -88t-39.5 -72t-59.5 -49.5t-83.5 -18.5q-25 0 -67 12.5t-87 28.5q-23 8 -43 14.5t-41 14.5q-41 12 -57 12q-43 0 -65.5 -25.5 t-24.5 -66.5h-105z" />
<glyph unicode="&#x2000;" horiz-adv-x="959" />
<glyph unicode="&#x2001;" horiz-adv-x="1919" />
<glyph unicode="&#x2002;" horiz-adv-x="959" />
<glyph unicode="&#x2003;" horiz-adv-x="1919" />
<glyph unicode="&#x2004;" horiz-adv-x="639" />
<glyph unicode="&#x2005;" horiz-adv-x="479" />
<glyph unicode="&#x2006;" horiz-adv-x="319" />
<glyph unicode="&#x2007;" horiz-adv-x="319" />
<glyph unicode="&#x2008;" horiz-adv-x="239" />
<glyph unicode="&#x2009;" horiz-adv-x="383" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="796" d="M90 463v209h617v-209h-617z" />
<glyph unicode="&#x2011;" horiz-adv-x="796" d="M90 463v209h617v-209h-617z" />
<glyph unicode="&#x2012;" horiz-adv-x="796" d="M90 463v209h617v-209h-617z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 469v197h1024v-197h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 469v197h2048v-197h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="380" d="M86 1098v209l209 151v-360h-209z" />
<glyph unicode="&#x2019;" horiz-adv-x="380" d="M86 1098v360h209v-209z" />
<glyph unicode="&#x201a;" horiz-adv-x="380" d="M86 -152v361h209v-209z" />
<glyph unicode="&#x201c;" horiz-adv-x="569" d="M37 1098v209l209 151v-360h-209zM324 1098v209l208 151v-360h-208z" />
<glyph unicode="&#x201d;" horiz-adv-x="569" d="M37 1098v360h209v-209zM324 1098v360h208v-209z" />
<glyph unicode="&#x201e;" horiz-adv-x="569" d="M37 -152v361h209v-209zM324 -152v361h208v-209z" />
<glyph unicode="&#x2022;" horiz-adv-x="1024" d="M147 729q0 76 29 142.5t78 115.5t115.5 78t142.5 29t142.5 -29t115.5 -78t78 -115.5t29 -142.5t-29 -142.5t-78 -115.5t-115.5 -77.5t-142.5 -28.5t-142.5 28.5t-115.5 77.5t-78 115.5t-29 142.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="2048" d="M238 0v209h208v-209h-208zM920 0v209h208v-209h-208zM1602 0v209h208v-209h-208z" />
<glyph unicode="&#x202f;" horiz-adv-x="383" />
<glyph unicode="&#x2039;" horiz-adv-x="493" d="M20 567l416 414v-209l-207 -205l207 -205v-208z" />
<glyph unicode="&#x203a;" horiz-adv-x="493" d="M57 154v208l207 205l-207 205v209l416 -414z" />
<glyph unicode="&#x205f;" horiz-adv-x="479" />
<glyph unicode="&#x20ac;" horiz-adv-x="761" d="M33 502l41 162h65q-2 10 -2 32v37v33v16.5t2 18.5h-106l41 160h78q20 262 109 384.5t245 122.5q74 0 123 -15t69 -32v-194q-23 23 -58.5 37t-96.5 14q-86 0 -131 -76t-56 -241h293l-41 -160h-262v-68v-69h227l-41 -162h-176q10 -174 57.5 -249t129.5 -75q61 0 97 13.5 t58 35.5v-198q-25 -18 -67 -27q-47 -12 -127 -12q-147 0 -236.5 117.5t-111.5 394.5h-123z" />
<glyph unicode="&#x2122;" horiz-adv-x="2027" d="M80 1323v135h668v-135h-261v-709h-147v709h-260zM850 614v844h233l240 -616l240 616h233v-844h-147v709h-4l-281 -709h-84l-279 709h-4v-709h-147z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1034" d="M0 0v1034h1034v-1034h-1034z" />
<glyph unicode="&#xfb01;" horiz-adv-x="833" d="M10 874v164h103v142q0 76 22.5 128t61.5 87q37 35 85 49t99 14h96v-196h-94q-61 0 -61 -72v-152h155v-164h-155v-874h-209v874h-103zM539 0v1038h209v-1038h-209zM539 1249v209h209v-209h-209z" />
<glyph unicode="&#xfb02;" horiz-adv-x="833" d="M10 874v164h103v142q0 76 22.5 128t61.5 87q37 35 85 49t99 14h96v-196h-94q-61 0 -61 -72v-152h155v-164h-155v-874h-209v874h-103zM539 279v1179h209v-1165q0 -59 21.5 -81t80.5 -28v-196q-72 0 -129 11t-98 44q-41 31 -62.5 87t-21.5 149z" />
<hkern u1="A" u2="&#x201d;" k="113" />
<hkern u1="A" u2="&#x2019;" k="113" />
<hkern u1="A" u2="&#x178;" k="113" />
<hkern u1="A" u2="&#xff;" k="37" />
<hkern u1="A" u2="&#xfd;" k="37" />
<hkern u1="A" u2="&#xdd;" k="113" />
<hkern u1="A" u2="y" k="37" />
<hkern u1="A" u2="w" k="37" />
<hkern u1="A" u2="v" k="37" />
<hkern u1="A" u2="Y" k="113" />
<hkern u1="A" u2="W" k="76" />
<hkern u1="A" u2="V" k="76" />
<hkern u1="A" u2="T" k="113" />
<hkern u1="B" u2="&#xc6;" k="25" />
<hkern u1="B" u2="&#xc5;" k="25" />
<hkern u1="B" u2="&#xc4;" k="25" />
<hkern u1="B" u2="&#xc3;" k="25" />
<hkern u1="B" u2="&#xc2;" k="25" />
<hkern u1="B" u2="&#xc1;" k="25" />
<hkern u1="B" u2="&#xc0;" k="25" />
<hkern u1="B" u2="A" k="25" />
<hkern u1="D" u2="&#x178;" k="37" />
<hkern u1="D" u2="&#xdd;" k="37" />
<hkern u1="D" u2="&#xc6;" k="25" />
<hkern u1="D" u2="&#xc5;" k="25" />
<hkern u1="D" u2="&#xc4;" k="25" />
<hkern u1="D" u2="&#xc3;" k="25" />
<hkern u1="D" u2="&#xc2;" k="25" />
<hkern u1="D" u2="&#xc1;" k="25" />
<hkern u1="D" u2="&#xc0;" k="25" />
<hkern u1="D" u2="Y" k="37" />
<hkern u1="D" u2="W" k="25" />
<hkern u1="D" u2="V" k="37" />
<hkern u1="D" u2="A" k="25" />
<hkern u1="F" u2="&#x153;" k="37" />
<hkern u1="F" u2="&#xf8;" k="37" />
<hkern u1="F" u2="&#xf6;" k="37" />
<hkern u1="F" u2="&#xf5;" k="37" />
<hkern u1="F" u2="&#xf4;" k="37" />
<hkern u1="F" u2="&#xf3;" k="37" />
<hkern u1="F" u2="&#xf2;" k="37" />
<hkern u1="F" u2="&#xef;" k="25" />
<hkern u1="F" u2="&#xee;" k="25" />
<hkern u1="F" u2="&#xed;" k="25" />
<hkern u1="F" u2="&#xec;" k="25" />
<hkern u1="F" u2="&#xeb;" k="29" />
<hkern u1="F" u2="&#xea;" k="37" />
<hkern u1="F" u2="&#xe9;" k="37" />
<hkern u1="F" u2="&#xe8;" k="37" />
<hkern u1="F" u2="&#xc6;" k="102" />
<hkern u1="F" u2="&#xc5;" k="102" />
<hkern u1="F" u2="&#xc4;" k="102" />
<hkern u1="F" u2="&#xc3;" k="102" />
<hkern u1="F" u2="&#xc2;" k="102" />
<hkern u1="F" u2="&#xc1;" k="102" />
<hkern u1="F" u2="&#xc0;" k="102" />
<hkern u1="F" u2="r" k="37" />
<hkern u1="F" u2="o" k="37" />
<hkern u1="F" u2="i" k="25" />
<hkern u1="F" u2="e" k="37" />
<hkern u1="F" u2="A" k="102" />
<hkern u1="F" u2="&#x2e;" k="227" />
<hkern u1="F" u2="&#x2c;" k="227" />
<hkern u1="J" u2="&#x153;" k="12" />
<hkern u1="J" u2="&#xfc;" k="12" />
<hkern u1="J" u2="&#xfb;" k="12" />
<hkern u1="J" u2="&#xfa;" k="12" />
<hkern u1="J" u2="&#xf9;" k="12" />
<hkern u1="J" u2="&#xf8;" k="12" />
<hkern u1="J" u2="&#xf6;" k="12" />
<hkern u1="J" u2="&#xf5;" k="12" />
<hkern u1="J" u2="&#xf4;" k="12" />
<hkern u1="J" u2="&#xf3;" k="12" />
<hkern u1="J" u2="&#xf2;" k="12" />
<hkern u1="J" u2="&#xeb;" k="12" />
<hkern u1="J" u2="&#xea;" k="12" />
<hkern u1="J" u2="&#xe9;" k="12" />
<hkern u1="J" u2="&#xe8;" k="12" />
<hkern u1="J" u2="&#xe6;" k="12" />
<hkern u1="J" u2="&#xe5;" k="12" />
<hkern u1="J" u2="&#xe4;" k="12" />
<hkern u1="J" u2="&#xe3;" k="12" />
<hkern u1="J" u2="&#xe2;" k="12" />
<hkern u1="J" u2="&#xe1;" k="12" />
<hkern u1="J" u2="&#xe0;" k="12" />
<hkern u1="J" u2="u" k="12" />
<hkern u1="J" u2="o" k="12" />
<hkern u1="J" u2="e" k="12" />
<hkern u1="J" u2="a" k="12" />
<hkern u1="J" u2="&#x2e;" k="37" />
<hkern u1="J" u2="&#x2c;" k="37" />
<hkern u1="K" u2="&#x153;" k="25" />
<hkern u1="K" u2="&#x152;" k="25" />
<hkern u1="K" u2="&#xff;" k="37" />
<hkern u1="K" u2="&#xfd;" k="37" />
<hkern u1="K" u2="&#xfc;" k="25" />
<hkern u1="K" u2="&#xfb;" k="25" />
<hkern u1="K" u2="&#xfa;" k="25" />
<hkern u1="K" u2="&#xf9;" k="25" />
<hkern u1="K" u2="&#xf8;" k="25" />
<hkern u1="K" u2="&#xf6;" k="25" />
<hkern u1="K" u2="&#xf5;" k="25" />
<hkern u1="K" u2="&#xf4;" k="25" />
<hkern u1="K" u2="&#xf3;" k="25" />
<hkern u1="K" u2="&#xf2;" k="25" />
<hkern u1="K" u2="&#xeb;" k="25" />
<hkern u1="K" u2="&#xea;" k="25" />
<hkern u1="K" u2="&#xe9;" k="25" />
<hkern u1="K" u2="&#xe8;" k="25" />
<hkern u1="K" u2="&#xd8;" k="25" />
<hkern u1="K" u2="&#xd6;" k="25" />
<hkern u1="K" u2="&#xd5;" k="25" />
<hkern u1="K" u2="&#xd4;" k="25" />
<hkern u1="K" u2="&#xd3;" k="25" />
<hkern u1="K" u2="&#xd2;" k="25" />
<hkern u1="K" u2="y" k="37" />
<hkern u1="K" u2="u" k="25" />
<hkern u1="K" u2="o" k="25" />
<hkern u1="K" u2="e" k="25" />
<hkern u1="K" u2="O" k="25" />
<hkern u1="L" u2="&#x201d;" k="154" />
<hkern u1="L" u2="&#x2019;" k="188" />
<hkern u1="L" u2="&#x178;" k="188" />
<hkern u1="L" u2="&#xff;" k="113" />
<hkern u1="L" u2="&#xfd;" k="113" />
<hkern u1="L" u2="&#xdd;" k="188" />
<hkern u1="L" u2="y" k="113" />
<hkern u1="L" u2="Y" k="188" />
<hkern u1="L" u2="W" k="152" />
<hkern u1="L" u2="V" k="152" />
<hkern u1="L" u2="T" k="152" />
<hkern u1="O" u2="&#x178;" k="37" />
<hkern u1="O" u2="&#xdd;" k="37" />
<hkern u1="O" u2="&#xc6;" k="25" />
<hkern u1="O" u2="&#xc5;" k="25" />
<hkern u1="O" u2="&#xc4;" k="25" />
<hkern u1="O" u2="&#xc3;" k="25" />
<hkern u1="O" u2="&#xc2;" k="25" />
<hkern u1="O" u2="&#xc1;" k="25" />
<hkern u1="O" u2="&#xc0;" k="25" />
<hkern u1="O" u2="Y" k="37" />
<hkern u1="O" u2="W" k="25" />
<hkern u1="O" u2="V" k="37" />
<hkern u1="O" u2="T" k="25" />
<hkern u1="O" u2="A" k="25" />
<hkern u1="P" u2="&#x153;" k="25" />
<hkern u1="P" u2="&#xf8;" k="25" />
<hkern u1="P" u2="&#xf6;" k="25" />
<hkern u1="P" u2="&#xf5;" k="25" />
<hkern u1="P" u2="&#xf4;" k="25" />
<hkern u1="P" u2="&#xf3;" k="25" />
<hkern u1="P" u2="&#xf2;" k="25" />
<hkern u1="P" u2="&#xeb;" k="25" />
<hkern u1="P" u2="&#xea;" k="25" />
<hkern u1="P" u2="&#xe9;" k="25" />
<hkern u1="P" u2="&#xe8;" k="25" />
<hkern u1="P" u2="&#xe6;" k="25" />
<hkern u1="P" u2="&#xe5;" k="25" />
<hkern u1="P" u2="&#xe4;" k="12" />
<hkern u1="P" u2="&#xe3;" k="25" />
<hkern u1="P" u2="&#xe2;" k="25" />
<hkern u1="P" u2="&#xe1;" k="25" />
<hkern u1="P" u2="&#xe0;" k="25" />
<hkern u1="P" u2="&#xc6;" k="76" />
<hkern u1="P" u2="&#xc5;" k="76" />
<hkern u1="P" u2="&#xc4;" k="76" />
<hkern u1="P" u2="&#xc3;" k="76" />
<hkern u1="P" u2="&#xc2;" k="76" />
<hkern u1="P" u2="&#xc1;" k="76" />
<hkern u1="P" u2="&#xc0;" k="76" />
<hkern u1="P" u2="o" k="25" />
<hkern u1="P" u2="e" k="25" />
<hkern u1="P" u2="a" k="25" />
<hkern u1="P" u2="A" k="76" />
<hkern u1="P" u2="&#x2e;" k="264" />
<hkern u1="P" u2="&#x2c;" k="264" />
<hkern u1="R" u2="&#x178;" k="37" />
<hkern u1="R" u2="&#xdd;" k="37" />
<hkern u1="R" u2="Y" k="37" />
<hkern u1="R" u2="T" k="37" />
<hkern u1="T" u2="&#x153;" k="127" />
<hkern u1="T" u2="&#x152;" k="25" />
<hkern u1="T" u2="&#xff;" k="113" />
<hkern u1="T" u2="&#xfd;" k="113" />
<hkern u1="T" u2="&#xfc;" k="63" />
<hkern u1="T" u2="&#xfb;" k="127" />
<hkern u1="T" u2="&#xfa;" k="127" />
<hkern u1="T" u2="&#xf9;" k="127" />
<hkern u1="T" u2="&#xf8;" k="127" />
<hkern u1="T" u2="&#xf6;" k="63" />
<hkern u1="T" u2="&#xf5;" k="127" />
<hkern u1="T" u2="&#xf4;" k="127" />
<hkern u1="T" u2="&#xf3;" k="127" />
<hkern u1="T" u2="&#xf2;" k="63" />
<hkern u1="T" u2="&#xef;" k="37" />
<hkern u1="T" u2="&#xee;" k="-8" />
<hkern u1="T" u2="&#xed;" k="18" />
<hkern u1="T" u2="&#xec;" k="-18" />
<hkern u1="T" u2="&#xeb;" k="63" />
<hkern u1="T" u2="&#xea;" k="96" />
<hkern u1="T" u2="&#xe9;" k="127" />
<hkern u1="T" u2="&#xe8;" k="96" />
<hkern u1="T" u2="&#xe6;" k="127" />
<hkern u1="T" u2="&#xe5;" k="63" />
<hkern u1="T" u2="&#xe4;" k="33" />
<hkern u1="T" u2="&#xe3;" k="63" />
<hkern u1="T" u2="&#xe2;" k="63" />
<hkern u1="T" u2="&#xe1;" k="127" />
<hkern u1="T" u2="&#xe0;" k="63" />
<hkern u1="T" u2="&#xd8;" k="25" />
<hkern u1="T" u2="&#xd6;" k="25" />
<hkern u1="T" u2="&#xd5;" k="25" />
<hkern u1="T" u2="&#xd4;" k="25" />
<hkern u1="T" u2="&#xd3;" k="25" />
<hkern u1="T" u2="&#xd2;" k="25" />
<hkern u1="T" u2="&#xc6;" k="113" />
<hkern u1="T" u2="&#xc5;" k="113" />
<hkern u1="T" u2="&#xc4;" k="113" />
<hkern u1="T" u2="&#xc3;" k="113" />
<hkern u1="T" u2="&#xc2;" k="113" />
<hkern u1="T" u2="&#xc1;" k="113" />
<hkern u1="T" u2="&#xc0;" k="113" />
<hkern u1="T" u2="y" k="113" />
<hkern u1="T" u2="w" k="152" />
<hkern u1="T" u2="u" k="127" />
<hkern u1="T" u2="r" k="127" />
<hkern u1="T" u2="o" k="127" />
<hkern u1="T" u2="i" k="37" />
<hkern u1="T" u2="e" k="127" />
<hkern u1="T" u2="a" k="127" />
<hkern u1="T" u2="O" k="25" />
<hkern u1="T" u2="A" k="113" />
<hkern u1="T" u2="&#x3b;" k="127" />
<hkern u1="T" u2="&#x3a;" k="123" />
<hkern u1="T" u2="&#x2e;" k="188" />
<hkern u1="T" u2="&#x2d;" k="127" />
<hkern u1="T" u2="&#x2c;" k="188" />
<hkern u1="U" u2="&#xc6;" k="25" />
<hkern u1="U" u2="&#xc5;" k="25" />
<hkern u1="U" u2="&#xc4;" k="25" />
<hkern u1="U" u2="&#xc3;" k="25" />
<hkern u1="U" u2="&#xc2;" k="25" />
<hkern u1="U" u2="&#xc1;" k="25" />
<hkern u1="U" u2="&#xc0;" k="25" />
<hkern u1="U" u2="A" k="25" />
<hkern u1="V" u2="&#x153;" k="76" />
<hkern u1="V" u2="&#xfc;" k="61" />
<hkern u1="V" u2="&#xfb;" k="61" />
<hkern u1="V" u2="&#xfa;" k="61" />
<hkern u1="V" u2="&#xf9;" k="61" />
<hkern u1="V" u2="&#xf8;" k="76" />
<hkern u1="V" u2="&#xf6;" k="39" />
<hkern u1="V" u2="&#xf5;" k="76" />
<hkern u1="V" u2="&#xf4;" k="57" />
<hkern u1="V" u2="&#xf3;" k="76" />
<hkern u1="V" u2="&#xf2;" k="76" />
<hkern u1="V" u2="&#xeb;" k="39" />
<hkern u1="V" u2="&#xea;" k="39" />
<hkern u1="V" u2="&#xe9;" k="76" />
<hkern u1="V" u2="&#xe8;" k="39" />
<hkern u1="V" u2="&#xe6;" k="76" />
<hkern u1="V" u2="&#xe5;" k="39" />
<hkern u1="V" u2="&#xe4;" k="39" />
<hkern u1="V" u2="&#xe3;" k="39" />
<hkern u1="V" u2="&#xe2;" k="39" />
<hkern u1="V" u2="&#xe1;" k="76" />
<hkern u1="V" u2="&#xe0;" k="39" />
<hkern u1="V" u2="&#xc6;" k="76" />
<hkern u1="V" u2="&#xc5;" k="76" />
<hkern u1="V" u2="&#xc4;" k="76" />
<hkern u1="V" u2="&#xc3;" k="76" />
<hkern u1="V" u2="&#xc2;" k="76" />
<hkern u1="V" u2="&#xc1;" k="76" />
<hkern u1="V" u2="&#xc0;" k="76" />
<hkern u1="V" u2="u" k="61" />
<hkern u1="V" u2="o" k="76" />
<hkern u1="V" u2="e" k="76" />
<hkern u1="V" u2="a" k="76" />
<hkern u1="V" u2="A" k="76" />
<hkern u1="V" u2="&#x3b;" k="37" />
<hkern u1="V" u2="&#x3a;" k="37" />
<hkern u1="V" u2="&#x2e;" k="188" />
<hkern u1="V" u2="&#x2d;" k="76" />
<hkern u1="V" u2="&#x2c;" k="188" />
<hkern u1="W" u2="&#x153;" k="37" />
<hkern u1="W" u2="&#x152;" k="25" />
<hkern u1="W" u2="&#xff;" k="25" />
<hkern u1="W" u2="&#xfd;" k="25" />
<hkern u1="W" u2="&#xfc;" k="29" />
<hkern u1="W" u2="&#xfb;" k="37" />
<hkern u1="W" u2="&#xfa;" k="37" />
<hkern u1="W" u2="&#xf9;" k="37" />
<hkern u1="W" u2="&#xf8;" k="37" />
<hkern u1="W" u2="&#xf6;" k="29" />
<hkern u1="W" u2="&#xf5;" k="37" />
<hkern u1="W" u2="&#xf4;" k="37" />
<hkern u1="W" u2="&#xf3;" k="37" />
<hkern u1="W" u2="&#xf2;" k="37" />
<hkern u1="W" u2="&#xeb;" k="37" />
<hkern u1="W" u2="&#xea;" k="29" />
<hkern u1="W" u2="&#xe9;" k="37" />
<hkern u1="W" u2="&#xe8;" k="37" />
<hkern u1="W" u2="&#xe6;" k="37" />
<hkern u1="W" u2="&#xe5;" k="37" />
<hkern u1="W" u2="&#xe4;" k="29" />
<hkern u1="W" u2="&#xe3;" k="37" />
<hkern u1="W" u2="&#xe2;" k="37" />
<hkern u1="W" u2="&#xe1;" k="37" />
<hkern u1="W" u2="&#xe0;" k="37" />
<hkern u1="W" u2="&#xd8;" k="25" />
<hkern u1="W" u2="&#xd6;" k="25" />
<hkern u1="W" u2="&#xd5;" k="25" />
<hkern u1="W" u2="&#xd4;" k="25" />
<hkern u1="W" u2="&#xd3;" k="25" />
<hkern u1="W" u2="&#xd2;" k="25" />
<hkern u1="W" u2="&#xc6;" k="61" />
<hkern u1="W" u2="&#xc5;" k="61" />
<hkern u1="W" u2="&#xc4;" k="61" />
<hkern u1="W" u2="&#xc3;" k="61" />
<hkern u1="W" u2="&#xc2;" k="61" />
<hkern u1="W" u2="&#xc1;" k="61" />
<hkern u1="W" u2="&#xc0;" k="61" />
<hkern u1="W" u2="y" k="25" />
<hkern u1="W" u2="u" k="37" />
<hkern u1="W" u2="o" k="37" />
<hkern u1="W" u2="e" k="37" />
<hkern u1="W" u2="a" k="37" />
<hkern u1="W" u2="O" k="25" />
<hkern u1="W" u2="A" k="61" />
<hkern u1="W" u2="&#x3b;" k="37" />
<hkern u1="W" u2="&#x3a;" k="37" />
<hkern u1="W" u2="&#x2e;" k="152" />
<hkern u1="W" u2="&#x2d;" k="37" />
<hkern u1="W" u2="&#x2c;" k="152" />
<hkern u1="Y" u2="&#x153;" k="113" />
<hkern u1="Y" u2="&#xfc;" k="84" />
<hkern u1="Y" u2="&#xfb;" k="113" />
<hkern u1="Y" u2="&#xfa;" k="113" />
<hkern u1="Y" u2="&#xf9;" k="113" />
<hkern u1="Y" u2="&#xf8;" k="113" />
<hkern u1="Y" u2="&#xf6;" k="84" />
<hkern u1="Y" u2="&#xf5;" k="113" />
<hkern u1="Y" u2="&#xf4;" k="113" />
<hkern u1="Y" u2="&#xf3;" k="113" />
<hkern u1="Y" u2="&#xf2;" k="113" />
<hkern u1="Y" u2="&#xef;" k="37" />
<hkern u1="Y" u2="&#xee;" k="37" />
<hkern u1="Y" u2="&#xed;" k="37" />
<hkern u1="Y" u2="&#xec;" k="37" />
<hkern u1="Y" u2="&#xeb;" k="113" />
<hkern u1="Y" u2="&#xea;" k="113" />
<hkern u1="Y" u2="&#xe9;" k="113" />
<hkern u1="Y" u2="&#xe8;" k="113" />
<hkern u1="Y" u2="&#xe6;" k="113" />
<hkern u1="Y" u2="&#xe5;" k="113" />
<hkern u1="Y" u2="&#xe4;" k="113" />
<hkern u1="Y" u2="&#xe3;" k="113" />
<hkern u1="Y" u2="&#xe2;" k="113" />
<hkern u1="Y" u2="&#xe1;" k="113" />
<hkern u1="Y" u2="&#xe0;" k="113" />
<hkern u1="Y" u2="&#xc6;" k="113" />
<hkern u1="Y" u2="&#xc5;" k="113" />
<hkern u1="Y" u2="&#xc4;" k="113" />
<hkern u1="Y" u2="&#xc3;" k="113" />
<hkern u1="Y" u2="&#xc2;" k="113" />
<hkern u1="Y" u2="&#xc1;" k="113" />
<hkern u1="Y" u2="&#xc0;" k="113" />
<hkern u1="Y" u2="u" k="113" />
<hkern u1="Y" u2="o" k="113" />
<hkern u1="Y" u2="i" k="37" />
<hkern u1="Y" u2="e" k="113" />
<hkern u1="Y" u2="a" k="113" />
<hkern u1="Y" u2="A" k="113" />
<hkern u1="Y" u2="&#x3b;" k="113" />
<hkern u1="Y" u2="&#x3a;" k="76" />
<hkern u1="Y" u2="&#x2e;" k="188" />
<hkern u1="Y" u2="&#x2d;" k="152" />
<hkern u1="Y" u2="&#x2c;" k="188" />
<hkern u1="a" u2="&#xff;" k="25" />
<hkern u1="a" u2="&#xfd;" k="25" />
<hkern u1="a" u2="y" k="25" />
<hkern u1="a" u2="w" k="12" />
<hkern u1="a" u2="v" k="12" />
<hkern u1="b" u2="&#xff;" k="25" />
<hkern u1="b" u2="&#xfd;" k="25" />
<hkern u1="b" u2="y" k="25" />
<hkern u1="b" u2="v" k="12" />
<hkern u1="c" u2="&#xff;" k="37" />
<hkern u1="c" u2="&#xfd;" k="37" />
<hkern u1="c" u2="y" k="37" />
<hkern u1="c" u2="l" k="25" />
<hkern u1="c" u2="k" k="25" />
<hkern u1="c" u2="h" k="25" />
<hkern u1="e" u2="&#xff;" k="25" />
<hkern u1="e" u2="&#xfd;" k="25" />
<hkern u1="e" u2="y" k="25" />
<hkern u1="e" u2="v" k="25" />
<hkern u1="f" u2="&#x201d;" k="-25" />
<hkern u1="f" u2="&#x2019;" k="-37" />
<hkern u1="k" u2="&#x153;" k="25" />
<hkern u1="k" u2="&#xf8;" k="25" />
<hkern u1="k" u2="&#xf6;" k="25" />
<hkern u1="k" u2="&#xf5;" k="25" />
<hkern u1="k" u2="&#xf4;" k="25" />
<hkern u1="k" u2="&#xf3;" k="25" />
<hkern u1="k" u2="&#xf2;" k="25" />
<hkern u1="k" u2="&#xeb;" k="25" />
<hkern u1="k" u2="&#xea;" k="25" />
<hkern u1="k" u2="&#xe9;" k="25" />
<hkern u1="k" u2="&#xe8;" k="25" />
<hkern u1="k" u2="o" k="25" />
<hkern u1="k" u2="e" k="25" />
<hkern u1="o" u2="&#xff;" k="25" />
<hkern u1="o" u2="&#xfd;" k="25" />
<hkern u1="o" u2="y" k="25" />
<hkern u1="o" u2="w" k="12" />
<hkern u1="o" u2="v" k="12" />
<hkern u1="p" u2="&#xff;" k="25" />
<hkern u1="p" u2="&#xfd;" k="25" />
<hkern u1="p" u2="y" k="25" />
<hkern u1="r" u2="&#x153;" k="12" />
<hkern u1="r" u2="&#xff;" k="-37" />
<hkern u1="r" u2="&#xfd;" k="-37" />
<hkern u1="r" u2="&#xf8;" k="12" />
<hkern u1="r" u2="&#xf6;" k="12" />
<hkern u1="r" u2="&#xf5;" k="12" />
<hkern u1="r" u2="&#xf4;" k="12" />
<hkern u1="r" u2="&#xf3;" k="12" />
<hkern u1="r" u2="&#xf2;" k="12" />
<hkern u1="r" u2="&#xe7;" k="25" />
<hkern u1="r" u2="&#xe6;" k="12" />
<hkern u1="r" u2="&#xe5;" k="12" />
<hkern u1="r" u2="&#xe4;" k="12" />
<hkern u1="r" u2="&#xe3;" k="12" />
<hkern u1="r" u2="&#xe2;" k="12" />
<hkern u1="r" u2="&#xe1;" k="12" />
<hkern u1="r" u2="&#xe0;" k="12" />
<hkern u1="r" u2="y" k="-37" />
<hkern u1="r" u2="v" k="-37" />
<hkern u1="r" u2="o" k="12" />
<hkern u1="r" u2="c" k="25" />
<hkern u1="r" u2="a" k="12" />
<hkern u1="r" u2="&#x2e;" k="152" />
<hkern u1="r" u2="&#x2d;" k="76" />
<hkern u1="r" u2="&#x2c;" k="152" />
<hkern u1="s" u2="w" k="25" />
<hkern u1="v" u2="&#x153;" k="12" />
<hkern u1="v" u2="&#xf8;" k="12" />
<hkern u1="v" u2="&#xf6;" k="12" />
<hkern u1="v" u2="&#xf5;" k="12" />
<hkern u1="v" u2="&#xf4;" k="12" />
<hkern u1="v" u2="&#xf3;" k="12" />
<hkern u1="v" u2="&#xf2;" k="12" />
<hkern u1="v" u2="&#xeb;" k="12" />
<hkern u1="v" u2="&#xea;" k="12" />
<hkern u1="v" u2="&#xe9;" k="12" />
<hkern u1="v" u2="&#xe8;" k="12" />
<hkern u1="v" u2="&#xe6;" k="12" />
<hkern u1="v" u2="&#xe5;" k="12" />
<hkern u1="v" u2="&#xe4;" k="12" />
<hkern u1="v" u2="&#xe3;" k="12" />
<hkern u1="v" u2="&#xe2;" k="12" />
<hkern u1="v" u2="&#xe1;" k="12" />
<hkern u1="v" u2="&#xe0;" k="12" />
<hkern u1="v" u2="o" k="12" />
<hkern u1="v" u2="e" k="12" />
<hkern u1="v" u2="a" k="12" />
<hkern u1="v" u2="&#x2e;" k="113" />
<hkern u1="v" u2="&#x2c;" k="113" />
<hkern u1="w" u2="&#x153;" k="12" />
<hkern u1="w" u2="&#xf8;" k="12" />
<hkern u1="w" u2="&#xf6;" k="12" />
<hkern u1="w" u2="&#xf5;" k="12" />
<hkern u1="w" u2="&#xf4;" k="12" />
<hkern u1="w" u2="&#xf3;" k="12" />
<hkern u1="w" u2="&#xf2;" k="12" />
<hkern u1="w" u2="&#xeb;" k="12" />
<hkern u1="w" u2="&#xea;" k="12" />
<hkern u1="w" u2="&#xe9;" k="12" />
<hkern u1="w" u2="&#xe8;" k="12" />
<hkern u1="w" u2="&#xe6;" k="12" />
<hkern u1="w" u2="&#xe5;" k="12" />
<hkern u1="w" u2="&#xe4;" k="12" />
<hkern u1="w" u2="&#xe3;" k="12" />
<hkern u1="w" u2="&#xe2;" k="12" />
<hkern u1="w" u2="&#xe1;" k="12" />
<hkern u1="w" u2="&#xe0;" k="12" />
<hkern u1="w" u2="o" k="12" />
<hkern u1="w" u2="e" k="12" />
<hkern u1="w" u2="a" k="12" />
<hkern u1="w" u2="&#x2e;" k="76" />
<hkern u1="w" u2="&#x2c;" k="76" />
<hkern u1="y" u2="&#x153;" k="12" />
<hkern u1="y" u2="&#xf8;" k="12" />
<hkern u1="y" u2="&#xf6;" k="12" />
<hkern u1="y" u2="&#xf5;" k="12" />
<hkern u1="y" u2="&#xf4;" k="12" />
<hkern u1="y" u2="&#xf3;" k="12" />
<hkern u1="y" u2="&#xf2;" k="12" />
<hkern u1="y" u2="&#xeb;" k="12" />
<hkern u1="y" u2="&#xea;" k="12" />
<hkern u1="y" u2="&#xe9;" k="12" />
<hkern u1="y" u2="&#xe8;" k="12" />
<hkern u1="y" u2="&#xe6;" k="12" />
<hkern u1="y" u2="&#xe5;" k="12" />
<hkern u1="y" u2="&#xe4;" k="12" />
<hkern u1="y" u2="&#xe3;" k="12" />
<hkern u1="y" u2="&#xe2;" k="12" />
<hkern u1="y" u2="&#xe1;" k="12" />
<hkern u1="y" u2="&#xe0;" k="12" />
<hkern u1="y" u2="o" k="12" />
<hkern u1="y" u2="e" k="12" />
<hkern u1="y" u2="a" k="12" />
<hkern u1="y" u2="&#x2e;" k="113" />
<hkern u1="y" u2="&#x2c;" k="113" />
<hkern u1="z" u2="&#x153;" k="12" />
<hkern u1="z" u2="&#xf8;" k="12" />
<hkern u1="z" u2="&#xf6;" k="12" />
<hkern u1="z" u2="&#xf5;" k="12" />
<hkern u1="z" u2="&#xf4;" k="12" />
<hkern u1="z" u2="&#xf3;" k="12" />
<hkern u1="z" u2="&#xf2;" k="12" />
<hkern u1="z" u2="&#xeb;" k="12" />
<hkern u1="z" u2="&#xea;" k="12" />
<hkern u1="z" u2="&#xe9;" k="12" />
<hkern u1="z" u2="&#xe8;" k="12" />
<hkern u1="z" u2="o" k="12" />
<hkern u1="z" u2="e" k="12" />
<hkern u1="&#xc0;" u2="&#x201d;" k="113" />
<hkern u1="&#xc0;" u2="&#x2019;" k="113" />
<hkern u1="&#xc0;" u2="&#x178;" k="113" />
<hkern u1="&#xc0;" u2="&#xff;" k="37" />
<hkern u1="&#xc0;" u2="&#xfd;" k="37" />
<hkern u1="&#xc0;" u2="&#xdd;" k="113" />
<hkern u1="&#xc0;" u2="y" k="37" />
<hkern u1="&#xc0;" u2="w" k="37" />
<hkern u1="&#xc0;" u2="v" k="37" />
<hkern u1="&#xc0;" u2="Y" k="113" />
<hkern u1="&#xc0;" u2="W" k="76" />
<hkern u1="&#xc0;" u2="V" k="76" />
<hkern u1="&#xc0;" u2="T" k="113" />
<hkern u1="&#xc1;" u2="&#x201d;" k="113" />
<hkern u1="&#xc1;" u2="&#x2019;" k="113" />
<hkern u1="&#xc1;" u2="&#x178;" k="113" />
<hkern u1="&#xc1;" u2="&#xff;" k="37" />
<hkern u1="&#xc1;" u2="&#xfd;" k="37" />
<hkern u1="&#xc1;" u2="&#xdd;" k="113" />
<hkern u1="&#xc1;" u2="y" k="37" />
<hkern u1="&#xc1;" u2="w" k="37" />
<hkern u1="&#xc1;" u2="v" k="37" />
<hkern u1="&#xc1;" u2="Y" k="113" />
<hkern u1="&#xc1;" u2="W" k="76" />
<hkern u1="&#xc1;" u2="V" k="76" />
<hkern u1="&#xc1;" u2="T" k="113" />
<hkern u1="&#xc2;" u2="&#x201d;" k="113" />
<hkern u1="&#xc2;" u2="&#x2019;" k="113" />
<hkern u1="&#xc2;" u2="&#x178;" k="113" />
<hkern u1="&#xc2;" u2="&#xff;" k="37" />
<hkern u1="&#xc2;" u2="&#xfd;" k="37" />
<hkern u1="&#xc2;" u2="&#xdd;" k="113" />
<hkern u1="&#xc2;" u2="y" k="37" />
<hkern u1="&#xc2;" u2="w" k="37" />
<hkern u1="&#xc2;" u2="v" k="37" />
<hkern u1="&#xc2;" u2="Y" k="113" />
<hkern u1="&#xc2;" u2="W" k="76" />
<hkern u1="&#xc2;" u2="V" k="76" />
<hkern u1="&#xc2;" u2="T" k="113" />
<hkern u1="&#xc3;" u2="&#x201d;" k="113" />
<hkern u1="&#xc3;" u2="&#x2019;" k="113" />
<hkern u1="&#xc3;" u2="&#x178;" k="113" />
<hkern u1="&#xc3;" u2="&#xff;" k="37" />
<hkern u1="&#xc3;" u2="&#xfd;" k="37" />
<hkern u1="&#xc3;" u2="&#xdd;" k="113" />
<hkern u1="&#xc3;" u2="y" k="37" />
<hkern u1="&#xc3;" u2="w" k="37" />
<hkern u1="&#xc3;" u2="v" k="37" />
<hkern u1="&#xc3;" u2="Y" k="113" />
<hkern u1="&#xc3;" u2="W" k="76" />
<hkern u1="&#xc3;" u2="V" k="76" />
<hkern u1="&#xc3;" u2="T" k="113" />
<hkern u1="&#xc4;" u2="&#x201d;" k="113" />
<hkern u1="&#xc4;" u2="&#x2019;" k="113" />
<hkern u1="&#xc4;" u2="&#x178;" k="113" />
<hkern u1="&#xc4;" u2="&#xff;" k="37" />
<hkern u1="&#xc4;" u2="&#xfd;" k="37" />
<hkern u1="&#xc4;" u2="&#xdd;" k="113" />
<hkern u1="&#xc4;" u2="y" k="37" />
<hkern u1="&#xc4;" u2="w" k="37" />
<hkern u1="&#xc4;" u2="v" k="37" />
<hkern u1="&#xc4;" u2="Y" k="113" />
<hkern u1="&#xc4;" u2="W" k="76" />
<hkern u1="&#xc4;" u2="V" k="76" />
<hkern u1="&#xc4;" u2="T" k="113" />
<hkern u1="&#xc5;" u2="&#x201d;" k="113" />
<hkern u1="&#xc5;" u2="&#x2019;" k="113" />
<hkern u1="&#xc5;" u2="&#x178;" k="113" />
<hkern u1="&#xc5;" u2="&#xff;" k="37" />
<hkern u1="&#xc5;" u2="&#xfd;" k="37" />
<hkern u1="&#xc5;" u2="&#xdd;" k="113" />
<hkern u1="&#xc5;" u2="y" k="37" />
<hkern u1="&#xc5;" u2="w" k="37" />
<hkern u1="&#xc5;" u2="v" k="37" />
<hkern u1="&#xc5;" u2="Y" k="113" />
<hkern u1="&#xc5;" u2="W" k="76" />
<hkern u1="&#xc5;" u2="V" k="76" />
<hkern u1="&#xc5;" u2="T" k="113" />
<hkern u1="&#xd0;" u2="&#x178;" k="37" />
<hkern u1="&#xd0;" u2="&#xdd;" k="37" />
<hkern u1="&#xd0;" u2="&#xc6;" k="25" />
<hkern u1="&#xd0;" u2="&#xc5;" k="25" />
<hkern u1="&#xd0;" u2="&#xc4;" k="25" />
<hkern u1="&#xd0;" u2="&#xc3;" k="25" />
<hkern u1="&#xd0;" u2="&#xc2;" k="25" />
<hkern u1="&#xd0;" u2="&#xc1;" k="25" />
<hkern u1="&#xd0;" u2="&#xc0;" k="25" />
<hkern u1="&#xd0;" u2="Y" k="37" />
<hkern u1="&#xd0;" u2="W" k="25" />
<hkern u1="&#xd0;" u2="V" k="37" />
<hkern u1="&#xd0;" u2="A" k="25" />
<hkern u1="&#xd2;" u2="&#x178;" k="37" />
<hkern u1="&#xd2;" u2="&#xdd;" k="37" />
<hkern u1="&#xd2;" u2="&#xc6;" k="25" />
<hkern u1="&#xd2;" u2="&#xc5;" k="25" />
<hkern u1="&#xd2;" u2="&#xc4;" k="25" />
<hkern u1="&#xd2;" u2="&#xc3;" k="25" />
<hkern u1="&#xd2;" u2="&#xc2;" k="25" />
<hkern u1="&#xd2;" u2="&#xc1;" k="25" />
<hkern u1="&#xd2;" u2="&#xc0;" k="25" />
<hkern u1="&#xd2;" u2="Y" k="37" />
<hkern u1="&#xd2;" u2="W" k="25" />
<hkern u1="&#xd2;" u2="V" k="37" />
<hkern u1="&#xd2;" u2="T" k="25" />
<hkern u1="&#xd2;" u2="A" k="25" />
<hkern u1="&#xd3;" u2="&#x178;" k="37" />
<hkern u1="&#xd3;" u2="&#xdd;" k="37" />
<hkern u1="&#xd3;" u2="&#xc6;" k="25" />
<hkern u1="&#xd3;" u2="&#xc5;" k="25" />
<hkern u1="&#xd3;" u2="&#xc4;" k="25" />
<hkern u1="&#xd3;" u2="&#xc3;" k="25" />
<hkern u1="&#xd3;" u2="&#xc2;" k="25" />
<hkern u1="&#xd3;" u2="&#xc1;" k="25" />
<hkern u1="&#xd3;" u2="&#xc0;" k="25" />
<hkern u1="&#xd3;" u2="Y" k="37" />
<hkern u1="&#xd3;" u2="W" k="25" />
<hkern u1="&#xd3;" u2="V" k="37" />
<hkern u1="&#xd3;" u2="T" k="25" />
<hkern u1="&#xd3;" u2="A" k="25" />
<hkern u1="&#xd4;" u2="&#x178;" k="37" />
<hkern u1="&#xd4;" u2="&#xdd;" k="37" />
<hkern u1="&#xd4;" u2="&#xc6;" k="25" />
<hkern u1="&#xd4;" u2="&#xc5;" k="25" />
<hkern u1="&#xd4;" u2="&#xc4;" k="25" />
<hkern u1="&#xd4;" u2="&#xc3;" k="25" />
<hkern u1="&#xd4;" u2="&#xc2;" k="25" />
<hkern u1="&#xd4;" u2="&#xc1;" k="25" />
<hkern u1="&#xd4;" u2="&#xc0;" k="25" />
<hkern u1="&#xd4;" u2="Y" k="37" />
<hkern u1="&#xd4;" u2="W" k="25" />
<hkern u1="&#xd4;" u2="V" k="37" />
<hkern u1="&#xd4;" u2="T" k="25" />
<hkern u1="&#xd4;" u2="A" k="25" />
<hkern u1="&#xd5;" u2="&#x178;" k="37" />
<hkern u1="&#xd5;" u2="&#xdd;" k="37" />
<hkern u1="&#xd5;" u2="&#xc6;" k="25" />
<hkern u1="&#xd5;" u2="&#xc5;" k="25" />
<hkern u1="&#xd5;" u2="&#xc4;" k="25" />
<hkern u1="&#xd5;" u2="&#xc3;" k="25" />
<hkern u1="&#xd5;" u2="&#xc2;" k="25" />
<hkern u1="&#xd5;" u2="&#xc1;" k="25" />
<hkern u1="&#xd5;" u2="&#xc0;" k="25" />
<hkern u1="&#xd5;" u2="Y" k="37" />
<hkern u1="&#xd5;" u2="W" k="25" />
<hkern u1="&#xd5;" u2="V" k="37" />
<hkern u1="&#xd5;" u2="T" k="25" />
<hkern u1="&#xd5;" u2="A" k="25" />
<hkern u1="&#xd6;" u2="&#x178;" k="37" />
<hkern u1="&#xd6;" u2="&#xdd;" k="37" />
<hkern u1="&#xd6;" u2="&#xc6;" k="25" />
<hkern u1="&#xd6;" u2="&#xc5;" k="25" />
<hkern u1="&#xd6;" u2="&#xc4;" k="25" />
<hkern u1="&#xd6;" u2="&#xc3;" k="25" />
<hkern u1="&#xd6;" u2="&#xc2;" k="25" />
<hkern u1="&#xd6;" u2="&#xc1;" k="25" />
<hkern u1="&#xd6;" u2="&#xc0;" k="25" />
<hkern u1="&#xd6;" u2="Y" k="37" />
<hkern u1="&#xd6;" u2="W" k="25" />
<hkern u1="&#xd6;" u2="V" k="37" />
<hkern u1="&#xd6;" u2="T" k="25" />
<hkern u1="&#xd6;" u2="A" k="25" />
<hkern u1="&#xd8;" u2="&#x178;" k="37" />
<hkern u1="&#xd8;" u2="&#xdd;" k="37" />
<hkern u1="&#xd8;" u2="&#xc6;" k="25" />
<hkern u1="&#xd8;" u2="&#xc5;" k="25" />
<hkern u1="&#xd8;" u2="&#xc4;" k="25" />
<hkern u1="&#xd8;" u2="&#xc3;" k="25" />
<hkern u1="&#xd8;" u2="&#xc2;" k="25" />
<hkern u1="&#xd8;" u2="&#xc1;" k="25" />
<hkern u1="&#xd8;" u2="&#xc0;" k="25" />
<hkern u1="&#xd8;" u2="Y" k="37" />
<hkern u1="&#xd8;" u2="W" k="25" />
<hkern u1="&#xd8;" u2="V" k="37" />
<hkern u1="&#xd8;" u2="T" k="25" />
<hkern u1="&#xd8;" u2="A" k="25" />
<hkern u1="&#xdd;" u2="&#x153;" k="113" />
<hkern u1="&#xdd;" u2="&#xfc;" k="84" />
<hkern u1="&#xdd;" u2="&#xfb;" k="113" />
<hkern u1="&#xdd;" u2="&#xfa;" k="113" />
<hkern u1="&#xdd;" u2="&#xf9;" k="113" />
<hkern u1="&#xdd;" u2="&#xf8;" k="113" />
<hkern u1="&#xdd;" u2="&#xf6;" k="84" />
<hkern u1="&#xdd;" u2="&#xf5;" k="113" />
<hkern u1="&#xdd;" u2="&#xf4;" k="113" />
<hkern u1="&#xdd;" u2="&#xf3;" k="113" />
<hkern u1="&#xdd;" u2="&#xf2;" k="113" />
<hkern u1="&#xdd;" u2="&#xef;" k="37" />
<hkern u1="&#xdd;" u2="&#xee;" k="37" />
<hkern u1="&#xdd;" u2="&#xed;" k="37" />
<hkern u1="&#xdd;" u2="&#xec;" k="37" />
<hkern u1="&#xdd;" u2="&#xeb;" k="113" />
<hkern u1="&#xdd;" u2="&#xea;" k="113" />
<hkern u1="&#xdd;" u2="&#xe9;" k="113" />
<hkern u1="&#xdd;" u2="&#xe8;" k="113" />
<hkern u1="&#xdd;" u2="&#xe6;" k="113" />
<hkern u1="&#xdd;" u2="&#xe5;" k="113" />
<hkern u1="&#xdd;" u2="&#xe4;" k="113" />
<hkern u1="&#xdd;" u2="&#xe3;" k="113" />
<hkern u1="&#xdd;" u2="&#xe2;" k="113" />
<hkern u1="&#xdd;" u2="&#xe1;" k="113" />
<hkern u1="&#xdd;" u2="&#xe0;" k="113" />
<hkern u1="&#xdd;" u2="&#xc6;" k="113" />
<hkern u1="&#xdd;" u2="&#xc5;" k="113" />
<hkern u1="&#xdd;" u2="&#xc4;" k="113" />
<hkern u1="&#xdd;" u2="&#xc3;" k="113" />
<hkern u1="&#xdd;" u2="&#xc2;" k="113" />
<hkern u1="&#xdd;" u2="&#xc1;" k="113" />
<hkern u1="&#xdd;" u2="&#xc0;" k="113" />
<hkern u1="&#xdd;" u2="u" k="113" />
<hkern u1="&#xdd;" u2="o" k="113" />
<hkern u1="&#xdd;" u2="i" k="37" />
<hkern u1="&#xdd;" u2="e" k="113" />
<hkern u1="&#xdd;" u2="a" k="113" />
<hkern u1="&#xdd;" u2="A" k="113" />
<hkern u1="&#xdd;" u2="&#x3b;" k="113" />
<hkern u1="&#xdd;" u2="&#x3a;" k="76" />
<hkern u1="&#xdd;" u2="&#x2e;" k="188" />
<hkern u1="&#xdd;" u2="&#x2d;" k="152" />
<hkern u1="&#xdd;" u2="&#x2c;" k="188" />
<hkern u1="&#xe0;" u2="&#xff;" k="25" />
<hkern u1="&#xe0;" u2="&#xfd;" k="25" />
<hkern u1="&#xe0;" u2="y" k="25" />
<hkern u1="&#xe0;" u2="w" k="12" />
<hkern u1="&#xe0;" u2="v" k="12" />
<hkern u1="&#xe1;" u2="&#xff;" k="25" />
<hkern u1="&#xe1;" u2="&#xfd;" k="25" />
<hkern u1="&#xe1;" u2="y" k="25" />
<hkern u1="&#xe1;" u2="w" k="12" />
<hkern u1="&#xe1;" u2="v" k="12" />
<hkern u1="&#xe2;" u2="&#xff;" k="25" />
<hkern u1="&#xe2;" u2="&#xfd;" k="25" />
<hkern u1="&#xe2;" u2="y" k="25" />
<hkern u1="&#xe2;" u2="w" k="12" />
<hkern u1="&#xe2;" u2="v" k="12" />
<hkern u1="&#xe3;" u2="&#xff;" k="25" />
<hkern u1="&#xe3;" u2="&#xfd;" k="25" />
<hkern u1="&#xe3;" u2="y" k="25" />
<hkern u1="&#xe3;" u2="w" k="12" />
<hkern u1="&#xe3;" u2="v" k="12" />
<hkern u1="&#xe4;" u2="&#xff;" k="25" />
<hkern u1="&#xe4;" u2="&#xfd;" k="25" />
<hkern u1="&#xe4;" u2="y" k="25" />
<hkern u1="&#xe4;" u2="w" k="12" />
<hkern u1="&#xe4;" u2="v" k="12" />
<hkern u1="&#xe5;" u2="&#xff;" k="25" />
<hkern u1="&#xe5;" u2="&#xfd;" k="25" />
<hkern u1="&#xe5;" u2="y" k="25" />
<hkern u1="&#xe5;" u2="w" k="12" />
<hkern u1="&#xe5;" u2="v" k="12" />
<hkern u1="&#xe6;" u2="&#xff;" k="25" />
<hkern u1="&#xe6;" u2="&#xfd;" k="25" />
<hkern u1="&#xe6;" u2="y" k="25" />
<hkern u1="&#xe6;" u2="w" k="12" />
<hkern u1="&#xe6;" u2="v" k="12" />
<hkern u1="&#xe7;" u2="&#xff;" k="37" />
<hkern u1="&#xe7;" u2="&#xfd;" k="37" />
<hkern u1="&#xe7;" u2="y" k="37" />
<hkern u1="&#xe7;" u2="l" k="25" />
<hkern u1="&#xe7;" u2="k" k="25" />
<hkern u1="&#xe7;" u2="h" k="25" />
<hkern u1="&#xe8;" u2="&#xff;" k="25" />
<hkern u1="&#xe8;" u2="&#xfd;" k="25" />
<hkern u1="&#xe8;" u2="y" k="25" />
<hkern u1="&#xe8;" u2="v" k="25" />
<hkern u1="&#xe9;" u2="&#xff;" k="25" />
<hkern u1="&#xe9;" u2="&#xfd;" k="25" />
<hkern u1="&#xe9;" u2="y" k="25" />
<hkern u1="&#xe9;" u2="v" k="25" />
<hkern u1="&#xea;" u2="&#xff;" k="25" />
<hkern u1="&#xea;" u2="&#xfd;" k="25" />
<hkern u1="&#xea;" u2="y" k="25" />
<hkern u1="&#xea;" u2="v" k="25" />
<hkern u1="&#xeb;" u2="&#xff;" k="25" />
<hkern u1="&#xeb;" u2="&#xfd;" k="25" />
<hkern u1="&#xeb;" u2="y" k="25" />
<hkern u1="&#xeb;" u2="v" k="25" />
<hkern u1="&#xf2;" u2="&#xff;" k="25" />
<hkern u1="&#xf2;" u2="&#xfd;" k="25" />
<hkern u1="&#xf2;" u2="y" k="25" />
<hkern u1="&#xf2;" u2="w" k="12" />
<hkern u1="&#xf2;" u2="v" k="12" />
<hkern u1="&#xf3;" u2="&#xff;" k="25" />
<hkern u1="&#xf3;" u2="&#xfd;" k="25" />
<hkern u1="&#xf3;" u2="y" k="25" />
<hkern u1="&#xf3;" u2="w" k="12" />
<hkern u1="&#xf3;" u2="v" k="12" />
<hkern u1="&#xf4;" u2="&#xff;" k="25" />
<hkern u1="&#xf4;" u2="&#xfd;" k="25" />
<hkern u1="&#xf4;" u2="y" k="25" />
<hkern u1="&#xf4;" u2="w" k="12" />
<hkern u1="&#xf4;" u2="v" k="12" />
<hkern u1="&#xf5;" u2="&#xff;" k="25" />
<hkern u1="&#xf5;" u2="&#xfd;" k="25" />
<hkern u1="&#xf5;" u2="y" k="25" />
<hkern u1="&#xf5;" u2="w" k="12" />
<hkern u1="&#xf5;" u2="v" k="12" />
<hkern u1="&#xf6;" u2="&#xff;" k="25" />
<hkern u1="&#xf6;" u2="&#xfd;" k="25" />
<hkern u1="&#xf6;" u2="y" k="25" />
<hkern u1="&#xf6;" u2="w" k="12" />
<hkern u1="&#xf6;" u2="v" k="12" />
<hkern u1="&#xf8;" u2="&#xff;" k="25" />
<hkern u1="&#xf8;" u2="&#xfd;" k="25" />
<hkern u1="&#xf8;" u2="y" k="25" />
<hkern u1="&#xf8;" u2="w" k="12" />
<hkern u1="&#xf8;" u2="v" k="12" />
<hkern u1="&#xfd;" u2="&#x153;" k="12" />
<hkern u1="&#xfd;" u2="&#xf8;" k="12" />
<hkern u1="&#xfd;" u2="&#xf6;" k="12" />
<hkern u1="&#xfd;" u2="&#xf5;" k="12" />
<hkern u1="&#xfd;" u2="&#xf4;" k="12" />
<hkern u1="&#xfd;" u2="&#xf3;" k="12" />
<hkern u1="&#xfd;" u2="&#xf2;" k="12" />
<hkern u1="&#xfd;" u2="&#xeb;" k="12" />
<hkern u1="&#xfd;" u2="&#xea;" k="12" />
<hkern u1="&#xfd;" u2="&#xe9;" k="12" />
<hkern u1="&#xfd;" u2="&#xe8;" k="12" />
<hkern u1="&#xfd;" u2="&#xe6;" k="12" />
<hkern u1="&#xfd;" u2="&#xe5;" k="12" />
<hkern u1="&#xfd;" u2="&#xe4;" k="12" />
<hkern u1="&#xfd;" u2="&#xe3;" k="12" />
<hkern u1="&#xfd;" u2="&#xe2;" k="12" />
<hkern u1="&#xfd;" u2="&#xe1;" k="12" />
<hkern u1="&#xfd;" u2="&#xe0;" k="12" />
<hkern u1="&#xfd;" u2="o" k="12" />
<hkern u1="&#xfd;" u2="e" k="12" />
<hkern u1="&#xfd;" u2="a" k="12" />
<hkern u1="&#xfd;" u2="&#x2e;" k="113" />
<hkern u1="&#xfd;" u2="&#x2c;" k="113" />
<hkern u1="&#xfe;" u2="&#xff;" k="25" />
<hkern u1="&#xfe;" u2="&#xfd;" k="25" />
<hkern u1="&#xfe;" u2="y" k="25" />
<hkern u1="&#xfe;" u2="v" k="12" />
<hkern u1="&#xff;" u2="&#x153;" k="12" />
<hkern u1="&#xff;" u2="&#xf8;" k="12" />
<hkern u1="&#xff;" u2="&#xf6;" k="12" />
<hkern u1="&#xff;" u2="&#xf5;" k="12" />
<hkern u1="&#xff;" u2="&#xf4;" k="12" />
<hkern u1="&#xff;" u2="&#xf3;" k="12" />
<hkern u1="&#xff;" u2="&#xf2;" k="12" />
<hkern u1="&#xff;" u2="&#xeb;" k="12" />
<hkern u1="&#xff;" u2="&#xea;" k="12" />
<hkern u1="&#xff;" u2="&#xe9;" k="12" />
<hkern u1="&#xff;" u2="&#xe8;" k="12" />
<hkern u1="&#xff;" u2="&#xe6;" k="12" />
<hkern u1="&#xff;" u2="&#xe5;" k="12" />
<hkern u1="&#xff;" u2="&#xe4;" k="12" />
<hkern u1="&#xff;" u2="&#xe3;" k="12" />
<hkern u1="&#xff;" u2="&#xe2;" k="12" />
<hkern u1="&#xff;" u2="&#xe1;" k="12" />
<hkern u1="&#xff;" u2="&#xe0;" k="12" />
<hkern u1="&#xff;" u2="o" k="12" />
<hkern u1="&#xff;" u2="e" k="12" />
<hkern u1="&#xff;" u2="a" k="12" />
<hkern u1="&#xff;" u2="&#x2e;" k="113" />
<hkern u1="&#xff;" u2="&#x2c;" k="113" />
<hkern u1="&#x152;" u2="&#x178;" k="37" />
<hkern u1="&#x152;" u2="&#xdd;" k="37" />
<hkern u1="&#x152;" u2="&#xc6;" k="25" />
<hkern u1="&#x152;" u2="&#xc5;" k="25" />
<hkern u1="&#x152;" u2="&#xc4;" k="25" />
<hkern u1="&#x152;" u2="&#xc3;" k="25" />
<hkern u1="&#x152;" u2="&#xc2;" k="25" />
<hkern u1="&#x152;" u2="&#xc1;" k="25" />
<hkern u1="&#x152;" u2="&#xc0;" k="25" />
<hkern u1="&#x152;" u2="Y" k="37" />
<hkern u1="&#x152;" u2="W" k="25" />
<hkern u1="&#x152;" u2="V" k="37" />
<hkern u1="&#x152;" u2="T" k="25" />
<hkern u1="&#x152;" u2="A" k="25" />
<hkern u1="&#x153;" u2="&#xff;" k="25" />
<hkern u1="&#x153;" u2="&#xfd;" k="25" />
<hkern u1="&#x153;" u2="y" k="25" />
<hkern u1="&#x153;" u2="w" k="12" />
<hkern u1="&#x153;" u2="v" k="12" />
<hkern u1="&#x178;" u2="&#x153;" k="113" />
<hkern u1="&#x178;" u2="&#xfc;" k="84" />
<hkern u1="&#x178;" u2="&#xfb;" k="113" />
<hkern u1="&#x178;" u2="&#xfa;" k="113" />
<hkern u1="&#x178;" u2="&#xf9;" k="113" />
<hkern u1="&#x178;" u2="&#xf8;" k="113" />
<hkern u1="&#x178;" u2="&#xf6;" k="84" />
<hkern u1="&#x178;" u2="&#xf5;" k="113" />
<hkern u1="&#x178;" u2="&#xf4;" k="113" />
<hkern u1="&#x178;" u2="&#xf3;" k="113" />
<hkern u1="&#x178;" u2="&#xf2;" k="113" />
<hkern u1="&#x178;" u2="&#xef;" k="37" />
<hkern u1="&#x178;" u2="&#xee;" k="37" />
<hkern u1="&#x178;" u2="&#xed;" k="37" />
<hkern u1="&#x178;" u2="&#xec;" k="37" />
<hkern u1="&#x178;" u2="&#xeb;" k="113" />
<hkern u1="&#x178;" u2="&#xea;" k="113" />
<hkern u1="&#x178;" u2="&#xe9;" k="113" />
<hkern u1="&#x178;" u2="&#xe8;" k="113" />
<hkern u1="&#x178;" u2="&#xe6;" k="113" />
<hkern u1="&#x178;" u2="&#xe5;" k="113" />
<hkern u1="&#x178;" u2="&#xe4;" k="113" />
<hkern u1="&#x178;" u2="&#xe3;" k="113" />
<hkern u1="&#x178;" u2="&#xe2;" k="113" />
<hkern u1="&#x178;" u2="&#xe1;" k="113" />
<hkern u1="&#x178;" u2="&#xe0;" k="113" />
<hkern u1="&#x178;" u2="&#xc6;" k="113" />
<hkern u1="&#x178;" u2="&#xc5;" k="113" />
<hkern u1="&#x178;" u2="&#xc4;" k="113" />
<hkern u1="&#x178;" u2="&#xc3;" k="113" />
<hkern u1="&#x178;" u2="&#xc2;" k="113" />
<hkern u1="&#x178;" u2="&#xc1;" k="113" />
<hkern u1="&#x178;" u2="&#xc0;" k="113" />
<hkern u1="&#x178;" u2="u" k="113" />
<hkern u1="&#x178;" u2="o" k="113" />
<hkern u1="&#x178;" u2="i" k="37" />
<hkern u1="&#x178;" u2="e" k="113" />
<hkern u1="&#x178;" u2="a" k="113" />
<hkern u1="&#x178;" u2="A" k="113" />
<hkern u1="&#x178;" u2="&#x3b;" k="113" />
<hkern u1="&#x178;" u2="&#x3a;" k="76" />
<hkern u1="&#x178;" u2="&#x2e;" k="188" />
<hkern u1="&#x178;" u2="&#x2d;" k="152" />
<hkern u1="&#x178;" u2="&#x2c;" k="188" />
<hkern u1="&#x2018;" u2="&#x2018;" k="70" />
<hkern u1="&#x2019;" u2="&#x2019;" k="70" />
<hkern u1="&#x2019;" u2="s" k="113" />
<hkern u1="&#x2019;" u2="d" k="152" />
<hkern u1="&#x201c;" u2="&#xc6;" k="113" />
<hkern u1="&#x201c;" u2="&#xc5;" k="113" />
<hkern u1="&#x201c;" u2="&#xc4;" k="113" />
<hkern u1="&#x201c;" u2="&#xc3;" k="113" />
<hkern u1="&#x201c;" u2="&#xc2;" k="113" />
<hkern u1="&#x201c;" u2="&#xc1;" k="113" />
<hkern u1="&#x201c;" u2="&#xc0;" k="113" />
<hkern u1="&#x201c;" u2="A" k="113" />
<hkern u1="&#xfb01;" u2="&#x201d;" k="-25" />
<hkern u1="&#xfb01;" u2="&#x2019;" k="-37" />
<hkern u1="&#xfb02;" u2="&#x201d;" k="-25" />
<hkern u1="&#xfb02;" u2="&#x2019;" k="-37" />
</font>
</defs></svg> 