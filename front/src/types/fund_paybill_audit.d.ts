interface FundPaybillAuditQuery {
    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 支出分类
     */
    projType?: string;

    /**
     * 支出用途
     */
    purposeId?: number;

    /**
     * 申请单位ID
     */
    applyOrgid?: string;

    /**
     * 收款单位
     */
    accountName?: string;

    /**
     * 状态（-1退回，0草稿，1已提交，2审批完成，3已拨付）
     */
    status?: number;

    /**
     * 申请日期开始
     */
    applyDateStart?: string;

    /**
     * 申请日期结束
     */
    applyDateEnd?: string;

    /**
     * 流程ID
     */
    flowId?: string;

    /**
     * 流程节点号码
     */
    nodeSeq?: number;
}

interface FundPaybillAuditListVo {
    /**
     * 主键
     */
    id?: string;

    /**
     * 拨款单编号
     */
    serialNumber?: string;

    /**
     * 项目ID
     */
    projId?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目编号
     */
    projSn?: string;

    /**
     * 支出分类
     */
    projType?: string;

    /**
     * 子项目ID
     */
    childId?: string;

    /**
     * 子项目名称
     */
    childName?: string;

    /**
     * 项目批准金额
     */
    projAmount?: number;

    /**
     * 当年下达用款计划
     */
    planYear?: number;

    /**
     * 累计下达用款计划
     */
    planAmount?: number;

    /**
     * 项目属性
     */
    typeId?: number;

    /**
     * 项目属性名称
     */
    typeName?: string;

    /**
     * 支出用途
     */
    purposeId?: number;

    /**
     * 支出用途名称
     */
    purposeName?: string;

    /**
     * 项目负责人
     */
    leaderName?: string;

    /**
     * 项目负责人电话
     */
    leaderMobile?: string;

    /**
     * 合同ID
     */
    contractId?: string;

    /**
     * 合同名称
     */
    contractName?: string;

    /**
     * 合同金额
     */
    contractAmount?: number;

    /**
     * 申请单位ID
     */
    applyOrgid?: string;

    /**
     * 申请单位名称
     */
    applyOrgname?: string;

    /**
     * 申请人ID
     */
    applyUserid?: string;

    /**
     * 申请人名称
     */
    applyUsername?: string;

    /**
     * 申请日期
     */
    applyDate?: Date;

    /**
     * 申请金额
     */
    applyAmount?: number;

    /**
     * 核定金额
     */
    checkAmount?: number;

    /**
     * 申请拨款类型
     */
    applyType?: number;

    /**
     * 申请理由
     */
    applyReason?: string;

    /**
     * 财务监理意见
     */
    fundOpinion?: string;

    /**
     * 财务监理附件
     */
    fundFile?: string;

    /**
     * 项目累计拨款金额
     */
    projPayed?: number;

    /**
     * 合同累计拨款金额
     */
    contractPayed?: number;

    /**
     * 项目管理单位
     */
    mgrOrgid?: string;

    /**
     * 审核处室
     */
    auditOrgid?: string;

    /**
     * 高质量记录ID
     */
    hqdpId?: string;

    /**
     * 流程ID
     */
    flowId?: string;

    /**
     * 节点序号
     */
    nodeSeq?: number;

    /**
     * 节点名称
     */
    nodeName?: string;

    /**
     * 状态（-1退回，0草稿，1已提交，2审批完成，3已拨付）
     */
    status?: number;

    /**
     * 收款人信息
     */
    skrs?: FundPaybillSkrVo[];

    /**
     * 是否可审核
     */
    canDoAudit?: boolean;

    /**
     * 是否可撤回
     */
    canDoRecall?: boolean;
}

interface FundPaybillDoAuditDto {
    /**
     * 拨款单ID（必填）
     */
    billId: string;

    /**
     * 0 退回 1 同意
     */
    operateResult: 0|1;

    /**
     * 操作理由
     */
    operateReason?: string;

    /**
     * 核定金额（必填，必须大于0）
     */
    checkAmount: number;

    /**
     * 是否发送短信
     */
    sendSms: 0|1;

    /**
     * 是否审核下一个
     */
    auditNext: 0|1;
}

interface FundPaybillNode {
    /**
     * 主键
     */
    id?: string;

    /**
     * 拨款单ID
     */
    paybillId?: string;

    /**
     * 节点序号
     */
    nodeSeq?: number;

    /**
     * 节点名称
     */
    nodeName?: string;

    /**
     * 上一节点序号（为了流程溯源）
     */
    prevNode?: number;

    /**
     * 处理人ID或用户名
     */
    operatorId?: string;

    /**
     * 处理人姓名
     */
    operatorName?: string;

    /**
     * 处理人用户名
     */
    operatorUserName?: string;

    /**
     * 处理时间
     */
    operateTime?: Date;

    /**
     * 处理意见（1通过，0退回）
     */
    operateResult?: number;

    /**
     * 处理理由
     */
    operateReason?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 是否多人认领
     */
    isMultiClaim?: boolean;
}

interface FundPaybillAuditQuery {
    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 支出分类
     */
    projType?: string;

    /**
     * 支出用途
     */
    purposeId?: number;

    /**
     * 申请单位ID
     */
    applyOrgid?: string;

    /**
     * 收款单位
     */
    accountName?: string;

    /**
     * 状态（-1退回，0草稿，1已提交，2审批完成，3已拨付）
     */
    status?: number;

    /**
     * 申请日期开始
     */
    applyDateStart?: Date;

    /**
     * 申请日期结束
     */
    applyDateEnd?: Date;

    /**
     * 流程ID
     */
    flowId?: string;

    /**
     * 流程节点号码
     */
    nodeSeq?: number;
}

interface FundPaybillAuditMonitorListVo {
    /**
     * 主键
     */
    id?: string;

    /**
     * 拨款单编号
     */
    serialNumber?: string;

    /**
     * 项目ID
     */
    projId?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目编号
     */
    projSn?: string;

    /**
     * 支出分类
     */
    projType?: string;

    /**
     * 子项目ID
     */
    childId?: string;

    /**
     * 子项目名称
     */
    childName?: string;

    /**
     * 项目批准金额
     */
    projAmount?: number;

    /**
     * 当年下达用款计划
     */
    planYear?: number;

    /**
     * 累计下达用款计划
     */
    planAmount?: number;

    /**
     * 项目属性
     */
    typeId?: number;

    /**
     * 项目属性名称
     */
    typeName?: string;

    /**
     * 支出用途
     */
    purposeId?: number;

    /**
     * 支出用途名称
     */
    purposeName?: string;

    /**
     * 项目负责人
     */
    leaderName?: string;

    /**
     * 项目负责人电话
     */
    leaderMobile?: string;

    /**
     * 合同ID
     */
    contractId?: string;

    /**
     * 合同名称
     */
    contractName?: string;

    /**
     * 合同金额
     */
    contractAmount?: number;

    /**
     * 申请单位ID
     */
    applyOrgid?: string;

    /**
     * 申请单位名称
     */
    applyOrgname?: string;

    /**
     * 申请人ID
     */
    applyUserid?: string;

    /**
     * 申请人名称
     */
    applyUsername?: string;

    /**
     * 申请日期
     */
    applyDate?: Date;

    /**
     * 申请金额
     */
    applyAmount?: number;

    /**
     * 核定金额
     */
    checkAmount?: number;

    /**
     * 申请拨款类型
     */
    applyType?: number;

    /**
     * 申请理由
     */
    applyReason?: string;

    /**
     * 财务监理意见
     */
    fundOpinion?: string;

    /**
     * 财务监理附件
     */
    fundFile?: string;

    /**
     * 项目累计拨款金额
     */
    projPayed?: number;

    /**
     * 合同累计拨款金额
     */
    contractPayed?: number;

    /**
     * 项目管理单位
     */
    mgrOrgid?: string;

    /**
     * 审核处室
     */
    auditOrgid?: string;

    /**
     * 高质量记录ID
     */
    hqdpId?: string;

    /**
     * 流程ID
     */
    flowId?: string;

    /**
     * 节点序号
     */
    nodeSeq?: number;

    /**
     * 节点名称
     */
    nodeName?: string;

    /**
     * 状态（-1退回，0草稿，1已提交，2审批完成，3已拨付）
     */
    status?: number;

    /**
     * 待处理人姓名
     */
    operatorNames?: string[];

    /**
     * 收款人信息
     */
    skrs?: FundPaybillSkrVo[];
}

/**
 * 拨款单收款人变更视图对象
 */
interface FundPaybillSkrChangeVo {
    /**
     * 主键（确保有序）
     */
    id?: string;

    /**
     * 拨款单ID
     */
    billId?: string;

    /**
     * 收款人ID
     */
    skrId?: string;

    /**
     * 修改人（ID）
     */
    changeUserid?: string;

    /**
     * 修改人（姓名）
     */
    changeUsername?: string;

    /**
     * 修改时间
     */
    changeTime?: Date;

    /**
     * 修改原因
     */
    changeReason?: string;

    /**
     * 修改前收款账号信息
     */
    originalInfo?: FundPaybillSkrChangeInfo;

    /**
     * 修改后收款账号信息
     */
    changeInfo?: FundPaybillSkrChangeInfo;

    /**
     * 修改凭证
     */
    files?: SysAttachment[];
}

/**
 * 拨款单收款人变更信息
 */
interface FundPaybillSkrChangeInfo {
    /**
     * 收款人名称
     */
    accountName?: string;

    /**
     * 收款人账号
     */
    accountNumber?: string;

    /**
     * 开户行名称
     */
    bankName?: string;

    /**
     * 开户行号
     */
    stationCode?: string;
}