/**
 * 重要单位视图对象
 */
interface FundImportantOrgVo {
    /**
     * 年度
     */
    year?: number;

    /**
     * 单位ID
     */
    orgId?: string;

    /**
     * 单位名称
     */
    orgName?: string;

    /**
     * 部门序号
     */
    seqNo?: string;

    /**
     * 是否重点单位（1:是）
     */
    ifImportant?: string;

    /**
     * 是否高质量单位（1:是）
     */
    ifHqdp?: string;

    /**
     * 高质量发展的可选择的项目
     */
    projectIds?: FundImportantOrgProjVo[];

    /**
     * 高质量发展的已选择的项目
     */
    ProjectHqdps?: ProjectHqdp[];

    projId?: string
}

/**
 * 重要单位项目视图对象
 */
interface FundImportantOrgProjVo {
    /**
     * 项目ID
     */
    projId?: string;

    /**
     * 项目名称
     */
    projName?: string;
}

/**
 * 高质量项目
 */
interface ProjectHqdp {
    /**
     * 主键（确保有序）
     */
    id?: string;

    /**
     * 年度
     */
    year?: number;

    /**
     * 单位ID（高质量项目主管部门ID）
     */
    orgId?: string;

    /**
     * 项目ID（关联项目ID）
     */
    projId?: string;
}