
/**
 * 项目绩效查询条件
 */
interface ProjectPerformanceQuery {
    /**
     * 主键（项目主键）
     */
    projId?: string;

    /**
     * 年度
     */
    year?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目单位
     */
    orgName?: string;

    /**
     * 项目ID
     */
    orgId?: string;

    /**
     * 执行期限（天）
     */
    period?: number;

    /**
     * 项目类型
     */
    typeId?: string;
}

/**
 * 项目绩效视图对象
 */
interface ProjectPerformanceVo {
    /**
     * 主键（项目主键）
     */
    projId?: string;

    /**
     * 年度
     */
    year?: number;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目单位
     */
    orgName?: string;

    /**
     * 项目ID
     */
    orgId?: string;

    /**
     * 执行期限（天）
     */
    period?: number;

    /**
     * 项目类型
     */
    typeId?: string;
}

/**
 * 项目绩效批次
 */
interface ProjectPerformanceBatch {
    /**
     * 主键
     */
    id?: string;

    /**
     * 项目ID
     */
    projId?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 批次标题（计财处进行整个化工区性质的绩效操作，需提供填写标题）
     */
    title?: string;

    /**
     * 项目预算完成可能性（“跟踪”时有效）
     */
    possibleBudget?: string;

    /**
     * 年度绩效目标完成可能性（“跟踪”时有效）
     */
    possibleTarget?: string;

    /**
     * 纠偏措施（“跟踪”时有效）
     */
    remedyMethod?: string;

    /**
     * 自评总分（“自评价”时有效）
     */
    evalTotal?: number;

    /**
     * 自评得分（“自评价”时有效）
     */
    evalScore?: number;

    /**
     * 自评得分100分制（“自评价”时有效）
     */
    evalScore100?: number;

    /**
     * 批次类型（二选一：跟踪、自评价）
     */
    typeName?: string;

    /**
     * 批次状态（0暂存、1提交）
     */
    status?: string;

    /**
     * 删除标识（0否，1是）
     */
    delFlag?: string;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;
}

/**
 * 项目绩效跟踪
 */
interface ProjectPerformanceTrack {
    /**
     * 主键
     */
    id?: string;

    /**
     * 批次ID（project_performance_batch 表外键）
     */
    batchId?: string;

    /**
     * 项目ID（project_info 表外键）
     */
    projId?: string;

    /**
     * 一级指标
     */
    indexLevel1?: string;

    /**
     * 二级指标
     */
    indexLevel2?: string;

    /**
     * 三级指标
     */
    indexLevel3?: string;

    /**
     * 排序号（默认1）
     */
    indexSort?: number;

    /**
     * 指标目标值
     */
    indexTarget?: string;

    /**
     * 跟踪期计划完成值
     */
    planPercent?: string;

    /**
     * 跟踪期实际完成值
     */
    actualPercent?: string;

    /**
     * 偏差原因分析
     */
    deviateReason?: string;
}

/**
 * 项目绩效批次视图对象
 */
interface ProjectPerformanceBatchVo {
    /**
     * ID（批次）
     */
    id?: string;

    /**
     * 项目ID（必填）
     */
    projId: string;

    /**
     * 项目名称（必填）
     */
    projName: string;

    /**
     * 批次标题（计财处进行整个化工区性质的绩效操作，需提供填写标题）
     */
    title?: string;

    /**
     * 项目预算完成可能性（“跟踪”时有效）
     */
    possibleBudget?: string;

    /**
     * 年度绩效目标完成可能性（“跟踪”时有效）
     */
    possibleTarget?: string;

    /**
     * 纠偏措施（“跟踪”时有效）
     */
    remedyMethod?: string;

    /**
     * 自评总分（“自评价”时有效）
     */
    evalTotal?: number;

    /**
     * 自评得分（“自评价”时有效）
     */
    evalScore?: number;

    /**
     * 自评得分100分制（“自评价”时有效）
     */
    evalScore100?: number;

    /**
     * 批次类型（必填，0：跟踪，1：自评价）
     */
    typeName: string;

    /**
     * 批次状态（必填，0：暂存，1：提交）
     */
    status: string;

    /**
     * 跟踪列表
     */
    tracks?: ProjectPerformanceTrack[];

    /**
     * 自评价列表
     */
    evals?: ProjectPerformanceEval[];
}