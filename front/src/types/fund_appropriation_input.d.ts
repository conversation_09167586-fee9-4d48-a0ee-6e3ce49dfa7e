interface FundAppropriationInputQuery {
    /**
     * SKR IDs
     */
    ids?: string[];

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目单位
     */
    applyOrgid?: string;

    /**
     * 项目管理单位
     */
    mgrOrgid?: string;

    /**
     * 收款单位
     */
    accountName?: string;

    /**
     * 收款账号
     */
    accountId?: string;

    /**
     * 申请日期开始
     */
    applyDateBegin?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 申请日期结束
     */
    applyDateEnd?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 拨款状态（0未拨付，1已拨付）（必填）
     */
    payStatus: number;

    /**
     * 拨付日期开始
     */
    payDateBegin?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 拨付日期结束
     */
    payDateEnd?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 拨付凭证
     */
    payVoucher?: string;

    /**
     * 拨款单编号
     */
    serialNumber?: string;
}

interface FundAppropriationInput {
    /**
     * fund-pay-bill-skr 主键ID
     */
    id?: string;

    /**
     * 拨款单编号
     */
    serialNumber?: string;

    /**
     * 项目ID
     */
    projId?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 银行账号ID
     */
    accountId?: string;

    /**
     * 开户行名
     */
    bankName?: string;

    /**
     * 开户行号
     */
    stationCode?: string;

    /**
     * 收款单位
     */
    accountName?: string;

    /**
     * 收款账号
     */
    accountNumber?: string;

    /**
     * 核准金额
     */
    amount?: number;

    /**
     * 申请日期
     */
    applyDate?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 拨付金额（必填）
     */
    payAmount: number;

    /**
     * 拨付日期（必填）
     */
    payDate: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 拨付凭证
     */
    payVoucher?: string;

    /**
     * 状态
     */
    status?: number;

    /**
     * 拨款单id
     */
    billId: string
}

interface FundBankIe {
    /**
     * 主键（确保有序）
     */
    id?: string;

    /**
     * 操作类型（1导入，2导出）
     */
    typeId?: string;

    /**
     * 导入或导出的 Excel 文件（sys_file 表 ID）
     */
    fileId?: string;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 操作描述
     */
    remark?: string;
}

/**
 * 拨款输入更新数据传输对象
 */
interface FundAppropriationInputUpdateDto {
    /**
     * fund-pay-bill-skr 主键ID（必填）
     */
    id: string;

    /**
     * 拨付日期（必填）
     */
    payDate: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 拨付凭证
     */
    payVoucher?: string;
}