interface ProjectImportant {
    /**
     * 主键;主键
     */
    id?: string;

    /**
     * 项目年度;项目年度
     */
    year?: number;

    /**
     * 重点项目ID;重点项目ID
     */
    projId?: string;

    /**
     * 项目编号;项目编号（此编号仅用于重点项目）
     */
    projCode?: string;

    /**
     * 项目阶段;项目阶段
     */
    stepNo?: number;

    /**
     * 项目阶段名称;项目阶段（1方案研究阶段、2预算评审及报批阶段、3待开工阶段、4实施阶段、5经常性项目）
     */
    stepName?: string;

    /**
     * 排序号;排序号
     */
    seqNo?: number;

    /**
     * 年度目标;年度目标
     */
    yearGoal?: string;

    /**
     * 进展说明及需协调问题;进展说明及需协调问题
     */
    progressIssue?: string;

    /**
     * 更新人;更新人，存储用户名
     */
    updateBy?: string;

    /**
     * 更新时间;更新时间
     */
    updateTime?: Date;

    /**
     * 项目月度计划;项目月度计划(json)，[{"month":1,"plan":"dddd"}]
     */
    monthPlan?: string;

    /**
     * 项目月度执行;项目月度执行(json)，[{"month":1,"action":"dddd"}]
     */
    monthAction?: string;
}

interface ProjectImportantVo {
    /**
     * 主键;主键，确保有序
     */
    id?: string;

    /**
     * 关联年度项目ID
     */
    relId?: string;

    /**
     * 项目年度;项目年度
     */
    year?: number;

    /**
     * 重点项目ID;重点项目ID
     */
    projId?: string;

    /**
     * 项目编号;项目编号（此编号仅用于重点项目）
     */
    projCode?: string;

    /**
     * 项目阶段;项目阶段
     */
    stepNo?: number;

    /**
     * 项目阶段名称;项目阶段（1方案研究阶段、2预算评审及报批阶段、3待开工阶段、4实施阶段、5经常性项目）
     */
    stepName?: string;

    /**
     * 排序号;排序号
     */
    seqNo?: number;

    /**
     * 年度目标;年度目标
     */
    yearGoal?: string;

    /**
     * 进展说明及需协调问题;进展说明及需协调问题
     */
    progressIssue?: string;

    /**
     * 更新人;更新人，存储用户名
     */
    updateBy?: string;

    /**
     * 更新时间;更新时间
     */
    updateTime?: Date;

    /**
     * 项目月度计划;项目月度计划(json)，[{"month":1,"plan":"dddd"}]
     */
    monthPlan?: string;

    /**
     * 项目月度执行;项目月度执行(json)，[{"month":1,"action":"dddd"}]
     */
    monthAction?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目资金
     */
    projAmount?: number;

    /**
     * 计划开工
     */
    planBegin?: Date;

    /**
     * 计划完工
     */
    projEnd?: Date;

    /**
     * 年度正式计划
     */
    yearPlan?: number;

    /**
     * 年度已执行
     */
    yearExecuted?: number;

    /**
     * 部门ID
     */
    deptId?: string;

    /**
     * 重点月度计划管理
     */
    projectImportantJhVo?: ProjectImportantJhVo;

    /**
     * 重点月度计划管理 List
     */
    projectImportantJhVos?: ProjectImportantJhVo[];

    /**
     * 重点月度实施管理
     */
    projectImportantExceVo?: ProjectImportantExceVo;

    /**
     * 重点月度实施管理 List
     */
    projectImportantExceVos?: ProjectImportantExceVo[];
}

interface ProjectImportantJhVo {
    // 根据实际字段定义
    month: number,
    /**
     * 月度管理内容任务 \n
     * 各个任务字符串以换行符隔开
     */
    plan: string
}

interface ProjectImportantExceVo {
    // 根据实际字段定义
    month: number,
    /**
     * 月度管理内容任务 \n
     * 各个任务字符串以换行符隔开
     */
    action: string
}

interface ProjectImpInputVo {
    /**
     * 主键;主键，确保有序
     */
    id?: string;

    /**
     * 关联年度项目ID
     */
    relId?: string;

    /**
     * 项目年度;项目年度
     */
    year?: number;

    /**
     * 项目id
     */
    projId?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目金额
     */
    projAmount?: number;

    /**
     * 部门ID
     */
    deptId?: string;

    /**
     * 项目阶段
     */
    stepNo?: number;

    /**
     * 项目编号
     */
    projCode?: string;

    /**
     * 年度目标
     */
    yearGoal?: string;

    /**
     * 月度计划计划
     */
    monthPlan?: string;

    /**
     * 重点月度计划管理
     */
    projectImportantJhVo?: ProjectImportantJhVo;

    /**
     * 重点月度计划管理 List
     */
    projectImportantJhVos?: ProjectImportantJhVo[];
}

interface ProjectImportantConditionVo {
    /**
     * 项目年度;项目年度
     */
    year?: number;

    /**
     * 重点项目ID;重点项目ID
     */
    projId?: string;

    /**
     * 项目阶段;项目阶段
     */
    stepNo?: number;

    /**
     * 项目单位
     */
    deptId?: string;
}

/**
 * 项目实施阶段视图对象
 */
interface ProjectImpStepVo {
    /**
     * 项目年度
     */
    year?: number;

    /**
     * 项目阶段
     */
    stepNo?: number;

    /**
     * 项目阶段名称（1方案研究阶段、2预算评审及报批阶段、3待开工阶段、4实施阶段、5经常性项目）
     */
    stepName?: string;

    /**
     * 项目金额
     */
    projAmount?: number;

    /**
     * 项目数量
     */
    projCount?: number;

    /**
     * 单位ID
     */
    orgId?: string;
}