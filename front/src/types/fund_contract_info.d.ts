 interface FundContractInfoQuery{
    projId?: string
    parta?: string
    partb?: string
}

/**
 * 合同信息视图对象
 */
interface FundContractInfoVo {
    /**
     * 主键
     */
    id?: string;

    /**
     * 项目ID
     */
    projId?: string;

    /**
     * 子项目ID
     */
    childId?: string;

    /**
     * 合同编号
     */
    code?: string;

    /**
     * 合同名称
     */
    name?: string;

    /**
     * 甲方
     */
    parta?: string;

    /**
     * 乙方
     */
    partb?: string;

    /**
     * 合同金额
     */
    amount?: number;

    /**
     * 签订日期
     */
    signDate?: Date;

    /**
     * 起始日期
     */
    validityBegin?: Date;

    /**
     * 截止日期
     */
    validityEnd?: Date;

    /**
     * 合同主要内容
     */
    content?: string;

    /**
     * 排序号，默认为 1
     */
    seqNo?: number;

    /**
     * 2019 年底前累计支付
     */
    amount2019?: number;

    /**
     * 合同创建部门
     */
    orgId?: string;

    /**
     * 合同创建部门名称
     */
    orgName?: string;

    /**
     * 创建人，存储用户名
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人，存储用户名
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 子项目名称
     */
    subProjName?: string;

    /**
     * 合同附件
     */
    files?: SysAttachment[];

    /**
     * 累计拨付总额
     */
    addUpAmount?: number;
}

interface FundContractInfoDto {
    /**
     * 主键（必填）
     */
    id: string;

    /**
     * 项目ID（必填）
     */
    projId: string;

    /**
     * 项目名称
     */
    projName: string;


    /**
     * 子项目ID（可选）
     */
    childId?: string;

    /**
     * 合同编号（可选）
     */
    code?: string;

    /**
     * 合同名称（必填）
     */
    name: string;

    /**
     * 甲方（可选）
     */
    parta?: string;

    /**
     * 乙方（可选）
     */
    partb?: string;

    /**
     * 合同金额（必填，必须大于等于0）
     */
    amount: number;

    /**
     * 签订日期（可选）
     */
    signDate?: Date;

    /**
     * 起始日期（必填）
     */
    validatyBegin: string;

    /**
     * 截止日期（必填）
     */
    validatyEnd: string;

    /**
     * 合同主要内容（必填）
     */
    content: string;

    /**
     * 排序号（可选，默认为1）
     */
    seqNo?: number;

    /**
     * 2019年底前累计支付（可选）
     */
    amount2019?: number;
}