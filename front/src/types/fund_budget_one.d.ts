/**
 * 预算信息
 */
interface FundBudget {
    /**
     * 主键（有序）
     */
    id?: string;

    /**
     * 维护单位ID
     */
    orgId?: number;

    /**
     * 维护单位名称
     */
    orgName?: string;

    /**
     * 预算年度
     */
    year?: number;

    /**
     * 预算类型（一上、二上等）
     */
    typeName?: string;

    /**
     * 预算表标题
     */
    title?: string;

    /**
     * 预算表单位（如：万元）
     */
    unit?: string;

    /**
     * 是否快照（1是0否，默认0）
     */
    isSnap?: string;

    /**
     * 快照名称
     */
    snapName?: string;

    /**
     * 列定义
     */
    columns?: string;

    /**
     * 状态（1发布0编辑，默认0）
     */
    status?: string;

    /**
     * 备注
     */
    remark?: string;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;
}

/**
 * 预算编制视图对象
 */
interface FundBudgetFormulateVo {
    /**
     * 主键（有序）
     */
    id?: string;

    /**
     * 预算表ID（fund_budget 表外键，必填）
     */
    budgetId: string;

    /**
     * 项目ID（对项目组情况时，可能为空）
     */
    projId?: string;

    /**
     * 项目名称（必填）
     */
    projName: string;

    /**
     * 项目类型编号（三个大类、A0、B0、C0，必填）
     */
    typeCode: string;

    /**
     * 项目类型名称
     */
    typeName?: string;

    /**
     * 考核主体ID
     */
    khorgId?: number;

    /**
     * 考核主体名称
     */
    khorgName?: string;

    /**
     * 用款计划金额
     */
    scheduleAmount?: number;

    /**
     * 预算金额小计
     */
    budgetAmount?: number;

    /**
     * 当年资金预算
     */
    yearAmount?: number;

    /**
     * 当年政府采购预算
     */
    yearPurchase?: number;

    /**
     * 其中购买服务预算
     */
    yearService?: number;

    /**
     * 备注
     */
    remark?: string;

    /**
     * 排序号（默认1）
     */
    seqNo?: number;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;

    /**
     * 预算明细列表
     */
    childDetailList?: FundBudgetFormulateVo[];
}

/**
 * 预算编制
 */
interface FundBudgetFormulate {
    /**
     * 主键（有序）
     */
    id?: string;

    /**
     * 预算表ID（fund_budget 表外键，必填）
     */
    budgetId: string;

    /**
     * 项目ID（对项目组情况时，可能为空）
     */
    projId?: string;

    /**
     * 项目名称（必填）
     */
    projName: string;

    /**
     * 项目类型编号（三个大类、A0、B0、C0，必填）
     */
    typeCode: string;

    /**
     * 项目类型名称
     */
    typeName?: string;

    /**
     * 考核主体ID
     */
    khorgId?: number;

    /**
     * 考核主体名称
     */
    khorgName?: string;

    /**
     * 用款计划金额
     */
    scheduleAmount?: number;

    /**
     * 预算金额小计
     */
    budgetAmount?: number;

    /**
     * 当年资金预算
     */
    yearAmount?: number;

    /**
     * 当年政府采购预算
     */
    yearPurchase?: number;

    /**
     * 其中购买服务预算
     */
    yearService?: number;

    /**
     * 备注
     */
    remark?: string;

    /**
     * 排序号（默认1）
     */
    seqNo?: number;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;

    /**
     * 预算明细
     */
    childDetailList?: FundBudgetFormulate[];

    /**
     * 大类排序
     */
    categorySort?: number;
}


/**
 * 预算项目明细数据传输对象
 */
interface FundBudgetProjectDetailDto {
    /**
     * 主键（有序，必填）
     */
    id: string;

    /**
     * 项目ID（对项目组情况时，可能为空）
     */
    projId?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 考核主体ID
     */
    khorgId?: number;

    /**
     * 考核主体名称
     */
    khorgName?: string;
}

/**
 * 列更新数据传输对象
 */
interface ColumnUpdateDto {
    /**
     * 主键（必填）
     */
    id: string;

    /**
     * 列名（必填）
     */
    columnName: string;

    /**
     * 列值
     */
    value?: string;
}

/**
 * 项目选择查询条件视图对象
 */
interface ProjectSelectQueryConditionVo {
    /**
     * 年份（必填）
     */
    year: string;

    /**
     * 单位名称
     */
    projectUnits?: string;

    /**
     * 项目名称
     */
    name?: string;

    /**
     * 项目类型
     */
    typeId?: string;

    /**
     * 项目性质代号
     */
    natureCode?: string;

    /**
     * 项目用途ids
     */
    purposeIds?: string[];

    /**
     * 考核部门ID
     */
    assessOrgid?: string;
}