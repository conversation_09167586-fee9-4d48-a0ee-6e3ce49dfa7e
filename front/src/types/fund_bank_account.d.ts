interface FundBankAccountQuery extends BaseEntity {
    /**
     * 账户管理单位
     */
    orgId?: string;

    /**
     * 开户银行
     */
    bankName?: string;

    /**
     * 户名/户号
     */
    cardNameNumber?: string;

    /**
     * 账号类型;账号类型（机构、个人）
     */
    typeName?: string;

    /**
     * 是否发展资金专户;是否发展资金专户（1是，0否）
     */
    isSpecial?: string;

    isOpenAccount?: string;

}

interface FundBankAccountVo {
    /**
     * 主键ID
     */
    id?: string;

    /**
     * 维护单位
     */
    orgId?: string;

    /**
     * 户名
     */
    name?: string;

    /**
     * 户号
     */
    cardNumber?: string;

    /**
     * 开户行
     */
    bankName?: string;

    /**
     * 开户网点代号
     */
    stationCode?: string;

    /**
     * 账户类型（机构，个人）
     */
    typeName?: string;

    /**
     * 是否发展资金专户（1是，0否）
     */
    isSpecial?: string;

    /**
     * 创建人（存储用户名）
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人（存储用户名）
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;
}

interface FundBankAccount {
    /**
     * 主键ID
     */
    id?: string;

    /**
     * 维护单位
     */
    orgId?: string;

    /**
     * 户名（必填）
     */
    name: string;

    /**
     * 户号（必填）
     */
    cardNumber: string;

    /**
     * 开户行（必填）
     */
    bankName: string;

    /**
     * 开户网点代号
     */
    stationCode?: string;

    /**
     * 账户类型（机构，个人）
     */
    typeName?: string;

    /**
     * 是否发展资金专户（1是，0否）
     */
    isSpecial?: string;

    /**
     * 删除标识（1是，0否）
     */
    delFlag?: string;

    /**
     * 创建人（存储用户名）
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人（存储用户名）
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;
}

type setAmountType = FundPaybillSkrDto & { 
    /** 收款人id */
    id?: string
    name: string, 
    bankName: string, 
    cardNumber: string, 
    stationCode?: string, 
    /** 对公账户为true，对私账户为false */
    isPublic: boolean 
}