/**
 * 项目查询视图对象
 */
interface ProjectQueryVo {
    /**
     * 项目名称
     */
    name?: string;

    /**
     * 申报单位ID
     */
    applyOrgid?: number;

    /**
     * 配合单位ID
     */
    cooperateOrgid?: number;

    /**
     * 配合单位名称
     */
    cooperateOrgname?: string;

    /**
     * 项目性质代号
     */
    natureCode?: string;

    /**
     * 项目类型ID
     */
    typeId?: string;

    /**
     * 项目用途ids
     */
    purposeIds?: string[];

    /**
     * 项目状态
     */
    status?: number;

    /**
     * 建设时间开始
     */
    beginDate?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 建设时间结束
     */
    endDate?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 起始年度
     */
    beginYear?: string;

    /**
     * 结束年度
     */
    endYear?: string;


    /**
     * 项目篮子ID
     */
    basketCode?: string;

    /**
     * 支出类型（0：数字化园区支出，1：精细化管理支出，2：政府购买服务）
     */
    zfType?: string;
}

/**
 * 项目查询实体
 */
interface ProjectQueryEntity {
    /**
     * 项目ID
     */
    id?: string;

    /**
     * 项目名称
     */
    name?: string;

    /**
     * 项目编号
     */
    sn?: string;

    /**
     * 申请部门名称
     */
    orgName?: string;

    /**
     * 估算金额
     */
    estAmount?: number;

    /**
     * 入库金额
     */
    libAmount?: number;

    /**
     * 核定金额
     */
    checkAmount?: number;

    /**
     * 项目周期起始
     */
    beginDate?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 项目周期结束
     */
    endDate?: string; // 使用 ISO 格式的字符串表示日期

    /**
     * 项目状态
     */
    status?: string;

    /**
     * 项目篮子ID
     */
    basketCode?: string;

    /**
     * 项目篮子名称
     */
    basketName?: string;
}

/**
 * 计划查询实体
 */
interface PlanQueryEntity {
    /**
     * 项目ID
     */
    id?: string;

    /**
     * 项目名称
     */
    name?: string;

    /**
     * 项目编号
     */
    sn?: string;

    /**
     * 申报单位
     */
    orgName?: string;

    /**
     * 核定/合同金额
     */
    checkAmount?: number;

    /**
     * 年度
     */
    year?: number;

    /**
     * 当前计划
     */
    currPlan?: number;

    /**
     * 计划调整
     */
    adjustAmount?: number;

    /**
     * 当年执行
     */
    currPayed?: number;

    /**
     * 截止上年已执行
     */
    prevPayed?: number;
}

/**
 * 项目调整视图对象
 */
interface ProjectAdjustVo {
    /**
     * 项目名称
     */
    name?: string;

    /**
     * 申报单位ID
     */
    applyOrgid?: number;

    /**
     * 配合单位ID
     */
    cooperateOrgid?: number;

    /**
     * 配合单位名称
     */
    cooperateOrgname?: string;

    /**
     * 项目性质代号
     */
    natureCode?: string;

    /**
     * 项目类型ID
     */
    typeId?: string;

    /**
     * 项目用途ids
     */
    purposeIds?: string[];

    /**
     * 审批状态
     */
    status?: number;

    /**
     * 起始年度
     */
    beginYear?: string;

    /**
     * 结束年度
     */
    endYear?: string;
}

/**
 * 计划调整查询实体
 */
interface PlanAdjustQueryEntity {
    /**
     * 项目ID
     */
    id?: string;

    /**
     * 项目名称
     */
    name?: string;

    /**
     * 项目编号
     */
    sn?: string;

    /**
     * 申报单位
     */
    orgName?: string;

    /**
     * 项目用途
     */
    purpose?: string;

    /**
     * 年度
     */
    year?: number;

    /**
     * 当前计划
     */
    currPlan?: number;

    /**
     * 计划调整
     */
    applyAdjust?: number;

    /**
     * 调整理由
     */
    applyReason?: string;

    /**
     * 回退理由
     */
    applyRefused?: string;

    /**
     * 状态
     */
    status?: string;
}

interface BudgetQueryVo {
  /**
   * 项目名称
   */
  name?: string;

  /**
   * 上报部门id
   */
  submitOrgid?: number;

  /**
   * 考核部门id
   */
  assessOrgid?: number;

  /**
   * 项目类型id
   */
  typeId?: string;

  /**
   * 项目性质代码
   */
  natureCode?: string;

  /**
   * 项目批准金额
   */
  approvedAmount?: string;

  /**
   * 执行开始时间
   */
  beginDate: string; // LocalDate 转换为 string

  /**
   * 执行结束时间
   */
  endDate: string; // LocalDate 转换为 string

  /**
   * 最小执行率
   */
  minRate?: number; // BigDecimal 转换为 number

  /**
   * 最大执行率
   */
  maxRate?: number; // BigDecimal 转换为 number
}

interface BudgetQueryEntity {
  /**
   * 项目ID
   */
  id?: string;

  /**
   * 项目名称
   */
  name?: string;

  /**
   * 项目编号
   */
  sn?: string;

  /**
   * 上报主体名称
   */
  submitOrgname?: string;

  /**
   * 考核主体名称
   */
  assessOrgname?: string;

  /**
   * 项目类型
   */
  typeName?: string;

  /**
   * 项目属性编号
   */
  projType?: string;

  /**
   * 批准金额
   */
  approvedAmount?: number; // BigDecimal 转换为 number

  /**
   * 年份
   */
  year?: number; // Integer 转换为 number

  /**
   * 已安排计划
   */
  arrangedAmount?: number; // BigDecimal 转换为 number

  /**
   * 执行金额
   */
  executedAmount?: number; // BigDecimal 转换为 number

  /**
   * 执行比例
   */
  executedRate?: number; // BigDecimal 转换为 number
}