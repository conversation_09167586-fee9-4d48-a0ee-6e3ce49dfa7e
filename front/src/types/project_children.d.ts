interface ProjectChildrenVo {
    /**
     * 主键
     */
    id?: string;

    /**
     * 项目ID
     */
    projId?: string;

    /**
     * 子项目名称
     */
    name?: string;

    /**
     * 合同金额
     */
    amount?: number;

    /**
     * 排序号
     */
    seqNo?: number;

    /**
     * 备注
     */
    remark?: string;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;
}

interface ProjectChildrenConditionVo {
    /**
     * 主键
     */
    projId?: string;

    /**
     * 年份
     */
    year?: string;

    /**
     * 项目单位ID
     */
    orgId?: string;

    /**
     * 项目单位名称
     */
    orgName?: string;

    /**
     * 项目名称
     */
    name?: string;

    /**
     * 项目类型
     */
    typeId?: string;

    /**
     * 项目用途ids
     */
    purposeIds?: string[];

    /**
     * 是否包含子项目（1：包含子项目，0：不包含子项目）
     */
    ifcontain?: string;
}

interface ProjectChildrenVo {
    /**
     * 主键
     */
    id?: string;

    /**
     * 年度
     */
    year?: number;

    /**
     * 项目名称
     */
    name?: string;

    /**
     * 项目类型
     */
    typeName?: string;

    /**
     * 项目用途（JSON 格式）
     */
    purpose?: string;

    /**
     * 项目金额
     */
    amount?: number;

    /**
     * 预算金额
     */
    bugetAmount?: number;

    /**
     * 项目单位
     */
    unitName?: string;

    /**
     * 子项目数量
     */
    subprojcount?: number;

    /**
     * 子项目预算信息
     */
    projectChildrenBudgetVos?: ProjectChildrenBudgetVo[];

    projectPlanBudgetVos?: ProjectChildrenBudgetVo[];

    /**
     * 排序号
     */
    seqNo?: number;

    /**
     * 备注
     */
    remark?: string;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;
}

interface ProjectChildrenBudgetVo {
    /**
     * 主键
     */
    id?: string;

    /**
     * 项目ID
     */
    projId?: string;

    /**
     * 子项目名称
     */
    name?: string;

    /**
     * 项目金额
     */
    amount?: number;

    /**
     * 预算年度
     */
    year?: number;

    /**
     * 预算金额
     */
    bugetAmount?: number;

    /**
     * 备注
     */
    remark?: string;
}