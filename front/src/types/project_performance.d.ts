interface IProjectPerformance {
    /**
     * 主键;主键，无意义，确保有序
     */
    id?: string;

    /**
     * 项目ID;项目ID
     */
    projId?: string;

    /**
     * 一级指标;一级指标
     */
    indexLevel1?: string;

    /**
     * 二级指标;二级指标
     */
    indexLevel2?: string;

    /**
     * 三级指标;三级指标
     */
    indexLevel3?: string;

    /**
     * 指标目标值;指标目标值
     */
    indexTarget?: string;

    /**
     * 排序号;排序号
     */
    indexSort: number;

    /**
     * 创建时间;创建时间
     */
    createTime?: Date;

    /**
     * 创建人;创建人
     */
    createBy?: string;

    /**
     * 更新时间;更新时间
     */
    updateTime?: Date;

    /**
     * 更新人;更新人
     */
    updateBy?: string;
}