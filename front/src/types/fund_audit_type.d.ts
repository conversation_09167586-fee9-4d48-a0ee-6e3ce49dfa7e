/**
 * 审核类型视图对象
 */
interface FundAuditTypeVo {
    /**
     * 主键
     */
    id?: string;

    /**
     * 类型名称
     */
    name?: string;

    /**
     * 排序号（默认1）
     */
    seqNo?: number;

    /**
     * 备注
     */
    remark?: string;

    /**
     * 是否可用（1是0否，默认1）
     */
    isEnable?: string;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;

    /**
     * 顶级资料清单数量
     */
    topDetailsCount?: number;

    /**
     * 资料清单树形结构
     */
    treeDetails?: FundAuditTemplateVo[];
}

/**
 * 审核模板视图对象
 */
interface FundAuditTemplateVo {
    /**
     * 主键
     */
    id?: string;

    /**
     * 审计类型ID（fund_audit_type 表外键）
     */
    typeId?: string;

    /**
     * 父级ID（本表外键）
     */
    parentId?: string;

    /**
     * 资料名称
     */
    name?: string;

    /**
     * 资料模板文件（存储 sys_file 的 ID）
     */
    fileId?: string;

    /**
     * 是否必须（1是，0否）
     */
    isRequire?: string;

    /**
     * 排序号
     */
    seqNo?: number;

    /**
     * 备注
     */
    remark?: string;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;

    /**
     * 模板文件
     */
    templateFile?: SysAttachment;

    /**
     * 子节点
     */
    children?: FundAuditTemplateVo[];
}

interface FundAuditTypeDto {
    /**
     * 主键
     */
    id?: string;

    /**
     * 类型名称（必填）
     */
    name: string;

    /**
     * 排序号（必填，默认1）
     */
    seqNo: number;

    /**
     * 备注
     */
    remark?: string;

    /**
     * 是否可用（必填，1是0否，默认1）
     */
    isEnable: string;
}

interface FundAuditTemplateDto {
    /**
     * 主键（必填）
     */
    id: string;

    /**
     * 审计类型ID（必填，fund_audit_type 表外键）
     */
    typeId: string;

    /**
     * 父级ID（本表外键）
     */
    parentId?: string;

    /**
     * 资料名称（必填）
     */
    name: string;

    /**
     * 资料模板文件（存储 sys_file 的 ID）
     */
    fileId?: string;

    /**
     * 是否必须（必填，1是，0否）
     */
    isRequire: string;

    /**
     * 排序号（必填）
     */
    seqNo: number;

    /**
     * 备注
     */
    remark?: string;
}