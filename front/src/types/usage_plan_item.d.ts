interface IUsagePlanItem {
  id?: string;
  /** 用款计划表ID */
  fundId?: string;
  /** 数据项类型，0类型 1项目，2组 */
  itemType?: number;
  /** 组汇总方式，1自动累加，2自定义 */
  sumMode?: number;
  /** 父项ID */
  parentId?: string;
  /** 排序号，默认1 */
  seqNo?: number;
  /** 项目ID */
  projId?: string;
  /** 项目名称(组名称） */
  projName: string;
  /** 项目编号 */
  projSn: string;
  /** 上报单位ID */
  sbOrgid?: string;
  /** 上报单位名称 */
  sbOrgname?: string;
  /** 考核主体ID */
  assessOrgid?: number;
  /** 考核主体名称 */
  assessOrgname?: string;
  /** 是否重点项目（Y是，N否） */
  isImp?: string;
  /** 重点项目类型 */
  impName?: string;
  /** 项目篮子ID */
  basketId?: string;
  /** 项目篮子名称 */
  basketName?: string;
  /** 项目性质（阶段性-新增，阶段性-结转，经常性） */
  natureName?: string;
  /** 项目估算 */
  estAmount?: number|null;
  /** 入库金额 */
  libAmount?: number|null;
  /** 核定金额 */
  checkAmount?: number|null;
  /** 合同金额 */
  contractAmount?: number|null;
  /** 至上年底累计执行 */
  prevAmount?: number|null;
  /** 上年调整计划 */
  prevAdjust?: number|null;
  /** 上年实际执行 */
  prevPayed?: number|null;
  /** 当年资金需求 */
  currAmount?: number|null;
  /** 当年初步使用计划 */
  currEarly?: number|null;
  /** 当年正式计划 */
  currFormal?: number|null;
  /** 当年调整计划 */
  currAdjust?: number|null;
  /** 当年已执行 */
  currPayed?: number|null;
  /** 开工(采购)时间 */
  beginDate?: string;
  /** 竣工(完成)时间 */
  endDate?: string;
  /** 备注1 */
  remark1?: string;
  /** 备注2 */
  remark2?: string;
  /** 项目信息 */
  projectInfo?: IProjectInfoVo
  /** A0、B0、C0 */
  typeCode: string;
  [key: string]: any
}