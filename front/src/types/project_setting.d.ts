interface ProjectSetQuery {
  /** 项目名称 */
  name?: string;

  /** 申报单位ID */
  applyOrgid?: number;

  /** 项目类型ID（字典表） */
  typeId?: string;

  /** 执行年度（开始） */
  beginYear?: string;

  /** 执行年度（结束） */
  endYear?: string;

  /** 是否涉及数字化园区支出（0否，1是） */
  isSzhyq?: string;

  /** 是否涉及精细化管理（0否，1是） */
  isJxhgl?: string;

  /**
   * 是否涉及政府购买服务;是否涉及政府购买服务（0否，1是）
   */
  isZfgm?: string;

  /** 项目篮子ID */
  basketCode?: number;

  /** 项目状态（-1退回申报人、0草稿、1已上报） */
  state?: number;

  /** 项目用途ID列表（多选） */
  purposeIds?: string[];
}

interface ProjectSet {
  /** 主键 */
  id?: string;

  /** 项目名称 */
  name?: string;

  /** 项目正式名称（优先于项目名称） */
  formalName?: string;

  /** 申报单位ID */
  applyOrgid?: number;

  /** 申报单位名称 */
  applyOrgname?: string;

  /** 项目类型ID（字典表） */
  typeId?: string;

  /** 项目类型名称 */
  typeName?: string;

  /** 周期开始日期（yyyy-MM-dd） */
  beginDate?: string;

  /** 周期结束日期（yyyy-MM-dd） */
  endDate?: string;

  /** 是否涉及数字化园区支出（0否，1是） */
  isSzhyq?: string;

  /** 是否涉及精细化管理（0否，1是） */
  isJxhgl?: string;

  /** 项目篮子ID */
  basketCode?: number;

  /** 项目篮子名称 */
  basketName?: string;

  /** 项目状态（-1退回申报人、0草稿、1已上报） */
  state?: number;

  /** 项目用途（JSON 字符串） */
  purpose?: string;
}
