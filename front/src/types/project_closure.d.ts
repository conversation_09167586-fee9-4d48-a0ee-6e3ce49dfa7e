/**
 * 项目结项查询条件
 */
interface ProjectClosureQuery {
    /**
     * 项目单位
     */
    applyOrgid?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目年度
     */
    firstPayYear?: number;

    /**
     * 状态
     */
    status?: string;

    /**
     * 申请时间开始
     */
    applyTimeBegin?: string;

    /**
     * 申请时间结束
     */
    applyTimeEnd?: string;

    /**
     * 是否结项完成（申请列表用）
     */
    isCompleted?: number;
}

/**
 * 项目结项列表视图对象
 */
interface ProjectClosureListVo {
    /**
     * 项目ID（project_info 表外键）
     */
    projId?: string;

    /**
     * 结项ID
     */
    id?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目金额（万元）
     */
    projAmount?: number;

    /**
     * 累计执行金额（万元）
     */
    payedAmount?: number;

    /**
     * 未执行金额（万元）
     */
    balanceAmount?: number;

    /**
     * 项目申请单位
     */
    applyOrgname?: string;

    /**
     * 项目上报单位
     */
    submitOrgname?: string;

    /**
     * 项目考核单位
     */
    assessOrgname?: string;

    /**
     * 项目配合单位
     */
    cooperateOrgname?: string;

    /**
     * 项目首次支付年度
     */
    firstPayYear?: number;

    /**
     * 流程ID（sys_flow 表外键）
     */
    flowId?: string;

    /**
     * 流程节点
     */
    nodeSeq?: number;

    /**
     * 流程节点名称
     */
    nodeName?: string;

    /**
     * 归档编号
     */
    archiveNumber?: string;

    /**
     * 结项状态（-1退回修改，0暂存，1提交，2完成，3归档中，4已归档）
     */
    status?: number;

    /**
     * 结项状态名称
     */
    statusName?: string;

    /**
     * 申请人姓名
     */
    applyUsername?: string;

    /**
     * 申请日期
     */
    applyTime?: Date;

    /**
     * 是否可申请
     */
    canDoApply?: boolean;

    /**
     * 是否可修改提交
     */
    canDoEdit?: boolean;
}

/**
 * 项目结项金额汇总视图对象
 */
interface ProjectClosureAmountSumVo {
    /**
     * 项目金额合计（万元）
     */
    totalProjAmount?: number;

    /**
     * 累计执行金额合计（万元）
     */
    totalPayedAmount?: number;

    /**
     * 未执行金额合计（万元）
     */
    totalBalanceAmount?: number;
}

/**
 * 项目结项审核列表视图对象
 */
interface ProjectClosureAuditListVo {
    /**
     * 项目ID（project_info 表外键）
     */
    projId?: string;

    /**
     * 结项ID
     */
    id?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 项目金额（万元）
     */
    projAmount?: number;

    /**
     * 累计执行金额（万元）
     */
    payedAmount?: number;

    /**
     * 未执行金额（万元）
     */
    balanceAmount?: number;

    /**
     * 项目申请单位
     */
    applyOrgname?: string;

    /**
     * 项目上报单位
     */
    submitOrgname?: string;

    /**
     * 项目考核单位
     */
    assessOrgname?: string;

    /**
     * 项目配合单位
     */
    cooperateOrgname?: string;

    /**
     * 项目首次支付年度
     */
    firstPayYear?: number;

    /**
     * 流程ID（sys_flow 表外键）
     */
    flowId?: string;

    /**
     * 流程节点
     */
    nodeSeq?: number;

    /**
     * 流程节点名称
     */
    nodeName?: string;

    /**
     * 归档编号
     */
    archiveNumber?: string;

    /**
     * 状态（-1退回修改，0暂存，1提交，2完成，3归档中，4已归档）
     */
    status?: number;

    /**
     * 申请人姓名
     */
    applyUsername?: string;

    /**
     * 申请日期
     */
    applyTime?: Date;

    /**
     * 是否可审核
     */
    canDoAudit?: boolean;

        /**
     * 申请结项理由
     */
    applyReason?: string;

    /**
     * 快捷理由
     */
    handyReason?: string;
}

/**
 * 项目结项视图对象
 */
interface ProjectClosureVo {
    /**
     * 主键（有序）
     */
    id?: string;

    /**
     * 项目ID（project_info 表外键）
     */
    projId?: string;

    /**
     * 项目名称
     */
    projName?: string;

    /**
     * 申请结项理由
     */
    applyReason?: string;

    /**
     * 快捷理由
     */
    handyReason?: string;

    /**
     * 流程ID（sys_flow 表外键）
     */
    flowId?: string;

    /**
     * 流程节点
     */
    nodeSeq?: number;

    /**
     * 流程节点名称
     */
    nodeName?: string;

    /**
     * 归档编号
     */
    archiveNumber?: string;

    /**
     * 状态（-1退回修改，0暂存，1提交，2完成，3归档中，4已归档）
     */
    status?: number;

    /**
     * 创建人（申请人）
     */
    createBy?: string;

    /**
     * 创建时间（申请时间）
     */
    createTime?: Date;

    /**
     * 更新人
     */
    updateBy?: string;

    /**
     * 更新时间
     */
    updateTime?: Date;

    /**
     * 申请用户ID
     */
    applyUserid?: string;

    /**
     * 申请用户名
     */
    applyUsername?: string;

    /**
     * 申请时间
     */
    applyTime?: Date;
}

/**
 * 项目结项数据传输对象
 */
interface ProjectClosureDto {
    /**
     * 主键（有序）
     */
    id?: string;

    /**
     * 项目ID（project_info 表外键，必填）
     */
    projId: string;

    /**
     * 申请结项理由
     */
    applyReason?: string;

    /**
     * 快捷理由
     */
    handyReason?: string;
}

/**
 * 项目结项审核操作数据传输对象
 */
interface ProjectClosureDoAuditDto {
    /**
     * 结项申请ID（必填）
     */
    closureId: string;

    /**
     * 操作结果（必填）
     */
    operateResult: number;

    /**
     * 操作理由
     */
    operateReason?: string;
}

/**
 * 项目结项节点视图对象
 */
interface ProjectClosureNodeVo {
    /**
     * 主键（确保有序）
     */
    id?: string;

    /**
     * 结项ID（project_closure 表外键）
     */
    closeId?: string;

    /**
     * 节点序号
     */
    nodeSeq?: number;

    /**
     * 节点名称
     */
    nodeName?: string;

    /**
     * 上一节点（用于流程溯源）
     */
    prevNode?: number;

    /**
     * 处理人ID或用户名
     */
    operatorId?: string;

    /**
     * 处理人姓名
     */
    operatorName?: string;

    /**
     * 处理人用户名
     */
    operatorUserName?: string;

    /**
     * 处理时间
     */
    operateTime?: Date;

    /**
     * 处理结果（1提交，0退回）
     */
    operateResult?: number;

    /**
     * 处理理由
     */
    operateReason?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 是否多人抢办标记
     */
    isMultiClaim?: boolean;
}

/**
 * 归档待处理附件视图对象
 */
interface ArchivePendingAttachmentVo {
    /**
     * 附件ID
     */
    id?: string;

    /**
     * 附件大类
     */
    primaryType?: string;

    /**
     * 名称
     */
    fileName?: string;

    /**
     * 存储路径
     */
    filePath?: string;

    /**
     * 类型（后缀名，如pdf|.pdf）
     */
    fileExt?: string;

    /**
     * 大小，单位：字节（B）
     */
    fileSize?: number;

    /**
     * 排序号（默认1）
     */
    seqNo?: number;

    /**
     * 是否已使用
     */
    isUsed?: boolean;

    /**
     * 特殊（拨款单id）
     */
    paybillId?: string;
}

/**
 * 归档文件视图对象
 */
interface ArchiveFileVo {
    /**
     * 主键（有序）
     */
    id?: string;

    /**
     * 结项ID（project_closure 表外键）
     */
    closeId?: string;

    /**
     * 项目ID（project_info 表外键）
     */
    projId?: string;

    /**
     * 父级ID（本表内关联）
     */
    parentId?: string;

    /**
     * 类型（F文件，D目录）
     */
    typeCode?: string;

    /**
     * 名称（目录名或文件名，带后缀）
     */
    name?: string;

    /**
     * 文件ID（name=F时，关联sys_file表）
     */
    fileId?: string;

    /**
     * 排序号
     */
    seqNo?: number;

    /**
     * 创建人
     */
    createBy?: string;

    /**
     * 创建时间
     */
    createTime?: Date;

    /**
     * 关联附件
     */
    attachment?: ISysAttachment;

    /**
     * 子节点
     */
    children?: ArchiveFileVo[];
}

/**
 * 归档文件数据传输对象
 */
interface ArchiveFileDto {
    /**
     * 主键
     */
    id?: string;

    /**
     * 项目ID（project_info 表外键，必填）
     */
    projId: string;

    /**
     * 父级ID（本表内关联）
     */
    parentId?: string;

    /**
     * 类型（F文件，D目录，必填）
     */
    typeCode: string;

    /**
     * 名称（目录名或文件名，带后缀，必填）
     */
    name: string;

    /**
     * 文件ID（name=F时，关联sys_file表）
     */
    fileId?: string;
}

/**
 * 归档拨款单文件数据传输对象
 */
interface ArchivePaybillFileDto {
    /**
     * 项目ID（project_info 表外键，必填）
     */
    projId: string;

    /**
     * 父级ID（本表内关联）
     */
    parentId?: string;

    /**
     * 拨款单ID（必填）
     */
    paybillId: string;

    /**
     * 源数据类型（必填）
     */
    sourceType: string;

    /**
     * 附件大类（资料清单名称，必填）
     */
    primaryType: string;
}