// src/casConfig.js
import { ref } from 'vue';
import { casConfigApi } from '@/api/cas';

// 创建响应式配置对象
export const casConfig = ref({
  casEnable: 1, // 默认值
});

// 异步加载 CAS 配置的函数
export async function loadCasConfig() {
  try {
   // const response = await fetch('/system/config/casConfig'); // 调用后端接口
    const response = await casConfigApi();
    const data = response.data;
    console.log(data);
    // 更新配置（自动触发响应式更新）
    casConfig.value.casEnable = data.configValue;
  } catch (error) {
    console.error('加载 CAS 配置失败:', error);
    // 保留默认值或显示错误提示
  }
}
