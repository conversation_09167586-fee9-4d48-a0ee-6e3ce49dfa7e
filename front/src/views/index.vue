<template>
  <div class="app-container" style="background-color: #F5FAFF;">
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="p-[3px] bg-white rounded-[8px] box-card" style="height: 270px; overflow-y: auto;">
          <div class="bg rounded-[5px] p-[15px]">
            <div class="flex justify-between relative ">
              <div class="flex">
                <div class="bg-[#F1F5FC] w-[100px] h-[100px] flex justify-center items-center">
                  <span
                    class="bg-[#007AFF] w-[60px] h-[60px] rounded-full flex justify-center items-center overflow-hidden"><img
                      src="../assets/images/yonghuming.png" /></span>
                </div>
                <h1 class="text-lg ml-[10px] text-[#444]">{{ userStore.user.nickName }}，欢迎您访问本系统<br /><span
                    class="text-base leading-[40px]">部门：{{ userStore.deptName }}</span></h1>
              </div>
              <div class="absolute right-0 top-[-10px]"><img src="../assets/images/index_pic1.png" /></div>
            </div>
            <div class="grid grid-cols-2 gap-4 border-t mt-[20px] mb-0 pt-[13px]">
              <div class="flex sybg1 items-center justify-around">
                <div
                  class="bg-white/50 backdrop-blur-sm rounded-full h-[55px] w-[55px] flex items-center justify-center border border-solid border-[#E1EEFF] flex-0">
                  <img src="../assets/images/syicon1.png" class="w-[30px]" />
                </div>
                <div class="text-[1rem] text-[#484848]">项目管理待办</div>
                <el-dropdown :disabled="getProjectTodoCount == 0">
                  <div class="arial_font text-[#F32C00] text-[28px] digital cursor-pointer hover:text-[#ff620c]"
                    :class="{ 'text-gray hover:text-gray': getProjectTodoCount == 0 }">{{
                      getProjectTodoCount }}
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="projectTodoMap['待受理'] > 0" @click="$router.push({ path: '/lib/manage' })">
                        <div class="w-[100px] flex justify-between"><span>待受理</span> <span
                            class="digital text-[#F32C00] text-[18px]">{{ projectTodoMap['待受理'] }}</span></div>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="projectTodoMap['待提交'] > 0" @click="$router.push({ path: '/lib/apply' })">
                        <div class="w-[100px] flex justify-between"><span>待提交</span> <span
                            class="digital text-[#F32C00] text-[18px]">{{ projectTodoMap['待提交'] }}</span></div>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="projectTodoMap['待计划'] > 0" @click="$router.push({ path: '/lib/apply' })">
                        <div class="w-[100px] flex justify-between"><span>待计划</span> <span
                            class="digital text-[#F32C00] text-[18px]">{{ projectTodoMap['待计划'] }}</span></div>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="projectTodoMap['已退回'] > 0" @click="$router.push({ path: '/lib/apply' })">
                        <div class="w-[100px] flex justify-between"><span>已退回</span> <span
                            class="digital text-[#F32C00] text-[18px]">{{ projectTodoMap['已退回'] }}</span></div>
                      </el-dropdown-item>


                    </el-dropdown-menu>
                  </template>
                </el-dropdown>

              </div>

              <div class="flex sybg2 items-center justify-around">
                <div
                  class="bg-white/50 backdrop-blur-sm rounded-full h-[55px] w-[55px] flex items-center justify-center border border-solid border-[#E1EEFF]">
                  <img src="../assets/images/syicon2.png" class="w-[30px]" />
                </div>
                <div class="text-[1rem] text-[#484848]">资金管理待办</div>
                <el-dropdown :disabled="getFundTodoCount == 0">
                  <div class="arial_font text-[#F32C00] text-[28px]  digital cursor-pointer hover:text-[#ff620c]"
                    :class="{ 'text-gray hover:text-gray': getFundTodoCount == 0 }">{{
                      getFundTodoCount }}
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="fundTodoMap['待审批'] > 0"
                        @click="$router.push({ path: '/fund/manage/manage/bill/audit' })">
                        <div class="w-[100px] flex justify-between"><span>待审批</span> <span
                            class="digital text-[#F32C00] text-[18px]">{{ fundTodoMap['待审批'] }}</span></div>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="fundTodoMap['已退回'] > 0"
                        @click="$router.push({ path: '/fund/manage/manage/bill/apply' })">
                        <div class="w-[100px] flex justify-between"><span>已退回</span> <span
                            class="digital text-[#F32C00] text-[18px]">{{ fundTodoMap['已退回'] }}</span></div>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="fundTodoMap['政府采购'] > 0"
                        @click="$router.push({ path: '/fund/gov/purchase/audit' })">
                        <div class="w-[100px] flex justify-between"><span>政府采购</span> <span
                            class="digital text-[#F32C00] text-[18px]">{{ fundTodoMap['政府采购'] }}</span></div>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="fundTodoMap['资金监督'] > 0"
                        @click="$router.push({ path: '/fund/supervise/audit-project' })">
                        <div class="w-[100px] flex justify-between"><span>资金监督</span> <span
                            class="digital text-[#F32C00] text-[18px]">{{ fundTodoMap['资金监督'] }}</span></div>
                      </el-dropdown-item>


                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>

            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card relative" style="height: 270px; overflow-y: auto; padding: 0!important;">
          <template v-slot:header>
            <div slot="header" class="clearfix ">
              <span class="tit">通知公告</span>
              <el-button style="margin-top:-5px ;float:right" type="text"
                @click="noticeTableShow = true;">更多>></el-button>
            </div>
          </template>
          <div class="absolute bottom-0 right-0 z-0">
            <!-- <img src="../assets/images/index_pic2.png" /> -->
          </div>
          <div v-for="notice in noticeList" class="gg flex justify-between text-[14px]"
            @click="handleNoticeShow(notice)">
            <a class="line-clamp-1 w-[70%] pl-[12px] hover:text-[#007AFF]">
              {{ notice.noticeTitle }}
            </a>
            <em class="not-italic text-[#666]">
              {{ dateFormat(notice.releaseTime) }}
            </em>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card" style="height: 550px; overflow-y: auto; " v-loading="chartLoading">
          <template v-slot:header>
            <div class="h-27px flex items-start justify-between">

              <span class="tit">项目库与资金统计</span>
              <el-radio-group v-model="projectAndFundType" size="small">
                <el-radio-button value="0">项目类型</el-radio-button>
                <el-radio-button value="1">项目用途</el-radio-button>
              </el-radio-group>
              <!-- <el-button style="margin-top:-5px ;float:right" type="text">更多>></el-button> -->
            </div>
          </template>
          <div ref="chartRef" class="mr-[10px] h-full">
            <!-- <img src='../assets/images/image2.png' style="object-fit:contain; height: 400px;width: 100%;" /> -->
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card" style="height: 550px; overflow-y: auto;" v-loading="budgetExecutionLoading">
          <template v-slot:header>
            <div class="h-27px">
              <span class="tit">预算执行排名情况（统计时间：{{ dayjs().format('YYYY年MM月') }}）</span>

              <!-- <el-button style="margin-top:-5px ;float:right" type="text"></el-button> -->
            </div>
          </template>
          <div class="mr-[10px] h-full">
            <el-table :data="budgetExecutionList" border height="calc(100% - 20px)" show-summary
              :summary-method="getSummaries">
              <el-table-column type="index" label="排名" align="center" width="60px"></el-table-column>
              <el-table-column prop="assessOrgname" label="考核主体" header-align="center" align="left" min-width="100px">
                <template #default="{ row }">
                  <template v-if="[100, 5479].includes(userStore.deptId) || row.assessOrgId == userStore.deptId">
                    <el-link type="primary" @click="handleViewBudgetExecutionDetaill(row)">{{ row.assessOrgname
                    }}</el-link>
                  </template>
                  <template v-else>
                    {{ row.assessOrgname }}
                  </template>
                </template>
              </el-table-column>
              <el-table-column label="年度计划(万元)" align="center">
                <el-table-column prop="planAmount" label="全年计划" header-align="center" align="right"
                  :formatter="(row) => numberFormat(row.planAmount, 2)"></el-table-column>
                <el-table-column prop="arrangedAmount" label="已安排计划" header-align="center" align="right"
                  :formatter="(row) => numberFormat(row.arrangedAmount, 2)"></el-table-column>
              </el-table-column>
              <el-table-column label="执行率" align="center">
                <el-table-column prop="executedAmount" label="已执行(万元)" header-align="center" align="right"
                  :formatter="(row) => numberFormat(row.executedAmount, 2)"></el-table-column>
                <el-table-column prop="proportion" label="全年" header-align="center" align="right"
                  :formatter="(row) => `${row.proportion} %`"></el-table-column>
                <el-table-column prop="currentProportion" :label="`1-${dayjs().month() + 1}月`" header-align="center"
                  align="right"></el-table-column>
              </el-table-column>
            </el-table>
            <!-- <img src='../assets/images/image1.png' style="object-fit: contain; height: 400px;width: 100%;" /> -->
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-dialog title="通知公告" v-model="noticeTableShow" width="900px" :close-on-click-modal="false" destroy-on-close>
      <NoticeTable></NoticeTable>
    </el-dialog>
    <el-dialog title="通知公告" v-model="noticeShow" width="900px" :close-on-click-modal="false" destroy-on-close>
      <NoticeDetail :notice-info="noticeInfo!"></NoticeDetail>
    </el-dialog>
    <el-dialog title="预算执行情况" v-model="budgetExecutionDetailShow" width="1200px" :close-on-click-modal="false"
      destroy-on-close>
      <BudgetExecutionDetail :dept-id="currentDeptId!"></BudgetExecutionDetail>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
// @ts-ignore
import { showNoticeList, getShowNotice } from "@/api/system/notice";
import { dateFormat, numberFormat, sumAmount } from "@/utils/common";
// @ts-ignore
import useUserStore from '@/store/modules/user'
import { getBudgetExecution, getFundTodo, getProjectAndFundStatistics, getProjectTodo } from "@/api/system/home";
import * as echarts from 'echarts/core'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import { dayjs, TableColumnCtx } from "element-plus";
import { template } from "lodash";
import BudgetExecutionDetail from "./index/budgetExecutionDetail.vue";
import { BigNumber } from "bignumber.js";
import NoticeTable from "./index/noticeTable.vue";
import FileTable from '@/components/Table/FileTable.vue';
import NoticeDetail from "./index/noticeDetail.vue";

echarts.use([BarChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, CanvasRenderer])

const userStore = useUserStore()



const { proxy } = getCurrentInstance() as any;


const activeTab = ref('first')

const projectTodoMap = ref<Record<string, number>>({})
const getProjectTodoCount = computed(() => sumAmount(Object.values(projectTodoMap.value)) ?? 0)
getProjectTodo().then(res => {
  projectTodoMap.value = res.data
})

const fundTodoMap = ref<Record<string, number>>({})
const getFundTodoCount = computed(() => sumAmount(Object.values(fundTodoMap.value)) ?? 0)
getFundTodo().then(res => {
  fundTodoMap.value = res.data
})

const noticeTableShow = ref(false)
const noticeList = ref<SysNotice[]>([])
showNoticeList({ pageSize: 5, pageNum: 1 }).then((res: IResult<SysNotice>) => {
  noticeList.value = res.rows ?? []
})
const noticeShow = ref(false)
const noticeInfo = ref<SysNotice>()
const handleNoticeShow = (notice: SysNotice) => {
  getShowNotice(notice.noticeId).then((res: IResult<SysNotice>) => {
    noticeShow.value = true
    noticeInfo.value = res.data
  })
}



const projectAndFundType = ref('0')
const chartLoading = ref(false)
const chartRef = ref<HTMLDivElement>()
const handleSearchStatictics = () => {
  chartLoading.value = true
  getProjectAndFundStatistics(projectAndFundType.value).then(res => {
    if (!res.data) return
    const years = Object.keys(res.data)
    let seriesList: { name: string, type: string, data: number[] }[] = [];
    if (projectAndFundType.value == '0') {
      seriesList = [
        { name: "投资建设类", type: 'A0', data: [] },
        { name: "运行维护类", type: 'B0', data: [] },
        { name: "政策性扶持", type: 'C0', data: [] },
      ];

    } else {
      seriesList = [
        { name: "基础设施建设维护", type: '5493', data: [] },
        { name: "安全应急", type: '5494', data: [] },
        { name: "绿色环保", type: '5495', data: [] },
        { name: "招商科创", type: '5497', data: [] },
        { name: "其他管理", type: '5559', data: [] },
      ];
    }
    years.forEach(year => {
      const yearData = res.data![year];
      seriesList.forEach(item => {
        item.data.push(yearData[item.type]);
      });
    });
    // console.log(result)
    let chart: echarts.ECharts | null = null

    const getOption = (): echarts.EChartsCoreOption => ({
      // title: { text: '堆叠柱状图示例' },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any) => {
          // params 是当前 X 轴类目下所有系列的值
          let total = sumAmount(params.map((t: any) => t.value))

          // 标题部分（类目 + 总额）
          let html = `${params[0].axisValue}（总额：${total}）<br/>`

          // 每个系列部分（名称、金额、占比）
          params.forEach((item: any) => {
            const percent = total ? BigNumber(item.value).dividedBy(total).multipliedBy(100).toFixed(2) : '0.00'
            html += `
          <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background:${item.color}"></span>
          ${item.seriesName}：${item.value}（${percent} %）<br/>
        `
          })
          return html
        }
      },
      legend: { top: 24 },
      grid: { left: 24, right: 24, bottom: 24, top: 72, containLabel: true },
      xAxis: {
        type: 'category',
        data: years,
        axisTick: { alignWithLabel: true }
      },
      yAxis: { type: 'value' },
      series: seriesList.map(s => ({
        name: s.name,
        type: 'bar',
        stack: 'total',        // 关键：堆叠
        emphasis: { focus: 'series' },
        data: s.data,
        barMaxWidth: 38,
      }))
    })
    if (!chart) {
      chart = echarts.init(chartRef.value)
    }
    chart.clear()
    chart.setOption(getOption())
  }).finally(() => chartLoading.value = false)
}
watch(projectAndFundType, handleSearchStatictics, { immediate: true })

const budgetExecutionLoading = ref(true)
const budgetExecutionList = ref<BudgetExecution[]>([])
getBudgetExecution().then(res => {
  budgetExecutionList.value = res.data ?? []
}).finally(() => budgetExecutionLoading.value = false)

const currentDeptId = ref()
const budgetExecutionDetailShow = ref(false)
const handleViewBudgetExecutionDetaill = (row: BudgetExecution) => {
  currentDeptId.value = row.assessOrgId
  budgetExecutionDetailShow.value = true
}

interface SummaryMethodProps<T = any> {
  columns: TableColumnCtx[]
  data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode | number | null)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
    }
    const planAmountSum = sumAmount(budgetExecutionList.value.map(t => t.planAmount)) ?? 0
    if (column.property == 'planAmount') {
      sums[index] = numberFormat(planAmountSum ?? 0, 2)
    }
    const arrangedAmountSum = sumAmount(budgetExecutionList.value.map(t => t.arrangedAmount)) ?? 0
    if (column.property == 'arrangedAmount') {
      sums[index] = numberFormat(arrangedAmountSum ?? 0, 2)
    }
    const executedAmountSum = sumAmount(budgetExecutionList.value.map(t => t.executedAmount)) ?? 0
    if (column.property == 'executedAmount') {
      sums[index] = numberFormat(executedAmountSum ?? 0, 2)
    }
    if (column.property == 'proportion') {
      if (arrangedAmountSum == 0) {
        sums[index] = '0.00 %'
      } else {
        sums[index] = numberFormat(BigNumber(executedAmountSum).dividedBy(arrangedAmountSum).multipliedBy(100).toNumber(), 2) + ' %'
      }

    }

  })
  return sums as any
}
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans",
  "Helvetica Neue",
  Helvetica,
  Arial,
  sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}

.bg {
  background: linear-gradient(90deg, #FFFFFF 0%, #EBF2FC 100%);
}

.border-t {
  border-top: solid 1px #D0DAEA;
}

.gg {
  border-bottom: solid 1px #ECECEC;
  margin-bottom: 10px;
  position: relative;
  line-height: 32px;
  height: 32px;
  overflow: hidden;
}

.gg::before {
  content: '';
  width: 5px;
  height: 5px;
  top: 12px;
  position: absolute;
  background-color: #C1DAE5;
  border-radius: 100%;
}

.tit {
  display: inline-block;
  position: relative;
  padding-left: 10px;
  font-weight: 500;
}

.tit::before {
  content: '';
  position: absolute;
  width: 4px;
  height: 14px;
  background-color: #007AFF;
  border-radius: 10px;
  top: 4px;
  left: 0;
}

.box-card {
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.05);
  border: none;
}

.sybg1 {


  background: url(../assets/images/sybg.png) no-repeat;
  background-size: cover;
  height: 95px;
  padding: 0 10px;
}

.sybg2 {
  background: url(../assets/images/sybg1.png) no-repeat;
  background-size: cover;
  height: 95px;
  padding: 0 1%;
}

.arial_font {
  font-family: Arial, Helvetica, sans-serif;
  font-weight: 400;
}

:deep(.el-card__body) {
  height: calc(100% - 41px);
}
</style>

<style>
.rich-text-content img {
  max-width: 100% !important;
  /* 确保图片宽度不会超出容器 */
  height: auto;
  /* 自动调整高度以保持比例 */
}
</style>