<template>
    <div v-loading="loading">
        <el-radio-group v-model="isAssess">
            <el-radio-button :value="0">上报主体</el-radio-button>
            <el-radio-button :value="1">考核主体</el-radio-button>
        </el-radio-group>
        <vxe-table ref="tableRef" :data="dataList" height="600px" class="mt-1" border show-footer :footer-data="footerData">
            <vxe-column type="seq" title="序号" width="60" align="center"></vxe-column>
            <vxe-column field="name" title="项目名称" header-align="center" align="left" min-width="120">
                <template #default="{ row }">
                    <el-link type="primary" @click="handleViewPayTable(row)">{{ row.name }}</el-link>
                </template>
            </vxe-column>
            <vxe-column field="assessOrgName" title="考核主体" align="center" width="120"></vxe-column>
            <vxe-column field="typeName" title="项目类型" align="center" width="140"></vxe-column>
            <vxe-column field="purposeName" title="项目用途" align="center" width="140"></vxe-column>
            <vxe-column field="year" title="年度" align="center" width="100"></vxe-column>
            <vxe-column field="arrangedAmount" title="已安排计划" header-align="center" align="right" width="120"
                :formatter="({ row }) => numberFormat(row.arrangedAmount, 2)"></vxe-column>
            <vxe-column field="executedAmount" title="已执行金额" header-align="center" align="right" width="120"
                :formatter="({ row }) => numberFormat(row.executedAmount, 2)"></vxe-column>
            <vxe-column field="executedRate" title="执行率" header-align="center" align="right" width="100"
                :formatter="({ row }) => `${row.executedRate}%`"></vxe-column>
        </vxe-table>

        <!-- <div class="flex justify-center mt-2">
            <el-button type="primary" @click="handlePrint">打印</el-button>
            <el-button>导出</el-button>
        </div> -->

        <el-dialog title="项目拨付明细" v-model="projectPayedTableShow" width="800px" :close-on-click-modal="false"
            destroy-on-close>
            <PayedTable :proj-id="currentProjectId"></PayedTable>
        </el-dialog>
    </div>
</template>

<script lang="tsx" setup>
import { getProjectBudgetExecution } from '@/api/system/home';
import { numberFormat, sumAmount } from '@/utils/common';
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import PayedTable from '../fund/manage/apply/component/payedTable.vue';
import { TableColumnCtx } from 'element-plus';

// @ts-ignore
VxeUI.use(VxePcUI)

const props = defineProps<{
    deptId: number
}>()

const loading = ref(false)
const isAssess = ref<0 | 1>(1)
const dataList = ref<ProjectBudgetExecution[]>([])
const handleSearch = () => {
    loading.value = true
    getProjectBudgetExecution(isAssess.value, props.deptId).then(res => {
        dataList.value = res.data ?? []
    }).finally(() => loading.value = false)
}
watch(isAssess, handleSearch, { immediate: true })

const projectPayedTableShow = ref(false)
const currentProjectId = ref()
const handleViewPayTable = (row: ProjectBudgetExecution) => {
    currentProjectId.value = row.id
    projectPayedTableShow.value = true
}

const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    tableRef.value?.getPrintHtml().then(res => {

        const html = res.html
        tableRef.value?.print({
            html: html,
        })
    })
}

const footerData = computed(() => {
    return [
        { 
            name: '总计', 
            arrangedAmount: numberFormat(sumAmount(dataList.value.map(t => t.arrangedAmount)) ?? 0, 2),
            executedAmount: numberFormat(sumAmount(dataList.value.map(t => t.executedAmount)) ?? 0, 2),
         }
        ]
})
</script>