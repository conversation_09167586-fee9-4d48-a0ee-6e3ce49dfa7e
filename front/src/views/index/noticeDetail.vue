<template>
     <el-scrollbar height="700px">
        <div class="rich-text-content" v-html="noticeInfo?.noticeContent"></div>

        <div class="flex flex-col gap-2">
            <div v-for="(file, index) in fileList" class="flex ">
                <span>{{ index + 1 }}、</span>
                <DownloadA :file="file"></DownloadA>
            </div>
        </div>
    </el-scrollbar>
</template>

<script lang="ts" setup>
import { getFileList1 } from '@/api/lib/project/file';
import DownloadA from '@/components/FileUpload/DownloadA.vue';


const props = defineProps<{
    noticeInfo: SysNotice
}>()

const fileList = ref<ISysAttachment[]>([]);
getFileList1(props.noticeInfo.noticeId!).then(res => {
    fileList.value = res.data ?? [];
});
</script>