<template>
    <div v-loading="noticeLoading">
        <!-- 表格数据 -->
        <el-table :data="noticeTableData" height="500px" border @row-click="handleNoticeShow">
            <el-table-column label="序号" align="center" type="index" width="100">
                <template #default="{ row, $index }">
                    <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                </template>
            </el-table-column>
            <el-table-column label="公告标题" header-align="center" align="left" prop="noticeTitle" :show-overflow-tooltip="true" />
            <el-table-column label="公告类型" align="center" prop="noticeType" width="100">
                <template #default="scope">
                    <dict-tag :options="sys_notice_type" :value="scope.row.noticeType" />
                </template>
            </el-table-column>
            <el-table-column label="发布人" align="center" prop="creator" width="100" />
            <el-table-column label="发布时间" align="center" prop="releaseTime" width="200">
                <template #default="scope">
                    <span>{{ dateFormat(scope.row.releaseTime, 'YYYY-MM-DD HH:mm:ss') }}</span>
                </template>
            </el-table-column>
        </el-table>
        <div class="flex justify-end">
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
        <el-dialog title="通知公告" v-model="noticeShow" width="900px" :close-on-click-modal="false" destroy-on-close>
             <NoticeDetail :notice-info="noticeInfo!"></NoticeDetail>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
// @ts-ignore
import { showNoticeList, getShowNotice } from "@/api/system/notice";
import { dateFormat } from "@/utils/common";
import NoticeDetail from "./noticeDetail.vue";

const { proxy } = getCurrentInstance() as any;
const { sys_notice_type } = proxy.useDict("sys_notice_type");

const noticeLoading = ref(false)
const noticeTableData = ref<SysNotice[]>([])
const total = ref(0)
const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const handleSearch = () => {
    noticeLoading.value = true
    showNoticeList(page).then((res: IResult<SysNotice>) => {
        noticeTableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => noticeLoading.value = false)
}
handleSearch()
const noticeShow = ref(false)
const noticeInfo = ref<SysNotice>()
const handleNoticeShow = (notice: SysNotice) => {
    getShowNotice(notice.noticeId).then((res: IResult<SysNotice>) => {
        noticeShow.value = true
        noticeInfo.value = res.data
    })
}
</script>