<template>
    <div class="app-container">
        <el-form :inline="true" :model="searchForm" class="filter-form" label-width="auto">
            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="!w-50" />
            </el-form-item>
            <el-form-item label="上报主体">
                <DeptSelect v-model="searchForm.submitOrgid" clearable class="w-50"></DeptSelect>
            </el-form-item>
            <el-form-item label="考核主体">
                <DeptSelect v-model="searchForm.assessOrgid" clearable class="w-50"></DeptSelect>
            </el-form-item>
            <el-form-item label="项目类型">
                <ProjectTypeSelect v-model="searchForm.typeId" placeholder="请选择项目类别" class="!w-50" clearable>
                </ProjectTypeSelect>
            </el-form-item>
            <el-form-item label="项目用途">
                <el-select v-model="searchForm.purposeIds" multiple collapse-tags placeholder="请选择项目用途" class="w-50" clearable>
                    <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="合同名称">
                <el-input v-model="searchForm.contractname" placeholder="请输入项目名称" clearable class="!w-50" />
            </el-form-item>
            <el-form-item label="收款单位">
                <el-input v-model="searchForm.accountName" placeholder="请输入项目名称" clearable class="!w-50" />
            </el-form-item>
            <el-form-item label="拨付日期">
                <DateRangePicker v-model:begin-date="searchForm.payBegin" v-model:end-date="searchForm.payEnd"
                    type="daterange" placeholder="" format="YYYY/MM/DD" value-format="YYYY-MM-DD" class="!w-60">
                </DateRangePicker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <!-- <el-button type="success" @click="handleExport">数据导出</el-button> -->
            </el-form-item>
        </el-form>

        <div v-loading="loading">
            <el-table :data="tableData" border show-summary :summary-method="getSummaries">
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="projName" label="项目名称" header-align="center" align="left" min-width="300px"
                    fixed="left"></el-table-column>
                <el-table-column prop="contractname" label="合同名称" header-align="center" align="left"
                    min-width="250px" />
                <el-table-column prop="amount" label="合同金额" header-align="center" align="right" min-width="120px"
                    :formatter="(row) => numberFormat(row.amount, 2)" />
                <el-table-column prop="totalamount" label="拨付总额" header-align="center" align="right" min-width="120px"
                    :formatter="(row) => numberFormat(row.totalamount, 2)" />
                <el-table-column prop="accountName" label="收款单位" align="center" min-width="200px" />
                <el-table-column prop="serialNumber" label="拨款单号" align="center" min-width="120px" />
                <el-table-column prop="payAmount" label="拨款金额" header-align="center" align="right" min-width="120px"
                    :formatter="(row) => numberFormat(row.payAmount, 2)" />
                <el-table-column prop="payDate" label="拨款日期" align="center" min-width="100px"
                    :formatter="(row) => dateFormat(row.payDate)" />
                <el-table-column prop="payVoucher" label="凭证号码" align="center" min-width="100px" />
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>

    </div>
</template>

<script setup lang="ts">
import { dayjs, TableColumnCtx } from 'element-plus'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
import { getContractAmount, getPayContractList } from '@/api/fund/search';
import { dateFormat, numberFormat } from '@/utils/common';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_basket_type');


const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const searchForm = reactive<FundPaybillContractConditionVo>({
    payBegin: dayjs().startOf('year').format('YYYY-MM-DD'),
    payEnd: dayjs().endOf('year').format('YYYY-MM-DD')
})
const total = ref(0)

const tableData = ref<FundPaybillContractlVo[]>([])
const tableAmount = ref<FundDataAmountSumVo>()
const loading = ref(false)
const handleSearch = () => {
    loading.value = true
    Promise.all([
        getPayContractList(searchForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getContractAmount(searchForm).then(res => {
            tableAmount.value = res.data
        })
    ]).finally(() => {
        loading.value = false
    })
}

const handleExport = () => {
    console.log('导出数据')
}

interface SummaryMethodProps<T = any> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计'
        }
        if (column.property == 'payAmount') {
            sums[index] = numberFormat(tableAmount.value?.contractTotalPayAmount ?? 0, 2)
        }

    })
    return sums as any
}

</script>
