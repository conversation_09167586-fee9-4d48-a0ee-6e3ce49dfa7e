<template>
    <div class="app-container">
        <el-form :inline="true" :model="searchForm" class="filter-form" label-width="auto">
            <el-form-item label="项目名称">
                <el-input v-model="searchForm.name" placeholder="请输入项目名称" clearable class="!w-50" />
            </el-form-item>
            <el-form-item label="上报主体">
                <DeptSelect v-model="searchForm.submitOrgid" clearable class="w-50"></DeptSelect>
            </el-form-item>
            <el-form-item label="考核主体">
                <DeptSelect v-model="searchForm.assessOrgid" clearable class="w-50"></DeptSelect>
            </el-form-item>
            <el-form-item label="项目类型">
                <ProjectTypeSelect v-model="searchForm.typeId" placeholder="请选择项目类别" class="!w-50" clearable>
                </ProjectTypeSelect>
            </el-form-item>
            <el-form-item label="项目属性">
                <el-select v-model="searchForm.natureCode" placeholder="请选择项目属性" class="w-50" clearable>
                    <!-- <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option> -->
                    <el-option label="结转项目" value="1"></el-option>
                    <el-option label="经常性项目" value="2"></el-option>
                    <el-option label="新增项目" value="3"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="项目批准金额">
                <el-input-number v-model="searchForm.approvedAmount" placeholder="" controls-position="right" clearable class="!w-50">
                    <template #suffix>元</template>
                </el-input-number>
            </el-form-item>
            
            
            
            <el-form-item label="执行区间">
                <DateRangePicker v-model:begin-date="searchForm.beginDate" v-model:end-date="searchForm.endDate"
                    type="daterange" placeholder="" format="YYYY" value-format="YYYY" class="!w-50" :clearable="false">
                </DateRangePicker>
            </el-form-item>
            <el-form-item label="执行率">
                <el-input-number v-model="searchForm.minRate" placeholder="" :controls="false" clearable class="!w-21">
                    <template #suffix>%</template>
                </el-input-number>
                <span style="margin: 0 8px;">至</span>
                <el-input-number v-model="searchForm.maxRate" placeholder="" :controls="false" clearable class="!w-21">
                    <template #suffix>%</template>
                </el-input-number>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <!-- <el-button type="success" @click="handleExport">数据导出</el-button> -->
            </el-form-item>
        </el-form>

        <el-table :data="tableData" border show-summary :summary-method="getSummaries">
            <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
            <el-table-column prop="projectName" label="项目名称" header-align="center" align="left" min-width="300px" fixed="left">
                <template #default="{ row }">
                    <el-link type="primary" @click="handleOpenPayedTable(row)">{{ row.name }}</el-link>
                </template>
            </el-table-column>
            <el-table-column prop="submitOrgname" label="上报主体" align="center" min-width="120px" />
            <el-table-column prop="assessOrgname" label="考核主体" align="center" min-width="120px" />
            <el-table-column prop="typeName" label="项目类型" align="center" min-width="120px" />
            <el-table-column prop="projType" label="项目属性" align="center" min-width="120px">
                <template #default="{ row }">
                    <span v-if="row.projType == '1'">结转项目</span>
                    <span v-if="row.projType == '2'">经常性项目</span>
                    <span v-if="row.projType == '3'">新增项目</span>
                </template>
            </el-table-column>
            <el-table-column prop="approvedAmount" label="项目批准金额" header-align="center" align="right" min-width="120px"
            :formatter="(row) => numberFormat(row.approvedAmount, 2)" />
            <el-table-column prop="year" label="年度" align="center" />
            <el-table-column prop="arrangedAmount" label="已安排计划" header-align="center" align="right" min-width="120px"
            :formatter="(row) => numberFormat(row.arrangedAmount, 2)" />
            <el-table-column prop="executedAmount" label="已执行金额" header-align="center" align="right" min-width="120px"
            :formatter="(row) => numberFormat(row.executedAmount, 2)" />
            <el-table-column prop="executedRate" label="执行率" header-align="center" align="right" min-width="120px"
            :formatter="(row) => `${row.executedRate} %`" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="handleSearch" />

        <el-dialog title="资金拨付明细" v-model="payedTableShow" width="800px" :close-on-click-modal="false"
            destroy-on-close>
            <PayedTable :proj-id="currentProjId!" />
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { dayjs, TableColumnCtx } from 'element-plus'
import PayedTable from '../manage/apply/component/payedTable.vue'
import { getBudgetList, getBudgetTotal } from '@/api/lib/project/search'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
import { numberFormat } from '@/utils/common';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_basket_type, project_status } = proxy.useDict("project_purpose", "project_nature", 'project_basket_type', 'project_status');


const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const searchForm = reactive<BudgetQueryVo>({
    beginDate: dayjs().startOf('year').format('YYYY-MM-DD'),
    endDate: dayjs().endOf('year').format('YYYY-MM-DD')
})
const total = ref(0)

const tableData = ref<BudgetQueryEntity[]>([])
const tableAmount = ref<BudgetQueryEntity>()
const loading = ref(false)
const handleSearch = () => {
    loading.value = true
    Promise.all([
        getBudgetList(searchForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getBudgetTotal(searchForm).then(res => {
            tableAmount.value = res.data
        })
    ]).finally(() => {
        loading.value = false
    })
}
handleSearch()

const payedTableShow = ref(false)
const currentProjId = ref<string>()
const handleOpenPayedTable = (row: any) => {
    payedTableShow.value = true
    currentProjId.value = row.id
}

const handleExport = () => {
    console.log('导出数据')
}

interface SummaryMethodProps<T = any> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计'
        }
        if (column.property == 'approvedAmount') {
            sums[index] = numberFormat(tableAmount.value?.approvedAmount ?? 0, 2)
        }
        if (column.property == 'arrangedAmount') {
            sums[index] = numberFormat(tableAmount.value?.arrangedAmount ?? 0, 2)
        }
        if (column.property == 'executedAmount') {
            sums[index] = numberFormat(tableAmount.value?.executedAmount ?? 0, 2)
        }

    })
    return sums as any
}
</script>
