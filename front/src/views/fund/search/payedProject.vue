<template>
    <div class="app-container">
        <el-form :inline="true" :model="searchForm" class="filter-form" label-width="auto">
            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="!w-50" />
            </el-form-item>
            <el-form-item label="上报主体">
                <DeptSelect v-model="searchForm.submitOrgid" clearable class="w-50"></DeptSelect>
            </el-form-item>
            <el-form-item label="考核主体">
                <DeptSelect v-model="searchForm.assessOrgid" clearable class="w-50"></DeptSelect>
            </el-form-item>
            <el-form-item label="项目类型">
                <ProjectTypeSelect v-model="searchForm.typeId" placeholder="请选择项目类别" class="!w-50" clearable>
                </ProjectTypeSelect>
            </el-form-item>
            <el-form-item label="项目用途">
                <el-select v-model="searchForm.purposeIds" multiple collapse-tags placeholder="请选择项目用途" class="w-50" clearable>
                    <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="年度">
                <el-date-picker v-model="searchForm.year" type="year" value-format="YYYY" :clearable="false"
                    class="!w-50" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <!-- <el-button type="success" @click="handleExport">数据导出</el-button> -->
            </el-form-item>
        </el-form>

        <div v-loading="loading">
            <el-table :data="tableData" border show-summary :summary-method="getSummaries"
                @cell-click="handleCellClick">
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="seqid" label="项目编号" align="center" width="80px" fixed="left"></el-table-column>
                <el-table-column prop="projName" label="项目名称" header-align="center" align="left" fixed="left"
                    min-width="300px"></el-table-column>
                <el-table-column prop="submitOrgname" label="上报主体" align="center" width="120px" />
                <el-table-column prop="assessOrgname" label="考核主体" align="center" width="120px" />
                <el-table-column prop="typeName" label="项目类型" align="center" width="150px" />
                <el-table-column prop="purpose" label="项目用途" align="center" width="150px">
                    <template #default="{ row }">
                        <span>{{jsonStrParseToJson<INameValue[]>(row.purpose)?.map(t => t.name).join('、')}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="projAmount" label="批准金额" header-align="center" align="right" min-width="130px"
                    :formatter="(row) => numberFormat(row.projAmount, 2)" />
                <el-table-column prop="totalPlanAmount" label="累计下达用款计划" header-align="center" align="right"
                    min-width="130px" :formatter="(row) => numberFormat(row.totalPlanAmount, 2)" />
                <el-table-column prop="yearPlanAmount" label="当年下达计划" header-align="center" align="right"
                    min-width="130px" :formatter="(row) => numberFormat(row.yearPlanAmount, 2)" />
                <el-table-column prop="totalPayAmount" label="累计实际支付" header-align="center" align="right"
                    min-width="130px" :formatter="(row) => numberFormat(row.totalPayAmount, 2)" />
                <el-table-column prop="funAmount" label="当年实际拨款" header-align="center" align="right" min-width="130px"
                    :formatter="(row) => numberFormat(row.funAmount, 2)" />
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>

        <el-dialog title="项目拨付明细" v-model="projectPayedTableShow" width="800px" :close-on-click-modal="false"
            destroy-on-close>
            <PayedTable :pay-list="payedList"></PayedTable>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { dayjs, TableColumnCtx } from 'element-plus'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import { getCurYearDetail, getPayTotalAmount, getPayTotalList, getPreYearDetail } from '@/api/fund/search';
import { jsonStrParseToJson, numberFormat } from '@/utils/common';
import PayedTable from '../manage/apply/component/payedTable.vue';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_basket_type');


const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const searchForm = reactive<FundPaybillTotalConditionVo>({
    year: dayjs().format('YYYY'),
})
const total = ref(0)

const tableData = ref<FundPaybillTotalVo[]>([])
const tableAmount = ref<FundDataAmountSumVo>()
const loading = ref(false)
const handleSearch = () => {
    loading.value = true
    Promise.all([
        getPayTotalList(searchForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getPayTotalAmount(searchForm).then(res => {
            tableAmount.value = res.data
        })
    ]).finally(() => {
        loading.value = false
    })
}
handleSearch()

const projectPayedTableShow = ref(false)
const payedList = ref<FundPaybillSkrSimpleVo[]>([])
const handleCellClick = (row: FundPaybillTotalVo, column: TableColumnCtx, cell: HTMLElement, event: Event) => {
    if (column.property === 'totalPayAmount') {
        getPreYearDetail({
            projId: row.id!,
            year: searchForm.year!
        }).then(res => {
            if (res.data && res.data.length > 0) {
                payedList.value = res.data
                projectPayedTableShow.value = true
            } else {
                proxy.$message.warning('暂无拨付数据')
            }
        })
    }
    if (column.property === 'funAmount') {
        getCurYearDetail({
            projId: row.id!,
            year: searchForm.year!
        }).then(res => {
            if (res.data && res.data.length > 0) {
                payedList.value = res.data
                projectPayedTableShow.value = true
            } else {
                proxy.$message.warning('暂无拨付数据')
            }
        })

    }
}

const handleExport = () => {
    console.log('导出数据')
}

interface SummaryMethodProps<T = any> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = '合计'
        }
        if (column.property == 'projAmount') {
            sums[index] = numberFormat(tableAmount.value?.projTotalAmount ?? 0, 2)
        }
        if (column.property == 'totalPlanAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalPaybillAmount ?? 0, 2)
        }
        if (column.property == 'yearPlanAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalCurrYearPlanAmount ?? 0, 2)
        }
        if (column.property == 'totalPayAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalPayRealAmount ?? 0, 2)
        }
        if (column.property == 'funAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalCurrYearAmount ?? 0, 2)
        }

    })
    return sums as any
}
</script>
