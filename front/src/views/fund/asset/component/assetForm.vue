<template>
    <!-- <el-scrollbar height="800px"> -->
    <div class="mb-15">
        <el-form ref="formRef" :model="form" :rules="formRules" class="flex flex-col gap-3" :inline-message="true">
            <el-descriptions :column="2" label-width="15%" border>
                <el-descriptions-item width="85%">
                    <template #label>
                        项目名称 <span class="text-red">*</span>
                    </template>
                    <el-form-item prop="projId" class="!mb-0">
                        <el-select v-model="form.projId" filterable clearable remote reserve-keyword
                            placeholder="请输入项目名称" remote-show-suffix :remote-method="remoteMethod"
                            :loading="projSelectLoading" @change="handleProjectChange" @clear="handleProjectClear">
                            <el-option v-for="item in projectOptions" :label="item.name" :value="item.id!" />
                        </el-select>
                    </el-form-item>

                </el-descriptions-item>
            </el-descriptions>
            <el-descriptions title="" :column="2" label-width="15%" border>
                <el-descriptions-item label="固定资产" width="85%" class-name="no-padding">
                    <el-descriptions :column="1" label-width="15%" border>
                        <el-descriptions-item label="建筑工程" width="85%" label-class-name="no-border-bottom no-border-top"
                            class-name="no-padding no-border-bottom">
                            <el-descriptions :column="1" label-width="20%" border>
                                <el-descriptions-item label="结构" width="80%" label-class-name="no-border-top"
                                    class-name="no-border-top">
                                    <el-input v-model="form.buildStruct" maxlength="255"></el-input>
                                </el-descriptions-item>
                                <el-descriptions-item label="面积">
                                    <div class="grid grid-cols-[70%_1fr] gap-1">
                                        <el-input-number v-model="form.buildArea" :min="0" controls-position="right"
                                            class="w-full"></el-input-number>
                                        <el-input v-model="form.buildAreaunit" placeholder="单位" maxlength="50"></el-input>
                                    </div>
                                </el-descriptions-item>
                                <el-descriptions-item label="金额">
                                    <el-input-number v-model="form.buildAmount" :min="0" controls-position="right"
                                        class="w-full"></el-input-number>
                                </el-descriptions-item>
                                <el-descriptions-item label="其中:分摊待摊投资" label-class-name="no-border-bottom"
                                    class-name="no-border-bottom">
                                    <el-input-number v-model="form.buildFtamount" :min="0" controls-position="right"
                                        class="w-full"></el-input-number>
                                </el-descriptions-item>
                            </el-descriptions>
                        </el-descriptions-item>
                    </el-descriptions>
                    <el-descriptions :column="1" label-width="15%" border>
                        <el-descriptions-item label="设备 工具 器具 家具 绿化等" width="85%" class-name="no-padding">
                            <el-descriptions :column="1" label-width="20%" border>
                                <el-descriptions-item label="名称" width="80%">
                                    <el-input v-model="form.otherName" maxlength="255"></el-input>
                                </el-descriptions-item>
                                <el-descriptions-item label="规格型号">
                                    <el-input v-model="form.otherModel" maxlength="255"></el-input>
                                </el-descriptions-item>
                                <el-descriptions-item label="数量">
                                    <div class="grid grid-cols-[70%_1fr] gap-1">
                                        <el-input-number v-model="form.otherQty" :min="0" controls-position="right"
                                            class="w-full"></el-input-number>
                                        <el-input v-model="form.otherQtyunit" placeholder="单位" maxlength="50"></el-input>
                                    </div>
                                </el-descriptions-item>
                                <el-descriptions-item label="金额">
                                    <el-input-number v-model="form.otherAmount" :min="0" controls-position="right"
                                        class="w-full"></el-input-number>
                                </el-descriptions-item>
                                <el-descriptions-item label="其中:设备安装费">
                                    <el-input-number v-model="form.otherSbamount" :min="0" controls-position="right"
                                        class="w-full"></el-input-number>
                                </el-descriptions-item>
                                <el-descriptions-item label="其中:分摊待摊投资" label-class-name="no-border-bottom"
                                    class-name="no-border-bottom">
                                    <el-input-number v-model="form.otherFtamount" :min="0" controls-position="right"
                                        class="w-full"></el-input-number>
                                </el-descriptions-item>
                            </el-descriptions>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="2" label-width="15%" border>
                <el-descriptions-item label="流动资产" width="85%" class-name="no-padding">
                    <el-descriptions :column="1" label-width="20%" border>
                        <el-descriptions-item label="名称" width="80%">
                            <el-input v-model="form.currentName" maxlength="255"></el-input>
                        </el-descriptions-item>
                        <el-descriptions-item label="金额">
                            <el-input-number v-model="form.currentAmount" :min="0" controls-position="right"
                                class="w-full"></el-input-number>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="2" label-width="15%" border>
                <el-descriptions-item label="流动资产" width="85%" class-name="no-padding">
                    <el-descriptions :column="1" label-width="20%" border>
                        <el-descriptions-item label="名称" width="80%">
                            <el-input v-model="form.intanName" maxlength="255"></el-input>
                        </el-descriptions-item>
                        <el-descriptions-item label="金额">
                            <el-input-number v-model="form.intanAmount" :min="0" controls-position="right"
                                class="w-full"></el-input-number>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="2" label-width="15%" border>
                <el-descriptions-item label="附件" width="85%" class-name="no-padding">
                    <div class="p-2">
                        <my-file-upload :accept="fileType" :data="{
                            sourceId: form.id,
                            sourceType: EFileSoureType.FUND_ASSETS,
                            primaryType: EFileSoureType.FUND_ASSETS,
                        }" @upload-success="fileTableRef?.updateTable"></my-file-upload>
                        <div class="mt-2">
                            <FileTable ref="fileTableRef" height="200px" :source-id="form.id!"
                                :primaryType="EFileSoureType.FUND_ASSETS" :hiddenColumns="['index', 'primaryType']">
                            </FileTable>
                        </div>
                    </div>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>
    </div>
    <!-- </el-scrollbar> -->

    <div class="absolute bottom-0 left-0 w-full h-[60px] z-10 flex justify-center items-center bg-white">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="danger" @click="$emit('cancel')">关闭</el-button>
    </div>


</template>

<script setup lang="ts">
import { addAssets, getAssetsDetail, editAssets, getAssetsProjList } from '@/api/fund/assets';
import FileTable from '@/components/Table/FileTable.vue';
import { genSnowId } from '@/utils/common';
import { EFileSoureType } from '@/utils/constants';
import { ElMessage, FormInstance, FormRules } from 'element-plus';

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const emit = defineEmits(['cancel', 'close']);
const props = defineProps<{
    formId: string
}>()
const fileTableRef = ref<FileTableExposeType | null>(null)
const form = reactive<FundAssetsVo>({
    id: genSnowId()
})
const projectOptions = ref<any[]>([])
const projSelectLoading = ref(false)
const remoteMethod = (query: string) => {
    if (query) {
        projSelectLoading.value = true
        getAssetsProjList(query).then(res => {
            projectOptions.value = res.data ?? []
            console.log(projectOptions.value)
        }).finally(() => {
            projSelectLoading.value = false
        })
    } else {
        projectOptions.value = []
    }
}
const handleProjectChange = (val: string) => {
    const project = projectOptions.value.find(item => item.id === val)
    if (project) {
        form.projId = project?.id
        form.projName = project?.name ?? ''
    }
}
const handleProjectClear = () => {
    form.projId = ''
    form.projName = ''
}

if (props.formId) {
    const res = await getAssetsDetail(props.formId)
    Object.assign(form, res.data)

    if (res.data?.projId) {
        projectOptions.value = [{ id: res.data?.projId, name: res.data?.projName }]
    }

}


const formRules = reactive<FormRules<typeof form>>({
    projId: [
        { required: true, message: '请选择项目', trigger: 'blur' }
    ],
})
const formRef = ref<FormInstance>()
const handleSave = () => {
    formRef.value?.validate(valid => {
        if (valid) {
            let data = Object.assign({}, form) as FundAssets
            let save = addAssets
            if (props.formId) {
                save = editAssets
            }
            save(data).then(() => {
                ElMessage.success('保存成功')
                emit('close')
            })
        }
    })
}
</script>

<style scoped>
.no-padding {
    padding: 0;
}

:deep(.no-border-bottom) {
    border-bottom: none !important;
}

:deep(.no-border-top) {
    border-top: none !important;
}
</style>