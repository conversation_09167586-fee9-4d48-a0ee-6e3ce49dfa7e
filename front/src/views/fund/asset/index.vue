<template>
    <div class="app-container">
        <div class="flex justify-between items-center">
            <!-- 查询表单 -->
            <el-form :inline="true" :model="searchForm" class="filter-form">
                <el-form-item label="项目名称">
                    <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="!w-40" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button type="success" @click="handleAdd">新增资产</el-button>
                </el-form-item>
            </el-form>
            <right-toolbar :showRefresh="false" :columns="columns" :search="false">
                <el-tooltip class="item" effect="dark" content="上传Excel" placement="top">
                    <MyFileUpload accept=".xlsx,.xls" action="/fund/assest/importData"
                        :before-upload="handleBeforeUpload" :on-error="handleUploadError"
                        @upload-success="handleUploadSuccess">
                        <el-button class="ml-3" circle icon="Upload" />
                    </MyFileUpload>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
                    <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
                </el-tooltip>
            </right-toolbar>
        </div>

        <div v-loading="loading">

            <!-- 数据表格 -->
            <el-table :data="tableData" border show-summary :summary-method="getSummaries">
                <el-table-column type="index" label="序号" width="60" align="center">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="projName" label="项目名称" width="300" align="center" />
                <el-table-column label="固定资产" align="center"
                    v-if="columns[0].visible || columns[1].visible || columns[2].visible || columns[3].visible || columns[4].visible || columns[5].visible || columns[6].visible || columns[7].visible || columns[8].visible || columns[9].visible">
                    <el-table-column label="建筑工程" align="center"
                        v-if="columns[0].visible || columns[1].visible || columns[2].visible || columns[3].visible">
                        <el-table-column prop="buildStruct" label="结构" align="center" v-if="columns[0].visible" />
                        <el-table-column prop="buildArea" label="面积" align="center" v-if="columns[1].visible">
                            <template #default="{ row }">
                                {{ row.buildArea }} {{ row.buildAreaunit }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="buildAmount" label="金额" header-align="center" align="right"
                            :formatter="(row: any) => numberFormat(row.buildAmount, 2)" v-if="columns[2].visible" />
                        <el-table-column prop="buildFtamount" label="其中:分摊待摊投资" header-align="center" align="right"
                            :formatter="(row: any) => numberFormat(row.buildFtamount, 2)" v-if="columns[3].visible" />
                    </el-table-column>
                    <el-table-column label="设备  工具  器具  家具 绿化等" align="center"
                        v-if="columns[4].visible || columns[5].visible || columns[6].visible || columns[7].visible || columns[8].visible || columns[9].visible">
                        <el-table-column prop="otherName" label="名称" align="center" v-if="columns[4].visible" />
                        <el-table-column prop="otherModel" label="规格型号" align="center" v-if="columns[5].visible" />
                        <el-table-column prop="otherQty" label="数量" align="center" v-if="columns[6].visible">
                            <template #default="{ row }">
                                {{ row.otherQty }} {{ row.otherQtyunit }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="otherAmount" label="金额" header-align="center" align="right"
                            :formatter="(row: any) => numberFormat(row.otherAmount, 2)" v-if="columns[7].visible" />
                        <el-table-column prop="otherSbamount" label="其中:设备安装费" header-align="center" align="right"
                            :formatter="(row: any) => numberFormat(row.otherSbamount, 2)" v-if="columns[8].visible" />
                        <el-table-column prop="otherFtamount" label="其中:分摊待摊投资" header-align="center" align="right"
                            :formatter="(row: any) => numberFormat(row.otherFtamount, 2)" v-if="columns[9].visible" />
                    </el-table-column>
                </el-table-column>
                <el-table-column label="流动资产" align="center" v-if="columns[10].visible || columns[11].visible">
                    <el-table-column prop="currentName" label="名称" align="center" v-if="columns[10].visible" />
                    <el-table-column prop="currentAmount" label="金额" header-align="center" align="right"
                        :formatter="(row: any) => numberFormat(row.currentAmount, 2)" v-if="columns[11].visible" />
                </el-table-column>
                <el-table-column label="无形资产" align="center" v-if="columns[12].visible || columns[13].visible">
                    <el-table-column prop="intanName" label="名称" align="center" v-if="columns[12].visible" />
                    <el-table-column prop="intanAmount" label="金额" header-align="center" align="right"
                        :formatter="(row: any) => numberFormat(row.intanAmount, 2)" v-if="columns[13].visible" />
                </el-table-column>
                <el-table-column prop="totalAmount" label="小计" header-align="center" align="right"
                    :formatter="(row: any) => numberFormat(row.totalAmount, 2)"></el-table-column>
                <el-table-column label="操作" align="center" width="150">
                    <template #default="{ row }">
                        <el-button type="primary" link icon="edit" @click="handleEdit(row)">编辑</el-button>
                        <el-button type="danger" link icon="delete" @click="handleRemove(row)">删除</el-button>

                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
        <el-drawer title="资产编辑" v-model="assetFormShow" size="700px" :close-on-click-modal="false" destroy-on-close>
            <AssetForm :formId="currentFormId" @cancel="assetFormShow = false;"
                @close="assetFormShow = false; handleSearch()" />
            <!-- <footer class="el-dialog__footer h-10"></footer> -->
        </el-drawer>
    </div>
</template>

<script setup lang="tsx">
import { dayjs, ElLoading, ElMessage, ElMessageBox, TableColumnCtx } from 'element-plus';
import AssetForm from './component/assetForm.vue'
import { getAssetsList, getAssetsTotallist, removeAsset } from '@/api/fund/assets';
import { numberFormat, sumAmount } from '@/utils/common';
import MyFileUpload from '@/components/FileUpload/MyFileUpload.vue';

const { proxy } = getCurrentInstance() as { proxy: any };

const page = reactive({
    pageNum: 1,
    pageSize: 15
})


const searchForm = reactive<FundAssetsQueryVo>({})
const total = ref(0)
const tableAmount = ref<{
    totalBuildAmount: number
    totalOtherAmount: number
    totalCurrentAmount: number
    totalIntanAmount: number
    totalAmount: number
}>()
const tableData = ref<FundAssetsVo[]>([])
const loading = ref(false)

const handleSearch = () => {
    loading.value = true
    Promise.all([
        getAssetsList(searchForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getAssetsTotallist(searchForm).then(res => {
            tableAmount.value = res
        })
    ]).finally(() => {
        loading.value = false
    })
}
handleSearch()

const assetFormShow = ref(false)
const handleAdd = () => {
    currentFormId.value = ''
    assetFormShow.value = true
}

const currentFormId = ref('')
const handleEdit = (row: FundAssetsVo) => {
    assetFormShow.value = true
    currentFormId.value = row.id!
}

const handleRemove = (row: FundAssetsVo) => {
    ElMessageBox.confirm('是否确认删除？', '提示', {})
        .then(() => {
            removeAsset([row.id!]).then(res => {
                ElMessage.success('操作成功')
                handleSearch()
            })
        })
}

let loadingInstance: any = null
const handleBeforeUpload = () => {
    loadingInstance = ElLoading.service({
        text: '资产数据导入中...',
        fullscreen: true
    })
}
const handleUploadError = () => {
    ElMessage.error('资产数据导入失败')
    handleSearch()
    loadingInstance?.close()
}
const handleUploadSuccess = () => {
    ElMessage.success('资产数据导入成功')
    handleSearch()
    loadingInstance?.close()
}

const handleDownloadExcel = () => {
    proxy.download('/fund/assest/download', {
        ...searchForm
    }, `项目(计划)申报${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}

// 列显隐信息
const columns = ref([
    { key: 0, label: `结构`, visible: false },
    { key: 1, label: `面积`, visible: true },
    { key: 2, label: `金额`, visible: true },
    { key: 3, label: `其中:分摊待摊投资`, visible: true },
    { key: 4, label: `名称`, visible: false },
    { key: 5, label: `规格型号`, visible: false },
    { key: 6, label: `数量`, visible: true },
    { key: 7, label: `金额`, visible: true },
    { key: 8, label: `其中:设备安装费`, visible: true },
    { key: 9, label: `其中:分摊待摊投资`, visible: true },
    { key: 10, label: `名称`, visible: false },
    { key: 11, label: `金额`, visible: true },
    { key: 12, label: `名称`, visible: true },
    { key: 13, label: `金额`, visible: true }
]);

interface SummaryMethodProps<T = FundPaybillVo> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = (
                <div class="text-left" >
                    {`合计`
                    }
                </div>
            )
        }
        if (column.property == 'buildAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalBuildAmount ?? 0, 2)
        }
        if (column.property == 'otherAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalOtherAmount ?? 0, 2)
        }
        if (column.property == 'currentAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalCurrentAmount ?? 0, 2)
        }
        if (column.property == 'intanAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalIntanAmount ?? 0, 2)
        }
        if (column.property == 'totalAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalAmount ?? 0, 2)
        }

    })
    return sums as any
}
</script>