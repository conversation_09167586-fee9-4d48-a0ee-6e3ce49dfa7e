<template>
    <div class="app-container">
        <!-- 查询表单 -->
        <el-form :inline="true" :model="searchForm" class="filter-form">
            <el-form-item label="项目年度">
                <el-date-picker v-model="searchForm.year" type="year" placeholder="请选择" clearable class="!w-40" />
            </el-form-item>
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.orgId" clearable class="!w-40"></DeptSelect>
            </el-form-item>

            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="!w-40" />
            </el-form-item>

            <el-form-item label="项目类型">
                <ProjectTypeSelect v-model="searchForm.typeId" placeholder="请选择项目类别" class="!w-40" clearable>
                </ProjectTypeSelect>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
            </el-form-item>
        </el-form>

        <div>
            <!-- 数据表格 -->
            <el-table :data="tableData" border v-loading="loading">
                <el-table-column type="index" label="序号" width="60" align="center">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="projName" label="项目名称" header-align="center" align="left" />
                <el-table-column prop="orgName" label="项目单位" align="center" width="300" />
                <el-table-column prop="period" label="执行期限" align="center" width="200">
                    <template #default="{ row }">
                        起：{{ dateFormat(row.periodstart) }}<br>
                        止：{{ dateFormat(row.periodend) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="300">
                    <template #default="{ row }">
                        <div class="h-10 flex items-end gap-5 justify-center">
                            <el-badge :value="row.ifperformance == '1' ? '有' : '无'" :hidden="row.ifperformance == '1'"
                                class="item">
                                <el-button type="primary" @click="handleOpenTarget(row)">目标</el-button>
                            </el-badge>
                            <el-badge :value="row.trackCount" class="item">
                                <el-button type="primary" @click="handleOpenTrack(row)">跟踪</el-button>
                            </el-badge>

                            <el-badge :value="row.evalCount" class="item">
                                <el-button type="primary" @click="handleOpenEvaluate(row)">自评价</el-button>
                            </el-badge>
                        </div>


                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
        <el-dialog title="绩效目标管理" v-model="targetFormShow" width="1000px" :close-on-click-modal="false" destroy-on-close
            @close="handleSearch">
            <TargetForm :proj-id="currentProjectId" @cancel="targetFormShow = false;" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog title="绩效跟踪记录" v-model="traceTableShow" width="1000px" :close-on-click-modal="false" destroy-on-close
            @close="handleSearch">
            <TraceTable :proj-id="currentProjectId" @cancel="traceTableShow = false;" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog title="绩效自评价记录" v-model="evaluateTableShow" width="1000px" :close-on-click-modal="false"
            destroy-on-close @close="handleSearch">
            <EvaluateTable :proj-id="currentProjectId" @cancel="evaluateTableShow = false;" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import TargetForm from './component/target/form.vue'
import TraceTable from './component/trace/table.vue'
import EvaluateTable from './component/evaluate/table.vue'
import { gePerformanceProjList } from '@/api/lib/project/performanceBatch'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { dayjs } from 'element-plus'
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import { dateFormat } from '@/utils/common'

const { proxy } = getCurrentInstance() as { proxy: any };

const page = reactive({
    pageNum: 1,
    pageSize: 10
})

const searchForm = reactive<ProjectPerformanceQuery>({
    year: dayjs().format('YYYY'),
})
const total = ref(0)
const tableData = ref<ProjectPerformanceVo[]>([])
const loading = ref(false)

const handleSearch = () => {
    loading.value = true
    gePerformanceProjList(searchForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()

const currentProjectId = ref('')
const targetFormShow = ref(false)
const handleOpenTarget = (row: ProjectPerformanceVo) => {
    targetFormShow.value = true
    currentProjectId.value = row.projId!
}
const traceTableShow = ref(false)
const handleOpenTrack = (row: ProjectPerformanceVo) => {
    traceTableShow.value = true
    currentProjectId.value = row.projId!
}
const evaluateTableShow = ref(false)
const handleOpenEvaluate = (row: ProjectPerformanceVo) => {
    evaluateTableShow.value = true
    currentProjectId.value = row.projId!
}
</script>