<template>
    <div>
        <el-descriptions :column="1" label-width="150px" border>
            <el-descriptions-item label="批次标题">
                <el-input v-model="form.title" placeholder="请输入" :disabled="form.status == '1'"></el-input>
            </el-descriptions-item>
        </el-descriptions>
        <el-scrollbar height="700">
            <el-table :data="dataList" :span-method="objectSpanMethod" border>
                <el-table-column label="一级指标" prop="indexLevel1" align="center" width="150px"></el-table-column>
                <!-- <el-table-column label="分解目标" align="center"> -->
                <el-table-column label="二级指标" prop="indexLevel2" align="center" width="150px"></el-table-column>
                <el-table-column label="三级指标" prop="indexLevel3" align="center"></el-table-column>
                <el-table-column label="年度指标值" prop="indexTarget" align="center"></el-table-column>
                <el-table-column label="跟踪期计划完成值" prop="z4" align="center">
                    <template #default="{ row }">
                        <el-input v-model="row.planPercent" :disabled="form.status == '1'" maxlength="1000" />
                    </template>
                </el-table-column>
                <el-table-column label="跟踪期实际完成值" prop="z4" align="center">
                    <template #default="{ row }">
                        <el-input v-model="row.actualPercent" :disabled="form.status == '1'" maxlength="1000" />
                    </template>
                </el-table-column>
                <el-table-column label="偏差原因分析" prop="z4" align="center">
                    <template #default="{ row }">
                        <el-input v-model="row.deviateReason" :disabled="form.status == '1'" maxlength="1000" />
                    </template>
                </el-table-column>
                <!-- </el-table-column> -->

            </el-table>
            <el-descriptions :column="1" label-width="150px" border>
                <el-descriptions-item label="绩效跟踪总体判断" class-name="no-padding">
                    <el-descriptions :column="1" label-width="150px" border>
                        <el-descriptions-item label="年度项目预算完成可能性">
                            <el-input v-model="form.possibleBudget" :disabled="form.status == '1'" type="textarea"
                                :rows="3" maxlength="2000"></el-input>
                        </el-descriptions-item>
                        <el-descriptions-item label="年度绩效目标完成可能性">
                            <el-input v-model="form.possibleTarget" :disabled="form.status == '1'" type="textarea"
                                :rows="3" maxlength="2000"></el-input>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-descriptions-item>
                <el-descriptions-item label="纠偏措施" class-name="no-padding">
                    <el-input v-model="form.remedyMethod" :disabled="form.status == '1'" type="textarea"
                        :rows="3" maxlength="4000"></el-input>
                </el-descriptions-item>
            </el-descriptions>
        </el-scrollbar>
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" @click="handleSave">保存</el-button>
            <el-button type="success" @click="handleSubmit">提交</el-button>
            <el-button type="danger" @click="$emit('cancel')">关闭</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { editPerformanceTrace, gePerformanceInfo, gePerformanceList, savePerformanceTrace } from '@/api/lib/project/performanceBatch';
import { genSnowId } from '@/utils/common';
import { ElMessage, ElMessageBox } from 'element-plus';


const emit = defineEmits(['close', 'cancel']);
const props = defineProps<{
    projId: string;
    batchId?: string
}>();

const form = reactive<ProjectPerformanceBatchVo>({
    id: props.batchId,
    possibleBudget: '',
    possibleTarget: '',
    remedyMethod: ''
} as ProjectPerformanceBatchVo)
const dataList = ref<ProjectPerformanceTrack[]>([])

if (props.batchId) {
    gePerformanceInfo(props.projId, '0', props.batchId).then(res => {
        Object.assign(form, res.data)
        dataList.value = res.data?.tracks ?? []
    })
}
else {
    gePerformanceList(props.projId).then(res => {
        dataList.value = (res.data ?? []).sort((a, b) => a.indexLevel1!.localeCompare(b.indexLevel1!))

    });
}


const loading = ref(false)
const handleSave = () => {
    let save = savePerformanceTrace
    if (form.id) {
        save = editPerformanceTrace
    }
    const data: any = {
        id: form.id,
        projId: props.projId,
        title: form.title,
        typeName: '0',
        status: 0,
        possibleBudget: form.possibleBudget,
        possibleTarget: form.possibleTarget,
        remedyMethod: form.remedyMethod,
        tracks: dataList.value
    }
    loading.value = true
    save(data).then(res => {
        ElMessage.success('保存成功')
        form.id = res.data ?? form.id
    }).finally(() => {
        loading.value = false
    })
}

const handleSubmit = () => {
    ElMessageBox.confirm('是否确认提交？', '提示', {
        type: 'warning'
    }).then(() => {
        let save = savePerformanceTrace
        if (form.id) {
            save = editPerformanceTrace
        }
        const data: any = {
            id: form.id,
            projId: props.projId,
            title: form.title,
            typeName: '0',
            status: 1,
            possibleBudget: form.possibleBudget,
            possibleTarget: form.possibleTarget,
            remedyMethod: form.remedyMethod,
            tracks: dataList.value
        }
        loading.value = true
        save(data).then(() => {
            ElMessage.success('保存成功')
            emit('close')
        }).finally(() => {
            loading.value = false
        })
    })
}

/**
 * 由chatgpt生成
 * @param param0 
 */
const objectSpanMethod = ({
    row,
    column,
    rowIndex,
    columnIndex,
}: {
    row: IProjectPerformance
    column: any
    rowIndex: number
    columnIndex: number
}) => {
    // 仅对 indexLevel1 列（第 0 列）进行合并
    if (columnIndex !== 0) return;

    const spanMap = new Map<string, { startIndex: number; count: number }>();
    const valueCount: Record<string, number> = {};

    // 遍历一次，记录每种 indexLevel1 出现的位置和次数
    dataList.value.forEach((item, idx) => {
        const key = item.indexLevel1 as any;
        if (!valueCount[key]) {
            valueCount[key] = 1;
            spanMap.set(key, { startIndex: idx, count: 1 });
        } else {
            valueCount[key]++;
            const spanInfo = spanMap.get(key);
            if (spanInfo) {
                spanInfo.count++;
            }
        }
    });

    const key = row.indexLevel1;
    const spanInfo = spanMap.get(key as any);

    if (spanInfo?.startIndex === rowIndex) {
        return {
            rowspan: spanInfo.count,
            colspan: 1,
        };
    } else {
        return {
            rowspan: 0,
            colspan: 0,
        };
    }
};
</script>

<style scoped>
.no-padding {
    padding: 0;
}

:deep(.no-border-bottom) {
    border-bottom: none !important;
}

:deep(.no-border-top) {
    border-top: none !important;
}
</style>