<template>
    <div>
        <el-descriptions :column="1" label-width="150px" border>
            <el-descriptions-item label="批次标题">
                <el-input v-model="form.title" placeholder="请输入" :disabled="form.status == '1'"></el-input>
            </el-descriptions-item>
        </el-descriptions>
        <el-scrollbar height="700">
            <el-table :data="dataList" :span-method="objectSpanMethod" border show-summary
                :summary-method="getSummaries">
                <el-table-column label="一级指标" prop="indexLevel1" align="center" width="150px"></el-table-column>
                <el-table-column label="二级指标" prop="indexLevel2" align="center" width="150px"></el-table-column>
                <el-table-column label="三级指标" prop="indexLevel3" align="center"></el-table-column>
                <el-table-column label="年度指标值" prop="indexTarget" align="center"></el-table-column>
                <el-table-column label="实际完成值" prop="yearActual" align="center">
                    <template #default="{ row }">
                        <el-input v-model="row.yearActual" :disabled="form.status == '1'" maxlength="1000" />
                    </template>
                </el-table-column>
                <el-table-column label="分值" prop="pointValue" align="center">
                    <template #default="{ row }">
                        <el-input-number v-model="row.pointValue" :min="0" controls-position="right"
                            :disabled="form.status == '1'" class="!w-full" />
                    </template>
                </el-table-column>
                <el-table-column label="得分" prop="scoreValue" align="center">
                    <template #default="{ row }">
                        <el-input-number v-model="row.scoreValue" :min="0" controls-position="right"
                            :disabled="form.status == '1'" class="!w-full" />
                    </template>
                </el-table-column>
                <el-table-column label="偏差原因分析及措施改进" prop="deviateReason" align="center">
                    <template #default="{ row }">
                        <el-input v-model="row.deviateReason" :disabled="form.status == '1'" maxlength="1000" />
                    </template>
                </el-table-column>

            </el-table>
        </el-scrollbar>
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" @click="handleSave">保存</el-button>
            <el-button type="success" @click="handleSubmit">提交</el-button>
            <el-button type="danger" @click="$emit('cancel')">关闭</el-button>
        </div>
    </div>
</template>

<script setup lang="tsx">
import { editPerformanceTrace, gePerformanceInfo, gePerformanceList, savePerformanceTrace } from '@/api/lib/project/performanceBatch';
import { genSnowId } from '@/utils/common';
import { ElMessage, ElMessageBox, TableColumnCtx } from 'element-plus';
import { sum } from 'lodash';


const emit = defineEmits(['close', 'cancel']);
const props = defineProps<{
    projId: string;
    batchId?: string
}>();

const form = reactive<ProjectPerformanceBatchVo>({
    id: props.batchId,
    evalTotal: undefined,
    evalScore: undefined,
} as ProjectPerformanceBatchVo)
const dataList = ref<ProjectPerformanceTrack[]>([])

if (props.batchId) {
    gePerformanceInfo(props.projId, '1', props.batchId).then(res => {
        Object.assign(form, res.data)
        dataList.value = res.data?.evals ?? []
    })
}
else {
    gePerformanceList(props.projId).then(res => {
        dataList.value = (res.data ?? []).sort((a, b) => a.indexLevel1!.localeCompare(b.indexLevel1!))

    });
}


const loading = ref(false)
const handleSave = () => {
    let save = savePerformanceTrace
    if (form.id) {
        save = editPerformanceTrace
    }
    const data: any = {
        id: form.id,
        projId: props.projId,
        title: form.title,
        typeName: '1',
        status: 0,
        evalTotal: form.evalTotal,
        evalScore: form.evalScore,
        evals: dataList.value
    }
    loading.value = true
    save(data).then(res => {
        ElMessage.success('保存成功')
        form.id = res.data ?? form.id
    }).finally(() => {
        loading.value = false
    })
}

const handleSubmit = () => {
    ElMessageBox.confirm('是否确认提交？', '提示', {
        type: 'warning'
    }).then(() => {
        let save = savePerformanceTrace
        if (form.id) {
            save = editPerformanceTrace
        }
        const data: any = {
            id: form.id,
            projId: props.projId,
            title: form.title,
            typeName: '1',
            status: 1,
            evalTotal: form.evalTotal,
            evalScore: form.evalScore,
            evals: dataList.value
        }
        loading.value = true
        save(data).then(() => {
            ElMessage.success('保存成功')
            emit('close')
        }).finally(() => {
            loading.value = false
        })
    })
}

/**
 * 由chatgpt生成
 * @param param0 
 */
const objectSpanMethod = ({
    row,
    column,
    rowIndex,
    columnIndex,
}: {
    row: IProjectPerformance
    column: any
    rowIndex: number
    columnIndex: number
}) => {
    // 仅对 indexLevel1 列（第 0 列）进行合并
    if (columnIndex !== 0) return;

    const spanMap = new Map<string, { startIndex: number; count: number }>();
    const valueCount: Record<string, number> = {};

    // 遍历一次，记录每种 indexLevel1 出现的位置和次数
    dataList.value.forEach((item, idx) => {
        const key = item.indexLevel1 as any;
        if (!valueCount[key]) {
            valueCount[key] = 1;
            spanMap.set(key, { startIndex: idx, count: 1 });
        } else {
            valueCount[key]++;
            const spanInfo = spanMap.get(key);
            if (spanInfo) {
                spanInfo.count++;
            }
        }
    });

    const key = row.indexLevel1;
    const spanInfo = spanMap.get(key as any);

    if (spanInfo?.startIndex === rowIndex) {
        return {
            rowspan: spanInfo.count,
            colspan: 1,
        };
    } else {
        return {
            rowspan: 0,
            colspan: 0,
        };
    }
};

interface SummaryMethodProps<T = ProjectPerformanceTrack> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns: tableColumns, data } = param
    const sums: (string | VNode | number | null)[] = []
    tableColumns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = (
                <div class="text-center">总分</div>
            )
        }
        if (column.property == 'pointValue') {
            sums[index] = (
                <el-input-number modelValue={form.evalTotal}
                    onUpdate:modelValue={(val: number) => form.evalTotal = val} min={0} controls-position="right" class="w-full"></el-input-number>
            )
        }
        if (column.property == 'scoreValue') {
            sums[index] = (
                <el-input-number modelValue={form.evalScore}
                    onUpdate:modelValue={(val: number) => form.evalScore = val} min={0} controls-position="right" class="w-full"></el-input-number>
            )
        }
    })
    return sums as any
}
</script>

<style scoped>
.no-padding {
    padding: 0;
}

:deep(.no-border-bottom) {
    border-bottom: none !important;
}

:deep(.no-border-top) {
    border-top: none !important;
}
</style>