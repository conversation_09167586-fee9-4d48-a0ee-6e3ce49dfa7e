<template>
    <div>
        <el-table :data="tableData" border height="500px">
            <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
            <el-table-column prop="title" label="标题" align="center"></el-table-column>
            <el-table-column prop="createTime" label="日期" width="120" align="center"
                :formatter="(row: any) => dateFormat(row.createTime)"></el-table-column>
            <el-table-column prop="createBy" label="提交人" width="120" align="center"></el-table-column>
            <el-table-column prop="createBy" label="状态" width="120" align="center">
                <template #default="{ row }">
                    <el-tag v-if="row.status === '0'" type="info">暂存</el-tag>
                    <el-tag v-if="row.status === '1'" type="success">已提交</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
                <template #default="{ row }">
                    <el-button v-if="row.status === '0'" type="primary" icon="edit" link
                        @click="handleEdit(row)">编辑</el-button>
                    <el-button v-if="row.status === '1'" type="primary" icon="view" link
                        @click="handleView(row)">查看</el-button>
                    <el-button type="danger" icon="delete" link
                        @click="handleRemove(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 操作按钮 -->
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="success" @click="handleAdd">新建自评价</el-button>
        </div>
        <el-dialog title="自评价表" v-model="traceFormShow" width="1200px" :close-on-click-modal="false" destroy-on-close
            @close="handleSearch()">
            <TraceForm :proj-id="projId" :batch-id="currentBatchId" @cancel="traceFormShow = false;"
                @close="traceFormShow = false;" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { gePerformanceBatchList, removePerformanceBatch } from '@/api/lib/project/performanceBatch';
import TraceForm from './form.vue';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps<{
    projId: string;
}>();

const tableData = ref<ProjectPerformanceBatch[]>([])
const handleSearch = () => {
    gePerformanceBatchList(props.projId, '1').then(res => {
        tableData.value = res.data ?? []
    })
}
handleSearch()

const currentBatchId = ref<string>()
const traceFormShow = ref(false);
const handleAdd = () => {
    currentBatchId.value = undefined
    traceFormShow.value = true;
}

const handleEdit = (row: ProjectPerformanceBatch) => {
    currentBatchId.value = row.id!
    traceFormShow.value = true;
}

const handleView = (row: ProjectPerformanceBatch) => {
    currentBatchId.value = row.id!
    traceFormShow.value = true;
}

const handleRemove = (row: ProjectPerformanceBatch) => {
    ElMessageBox.confirm('是否确认删除？', '提示', {
        type: 'warning'
    }).then(() => {
        removePerformanceBatch(row.id!).then(() => {
            ElMessage.success('删除成功！')
            handleSearch()

        })
    })
}
</script>