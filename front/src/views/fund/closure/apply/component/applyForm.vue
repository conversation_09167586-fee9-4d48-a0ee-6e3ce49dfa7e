<template>
    <div>
        <el-descriptions :column="1" label-width="20%" border>
            <el-descriptions-item width="80%">
                <template #label>
                    项目名称 <span class="text-red">*</span>
                </template>
                {{ props.data?.projName }}
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    结项理由 <span class="text-red">*</span>
                </template>
                <el-select v-model="form.handyReason" class="!w-40">
                    <el-option label="完成拨款" value="完成拨款"></el-option>
                    <el-option label="项目终止" value="项目终止"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                </el-select>
                <el-input v-model="form.applyReason" type="textarea" :rows="5" class="mt-1" maxlength="1000"></el-input>
            </el-descriptions-item>
        </el-descriptions>
        <!-- 操作按钮 -->
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
            <el-button type="success" :loading="loading" @click="handleSubmit">提交</el-button>
            <el-button @click="$emit('close')">关闭窗口</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getClosureDetail, saveClosure, submitClosure } from '@/api/lib/project/closure';
import { ElMessage, ElMessageBox } from 'element-plus';

const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    data?: ProjectClosureListVo
}>()

const form = reactive<ProjectClosureVo>({})
if (props.data?.id) {
    const res = await getClosureDetail(props.data.id)
    Object.assign(form, res.data)
}

watch(() => form.handyReason, () => {
    form.applyReason = ''
})

const loading = ref(false)
const handleSave = () => {
    loading.value = true
    saveClosure({
        id: form.id,
        projId: props.data?.projId!,
        applyReason: form.applyReason,
        handyReason: form.handyReason
    }).then(res => {
        ElMessage.success('保存成功')
        form.id = res.data
    }).finally(() => {
        loading.value = false
    })
}

const handleSubmit = () => {
    if (!form.handyReason && !form.applyReason) {
        ElMessage.warning('请选择或输入结项理由')
        return
    }
    ElMessageBox.confirm('是否确认提交？', '提示', {
        type: 'warning'
    }).then(() => {
        loading.value = true
        submitClosure({
            id: form.id,
            projId: props.data?.projId!,
            applyReason: form.applyReason,
            handyReason: form.handyReason
        }).then(res => {
            ElMessage.success('保存成功')
            emit('close')
        }).finally(() => {
            loading.value = false
        })
    })
}

</script>