<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :inline="true" :model="searchForm" label-width="auto">
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.applyOrgid" clearable class="!w-50"></DeptSelect>
            </el-form-item>

            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" class="!w-50" clearable />
            </el-form-item>

            <el-form-item label="项目年度">
                <el-date-picker v-model="searchForm.firstPayYear" type="year" value-format="YYYY" placeholder="首次使用发展资金的年度" class="!w-50" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" class="!w-50" clearable>
                    <el-option v-for="item in closure_status" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
            </el-form-item>

            <el-form-item>
                <el-checkbox v-model="searchForm.isCompleted" :true-value="0" :false-value="1">仅显示‘待结项’项目</el-checkbox>
            </el-form-item>
        </el-form>

        <div v-loading="loading">
            <!-- 数据表格 -->
            <el-table :data="tableData" show-summary :summary-method="getSummaries" border>
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left" >
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="projName" label="项目名称" header-align="center" align="left" width="300px" fixed="left" />
                <el-table-column prop="applyOrgname" label="项目单位" align="center" width="150px" />
                <el-table-column prop="firstPayYear" label="项目年度" align="center" width="100px" />
                <el-table-column prop="projAmount" label="项目金额(万元)" header-align="center" align="right" width="110px"
                    :formatter="t => numberFormat(t.projAmount, 4)">
                    <template #header>
                        <div>项目金额</div>
                        <div>(万元)</div>
                    </template>
                </el-table-column>
                <el-table-column prop="payedAmount" label="累计执行金额(万元)" header-align="center" align="right" width="110px"
                    :formatter="t => numberFormat(t.payedAmount, 4)">
                    <template #header>
                        <div>累计执行金额</div>
                        <div>(万元)</div>
                    </template>
                </el-table-column>
                <el-table-column prop="balanceAmount" label="未执行(万元)" header-align="center" align="right" width="110px"
                    :formatter="t => numberFormat(t.balanceAmount, 4)">
                    <template #header>
                        <div>未执行</div>
                        <div>(万元)</div>
                    </template>
                </el-table-column>
                <el-table-column prop="applyUsername" label="申请人" align="center" width="110px" />
                <el-table-column prop="applyTime" label="申请日期" align="center" width="130px"
                    :formatter="t => dateFormat(t.applyTime)" />
                <el-table-column prop="applyReason" label="申请理由" align="center">
                    <template #default="{ row }">
                        {{ row.handyReason ?? row.applyReason }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center" width="100px">
                    <template #default="{ row }">
                        <dict-tag :options="closure_status" :value="row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100px" fixed="right">
                    <template #default="{ row }">
                        <el-button v-if="row.canDoApply" v-hasPermi="['project:closure:save', 'project:closure:submit']" type="primary" link size="small"
                            @click="handleApply(row)">申请结项</el-button>
                        <template v-if="(row.status == 0 || row.status == -1) && row.canDoEdit">
                            <el-button type="primary" link size="small" @click="handleEdit(row)">编辑</el-button>
                            <el-button type="danger" link size="small" @click="handleRemove(row)">删除</el-button>
                        </template>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
        <el-dialog title="结项申请" v-model="applyFormShow" width="600px" :close-on-click-modal="false" destroy-on-close>
            <ApplyForm :data="currentRow" @cancel="applyFormShow = false;"
                @close="applyFormShow = false; handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox, TableColumnCtx } from 'element-plus'
import ApplyForm from './component/applyForm.vue'
import { getClosureList, getClosureListAmount, removeClosure } from '@/api/lib/project/closure'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { dateFormat, numberFormat } from '@/utils/common';

const { proxy } = getCurrentInstance() as { proxy: any };
const { closure_status } = proxy.useDict("closure_status");


const page = reactive({
    pageNum: 1,
    pageSize: 15
})
const searchForm = reactive<ProjectClosureQuery>({
    isCompleted: 0
})
const total = ref(0)
const tableData = ref<ProjectClosureListVo[]>([])
const tableAmount = ref<ProjectClosureAmountSumVo>()
const loading = ref(false)

const handleSearch = () => {
    loading.value = true
    Promise.all([
        getClosureList(searchForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getClosureListAmount(searchForm).then(res => {
            tableAmount.value = res.data
        })
    ]).finally(() => {
        loading.value = false
    })
}
handleSearch()
watch(() => searchForm.isCompleted, () => handleSearch())

const currentRow = ref<ProjectClosureListVo>()
const applyFormShow = ref(false)
const handleApply = (row: ProjectClosureListVo) => {
    applyFormShow.value = true
    currentRow.value = row
}

const handleEdit = (row: ProjectClosureListVo) => {
    applyFormShow.value = true
    currentRow.value = row
}

const handleRemove = (row: ProjectClosureListVo) => {
    ElMessageBox.confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        removeClosure([row.id!]).then(res => {
            ElMessage.success('删除成功');
            handleSearch()
        })
    })

}


interface SummaryMethodProps<T = any> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {
        if (index == 0) {
            sums[index] = '合计'
        }
        if (column.property === 'projAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalProjAmount ?? 0, 4)
        }
        if (column.property === 'payedAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalPayedAmount ?? 0, 4)
        }
        if (column.property === 'balanceAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalBalanceAmount ?? 0, 4)
        }

    })
    return sums as any
}
</script>
