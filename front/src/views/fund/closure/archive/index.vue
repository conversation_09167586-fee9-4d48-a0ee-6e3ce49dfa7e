<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :inline="true" :model="searchForm" label-width="auto">
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.applyOrgid" clearable class="!w-50"></DeptSelect>
            </el-form-item>

            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" class="!w-50" clearable />
            </el-form-item>

            <el-form-item label="项目年度">
                <el-date-picker v-model="searchForm.firstPayYear" type="year" value-format="YYYY"
                    placeholder="首次使用发展资金的年度" class="!w-50" />
            </el-form-item>

            <el-form-item label="申请日期">
                <DateRangePicker v-model:begin-date="searchForm.applyTimeBegin"
                    v-model:end-date="searchForm.applyTimeEnd" type="daterange" placeholder="" format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD" class="!w-60">
                </DateRangePicker>
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" class="!w-50" clearable>
                    <el-option v-for="item in closure_status" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="info" @click="handleDownload">数据导出</el-button>
            </el-form-item>
        </el-form>

        <div>
            <!-- 数据表格 -->
            <el-table :data="tableData" border v-loading="loading">
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left" >
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="projName" label="项目名称" header-align="center" align="left" min-width="300" fixed="left" />
                <el-table-column prop="applyOrgname" label="项目单位" align="center" width="150px" />
                <el-table-column prop="firstPayYear" label="项目年度" align="center" width="100px" />
                <el-table-column prop="projAmount" label="项目金额(万元)" header-align="center" align="right" width="110px"
                    :formatter="t => numberFormat(t.projAmount, 4)">
                    <template #header>
                        <div>项目金额</div>
                        <div>(万元)</div>
                    </template>
                </el-table-column>
                <el-table-column prop="payedAmount" label="累计执行金额(万元)" header-align="center" align="right" width="110px"
                    :formatter="t => numberFormat(t.payedAmount, 4)">
                    <template #header>
                        <div>累计执行金额</div>
                        <div>(万元)</div>
                    </template>
                </el-table-column>
                <el-table-column prop="applicant" label="申请情况" align="center" width="200px">
                    <template #default="{ row }">
                        <div class="flex flex-col justify-start items-start">
                            <span>申&ensp;请&ensp;&ensp;人：{{ row.applyUsername }}</span>
                            <span>申请日期：{{ dateFormat(row.applyTime) }}</span>
                            <span>申请理由：{{ row.handyReason ?? row.applyReason }}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center" width="100px">
                    <template #default="{ row }">
                        <dict-tag :options="closure_status" :value="row.status" />
                    </template>
                </el-table-column>
                <el-table-column prop="archiveNumber" label="归档编号" align="center" width="150px" />
                <el-table-column label="操作" align="center" width="100px" fixed="right">
                    <template #default="{ row }">
                        <el-button v-if="row.status == 4" type="text" size="small" @click="handleArchive(row)">查看</el-button>
                        <el-button v-else type="text" size="small" @click="handleArchive(row)">资料整理</el-button>
                        
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
        <el-dialog :title="`${currentRow?.projName}-项目归档`" v-model="archiveFormShow" width="1200px" :close-on-click-modal="false" destroy-on-close>
            <ArchiveForm :data="currentRow!" @close="archiveFormShow = false;handleSearch()" />
            <!-- <footer class="el-dialog__footer h-10"></footer> -->
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { dayjs, TableColumnCtx } from 'element-plus'
import ArchiveForm from './component/archiveForm.vue'
import { getClosureArchiveList } from '@/api/lib/project/closureArchive';
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { dateFormat, numberFormat } from '@/utils/common';
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';

const { proxy } = getCurrentInstance() as { proxy: any };
const { closure_status } = proxy.useDict("closure_status");

const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const searchForm = reactive<ProjectClosureQuery>({
    applyTimeBegin: dayjs().format('YYYY-01-01'),
    applyTimeEnd: dayjs().format('YYYY-12-31'),
})
const total = ref(0)
const tableData = ref<ProjectClosureListVo[]>([])
const loading = ref(false)

const handleSearch = () => {
    loading.value = true
    getClosureArchiveList(searchForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()

const currentRow = ref<ProjectClosureListVo>()
const archiveFormShow = ref(false)
const handleArchive = (row: ProjectClosureListVo) => {
    archiveFormShow.value = true
    currentRow.value = row
}

const handleDownload = () => {
    proxy.download('/project/closureArchive/export', {
        ...searchForm
    }, `归档导出${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>
<style scoped>
:deep(.el-tree){
    font-size:12px;
}
</style>
