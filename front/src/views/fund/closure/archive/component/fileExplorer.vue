<template>
    <div>
        <div class="p-2 border border-solid">
            <div class="flex items-center gap-3">
                <el-icon class="cursor-pointer hover:opacity-80" size="20" @click="handleBack">
                    <Back />
                </el-icon>
                <el-icon class="cursor-pointer" size="20" @click="handleRefresh">
                    <RefreshLeft />
                </el-icon>
                <el-breadcrumb separator="/">
                    <el-breadcrumb-item class="cursor-pointer" v-for="(path, index) in paths"
                        @click="handleToPath(path, index)">
                        {{ path.name }}
                    </el-breadcrumb-item>
                </el-breadcrumb>
            </div>

        </div>
        <div ref="containerRef" class="border border-solid mt-2" v-loading="loading">
            <el-scrollbar height="500px" @click="handleBlankClick">
                <div class="p-2">
                    <div class="flex flex-wrap gap-3">
                        <div v-for="item in files" :id="String(item.id)"
                            class="archive-file w-30 h-30 hover:bg-[--el-color-info-light-8] rounded-md">
                            <div class="grid grid-rows-[60%_1fr] items-center justify-center h-full rounded-md p-2"
                                :class="{ 'bg-[--el-color-info-light-8]': selectFiles.includes(item) }"
                                @click.stop="handleClick(item)" @dblclick.stop="handleDblClick(item)">
                                <div class="text-center">
                                    <template v-if="item.typeCode == EFileType.D">
                                        <i class="iconfont icon-wenjianjia text-10"></i>
                                    </template>
                                    <template v-else>
                                        <component :is="getFileIconByExt(item.attachment?.fileExt)"></component>
                                    </template>
                                </div>
                                <span class="select-none break-all text-center truncate-2" :title="item.name">{{
                                    item.name }}</span>
                            </div>

                        </div>
                    </div>

                </div>

            </el-scrollbar>
        </div>
        <div class="flex justify-center gap-3 mt-2">
            <template v-if="data.status != 4">
                <MyFileUpload action="/project/closureArchive/uploadArchive" :accept="fileType" :data="{
                    projId: data.projId,
                    parentId: getCurrentParentId,
                    sourceType: EFileSoureType.PROJECT_ARCHIVE,
                    primaryType: EFileSoureType.PROJECT_ARCHIVE
                }" :before-upload="handleBeforeUpload" :on-error="handleUploadError"
                    @upload-success="handleUploadSuccess">
                    <el-button type="primary">文件上传</el-button>
                </MyFileUpload>

                <el-button type="success" @click="handleArchive">确认归档</el-button>
            </template>
            <template v-else>
                <el-button type="primary" @click="handleDownload">一键下载</el-button>
                <el-button type="danger" class="!ml-0" @click="handleCancelArchive">取消归档</el-button>
            </template>
            <el-button class="!ml-0" @click="$emit('close')">关闭窗口</el-button>
        </div>
    </div>
    <el-dialog v-model="fileViewShow" width="1000px" append-to-body>
        <template #header>
            <span class="text-[16px]">预览文件</span>
        </template>
        <iframe :src="fileUrl" class="w-full h-[700px] border-none" />
    </el-dialog>
</template>

<script setup lang="tsx">
import '@imengyu/vue3-context-menu/lib/vue3-context-menu.css'
import ContextMenu, { MenuItem } from '@imengyu/vue3-context-menu'
import {
    View, DocumentCopy, DocumentDelete, DocumentRemove,
    DocumentChecked, Folder, Document, DocumentAdd, FolderAdd, Back, RefreshLeft, Delete,
    SetUp
} from '@element-plus/icons-vue'
import { downloadFile } from '@/api/lib/project/file'
import { dayjs, ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { genSnowId } from '@/utils/common'
import { listArchiveFiles, listArchivePath, removeArchiveFile, revokeArchive, saveArchiveFile, saveArchiveNumber, saveArchivePaybillFile } from '@/api/lib/project/closureArchive'
import { EFileSoureType, EFileType } from '@/utils/constants'
import MyFileUpload from '@/components/FileUpload/MyFileUpload.vue'

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const emit = defineEmits(['close'])
const props = defineProps<{
    projId: string
    data: ProjectClosureListVo
}>()


const config = {
    previewFileIconSettings: {
        'doc': (<i class="iconfont icon-word-full text-10"></i>),
        'xls': (<i class="iconfont icon-excel-full text-10"></i>),
        'pdf': (<i class="iconfont icon-pdf text-10"></i>),
        'zip': (<i class="iconfont icon-ZIPtubiao text-10"></i>),
        'rar': (<i class="iconfont icon-RARtubiao text-10"></i>),
        'txt': (<i class="iconfont icon-text text-10"></i>),
        'img': (<i class="iconfont icon-jpg text-10"></i>),
    },
    previewFileExtSettings: {
        doc: (ext: string) => /(doc|docx)$/i.test(ext),
        xls: (ext: string) => /(xls|xlsx)$/i.test(ext),
        txt: (ext: string) => /(txt)$/i.test(ext),
        rar: (ext: string) => /(rar)$/i.test(ext),
        zip: (ext: string) => /(zip)$/i.test(ext),
        pdf: (ext: string) => /(pdf)$/i.test(ext),
        img: (ext: string) => /(jpg|png|bmp|webp|jpeg)$/i.test(ext),
    }
}

const getFileIconByExt = (fileExt?: string) => {
    if (fileExt) {
        for (const [key, matchFn] of Object.entries(config.previewFileExtSettings)) {
            if (matchFn(fileExt)) {
                return config.previewFileIconSettings[key as keyof typeof config.previewFileIconSettings]
            }
        }
    }

    return (<i class="iconfont icon-wenjian text-10"></i>) // 或者返回一个默认icon
}

const paths = ref<{ name: string, parentId?: string }[]>([{ name: '根目录', parentId: '' }])
const handleToPath = (path: typeof paths.value[0], index: number) => {
    paths.value.splice(index + 1, paths.value.length)
}
const getCurrentParentId = computed(() => {
    return paths.value.at(-1)?.parentId
})

const loading = ref(false)
const files = ref<ArchiveFileVo[]>([])
const handleSearch = () => {
    loading.value = true
    listArchiveFiles(props.projId, getCurrentParentId.value).then(res => {
        files.value = res.data ?? []
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()
watch(() => paths.value.length, () => {
    handleSearch()
})



const containerRef = ref<HTMLDivElement>()
const menuGroup = {
    add: {
        label: '新建文件夹',
        icon: (<el-icon><FolderAdd /></el-icon>),
        onClick: () => handleCreateFolder(),
    },
    upload: {
        label: '文件上传',
        icon: (<el-icon><DocumentAdd /></el-icon>),
        onClick: () => alert('Click Simple item'),
    },
    open: {
        label: '打开',
        icon: (<el-icon><View /></el-icon>),
        onClick: () => handleOpenFolder(),
    },
    preview: {
        label: '预览',
        icon: (<el-icon><View /></el-icon>),
        onClick: () => handlePreview(contextMenuSelectFile.value?.attachment),
    },
    rename: {
        label: '重命名',
        // icon: (<el-icon><FolderAdd /></el-icon>),
        onClick: () => handleRename(),
    },
    copy: {
        label: '复制',
        icon: (<el-icon><DocumentCopy /></el-icon>),
        onClick: () => handleCopy(),
    },
    cut: {
        label: '剪切',
        icon: (<el-icon><DocumentRemove /></el-icon>),
        onClick: () => handleCut(),
    },
    paste: {
        label: '粘贴',
        icon: (<el-icon><DocumentChecked /></el-icon>),
        onClick: () => handlePaste(),
    },
    delete: {
        label: '删除',
        icon: (<el-icon><Delete /></el-icon>),
        onClick: () => handleDelete(),
    },
}
const getFileByFileId = (fileId: string) => {
    return files.value.find(item => item.id === fileId)
}
const pasteFiles = ref<ArchiveFileVo[]>([])
const contextMenuSelectFile = ref<ArchiveFileVo>()
onMounted(() => {
    // 已归档不允许编辑
    if (props.data.status == 4) {
        return
    }
    const getArchiveFileIdFromRightClick = (event: MouseEvent) => {
        // 获取事件中点击的元素
        let targetElement: HTMLElement | null = event.target as HTMLElement;

        // 从当前元素向上查找，直到找到body元素或找到archive-file类的元素
        while (targetElement !== null && targetElement !== document.body) {
            // 检查当前元素是否包含archive-file类
            if (targetElement.classList.contains('archive-file')) {
                // 如果找到archive-file类的元素，返回其id
                return targetElement.id;
            }
            // 如果当前元素不是archive-file类的元素，则向上查找父元素
            targetElement = targetElement.parentElement;
        }

        // 如果遍历到body仍然没有找到-filearchive类的元素，返回null
        return null;
    }
    containerRef.value!.oncontextmenu = (event) => {
        event.preventDefault()

        let menuItems: MenuItem[] = []
        const fileId = getArchiveFileIdFromRightClick(event) ?? ''
        if (fileId) {
            contextMenuSelectFile.value = getFileByFileId(fileId)

            // 如果选中的只有一个文件，并且这个文件和右键选中的文件不一致，那么就清空选中的列表
            if (selectFiles.value.length == 1) {
                if (fileId != selectFiles.value.at(0)?.id) {
                    selectFiles.value.length = 0

                }
            }
            // 添加右键选中的文件到选中的列表中
            setSelectFile(contextMenuSelectFile.value)
            // 如果选中的文件大于1，那么就只可以复制、剪切、删除
            if (selectFiles.value.length > 1) {
                menuItems = [menuGroup.copy, menuGroup.delete]
            } else {

                if (contextMenuSelectFile.value?.typeCode == EFileType.D) {
                    menuItems = [menuGroup.open, menuGroup.rename, menuGroup.copy, menuGroup.delete]
                }
                if (contextMenuSelectFile.value?.typeCode == EFileType.F) {
                    menuItems = [menuGroup.preview, menuGroup.rename, menuGroup.copy, menuGroup.delete]
                }
            }

        } else {
            // 如果有粘贴的文件，只显示创建文件夹和粘贴
            if (pasteFiles.value.length > 0) {
                menuItems = [menuGroup.add, menuGroup.paste]
            } else {
                // 点击空白位置只显示创建文件夹
                menuItems = [menuGroup.add]
            }
            selectFiles.value.length = 0
        }

        ContextMenu.showContextMenu({
            x: event.x,
            y: event.y,
            items: menuItems
        })

    }

})


const selectFiles = ref<ArchiveFileVo[]>([])
const setSelectFile = (file: any) => {
    if (!selectFiles.value.includes(file)) {
        selectFiles.value.push(file)
    }
}
const handleClick = (item: any) => {
    if (selectFiles.value.includes(item)) {
        selectFiles.value.splice(selectFiles.value.indexOf(item), 1)
    } else {
        setSelectFile(item)
    }
}

const handleDblClick = (item: ArchiveFileVo) => {
    if (item.typeCode == EFileType.D) {
        selectFiles.value = []
        // 进入文件夹
        paths.value.push({ name: item.name!, parentId: item.id })
    } else {
        handlePreview(item.attachment)
    }

}

const handleBlankClick = () => {
    selectFiles.value = []
}

const handleCreateFolder = () => {
    ElMessageBox.prompt('请输入文件夹名称', '', {})
        .then((res) => {
            saveArchiveFile({
                projId: props.projId,
                typeCode: EFileType.D,
                name: res.value,
                parentId: getCurrentParentId.value
            }).then(res => {
                ElMessage.success('创建文件夹成功')
                handleSearch()
            })
        })
}

const fileViewShow = ref(false)
const fileUrl = ref('')
const handlePreview = (att?: ISysAttachment) => {
    if (att && att.id) {
        const ext = att.fileExt!.toLocaleLowerCase()
        let mimeType = ''
        if (/(jpg|png|bmp|webp|jpeg)$/i.test(ext)) mimeType = 'image/' + att.fileExt
        if (/(pdf)$/i.test(ext)) mimeType = 'application/pdf'
        if (mimeType) {
            fileViewShow.value = true
            downloadFile(att.id).then(res => {
                const blob = new Blob([res], { type: mimeType })
                fileUrl.value = URL.createObjectURL(blob)
            })
        }

    }

}

/** 复制文件 */
const handleCopy = () => {
    pasteFiles.value.length = 0;
    selectFiles.value.forEach(file => {
        pasteFiles.value.push(file)

    });
}

/** 剪切文件 */
const handleCut = () => {
    pasteFiles.value.length = 0;
    selectFiles.value.forEach(file => {
        pasteFiles.value.push(file)

    });
}

/** 粘贴文件 */
const handlePaste = () => {
    if (pasteFiles.value.length > 0) {
        Promise.all(pasteFiles.value.map(item => {
            return saveArchiveFile({
                projId: props.projId,
                fileId: item.fileId,
                typeCode: item.typeCode!,
                name: item.name!,
                parentId: getCurrentParentId.value
            })
        })).then(res => {
            ElMessage.success('粘贴成功')
        }).finally(() => {
            handleSearch()
            pasteFiles.value.length = 0
        })
    }

}
/** 重命名 */
const handleRename = () => {
    ElMessageBox.prompt('请输入文件名称', '', {
        inputValue: contextMenuSelectFile.value?.name
    }).then(res => {
        saveArchiveFile({
            id: contextMenuSelectFile.value?.id,
            projId: contextMenuSelectFile.value?.projId,
            typeCode: contextMenuSelectFile.value?.typeCode,
            parentId: contextMenuSelectFile.value?.parentId,
            name: res.value,
            fileId: contextMenuSelectFile.value?.fileId,
        } as any).then(() => {
            ElMessage.success('重命名成功')
            handleSearch()
        })
    })
}

/** 删除文件 */
const handleDelete = () => {
    ElMessageBox.confirm('是否确认删除？', '提示', {
        type: 'warning'
    }).then(() => {
        if (selectFiles.value.length > 0) {
            Promise.all(
                selectFiles.value.map(t => removeArchiveFile(t.id!))
            ).finally(() => {
                handleSearch()
            })
        } else {
            removeArchiveFile(contextMenuSelectFile.value?.id!).then(() => {
                handleSearch()
            })
        }
    })

}

/** 打开文件夹 */
const handleOpenFolder = () => {
    paths.value.push({ name: contextMenuSelectFile.value?.name!, parentId: contextMenuSelectFile.value?.id })
}

const handleBack = () => {
    if (paths.value.length > 1) {
        paths.value.pop()
    }
}

const handleRefresh = () => {
    selectFiles.value = []
    contextMenuSelectFile.value = undefined
    handleSearch()
}

const handleArchive = () => {
    ElMessageBox.prompt('归档编号', '', {})
        .then(res => {
            saveArchiveNumber({ closureId: props.data.id!, archiveNumber: res.value }).then(res => {
                ElMessage.success('归档成功')
                emit('close')
            })
        })

}

const handleCancelArchive = () => {
    ElMessageBox.confirm('是否确认取消归档?', '提示', {
        type: 'warning'
    }).then(() => {
        revokeArchive(props.data.id!).then(res => {
            ElMessage.success('取消归档成功')
            emit('close')
        })
    })

}

const handleDownload = () => {
    proxy.download('/project/closureArchive/downloadAll', {
        projId: props.projId
    }, `项目归档资料${dayjs().format('YYYYMMDDHHmmss')}.zip`)
}

const handleBeforeUpload = () => {
    loading.value = true
}
const handleUploadError = () => {
    ElMessage.error('数据包导入失败')
    handleSearch()
    loading.value = false
}
const handleUploadSuccess = () => {
    ElMessage.success('数据包导入成功')
    handleSearch()
    loading.value = false
}


defineExpose({
    async addFile(file: ArchiveFileDto) {
        const data = {
            ...file,
            parentId: getCurrentParentId.value
        }
        try {
            return await saveArchiveFile(data)
        } finally {
            handleSearch()
        }
    },
    async addBillFile(file: ArchivePaybillFileDto) {
        const data = {
            ...file,
            parentId: getCurrentParentId.value
        }
        try {
            return await saveArchivePaybillFile(data)
        } finally {
            handleSearch()
        }

    }
})
</script>

<style>
.mx-menu-ghost-host {
    z-index: 100000 !important;
}

.truncate-2 {
    display: -webkit-box;
    /* 使用 WebKit 的弹性盒模型 */
    -webkit-box-orient: vertical;
    /* 垂直排列内容 */
    -webkit-line-clamp: 2;
    /* 限制为两行 */
    overflow: hidden;
    /* 隐藏超出部分 */
}
</style>