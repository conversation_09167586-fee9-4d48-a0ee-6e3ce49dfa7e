<template>
    <div id="test" class="flex gap-3">
        <div v-if="data.status != 4" class="w-400px border border-solid p-2">
            <el-scrollbar height="550px">
                <el-tree :data="fileTypeList" default-expand-all  :expand-on-click-node="false" :props="defaultProps"
                    @node-click="handleNodeClick" @node-drag-end="handleNodeDragEnd">
                    <template #default="{ node, data }">
                        <span @dblclick="handleDbClick(node.data)" :title="node.label" class="break-all">{{ node.label
                        }}</span>
                    </template>
                </el-tree>
            </el-scrollbar>
        </div>
        <div class="flex-1">
            <FileExplorer ref="FileExplorerRef" :data="data" :proj-id="data.projId!" @close="emit('close')"></FileExplorer>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { pendingAttachments } from '@/api/lib/project/closureArchive';
import FileExplorer from './fileExplorer.vue';
import { vDraggable } from '@neodrag/vue';
import { EFileSoureType, EFileType } from '@/utils/constants'
import { ElMessage } from 'element-plus';

const props = defineProps<{
    data: ProjectClosureListVo
}>()

const emit = defineEmits(['close'])
const defaultProps = {
    children: 'children',
    label: 'label',
}

type fileType = { id?: string, label?: string, paybillId?: string }
const fileTypeList = ref<fileType & { children: fileType[] }[]>([])
pendingAttachments(props.data.projId!).then(res => {
    if (res.data) {
        fileTypeList.value = Object.keys(res.data).map(key => {
            return {
                label: key,
                children: (res.data![key] ?? []).map(item => {
                    return {
                        id: item.id,
                        label: item.fileName,
                        paybillId: item.paybillId
                    }
                })
            }
        })
    }
})

const FileExplorerRef = ref<{
    addFile: (file: ArchiveFileDto) => Promise<any>,
    addBillFile: (file: ArchivePaybillFileDto) => Promise<any>
}>()

const handleNodeClick = (data: any) => {
    // console.log(data)
}

const handleDbClick = (data: fileType) => {
    // 如果是附件
    if (data.id) {
        FileExplorerRef.value?.addFile({
            fileId: data.id,
            projId: props.data.projId!,
            typeCode: EFileType.F,
            name: data.label!
        }).then(res => {
            ElMessage.success('添加成功')
        })
    }
    // 如果是拨款单
    if (data.paybillId) {
        FileExplorerRef.value?.addBillFile({
            projId: props.data.projId!,
            paybillId: data.paybillId,
            sourceType: EFileSoureType.PROJECT_ARCHIVE,
            primaryType: "资金申请表"
        }).then(res => {
            ElMessage.success('添加成功')
        })
    }

}

const handleNodeDragEnd = (a: any, b: any, c: any, d: any,) => {
    console.log(a, b, c, d)
}
</script>
<style scoped>
/* 关键样式：允许节点换行 */
:deep(.el-tree){width: 400px;}
:deep(.el-tree) {
  .el-tree-node {
    white-space: normal !important; /* 允许换行 */
    color: #222;
  }
  
  .el-tree-node__content {
    height: auto !important;        /* 高度自适应 */
    align-items: flex-start !important; /* 顶部对齐 */
    display: block !important;      /* 取消 flex 布局限制 */
  }

  .el-tree-node__children {
    overflow: visible !important;   /* 避免子内容被隐藏 */
  }
}

/* 节点文本样式（可选） */
/* .break-all {
  word-break: break-word;  
  white-space: normal;
  line-height: 18px;
  padding:5px 0 0 5px;
  
} */
</style>