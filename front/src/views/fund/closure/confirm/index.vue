<template>
    <div class="app-container">
        <!-- 查询条件 -->
        <el-form :inline="true" :model="searchForm" label-width="auto">
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.applyOrgid" clearable class="!w-50"></DeptSelect>
            </el-form-item>

            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" class="!w-50" clearable />
            </el-form-item>

            <el-form-item label="项目年度">
                <el-date-picker v-model="searchForm.firstPayYear" type="year" value-format="YYYY"
                    placeholder="首次使用发展资金的年度" class="!w-50" />
            </el-form-item>

            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" class="!w-50" clearable>
                    <el-option v-for="item in closure_status" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
            </el-form-item>
        </el-form>

        <div>
            <!-- 数据表格 -->
            <el-table :data="tableData" border v-loading="loading">
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="projName" label="项目名称" header-align="center" align="left" width="300px" fixed="left" />
                <el-table-column prop="applyOrgname" label="项目单位" align="center" width="150px" />
                <el-table-column prop="firstPayYear" label="项目年度" align="center" width="100px" />
                <el-table-column prop="projAmount" label="项目金额(万元)" header-align="center" align="right" width="110px"
                    :formatter="t => numberFormat(t.projAmount, 4)">
                    <template #header>
                        <div>项目金额</div>
                        <div>(万元)</div>
                    </template>
                </el-table-column>
                <el-table-column prop="payedAmount" label="累计执行金额(万元)" header-align="center" align="right" width="110px"
                    :formatter="t => numberFormat(t.payedAmount, 4)">
                    <template #header>
                        <div>累计执行金额</div>
                        <div>(万元)</div>
                    </template>
                </el-table-column>
                <el-table-column prop="applicant" align="center" width="150">
                    <template #header>
                        申请人 <br />
                        申请日期
                    </template>
                    <template #default="{ row }">
                        <div>{{ row.applyUsername }}</div>
                        <div>{{ dateFormat(row.applyTime) }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="applyReason" label="申请理由" align="center">
                    <template #default="{ row }">
                        {{ row.handyReason ?? row.applyReason }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center" width="100px">
                    <template #default="{ row }">
                        <dict-tag :options="closure_status" :value="row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100px" fixed="right">
                    <template #default="{ row }">
                        <el-button v-if="row.canDoAudit" type="primary" link size="small" @click="handleConfirm(row)">确认</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
        <el-dialog title="结项申请" v-model="confirmFormShow" width="800px" :close-on-click-modal="false" destroy-on-close>
            <ConfirmForm :data="currentRow" @cancel="confirmFormShow = false;" @close="confirmFormShow = false;handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { TableColumnCtx } from 'element-plus'
import ConfirmForm from './component/confirmForm.vue'
import { getClosureAuditList } from '@/api/lib/project/closure'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { dateFormat, numberFormat } from '@/utils/common';

const { proxy } = getCurrentInstance() as { proxy: any };
const { closure_status } = proxy.useDict("closure_status");

const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const searchForm = reactive<ProjectClosureQuery>({
    status: '1'
})
const total = ref(0)
const tableData = ref<ProjectClosureAuditListVo[]>([])
const loading = ref(false)

const handleSearch = () => {
    loading.value = true
    getClosureAuditList(searchForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()

const currentRow = ref<ProjectClosureAuditListVo>()
const confirmFormShow = ref(false)
const handleConfirm = (row: ProjectClosureAuditListVo) => {
    confirmFormShow.value = true
    currentRow.value = row
}

</script>
