<template>
    <div>
        <el-descriptions :column="1" label-width="20%" border>
            <el-descriptions-item width="80%">
                <template #label>
                    项目名称 <span class="text-red">*</span>
                </template>
                {{ props.data?.projName }}
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    结项理由 <span class="text-red">*</span>
                </template>
                <div v-if="props.data?.handyReason">【{{ props.data?.handyReason }}】</div>
                <div>{{ props.data?.applyReason }}</div>
            </el-descriptions-item>
        </el-descriptions>

        <el-table :data="nodeList" border class="mt-2" height="300px">
            <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
            <el-table-column prop="operatorName" label="操作人" align="center"></el-table-column>
            <el-table-column prop="operateTime" label="操作时间" align="center"
                :formatter="t => dateFormat(t.operateTime)"></el-table-column>
            <el-table-column prop="nodeName" label="操作名称" align="center"></el-table-column>
            <el-table-column prop="operateReason" label="操作内容" align="center"></el-table-column>
        </el-table>
        <!-- 操作按钮 -->
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" :loading="loading" @click="handlePass">同意</el-button>
            <el-button type="danger" :loading="loading" @click="handleReject">退回</el-button>
            <el-button @click="$emit('cancel')">关闭窗口</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { doAuditClosure, getAuditNodeList } from '@/api/lib/project/closure';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox } from 'element-plus';


const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    data?: ProjectClosureAuditListVo
}>()

const nodeList = ref<ProjectClosureNodeVo[]>([])
getAuditNodeList(props.data?.id!).then(res => {
    nodeList.value = res.data?.filter(item => item.isMultiClaim != true) ?? []
    const isMultiClaimList = res.data?.filter(item => item.isMultiClaim)
    if (isMultiClaimList && isMultiClaimList.length > 0) {
        nodeList.value.push({
            ...isMultiClaimList.at(0),
            operatorName: isMultiClaimList.map(t => t.operatorName).join('，')
        })
    }
})

const loading = ref(false)
const handlePass = () => {
    ElMessageBox.confirm('是否确认同意该结项申请？', '提示', {
        type: 'warning'
    }).then(() => {
        loading.value = true
        doAuditClosure({
            closureId: props.data?.id!,
            operateResult: 1,
        }).then(() => {
            ElMessage.success('审核成功')
            emit('close')
        }).finally(() => {
            loading.value = false
        })
    })
}

const handleReject = () => {
    ElMessageBox.prompt('请填写退回理由', '提示', {
        type: 'warning',
        inputValidator: (value) => {
            if (!value) {
                return '请填写退回理由'
            }
            if (value.length > 2000) {
                return '文字超长'
            }
            return true
        }
    }).then((res) => {
        loading.value = true
        doAuditClosure({
            closureId: props.data?.id!,
            operateResult: 0,
            operateReason: res.value
        }).then(() => {
            ElMessage.success('审核成功')
            emit('close')
        }).finally(() => {
            loading.value = false
        })
    })
}
</script>