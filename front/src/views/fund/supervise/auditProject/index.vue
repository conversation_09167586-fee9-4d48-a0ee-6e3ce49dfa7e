<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :inline="true" :model="searchForm" label-width="auto">
      <el-form-item label="项目单位">
        <DeptSelect v-model="searchForm.projCompany" clearable class="!w-40"></DeptSelect>
      </el-form-item>
      <el-form-item label="项目名称">
        <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="!w-40" />
      </el-form-item>
      <el-form-item label="审计类型">
        <el-select v-model="searchForm.typeId" placeholder="请选择审计类型" clearable class="w-40">
          <el-option v-for="item in auditTypeOptions" :label="item.name" :value="item.id!" />
        </el-select>
      </el-form-item>
      <el-form-item label="审计阶段">
        <el-select v-model="searchForm.nodeSeq" placeholder="请选择审计阶段" clearable class="w-40">
          <el-option v-for="item in flowNodeOptions" :label="item.nodeName" :value="item.nodeSeq!" />
        </el-select>
      </el-form-item>
      <el-form-item label="年份">
        <el-date-picker v-model="searchForm.year" type="year" value-format="YYYY" clearable placeholder="请选择审计年份"
          class="!w-40" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button v-hasPermi="['fund:audit:save']" type="success"
          @click="auditProjectFormShow = true">添加审计项目</el-button>
      </el-form-item>
    </el-form>


    <div v-loading="loading">
      <!-- 表格数据 -->
      <el-table :data="tableData" border>
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
          <template #default="{ row, $index }">
            <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="projName" label="项目名称" header-align="center" align="left" min-width="300px"
          fixed="left" />
        <el-table-column prop="projAmount" header-align="center" align="right" width="120"
          :formatter="(row: any) => numberFormat(row.projAmount, 4)">
          <template #header>
            <div>项目金额</div>
            <div>（万元）</div>
          </template>
        </el-table-column>
        <el-table-column prop="typeName" label="审计类型" align="center" width="150" />
        <el-table-column prop="year" label="审计年度" align="center" width="100" />
        <el-table-column prop="bsjName" label="被审计单位" align="center" width="120" />
        <el-table-column prop="auditCompany" label="会计师事务所" align="center" width="150" />
        <el-table-column prop="auditStart" label="审计日期" align="center" width="130"
          :formatter="(row: any) => dateFormat(row.auditStart)" />
        <el-table-column prop="auditAmount" label="审定金额（元）" header-align="center" align="right" width="130"
          :formatter="(row: any) => numberFormat(row.auditAmount, 2)" />
        <el-table-column prop="nodeName" label="审计阶段" align="center" width="120" />
        <!-- <el-table-column prop="annualBudget" label="督办提示" align="center" width="150" /> -->
        <el-table-column prop="paidDate" label="操作" align="center" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link icon="view" @click="handleView(row)">查看</el-button>
            <el-button v-hasPermi="['fund:audit:remove']" type="danger" link icon="delete"
              @click="handleRemove(row)">删除</el-button>
            <el-button v-hasPermi="['fund:audit:forceRecall']" type="primary" link
              @click="handleForceRecall(row)">错发收回</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
        @pagination="handleSearch" />
    </div>

    <el-dialog title="审计项目管理" v-model="auditProjectFormShow" width="600px" :close-on-click-modal="false"
      destroy-on-close>
      <AuditProjectForm @cancel="auditProjectFormShow = false;" @close="handleAuditProjectClose" />
      <footer class="el-dialog__footer h-10"></footer>
    </el-dialog>
    <!-- <el-dialog title="审计项目管理" v-model="auditDetailShow" fullscreen :close-on-click-modal="false" destroy-on-close>
      <AuditDetail :form-id="currentFormId" @cancel="auditDetailShow = false" />
      <footer class="el-dialog__footer h-10"></footer>
    </el-dialog> -->
  </div>
</template>

<script setup lang="tsx" name="Audit-project">
import { ref } from 'vue'
import AuditProjectForm from './component/auditProjectForm.vue'
import AuditDetail from './component/auditDetail.vue'
import { dateFormat, genSnowId, numberFormat } from '@/utils/common'
import { forceRecall, getAuditList, getAuditTypeOptions, removeFundAudit } from '@/api/fund/audit'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { getFlowNodeOption } from '@/api/system/flowNode'
import { EFlowType } from '@/utils/constants'
import { ElMessage, ElMessageBox } from 'element-plus'
import { BigNumber } from 'bignumber.js'


const router = useRouter()

const flowNodeOptions = ref<SysFlowNodeOptionVo[]>([]);
getFlowNodeOption(EFlowType.F02).then(res => {
  flowNodeOptions.value = res.data ?? []
})

const auditTypeOptions = ref<FundAuditTypeVo[]>([])
getAuditTypeOptions().then(res => {
  auditTypeOptions.value = res.data ?? []
})

const searchForm = reactive<FundAuditQuery>({})
const page = reactive({
  pageNum: 1,
  pageSize: 10
})

const total = ref(0)
const tableData = ref<FundAuditListVo[]>([])
const loading = ref(false);
const handleSearch = () => {
  loading.value = true
  getAuditList(searchForm, page).then(res => {
    tableData.value = res.rows ?? []
    total.value = res.total ?? 0
  }).finally(() => loading.value = false)
}
handleSearch()
onActivated(() => {
  handleSearch()
})


const currentFormId = ref('')
const auditProjectFormShow = ref(false)
const auditDetailShow = ref(false)
const handleAuditProjectClose = (formId: string) => {
  currentFormId.value = formId
  auditProjectFormShow.value = false
  router.push({ path: 'audit-project/detail', query: { id: formId } })
  handleSearch()
}

const handleView = (row: FundAuditListVo) => {
  currentFormId.value = row.id!
  router.push({ path: 'audit-project/detail', query: { id: row.id } })
}

const handleRemove = (row: FundAuditListVo) => {
  ElMessageBox.confirm('是否确认删除？', '提示', {
    type: 'warning'
  })
    .then(() => {
      removeFundAudit([row.id!]).then(res => {
        ElMessage.success('操作成功')
        handleSearch()
      })
    })
}

const handleForceRecall = (row: FundAuditListVo) => {
  ElMessageBox.confirm('是否确认强制撤回？', '提示', {
    type: 'warning'
  })
    .then(() => {
      forceRecall(row.id!).then(res => {
        ElMessage.success('撤回成功')
        handleSearch()
      })
    })
}

const handleExport = () => {
  console.log('导出数据')
  // 导出逻辑
}



</script>

<style lang="css" scoped>
:deep(.el-tabs__header) {
  margin-bottom: 0;
}
</style>