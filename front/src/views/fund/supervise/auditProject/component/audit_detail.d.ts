interface AuditPlan {
    typeId: string
    typeName: string
    remark: string
}

interface AuditNotice {
    orgName: string
    baseArticle: string
    auditOrg: string
    auditStart: string
    auditType: string
    projName: string
    remark: string
    notifyDate: string
}

interface Suggestion {
    orgName: string
    consultExpire: string
    consultDate: string
    projName: string
    files1: {
        name: string
        fileId: string
    }[]
    remark: string
}

interface Finalize {
    /** 审定金额 */
    auditAmount?: number,
    /** 审定报告 */
    files: {
        name: string
        fileId: string
    }[]
    /** 是否需要整改 1 是 0 否 */
    needRectify: string
    rectifyItem: {
        orgName: string
        auditStart: string
        auditOrg: string
        projName: string
        auditType: string
        rectifyExpire: string
        rectifyDate: string
        remark  : string
    }
}