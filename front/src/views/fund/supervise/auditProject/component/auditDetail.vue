<template>
    <div class="app-container">
        <div class="flex flex-col gap-3">
            <div class="grid grid-cols-3 p-4 bg-white rounded-xl shadow">
                <div class="break-all"><strong>项目名称：</strong>{{ auditForm.mainForm?.projName }}</div>
                <div class="break-all"><strong>被审计单位：</strong>
                    <el-tooltip>
                        <template #content>
                            被审计单位：{{ auditForm.mainForm?.bsjName }}<br />
                            申报主体：{{ auditForm.mainForm?.sbName }}<br />
                            考核主体：{{ auditForm.mainForm?.khName }}<br />
                            收款单位：{{ auditForm.mainForm?.skName }}
                        </template>
                        {{ auditForm.mainForm?.bsjName }}
                    </el-tooltip>
                </div>
                <div class="break-all"><strong>项目金额：</strong>{{ numberFormat(auditForm.mainForm?.projAmount ?? 0, 4) }} 万元</div>
            </div>
            <div class="flex gap-3">
                <div class="bg-white p-4 rounded-xl shadow w-20% min-h-170" :style="{ height: tableHeight + 'px', overflowY: 'auto' }">
                    <el-steps direction="vertical" :active="(auditForm.mainForm?.nodeSeq ?? 0) - 1" space="100px"
                        finish-status="success"
                        :process-status="auditForm.mainForm?.nodeSeq == completeStatus ? 'success' : 'process'">
                        <el-step v-for="item in nodeList" :title="item.nodeName" class="cursor-pointer "
                            :status="(!item.operatorNames || item.operatorNames?.length == 0) && (auditForm.mainForm?.nodeSeq ?? 0) > item.nodeSeq ? 'wait' : ''"
                            :class="{ 'line-through': (!item.operatorNames || item.operatorNames?.length == 0) && (auditForm.mainForm?.nodeSeq ?? 0) > item.nodeSeq }"
                            @click="handleNodeClick(item)">
                            <template #description>
                                <div v-if="item.operatorNames && item.operatorNames.length > 0">
                                    操作人：{{ item.operatorNames?.join('、') }}
                                    <span v-if="item.operateTime">, {{ dateFormat(item.operateTime, 'YYYY-MM-DD')
                                        }}</span>
                                </div>
                            </template>
                        </el-step>
                    </el-steps>
                </div>
                <div class="bg-white rounded-xl shadow p-3 flex-1" :style="{ height: tableHeight + 'px', overflowY: 'auto' }" v-loading="loading">
                    <component :is="getComponent" />
                </div>
            </div>
        </div>
    </div>

</template>

<script setup lang="tsx">
import { canUserOperate, getAuditNodeData, getAuditNodeStatusList, getFundAudit } from '@/api/fund/audit';
import { useAuditStore } from './store/formStore';
import { dateFormat, numberFormat } from '@/utils/common';

const tableHeight = ref(window.innerHeight - 200)
const formId = useRoute().query.id as string

/** 流程结束节点序号 */
const completeStatus = 10

const loading = ref(false)
const auditForm = useAuditStore()
await auditForm.fetchFundAudit(formId)
const nodeList = ref<FundAuditNodeStatusVo[]>([])
const activate = ref(0)
watch(() => auditForm.activate, async (val) => {
    loading.value = true
    await Promise.all([
        getAuditNodeData(formId, auditForm.activate).then(res => {
            auditForm.currentNodeData = res.data
        }),
        canUserOperate(auditForm.getAuditId!, auditForm.activate).then(res => {
            auditForm.userCanOperate = res.data ?? false
        }),
        getAuditNodeStatusList(formId).then(res => {
            nodeList.value = res.data ?? []
        }),
    ]).finally(() => {
        loading.value = false
    })
    activate.value = auditForm.activate
}, { immediate: true })

// 动态加载组件
// @ts-ignore
const modules = import.meta.glob('./auditDetail/*.vue')
const componentMap = new Map<string, any>()
for (let key in modules) {
    componentMap.set(key.split('/').at(-1)!.split('.').at(0)!, defineAsyncComponent(modules[key]))
}
const getComponent = computed(() => {
    const nodeUrl = nodeList.value.find(t => t.nodeSeq == activate.value)?.nodeUrl
    if (nodeUrl) {
        return componentMap.get(nodeUrl)
    }
})


const handleNodeClick = (item: FundAuditNodeStatusVo) => {
    // 点击的节点序号小于等于当前流程的节点序号
    // 点击的节点有操作人
    if (item.nodeSeq <= auditForm.mainForm?.nodeSeq! && (item.operatorNames && item.operatorNames.length > 0 || item.nodeSeq == completeStatus)) {
        auditForm.setActivate(item.nodeSeq!)
    }

}



</script>