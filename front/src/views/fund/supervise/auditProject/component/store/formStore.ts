import { getFundAudit } from "@/api/fund/audit"

export const useAuditStore = defineStore('superviseStore', () => {

    const activate = ref(0)

    const setActivate = (nodeSeq: number) => {
        activate.value = nodeSeq
    }

    const toNextNode = () => {
        activate.value++
    }

    const userCanOperate = ref(false)

    /**
     * 主表单
     */
    const mainForm = ref<FundAuditVo>()
    const fetchFundAudit = async (formId?: string) => {
        const formRes = await getFundAudit(formId ?? mainForm.value?.id!)
        mainForm.value = formRes.data
        activate.value = mainForm.value!.nodeSeq
    }



    /**
     * 当前节点数据
     */
    const currentNodeData = ref<FundAuditNodeDataResultVo>()

    /**
     * 当前节点的序号与主表单是否一致，用于判断是否可以提交
     */
    const isNodeSeqEqualMainForm = computed(() => {
        return mainForm.value?.nodeSeq === currentNodeData.value?.nodeSeq
    })

    const getAuditId = computed(() => {
        return mainForm.value?.id
    })

    return {
        activate,
        userCanOperate,
        setActivate,
        toNextNode,
        fetchFundAudit,
        mainForm,
        currentNodeData,
        isNodeSeqEqualMainForm,
        getAuditId
    }
})