<template>
    <div class="relative h-full">
        <div class="absolute top-0 left-0 z-10 w-full bg-white">
            <p class="my-0 text-5 font-bold">审核</p>
            <el-divider class="my-3" />
        </div>

        <el-scrollbar height="100%">
            <div class="w-full py-15">
                <el-table :data="docList" row-key="id" default-expand-all border>
                    <el-table-column prop="seqNo" label="序号" width="100px" align="center"></el-table-column>
                    <el-table-column prop="name" label="资料名称" width="200px" header-align="center" align="left">
                        <template #default="{ row }">
                            {{ row.name }}
                            <span v-if="row.isRequire == '1'" class="text-red text-4">*</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="资料模板" width="200px" header-align="center" align="left">
                        <template #default="{ row }">
                            <DownloadA v-if="row.templateFile" :file="row.templateFile"></DownloadA>
                        </template>
                    </el-table-column>
                    <el-table-column label="资料上传" header-align="center" align="left">
                        <template #default="{ row }">
                            <div v-if="fileMap.get(row.id)">
                                <el-table :data="fileMap.get(row.id) ?? []" border :show-header="false">
                                    <el-table-column type="index" label="序号" align="center"></el-table-column>
                                    <el-table-column prop="fileName" label="文件名">
                                        <template #default="{ row }">
                                            <DownloadA v-if="row.file" :file="row.file" class="inline"></DownloadA>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="fileName" label="审核意见" align="left" >
                                        <template #default="{ row }">
                                            <div class="grid grid-cols-[50px_1fr]">
                                                <el-tag v-if="row.approveResult == '1'" type="success">通过</el-tag>
                                                <el-tag v-if="row.approveResult == '0'" type="danger">退回</el-tag>
                                                <div v-if="row.approveName || row.approveReason"
                                                    class="inline ml-2 break-all">
                                                    {{ `${row.approveName}, ${row.approveReason ?? ''}` }}
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="60" align="center"
                                        v-if="auditForm.userCanOperate">
                                        <template #default="{ row }">
                                            <el-button type="danger" link @click="handleAudit(row)">审核</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-scrollbar>


        <!-- 操作按钮 -->
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <el-button
                    v-if="(auditForm.mainForm?.nodeSeq ?? 0) == (auditForm.currentNodeData?.nodeSeq ?? 0) && auditForm.userCanOperate"
                    type="success" :loading="loading" @click="handleSubmit">提交</el-button>
                <el-button v-if="auditForm.userCanOperate" type="primary" @click="handleAllAccept">一键审核</el-button>
                <el-button type="primary" @click="handleDownload">一键下载</el-button>
                <el-button type="danger" @click="$router.back()">返回列表</el-button>
            </div>
        </div>

        <el-dialog title="您的意见" v-model="fileAuditShow" width="400" :close-on-click-modal="false" destroy-on-close>
            <FileAudit :file-id="currentFileId" @close="fileAuditShow = false; handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>

</template>

<script setup lang="tsx">
import { canUserOperate, doPassAllFundAuditFiles, doBusiness, doclist, removeAuditFile } from '@/api/fund/audit';
import { useAuditStore } from '../store/formStore';
import MyFileUpload from '@/components/FileUpload/MyFileUpload.vue';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
import { dayjs, ElMessage, ElMessageBox } from 'element-plus';
import { foreach } from 'tree-lodash';
import FileAudit from './audit/fileAudit.vue';

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const auditForm = useAuditStore()

// const isOperate = (await canUserOperate(auditForm.mainForm?.id!, auditForm.activate)).data

const loading = ref(false)
const fileMap = reactive<Map<string, FundAuditFileVo[]>>(new Map())
const docList = ref<FundAuditDoclistVo[]>([])
const handleSearch = () => {
    doclist(auditForm.mainForm?.id!).then(res => {
        docList.value = res.data ?? []

        foreach(docList.value, (item: any) => {
            fileMap.set(item.id!, item.auditFiles)

        })
    })
}
handleSearch()

const fileAuditShow = ref(false)
const currentFileId = ref('')
const handleAudit = (file: FundAuditFileVo) => {
    currentFileId.value = file.id!
    fileAuditShow.value = true
}

const handleAllAccept = () => {
    ElMessageBox.confirm('确定要批量通过吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        doPassAllFundAuditFiles(auditForm.mainForm?.id!).then(res => {
            ElMessage.success('操作成功');
            handleSearch()
        })
    })
}


const handleDownload = () => {
    ElMessageBox.confirm('是否确认批量打包下载（审核退回的文件不打包）？', '提示', {
        type: 'warning',
    }).then(() => {
        proxy.download('/fund/audit/doclist/download', {
            auditId: auditForm.mainForm?.id
        }, `资料附件_${dayjs().format('YYYYMMDDHHmmss')}.zip`)
    })

}

const handleSubmit = () => {
    for (let i = 0; i < docList.value.length; i++) {
        const item = docList.value[i]
        if (item.isRequire == '1') {
            if ((fileMap.get(item.id!)?.length ?? 0) < 1) {
                ElMessage.error(`请上传：${item.name}附件`)
                return
            }
        }
    }

    ElMessageBox.confirm('是否确认提交（提交后无法再进行资料审核）？', '提示', {
        type: 'warning'
    }).then(() => {
        loading.value = true
        doBusiness({
            isSubmit: true,
            auditId: auditForm.getAuditId!,
        }).then(res => {
            ElMessage.success('保存成功')
            auditForm.fetchFundAudit()
            auditForm.toNextNode()
        }).finally(() => {
            loading.value = false
        })
    })


}
</script>
<style>
.snap-message-box .el-message-box__message {
    flex: 1;
}

.snap-message-box .el-message-box__btns {
    display: none;
}
</style>