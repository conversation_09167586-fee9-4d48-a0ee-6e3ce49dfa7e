<template>
    <div class="relative h-full">
        <div class="absolute top-0 left-0 z-10 w-full bg-white">
            <p class="my-0 text-5 font-bold">签收</p>
            <el-divider class="my-3" />
        </div>

        <el-scrollbar height="100%">
            <div class="w-full py-15">
                <Edit ref="editFormRef"></Edit>
            </div>

        </el-scrollbar>

        <!-- 操作按钮 -->
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <template v-if="auditForm.userCanOperate">
                    <el-button type="success" :loading="loading" @click="handleSubmit">确认签收</el-button>
                </template>
                <el-button type="danger" @click="$router.back()">返回列表</el-button>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import { doBusiness } from '@/api/fund/audit';
import { useAuditStore } from '../store/formStore';
import Edit from './signFor/edit.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const auditForm = useAuditStore()

const loading = ref(false)
const editFormRef = ref()

const handleSubmit = async () => {
    ElMessageBox.confirm('是否确认签收？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        loading.value = true
        doBusiness({
            isSubmit: true,
            auditId: auditForm.getAuditId!,
        }).then(res => {
            ElMessage.success('提交成功')
            auditForm.fetchFundAudit()
            auditForm.toNextNode()
        }).finally(() => {
            loading.value = false
        })
    })
}
</script>