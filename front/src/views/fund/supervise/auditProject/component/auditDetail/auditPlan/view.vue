<template>
    <el-descriptions :column="1" label-width="15%" border>
        <el-descriptions-item label="审计类型" width="85%">
            {{ auditForm.currentNodeData?.operateDataResult?.typeName }}
        </el-descriptions-item>
        <el-descriptions-item label="审计说明">
            {{ auditForm.currentNodeData?.operateDataResult?.remark }}
        </el-descriptions-item>
    </el-descriptions>


</template>

<script setup lang="ts">
import { useAuditStore } from '../../store/formStore';


const auditForm = useAuditStore()
</script>