<template>
    <div>
        <div class="doc-a4">
            <p class="header">审计通知</p>
            <span ref="orgNameRef" contenteditable="true" :innerHTML="form.orgName"></span>：
            <p ref="remarkRef" class="content">根据
                <span ref="baseArticleRef" contenteditable="true" :innerHTML="form.baseArticle"></span>，现决定委托
                <span ref="auditOrgRef" contenteditable="true" :innerHTML="form.auditOrg"></span>自
            <div class="inline relative">
                <span ref="auditStartRef" contenteditable="true" :innerHTML="form.auditStart"></span>
                <div class="absolute top--4 left--10 opacity-0">
                    <el-date-picker v-model="form.auditStart" type="date" value-format="YYYY年MM月DD日"
                        class="!w-37"></el-date-picker>
                </div>
            </div>，对你单位的
            <span ref="projNameRef" contenteditable="true" :innerHTML="form.projName"></span>进行
            <span ref="auditTypeRef" contenteditable="true" :innerHTML="form.auditType"></span>，请予配合，并提供有关资料和必要的工作条件。
            </p>
            <p class="footer">
                <span style="text-align:center!important;">
                    管委会计财处<br>
                    <div class="inline relative">
                        <span ref="notifyDateRef" contenteditable="true" :innerHTML="form.notifyDate"></span>
                        <div class="absolute top--4 left-0 opacity-0">
                            <el-date-picker v-model="form.notifyDate" type="date" value-format="YYYY年MM月DD日"
                                class="!w-37"></el-date-picker>
                        </div>
                    </div>

                </span>
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useAuditStore } from '../../store/formStore'

const auditForm = useAuditStore()

const form = reactive<AuditNotice>({
    orgName: auditForm.currentNodeData?.operateDataResult?.orgName ?? Array.from(new Set([auditForm.mainForm?.bsjName, auditForm.mainForm?.skName].filter(t => t))).join('、'),
    baseArticle: auditForm.currentNodeData?.operateDataResult?.baseArticle ?? '××（如《上海化学工业区管理委员会××年度内部审计监督工作计划》等，但因存在年度临时增加的项目，所以自行填写）',
    auditOrg: auditForm.currentNodeData?.operateDataResult?.auditOrg ?? '××事务所',
    auditStart: auditForm.currentNodeData?.operateDataResult?.auditStart ?? dayjs().format('YYYY年MM月DD日'),
    auditType: auditForm.currentNodeData?.operateDataResult?.auditType ?? auditForm.mainForm?.typeName,
    projName: auditForm.currentNodeData?.operateDataResult?.projName ?? auditForm.mainForm?.projName,
    remark: auditForm.currentNodeData?.operateDataResult?.remark,
    notifyDate: auditForm.currentNodeData?.operateDataResult?.notifyDate ?? dayjs().format('YYYY年MM月DD日'),
})
const orgNameRef = ref<HTMLSpanElement>()
const baseArticleRef = ref<HTMLSpanElement>()
const auditOrgRef = ref<HTMLSpanElement>()
const auditStartRef = ref<HTMLSpanElement>()
const projNameRef = ref<HTMLSpanElement>()
const auditTypeRef = ref<HTMLSpanElement>()
const notifyDateRef = ref<HTMLSpanElement>()
const remarkRef = ref<HTMLParagraphElement>()


defineExpose({
    async getData() {
        return {
            orgName: orgNameRef.value?.innerText ?? '',
            baseArticle: baseArticleRef.value?.innerText ?? '',
            auditOrg: auditOrgRef.value?.innerText ?? '',
            auditStart: auditStartRef.value?.innerText ?? '',
            projName: projNameRef.value?.innerText ?? '',
            auditType: auditTypeRef.value?.innerText ?? '',
            notifyDate: notifyDateRef.value?.innerText ?? '',
            remark: remarkRef.value?.innerText ?? ''
        }
    }
})
</script>

<style scoped>
.doc-a4 {
    width: 800px;
    margin: 30px auto;
    font-family: 仿宋_GB2312;
    font-size: 18px;
    line-height: 50px;
}

.doc-a4 .header {
    font-family: 黑体;
    font-size: 24px;
    text-align: center;
    margin-bottom: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}

.doc-a4 .content {
    margin-top: 15px;
    text-indent: 2em;
}

.doc-a4 .footer {
    text-align: right;
    margin-top: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}
</style>