<template>
    <div>
        <div class="doc-a4">
            <p class="header">意见征询单</p>
            <span ref="orgNameRef" contenteditable="true" :innerHTML="form.orgName"></span>：
            <p ref="remarkRef" class="content">
                <span ref="projNameRef" contenteditable="true"
                    :innerHTML="form.projName"></span>项目审计报告已完成，现将报告征求意见稿递交你单位，请于
            <div class="inline relative">
                <span ref="consultExpireRef" contenteditable="true" :innerHTML="form.consultExpire"></span>
                <div class="absolute top--4 left--10 opacity-0">
                    <el-date-picker v-model="form.consultExpire" type="date" value-format="YYYY年MM月DD日"
                        class="!w-37"></el-date-picker>
                </div>
            </div>
            前对报告所列事实、数据和结论等方面是否确切和恰当，提出书面意见。
            </p>
            <p class="footer">
                <span style="text-align:center!important;">
                    管委会计财处<br>
                    <div class="inline relative">
                        <span ref="consultDateRef" contenteditable="true" :innerHTML="form.consultDate"></span>
                        <div class="absolute top--4 left-0 opacity-0">
                            <el-date-picker v-model="form.consultDate" type="date" value-format="YYYY年MM月DD日"
                                class="!w-37"></el-date-picker>
                        </div>
                    </div>

                </span>
            </p>

            <div>
                <div class="flex items-center">
                    <span>附件：</span>
                    <MyFileUpload :data="{
                        sourceId: auditForm.getAuditId,
                        sourceType: EFileSoureType.FUND_SUPERVISION,
                        primaryType: '征求意见'
                    }" @upload-success="fileTableRef?.updateTable"></MyFileUpload>
                </div>
                <FileTable ref="fileTableRef" :source-id="auditForm.getAuditId!" primary-type="征求意见"
                :hidden-columns="['primaryType']"></FileTable>
            </div>
        </div>


    </div>
</template>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useAuditStore } from '../../store/formStore'
import MyFileUpload from '@/components/FileUpload/MyFileUpload.vue'
import { EFileSoureType } from '@/utils/constants'
import FileTable from '@/components/Table/FileTable.vue'

const auditForm = useAuditStore()

const form = reactive<Suggestion>({
    orgName: auditForm.currentNodeData?.operateDataResult?.orgName ?? Array.from(new Set([auditForm.mainForm?.bsjName, auditForm.mainForm?.skName].filter(t => t))).join('、'),
    consultExpire: auditForm.currentNodeData?.operateDataResult?.consultExpire ?? dayjs().format('YYYY年MM月DD日'),
    projName: auditForm.currentNodeData?.operateDataResult?.projName ?? auditForm.mainForm?.projName,
    remark: auditForm.currentNodeData?.operateDataResult?.remark,
    consultDate: auditForm.currentNodeData?.operateDataResult?.consultDate ?? dayjs().format('YYYY年MM月DD日'),
    files1: []
})
const orgNameRef = ref<HTMLSpanElement>()
const consultExpireRef = ref<HTMLSpanElement>()
const projNameRef = ref<HTMLSpanElement>()
const consultDateRef = ref<HTMLSpanElement>()
const remarkRef = ref<HTMLParagraphElement>()
const fileTableRef = ref<FileTableExposeType | null>(null)

defineExpose({
    async getData() {
        return {
            orgName: orgNameRef.value?.innerText ?? '',
            consultExpire: consultExpireRef.value?.innerText ?? '',
            projName: projNameRef.value?.innerText ?? '',
            consultDate: consultDateRef.value?.innerText ?? '',
            remark: remarkRef.value?.innerText ?? '',
            files1: fileTableRef.value?.getFileList().map(item => ({name: item.fileName, fileId: item.id}))
        }
    }
})
</script>

<style scoped>
.doc-a4 {
    width: 800px;
    margin: 30px auto;
    font-family: 仿宋_GB2312;
    font-size: 18px;
    line-height: 50px;
}

.doc-a4 .header {
    font-family: 黑体;
    font-size: 24px;
    text-align: center;
    margin-bottom: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}

.doc-a4 .content {
    margin-top: 15px;
    text-indent: 2em;
}

.doc-a4 .footer {
    text-align: right;
    margin-top: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}
</style>