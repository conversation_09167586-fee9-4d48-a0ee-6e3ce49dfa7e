<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item label="审计类型" width="85%">
                <el-form-item prop="typeId" class="!mb-0">
                    <el-select v-model="form.typeId" placeholder="请选择审计类型" clearable class="w-50">
                        <el-option v-for="item in auditTypeOptions" :label="item.name" :value="item.id!" />
                    </el-select>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="审计说明">
                <el-form-item prop="remark" class="!mb-0">
                    <el-input v-model="form.remark" type="textarea" :rows="5"></el-input>
                </el-form-item>

            </el-descriptions-item>
        </el-descriptions>
    </el-form>



</template>

<script setup lang="ts">
import { getAuditTypeOptions } from '@/api/fund/audit'
import { FormInstance } from 'element-plus'
import { useAuditStore } from '../../store/formStore'

const auditForm = useAuditStore()

const auditTypeOptions = ref<FundAuditTypeVo[]>([])
getAuditTypeOptions().then(res => {
  auditTypeOptions.value = res.data ?? []
})
const form = reactive<AuditPlan>({
    typeId: auditForm.currentNodeData?.operateDataResult?.typeId ?? '',
    typeName: auditForm.currentNodeData?.operateDataResult?.typeName ?? '',
    remark: auditForm.currentNodeData?.operateDataResult?.remark ?? ''
})
const rules = {
    typeId: [{ required: true, message: '请选择审计类型', trigger: 'blur' }]
}


const formRef = ref<FormInstance>()

defineExpose({
    async getData() {
        await formRef.value?.validate()
        form.typeName = auditTypeOptions.value.find(item => item.id === form.typeId)?.name ?? ''
        return {
            ...form
        }
    }
})
</script>