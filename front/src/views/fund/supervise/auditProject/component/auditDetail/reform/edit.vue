<template>
    <el-descriptions :column="1" label-width="15%" border>

        <el-descriptions-item label="整改报告" width="85%">
            <template #label>
                整改报告 <span class="text-4 text-red">*</span>
            </template>
            <div class="w-full max-w-200">
                <MyFileUpload :data="{
                    sourceId: auditForm.getAuditId,
                    sourceType: EFileSoureType.FUND_SUPERVISION,
                    primaryType: '整改报告'
                }" @upload-success="fileTableRef?.updateTable"></MyFileUpload>

                <div class="mt-2">
                    <FileTable ref="fileTableRef" :source-id="auditForm.getAuditId!" primary-type="整改报告"
                        :hidden-columns="['primaryType']"></FileTable>
                </div>
            </div>

        </el-descriptions-item>
        <el-descriptions-item label="整改说明" width="90%">
            <el-input v-model="form.remark" type="textarea"  :rows="5" class="w-full max-w-200"></el-input>
        </el-descriptions-item>
    </el-descriptions>
</template>

<script setup lang="ts">
import { EFileSoureType } from '@/utils/constants'
import FileTable from '@/components/Table/FileTable.vue'
import { useAuditStore } from '../../store/formStore';

const auditForm = useAuditStore()

const form = reactive({
    files: [],
    remark: auditForm.currentNodeData?.operateDataResult?.remark,
})

const fileTableRef = ref<FileTableExposeType | null>(null)

defineExpose({
    async getData() {
        return {
            remark: form.remark,
            files: fileTableRef.value?.getFileList().map(item => ({name: item.fileName, fileId: item.id}))
        }
    }
})
</script>