<template>
    <el-descriptions :column="1" label-width="15%" border>

        <el-descriptions-item label="整改报告" width="85%">
            <template #label>
                整改报告 <span class="text-4 text-red">*</span>
            </template>
            <div class="w-full max-w-200">
                <FileTable ref="fileTableRef" :source-id="auditForm.getAuditId!" primary-type="整改报告"
                    :hidden-columns="['primaryType', 'del']"></FileTable>
            </div>

        </el-descriptions-item>
        <el-descriptions-item label="整改说明" width="90%">
            {{ form.remark }}
        </el-descriptions-item>
    </el-descriptions>
</template>

<script setup lang="ts">
import { EFileSoureType } from '@/utils/constants'
import FileTable from '@/components/Table/FileTable.vue'
import { useAuditStore } from '../../store/formStore';

const auditForm = useAuditStore()

const form = reactive({
    files: [],
    remark: ''
})
watch(() => auditForm.currentNodeData?.operateDataResult, () => {
    if (auditForm.currentNodeData?.operateDataResult) {
        Object.assign(form, auditForm.currentNodeData?.operateDataResult)
    }

}, { immediate: true })

</script>