<template>
    <div class="relative h-full">
        <div class="absolute top-0 left-0 z-10 w-full bg-white">
            <p class="my-0 text-5 font-bold">定稿</p>
            <el-divider class="my-3" />
        </div>

        <el-scrollbar height="100%">
            <div class="w-full py-15">
                <Edit ref="editFormRef" v-if="auditForm.userCanOperate"></Edit>
                <View v-else></View>
            </div>

        </el-scrollbar>

        <!-- 操作按钮 -->
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <template v-if="auditForm.userCanOperate">
                    <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
                    <el-button type="success" :loading="loading" @click="handleSubmit">提交</el-button>
                </template>
                <el-button type="danger" @click="$router.back()">返回列表</el-button>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import { doBusiness } from '@/api/fund/audit';
import { useAuditStore } from '../store/formStore';
import Edit from './finalize/edit.vue';
import View from './finalize/view.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const auditForm = useAuditStore()

const loading = ref(false)
const editFormRef = ref()
const handleSave = async () => {
    const data = await editFormRef.value?.getData()
    loading.value = true
    doBusiness({
        isSubmit: false,
        auditId: auditForm.getAuditId!,
        operateData: data
    }).then(res => {
        ElMessage.success('保存成功')
        auditForm.fetchFundAudit()
    }).finally(() => {
        loading.value = false
    })
}

const handleSubmit = async () => {
    ElMessageBox.confirm('是否确认提交？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const data = await editFormRef.value?.getData()
        loading.value = true
        doBusiness({
            isSubmit: true,
            auditId: auditForm.getAuditId!,
            operateData: data
        }).then(res => {
            ElMessage.success('提交成功')
            auditForm.fetchFundAudit()
            auditForm.toNextNode()
        }).finally(() => {
            loading.value = false
        })
    })
}
</script>