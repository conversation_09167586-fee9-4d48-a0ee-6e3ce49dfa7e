<template>
    <div class="relative h-full">
        <div class="absolute top-0 left-0 z-10 w-full bg-white">
            <p class="my-0 text-5 font-bold">资料上传</p>
            <el-divider class="my-3" />
        </div>

        <el-scrollbar height="100%">
            <div class="w-full py-15">
                <el-table :data="docList" row-key="id" default-expand-all border height="100%">
                    <el-table-column prop="seqNo" label="序号" width="100px" align="center"></el-table-column>
                    <el-table-column prop="name" label="资料名称" width="200px" header-align="center" align="left">
                        <template #default="{ row }">
                            {{ row.name }}
                            <span v-if="row.isRequire == '1'" class="text-red text-4">*</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="资料模板" width="200px" header-align="center" align="left">
                        <template #default="{ row }">
                            <DownloadA v-if="row.templateFile" :file="row.templateFile"></DownloadA>
                        </template>
                    </el-table-column>
                    <el-table-column label="资料上传" header-align="center" align="left">
                        <template #default="{ row }">
                            <div v-if="isOperate" class="mb-2">
                                <MyFileUpload action="/fund/audit/file/upload" :accept="fileType" :data="{
                                    docId: row.id,
                                    sourceType: EFileSoureType.FUND_SUPERVISION,
                                    primaryType: '审计资料',
                                    subType: row.name
                                }" @upload-success="handleUploadSuccess">
                                    <el-button type="primary" size="small">上传文件</el-button>
                                </MyFileUpload>
                            </div>
                            <div v-if="fileMap.get(row.id)">
                                <el-table :data="fileMap.get(row.id) ?? []" border :show-header="false">
                                    <el-table-column type="index" label="序号" align="center"
                                        width="50"></el-table-column>
                                    <el-table-column prop="fileName" label="文件名">
                                        <template #default="{ row }">
                                            <DownloadA v-if="row.file" :file="row.file"></DownloadA>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="fileName" label="审核意见" align="left">
                                        <template #default="{ row }">
                                            <div class="grid grid-cols-[50px_1fr]">
                                                <el-tag v-if="row.approveResult == '1'" type="success">通过</el-tag>
                                                <el-tag v-if="row.approveResult == '0'" type="danger">退回</el-tag>
                                                <div v-if="row.approveName || row.approveReason"
                                                    class="inline ml-2 break-all">
                                                    {{ `${row.approveName}, ${row.approveReason ?? ''}` }}
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="60" align="center" v-if="isOperate">
                                        <template #default="{ row }">
                                            <el-button v-if="row.approveResult == '0'" type="danger" link
                                                @click="handleRemoveFile(row)">删除</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-scrollbar>



        <!-- 操作按钮 -->
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <el-button v-if="(auditForm.mainForm?.nodeSeq ?? 0) == (auditForm.currentNodeData?.nodeSeq ?? 0)"
                    type="success" :loading="loading" @click="handleSubmit">提交</el-button>
                <el-button type="danger" @click="$router.back()">返回列表</el-button>
            </div>
        </div>

    </div>

</template>

<script setup lang="ts">
import { canUserOperateDoclist, doBusiness, doclist, removeAuditFile } from '@/api/fund/audit';
import { useAuditStore } from '../store/formStore';
import MyFileUpload from '@/components/FileUpload/MyFileUpload.vue';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { foreach } from 'tree-lodash';
import { EFileSoureType } from '@/utils/constants';

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const auditForm = useAuditStore()

const isOperate = (await canUserOperateDoclist(auditForm.mainForm?.id!)).data

const loading = ref(false)
const fileMap = reactive<Map<string, FundAuditFileVo[]>>(new Map())
const docList = ref<FundAuditDoclistVo[]>([])
doclist(auditForm.mainForm?.id!).then(res => {
    docList.value = res.data ?? []

    foreach(docList.value, (item: any) => {
        fileMap.set(item.id!, item.auditFiles)

    })
})


const handleUploadSuccess = (res: IResult<FundAuditFileVo>) => {
    if (res.data?.id) {
        if (fileMap.get(res.data?.docId!)) {
            fileMap.get(res.data.docId!)!.push(res.data)
        } else {
            fileMap.set(res.data.docId!, [res.data])
        }
    }

}

const handleRemoveFile = (file: FundAuditFileVo) => {
    ElMessageBox.confirm('是否确认删除该文件？', '提示', {
        type: 'warning'
    }).then(() => {
        removeAuditFile(file.id!).then(res => {
            ElMessage.success('删除成功');

            fileMap.get(file.docId!)!.splice(fileMap.get(file.docId!)!.indexOf(file), 1)
        })
    })
}

const handleSubmit = () => {
    for (let i = 0; i < docList.value.length; i++) {
        const item = docList.value[i]
        if (item.isRequire == '1') {
            if ((fileMap.get(item.id!)?.length ?? 0) < 1) {
                ElMessage.error(`请上传：${item.name}附件`)
                return
            }
        }
    }

    ElMessageBox.confirm('是否确认提交资料？', '提示', {
        type: 'warning'
    }).then(() => {
        loading.value = true
        doBusiness({
            isSubmit: true,
            auditId: auditForm.getAuditId!,
        }).then(res => {
            ElMessage.success('保存成功')
            auditForm.fetchFundAudit()
            auditForm.toNextNode()
        }).finally(() => {
            loading.value = false
        })
    })


}
</script>