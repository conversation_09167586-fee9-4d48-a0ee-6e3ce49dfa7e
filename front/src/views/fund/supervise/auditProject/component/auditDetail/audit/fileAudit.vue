<template>
    <el-input type="textarea" v-model="message" :rows="5" placeholder="请输入您的意见" />

     <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
        <el-button type="danger" :loading="loading" @click="handleReject">退回</el-button>
        <el-button type="primary" :loading="loading" @click="handlePass">同意</el-button>
    </div>
</template>

<script setup lang="ts">
import { doAuditFundAuditFile } from '@/api/fund/audit';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['close'])
const props = defineProps<{
    fileId: string
}>()

const message = ref('')
const loading = ref(false)
const handlePass = () => {
    loading.value = true
    doAuditFundAuditFile({
        id: props.fileId,
        approveResult: '1',
        approveReason: message.value

    }).then(() => {
        ElMessage.success('审核成功');
         emit('close')
    }).finally(() => {
        loading.value = false
    })
}
const handleReject = () => {
    loading.value = true
    doAuditFundAuditFile({
        id: props.fileId,
        approveResult: '0',
        approveReason: message.value

    }).then(() => {
        ElMessage.success('审核成功');
        emit('close')
    }).finally(() => {
        loading.value = false
    })
}
</script>