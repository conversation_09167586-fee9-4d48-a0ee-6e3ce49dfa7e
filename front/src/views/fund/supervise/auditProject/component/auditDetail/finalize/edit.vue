<template>
    <div>

        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item label="审定金额" width="85%">
                <template #label>
                    审定金额 <span class="text-4 text-red">*</span>
                </template>
                <el-input-number v-model="form.auditAmount" :min="0" controls-position="right" class="!w-50">
                    <template #suffix>元</template>
                </el-input-number>
            </el-descriptions-item>
            <el-descriptions-item label="审定报告">
                <template #label>
                    审定报告 <span class="text-4 text-red">*</span>
                </template>
                <div class="w-full max-w-200">
                    <MyFileUpload :data="{
                        sourceId: auditForm.getAuditId,
                        sourceType: EFileSoureType.FUND_SUPERVISION,
                        primaryType: '定稿'
                    }" @upload-success="fileTableRef?.updateTable"></MyFileUpload>

                    <div class="mt-2">
                        <FileTable ref="fileTableRef" :source-id="auditForm.getAuditId!" primary-type="定稿"
                            :hidden-columns="['primaryType']"></FileTable>
                    </div>
                </div>

            </el-descriptions-item>
            <el-descriptions-item label="是否需要整改">
                <template #label>
                    是否需要整改 <span class="text-4 text-red">*</span>
                </template>
                <el-select v-model="form.needRectify" class="!w-50">
                    <el-option label="需要整改" value="1"></el-option>
                    <el-option label="无需整改" value="0"></el-option>
                </el-select>
            </el-descriptions-item>
        </el-descriptions>

        <div class="doc-a4">
            <p class="header">审计整改通知书</p>
            <span ref="orgNameRef" contenteditable="true" :innerHTML="form.rectifyItem.orgName"></span>：
            <p ref="remarkRef" class="content">
            <div class="inline relative">
                <span ref="auditStartRef" contenteditable="true" :innerHTML="form.rectifyItem.auditStart"></span>
                <div class="absolute top--4 left--10 opacity-0">
                    <el-date-picker v-model="form.rectifyItem.auditStart" type="date" value-format="YYYY年MM月DD日"
                        class="!w-37"></el-date-picker>
                </div>
            </div>起，我处委托
            <span ref="auditOrgRef" contenteditable="true" :innerHTML="form.rectifyItem.auditOrg"></span>对你单位（部门）的
            <span ref="projNameRef" contenteditable="true" :innerHTML="form.rectifyItem.projName"></span>进行了
            <span ref="auditTypeRef" contenteditable="true"
                :innerHTML="form.rectifyItem.auditType"></span>。现审计报告已送达给你单位（部门），请你单位（部门）按照审计报告中所提出的问题及建议予以整改落实，并于
            <div class="inline relative">
                <span ref="rectifyExpireRef" contenteditable="true" :innerHTML="form.rectifyItem.rectifyExpire"></span>
                <div class="absolute top--4 left--10 opacity-0">
                    <el-date-picker v-model="form.rectifyItem.rectifyExpire" type="date" value-format="YYYY年MM月DD日"
                        class="!w-37"></el-date-picker>
                </div>
            </div>前形成书面整改报告，说明相关问题整改情况报我处。
            </p>
            <p class="footer">
                <span style="text-align:center!important;">
                    管委会计财处<br>
                    <div class="inline relative">
                        <span ref="rectifyDateRef" contenteditable="true"
                            :innerHTML="form.rectifyItem.rectifyDate"></span>
                        <div class="absolute top--4 left--10 opacity-0">
                            <el-date-picker v-model="form.rectifyItem.rectifyDate" type="date"
                                value-format="YYYY年MM月DD日" class="!w-37"></el-date-picker>
                        </div>
                    </div>

                </span>
            </p>
        </div>
    </div>


</template>

<script setup lang="ts">
import { dayjs } from 'element-plus'
import { useAuditStore } from '../../store/formStore'
import { EFileSoureType } from '@/utils/constants'
import FileTable from '@/components/Table/FileTable.vue'


const auditForm = useAuditStore()

const form = reactive<Finalize>({
    auditAmount: auditForm.currentNodeData?.operateDataResult?.auditAmount,
    files: auditForm.currentNodeData?.operateDataResult?.files ?? [],
    needRectify: auditForm.currentNodeData?.operateDataResult?.needRectify ?? '1',
    rectifyItem: auditForm.currentNodeData?.operateDataResult?.rectifyItem ?? {
        auditStart: dayjs(auditForm.mainForm?.auditStart).format('YYYY年MM月DD日'),
        orgName: Array.from(new Set([auditForm.mainForm?.bsjName, auditForm.mainForm?.skName].filter(t => t))).join('、'),
        auditOrg: auditForm.mainForm?.auditCompany,
        projName: auditForm.mainForm?.projName,
        auditType: auditForm.mainForm?.typeName,
        rectifyExpire: dayjs().format('YYYY年MM月DD日'),
        rectifyDate: dayjs().format('YYYY年MM月DD日'),
    }
})
const orgNameRef = ref<HTMLSpanElement>()
const auditStartRef = ref<HTMLSpanElement>()
const auditOrgRef = ref<HTMLSpanElement>()
const projNameRef = ref<HTMLSpanElement>()
const auditTypeRef = ref<HTMLSpanElement>()
const rectifyExpireRef = ref<HTMLSpanElement>()
const rectifyDateRef = ref<HTMLSpanElement>()
const remarkRef = ref<HTMLParagraphElement>()

const fileTableRef = ref<FileTableExposeType | null>(null)

defineExpose({
    async getData() {
        return {
            auditAmount: form.auditAmount,
            needRectify: form.needRectify,
            rectifyItem: {
                orgName: orgNameRef.value?.innerText ?? '',
                auditStart: auditStartRef.value?.innerText ?? '',
                auditOrg: auditOrgRef.value?.innerText ?? '',
                projName: projNameRef.value?.innerText ?? '',
                auditType: auditTypeRef.value?.innerText ?? '',
                rectifyExpire: rectifyExpireRef.value?.innerText ?? '',
                rectifyDate: rectifyDateRef.value?.innerText ?? '',
                remark: remarkRef.value?.innerText ?? '',
            },

            files: fileTableRef.value?.getFileList().map(item => ({ name: item.fileName, fileId: item.id }))
        }
    }
})
</script>

<style scoped>
.doc-a4 {
    width: 800px;
    margin: 30px auto;
    font-family: 仿宋_GB2312;
    font-size: 18px;
    line-height: 50px;
}

.doc-a4 .header {
    font-family: 黑体;
    font-size: 24px;
    text-align: center;
    margin-bottom: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}

.doc-a4 .content {
    margin-top: 15px;
    text-indent: 2em;
}

.doc-a4 .footer {
    text-align: right;
    margin-top: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}
</style>