<template>
    <div class="relative h-full">
        <div class="absolute top-0 left-0 z-10 w-full bg-white">
            <p class="my-0 text-5 font-bold">征求意见</p>
            <el-divider class="my-3" />
        </div>
        <el-scrollbar height="100%">


            <div class="py-10">
                <Edit ref="editFormRef" v-if="auditForm.userCanOperate"></Edit>
                <View v-else></View>
            </div>
        </el-scrollbar>

        <!-- 操作按钮 -->
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <el-button v-if="auditForm.userCanOperate" type="primary" :loading="loading"
                    @click="handleSave">保存</el-button>
                <el-button v-if="auditForm.userCanOperate" type="success" :loading="loading"
                    @click="handleSubmit">提交</el-button>


                <el-button type="danger" @click="$router.back()">返回列表</el-button>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import { doBusiness } from '@/api/fund/audit';
import { useAuditStore } from '../store/formStore';
import Edit from './suggestion/edit.vue';
import View from './suggestion/view.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const auditForm = useAuditStore()

const isUpdate = ref(false)

const loading = ref(false)
const editFormRef = ref()
const handleSave = async () => {
    const data = await editFormRef.value?.getData()
    loading.value = true
    doBusiness({
        isSubmit: false,
        auditId: auditForm.getAuditId!,
        operateData: data
    }).then(res => {
        ElMessage.success('保存成功')
        auditForm.fetchFundAudit()
    }).finally(() => {
        loading.value = false
    })
}

const handleSubmit = async () => {
    ElMessageBox.confirm('是否确认提交该意见征询单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const data = await editFormRef.value?.getData()
        loading.value = true
        doBusiness({
            isSubmit: true,
            auditId: auditForm.getAuditId!,
            operateData: data
        }).then(res => {
            ElMessage.success('提交成功')
            auditForm.fetchFundAudit()
            auditForm.toNextNode()
        }).finally(() => {
            loading.value = false
        })
    })

}
</script>