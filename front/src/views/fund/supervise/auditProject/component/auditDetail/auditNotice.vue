<template>
    <div class="relative h-full">
        <p class="my-0 text-5 font-bold">审计通知</p>
        <el-divider class="my-3" />
        <template v-if="isUpdate">
            <Edit ref="editFormRef"></Edit>
        </template>
        <template v-else>
            <Edit ref="editFormRef" v-if="auditForm.userCanOperate"></Edit>
            <View v-else></View>
        </template>

        <!-- 操作按钮 -->
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <template v-if="isUpdate">
                    <el-button v-if="auditForm.userCanOperate" type="primary" :loading="loading"
                        @click="handleSave">保存</el-button>
                </template>
                <template v-else>
                    <template v-if="(auditForm.mainForm?.nodeSeq ?? 0) > (auditForm.currentNodeData?.nodeSeq ?? 0)">
                        <el-button v-if="auditForm.userCanOperate" type="warning"
                            @click="isUpdate = true">修改</el-button>
                    </template>
                    <template v-else>
                        <el-button v-if="auditForm.userCanOperate" type="primary" :loading="loading"
                            @click="handleSave">保存</el-button>
                        <el-button v-if="auditForm.userCanOperate" type="success" :loading="loading"
                            @click="handleSubmit">提交</el-button>
                    </template>
                </template>
                <el-button type="danger" @click="$router.back()">返回列表</el-button>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import { doBusiness } from '@/api/fund/audit';
import { useAuditStore } from '../store/formStore';
import Edit from './auditNotice/edit.vue';
import View from './auditNotice/view.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const auditForm = useAuditStore()

const isUpdate = ref(false)

const loading = ref(false)
const editFormRef = ref()
const handleSave = async () => {
    const data = await editFormRef.value?.getData()
    loading.value = true
    doBusiness({
        isSubmit: false,
        auditId: auditForm.getAuditId!,
        operateData: data
    }).then(res => {
        ElMessage.success('保存成功')
        auditForm.fetchFundAudit()
    }).finally(() => {
        loading.value = false
    })
}

const handleSubmit = async () => {
    ElMessageBox.confirm('是否确认提交该审计通知？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const data = await editFormRef.value?.getData()
        loading.value = true
        doBusiness({
            isSubmit: true,
            auditId: auditForm.getAuditId!,
            operateData: data
        }).then(res => {
            ElMessage.success('保存成功')
            auditForm.fetchFundAudit()
            auditForm.toNextNode()
        }).finally(() => {
            loading.value = false
        })
    })

}
</script>