<template>
    <div>
        <div class="doc-a4">
            <p class="header">意见征询单</p>
            <span>{{ form.orgName }}</span>：
            <p class="content">
                {{ form.remark }}
            </p>
            <p class="footer">
                <span style="text-align:center!important;">
                    管委会计财处<br><span contenteditable="false" data-elem-name="NoticeDate">{{ form.consultDate }}</span>
                </span>
            </p>

            <div>
                <div class="flex items-center">
                    <span>附件：</span>
                    <el-link type="primary" class="text-4" @click="handleDownloadYj">意见征询单.doc</el-link>
                </div>
                <FileTable :source-id="auditForm.getAuditId!" primary-type="征求意见"
                    :hidden-columns="['primaryType', 'del']">
                </FileTable>
            </div>
            <div>
                <div class="flex items-center">
                    <span>项目单位意见：</span>
                    <MyFileUpload :data="{
                        sourceId: auditForm.getAuditId,
                        sourceType: EFileSoureType.FUND_SUPERVISION,
                        primaryType: '意见反馈'
                    }" @upload-success="fileTableRef?.updateTable"></MyFileUpload>
                </div>
                <FileTable ref="fileTableRef" :source-id="auditForm.getAuditId!" primary-type="意见反馈"
                :hidden-columns="['primaryType']"></FileTable>
            </div>
        </div>

    </div>

</template>

<script setup lang="ts">
import { EFileSoureType } from '@/utils/constants';
import { useAuditStore } from '../../store/formStore';
import FileTable from '@/components/Table/FileTable.vue'

const { proxy } = getCurrentInstance() as { proxy: any };

const auditForm = useAuditStore()
const form = reactive<Suggestion>({} as any)
watch(() => auditForm.currentNodeData?.operateDataResult, () => {
    if (auditForm.currentNodeData?.operateDataResult) {
        Object.assign(form, auditForm.currentNodeData?.operateDataResult)
    }

}, { immediate: true })

const fileTableRef = ref<FileTableExposeType | null>(null)

const handleDownloadYj = () => {
    proxy.download('/fund/audit/downloadConsultation', {
        auditId: auditForm.getAuditId
    }, `意见征询单.doc`)
}

defineExpose({
    async getData() {
        return {
            ...form,
            files2: fileTableRef.value?.getFileList().map(item => ({name: item.fileName, fileId: item.id}))
        }
    }
})
</script>

<style scoped>
.doc-a4 {
    width: 800px;
    margin: 30px auto;
    font-family: 仿宋_GB2312;
    font-size: 18px;
    line-height: 50px;
}

.doc-a4 .header {
    font-family: 黑体;
    font-size: 24px;
    text-align: center;
    margin-bottom: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}

.doc-a4 .content {
    margin-top: 15px;
    text-indent: 2em;
}

.doc-a4 .footer {
    text-align: right;
    margin-top: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}
</style>