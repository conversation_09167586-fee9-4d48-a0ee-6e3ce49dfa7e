<template>
    <div>

        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item label="审定金额" width="85%">
                <template #label>
                    审定金额 <span class="text-4 text-red">*</span>
                </template>
                {{ numberFormat(form.auditAmount, 2) }} 元
            </el-descriptions-item>
            <el-descriptions-item label="审定报告">
                <template #label>
                    审定报告 <span class="text-4 text-red">*</span>
                </template>
                <div class="w-full max-w-200">
                    <FileTable ref="fileTableRef" :source-id="auditForm.getAuditId!" primary-type="定稿"
                        :hidden-columns="['primaryType', 'del']"></FileTable>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="是否需要整改">
                <template #label>
                    是否需要整改 <span class="text-4 text-red">*</span>
                </template>
                {{ form.needRectify == '1' ? '需要整改' : '无需整改' }}
            </el-descriptions-item>
        </el-descriptions>

        <div class="doc-a4">
            <p class="header">意见征询单</p>
            <span>{{ form.rectifyItem?.orgName }}</span>：
            <p class="content">
                {{ form.rectifyItem?.remark }}
            </p>
            <p class="footer">
                <span style="text-align:center!important;">
                    管委会计财处<br><span contenteditable="false">{{ form.rectifyItem?.rectifyDate }}</span>
                </span>
            </p>
        </div>
    </div>


</template>

<script setup lang="ts">
import { numberFormat } from '@/utils/common'
import { useAuditStore } from '../../store/formStore'
import FileTable from '@/components/Table/FileTable.vue'

const auditForm = useAuditStore()
const form = reactive<Finalize>({} as any)
watch(() => auditForm.currentNodeData?.operateDataResult, () => {
    if (auditForm.currentNodeData?.operateDataResult) {
        Object.assign(form, auditForm.currentNodeData?.operateDataResult)
    }

}, { immediate: true })
</script>

<style scoped>
.doc-a4 {
    width: 800px;
    margin: 30px auto;
    font-family: 仿宋_GB2312;
    font-size: 18px;
    line-height: 50px;
}

.doc-a4 .header {
    font-family: 黑体;
    font-size: 24px;
    text-align: center;
    margin-bottom: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}

.doc-a4 .content {
    margin-top: 15px;
    text-indent: 2em;
}

.doc-a4 .footer {
    text-align: right;
    margin-top: 20px;
}

.doc-a4 span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    min-width: 200px;
    padding: 0 10px;
}
</style>