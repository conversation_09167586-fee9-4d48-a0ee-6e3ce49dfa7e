<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="20%" border>
            <el-descriptions-item width="80%">
                <template #label>
                    项目名称 <span class="text-red">*</span>
                </template>
                <el-form-item prop="projName" class="!mb-0">
                    <el-select v-model="form.projName" allow-create filterable clearable remote reserve-keyword
                        placeholder="请输入项目名称" remote-show-suffix :remote-method="remoteMethod"
                        :loading="projSelectLoading" @change="handleProjectChange" @clear="handleProjectClear">
                        <el-option v-for="item in projectOptions" :label="item.projName" :value="item.projId!" />
                    </el-select>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    申报主体
                </template>
                <el-form-item prop="sbOrgid" class="!mb-0">
                    <DeptSelect v-model="form.sbOrgid" class="!w-50" :disabled="!!form.projId"></DeptSelect>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    考核主体
                </template>
                <el-form-item prop="khOrgid" class="!mb-0">
                    <DeptSelect v-model="form.khOrgid" class="!w-50" :disabled="!!form.projId"></DeptSelect>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    被审计单位 <span class="text-red">*</span>
                </template>
                <el-form-item prop="bsjOrgid" class="!mb-0">
                    <DeptSelect v-model="form.bsjOrgid" class="!w-50"></DeptSelect>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item width="80%">
                <template #label>
                    收款单位
                </template>
                <el-form-item prop="skOrgid" class="!mb-0">
                    <DeptSelect v-model="form.skOrgid" class="!w-50"></DeptSelect>
                    （如：公共事务中心）
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    项目金额 <span class="text-red">*</span>
                </template>
                <el-form-item prop="projAmount" class="!mb-0">
                    <el-input-number v-model="form.projAmount" :min="0" class="!w-50" controls-position="right"
                        placeholder="请输入项目金额" :disabled="!!form.projId" />
                    （单位：万元）
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item width="80%">
                <template #label>
                    累计执行金额
                </template>
                <el-form-item prop="payedAmount" class="!mb-0">
                    <el-input-number v-model="form.payedAmount" :min="0" class="!w-50" controls-position="right"
                        placeholder="请输入累计执行金额" :disabled="!!form.projId" />
                    （单位：万元）
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    审计年度 <span class="text-red">*</span>
                </template>
                <el-form-item prop="year" class="!mb-0">
                    <el-date-picker v-model="form.year" type="year" value-format="YYYY" class="!w-50" placeholder="请选择审计年度" />
                </el-form-item>
            </el-descriptions-item>
        </el-descriptions>
        <!-- 操作按钮 -->
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" @click="submitForm">下一步</el-button>
            <el-button @click="emit('cancel')">关闭窗口</el-button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from 'element-plus'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { getAuditProjectOptions, saveAuditProject,  } from '@/api/fund/audit'


const emit = defineEmits(['close', 'cancel'])


const projectOptions = ref<FundAuditProjectOptionVo[]>([])
const projSelectLoading = ref(false)
const remoteMethod = (query: string) => {
    if (query) {
        projSelectLoading.value = true
        getAuditProjectOptions(query).then(res => {
            projectOptions.value = res.data ?? []
        }).finally(() => {
            projSelectLoading.value = false
        })
    } else {
        projectOptions.value = []
    }
}
const handleProjectChange = (val: string) => {
    const project = projectOptions.value.find(item => item.projId === val)
    if (project) {
        form.projId = project?.projId
        form.projName = project?.projName ?? ''
        form.sbOrgid = project?.sbOrgid
        form.khOrgid = project?.khOrgid
        form.projAmount = project?.projAmount!
        form.payedAmount = project?.payedAmount
    }
}
const handleProjectClear = () => {
    form.projId = ''
    form.projName = ''
    form.sbOrgid = undefined
    form.khOrgid = undefined
    form.projAmount = undefined
    form.payedAmount = undefined
}


const form = reactive<FundAuditDto>({} as any)

const rules = {
    projName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    bsjOrgid: [{ required: true, message: '请选择被审计单位', trigger: 'blur' }],
    projAmount: [{ required: true, message: '请输入项目金额', trigger: 'blur' }],
    year: [{ required: true, message: '请选择审计年度', trigger: 'blur' }]
}

const formRef = ref<FormInstance>()
const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            saveAuditProject(form).then(res => {
                ElMessage.success('保存成功')
                emit('close', res.data)
            })
        }
    })
}

</script>
