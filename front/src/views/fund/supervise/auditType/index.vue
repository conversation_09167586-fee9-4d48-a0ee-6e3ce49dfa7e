<template>
    <div class="app-container" v-loading="loading">
        <div class="flex gap-2">
            <el-button type="primary" class="mb-4" @click="handleAdd">新增审计类型</el-button>
        </div>
        <el-table :data="dataList" border row-key="id">
            <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
            <el-table-column prop="name" label="类型" align="center"></el-table-column>
            <el-table-column prop="topDetailsCount" label="清单数量" align="center" width="220">
                <template #default="{ row }">
                    <el-link type="primary" @click="handleDataFormShow(row)">资料清单 ({{ row.topDetailsCount }})</el-link>
                </template>
            </el-table-column>
            <el-table-column prop="seqNo" label="排序" align="center" width="150"></el-table-column>
            <el-table-column label="操作" align="center" width="220">
                <template #default="{ row }">
                    <el-button type="primary" link icon="edit"
                        @click="handleEdit(row)">编辑</el-button>
                    <el-button type="danger" link icon="delete" @click="handleRemove(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog title="审计类型管理" v-model="auditTypeShow" width="500px" :close-on-click-modal="false" destroy-on-close>
            <TypeForm :form-id="currentFormId" @cancel="auditTypeShow = false" @close="auditTypeShow = false;search()"></TypeForm>
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog title="审计资料清单管理" v-model="dataFormShow" width="1000px" :close-on-click-modal="false" destroy-on-close @close="search()">
            <DataForm :type-id="currentFormId" @cancel="dataFormShow = false"></DataForm>
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>

</template>

<script setup lang="ts">
import { getList, updateTableClose, delByYear, getListSnap } from '@/api/lib/report/usagePlan';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox, formatter } from 'element-plus';
import TypeForm from './component/typeForm.vue';
import DataForm from './component/dataForm.vue';
import { getAuditTypeList, removeAuditType } from '@/api/fund/auditTypeManage';

const loading = ref(false);
const dataList = ref<FundAuditTypeVo[]>([]);
const search = () => {
    loading.value = true
    getAuditTypeList().then(res => {
        dataList.value = res.data ?? [];
    }).finally(() => {
        loading.value = false
    })
}
search()

const currentFormId = ref('');
const auditTypeShow = ref(false)
const handleAdd = () => {
    currentFormId.value = ''
    auditTypeShow.value = true
}

const handleEdit = (row: FundAuditTypeVo) => {
    currentFormId.value = row.id!
    auditTypeShow.value = true
}

const dataFormShow = ref(false)
const handleDataFormShow = (row: FundAuditTypeVo) => {
    currentFormId.value = row.id!
    dataFormShow.value = true
}

const handleRemove = (row: IUsagePlan) => {
    ElMessageBox.confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        removeAuditType([row.id]).then(res => {
            ElMessage.success('操作成功')
            search()
        })
    })
}
</script>