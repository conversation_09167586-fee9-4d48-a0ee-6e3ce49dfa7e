<template>
    <div class="grid grid-cols-[40%_1fr] gap-3">
        <div class="border border-solid border-[#cddcfd]">
            <div class="text-center bg-[#0079f6] py-2">
                <div class="text-4 text-white">
                    资料清单
                    <span class="text-white cursor-pointer" @click="handleAddTemplate(undefined)">（点击添加顶级资料）</span>
                </div>
            </div>
            <div class="mt-2">
                <el-scrollbar height="500px">
                    <el-tree :data="typeForm?.treeDetails ?? []" :expand-on-click-node="false" default-expand-all
                        :props="defaultProps" @node-click="handleNodeClick">
                        <template #default="{ node, data }">
                            <div class="node flex-1 py-2">
                                <div class="text-left break-all whitespace-normal">
                                    <span>{{ data.seqNo }}、</span>
                                    <span>{{ data.name }}</span>
                                    <span v-if="data.isRequire == '1'" class="text-red text-4">*</span>
                                    <div class="float-right inline-block hidden hover:block">
                                        <el-button type="primary" link size="small"
                                            @click.stop="handleAddTemplate(data)">添加下级</el-button>
                                        <el-button type="danger" link size="small"
                                            @click.stop="handleRemoveTemplate(data)">删除</el-button>
                                    </div>
                                </div>

                            </div>

                        </template>
                    </el-tree>
                </el-scrollbar>

            </div>
        </div>
        <div>
            <el-form :model="selectedTemplateForm" :rules="rules" ref="formRef" :inline-message="true">
                <el-descriptions :column="1" label-width="25%" border>
                    <el-descriptions-item width="75%">
                        <template #label>
                            上级
                        </template>
                        {{ getTemplateParent?.name }}
                        <!-- <el-form-item prop="groupNumber" class="!mb-0">
                            <el-input v-model="form.groupNumber" placeholder="请输入类别名称" />
                        </el-form-item> -->
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>
                            资料名称 <span class="text-red">*</span>
                        </template>
                        <el-form-item prop="name" class="!mb-0">
                            <el-input v-model="selectedTemplateForm.name" maxlength="200" />
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>
                            是否必须 <span class="text-red">*</span>
                        </template>
                        <el-form-item prop="isRequire" class="!mb-0">
                            <el-select v-model="selectedTemplateForm.isRequire">
                                <el-option label="是" value="1" />
                                <el-option label="否" value="0" />
                            </el-select>
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>
                            <div>排序号 <span class="text-red">*</span></div>
                        </template>
                        <el-form-item prop="seqNo" class="!mb-0">
                            <el-input-number v-model="selectedTemplateForm.seqNo" controls-position="right"
                                class="w-full" />
                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template #label>
                            资料模板
                        </template>
                        <el-form-item prop="unit" class="!mb-0">
                            <div class="flex flex-col gap-1">
                                <my-file-upload :accept="fileType" :data="{
                                    sourceId: selectedTemplateForm.id,
                                    sourceType: EFileSoureType.FUND_SUPERVISION,
                                    primaryType: '资料清单',
                                }" @upload-success="handleUploadSuccess"></my-file-upload>
                                <template v-if="templateFile">
                                    <DownloadA :file="templateFile"></DownloadA>
                                </template>
                            </div>

                        </el-form-item>
                    </el-descriptions-item>
                    <el-descriptions-item label="资料描述">
                        <el-form-item prop="remark" class="!mb-0">
                            <el-input type="textarea" v-model="selectedTemplateForm.remark" placeholder="请输入备注"
                                :rows="3" maxlength="500" />
                        </el-form-item>
                    </el-descriptions-item>
                </el-descriptions>
                <!-- 操作按钮 -->
                <div class="flex justify-center items-center mt-3">
                    <el-button type="primary" :loading="loading" @click="submitForm">保存</el-button>
                    <el-button @click="emit('cancel')">关闭窗口</el-button>
                </div>
            </el-form>
        </div>
    </div>

</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus'
import { getAuditTypeDetail, removeAuditTemplate, saveAuditTemplate } from '@/api/fund/auditTypeManage';
import { filter, find } from 'tree-lodash';
import { genSnowId } from '@/utils/common';
import { EFileSoureType } from '@/utils/constants';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
import { getFileById, getFileList } from '@/api/lib/project/file';

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    typeId: string
}>()
const typeForm = ref<FundAuditTypeVo>()
const getDetail = async () => {
    const res = await getAuditTypeDetail(props.typeId)
    typeForm.value = res.data
}
getDetail()

const selectedTemplateForm = ref<FundAuditTemplateVo>({
    id: genSnowId(),
    typeId: props.typeId,
    isRequire: '1'
})
const handleNodeClick = async (data: FundAuditTemplateVo) => {
    selectedTemplateForm.value = data
    if (data.fileId) {
        templateFile.value = (await getFileById(data.fileId)).data
    } else {
        templateFile.value = undefined

    }
}
const getTemplateParent = computed(() => {
    if (selectedTemplateForm.value.parentId) {
        return find((typeForm.value?.treeDetails ?? []) as any, (item) => {
            return item.id === selectedTemplateForm.value.parentId
        }, { childrenKey: 'children' })
    }

})

const handleAddTemplate = (node?: FundAuditTemplateVo) => {
    templateFile.value = undefined
    selectedTemplateForm.value = {
        id: genSnowId(),
        typeId: props.typeId,
        parentId: node?.id,
        isRequire: '1'
    }
}

const handleRemoveTemplate = (node: FundAuditTemplateVo) => {
    ElMessageBox.confirm('确定要删除吗？', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            removeAuditTemplate([node.id!]).then(() => {
                ElMessage.success('删除成功');
                getDetail()
                selectedTemplateForm.value = {
                    id: genSnowId(),
                    typeId: props.typeId,
                    isRequire: '1'
                }
            });
        })
}
const templateFile = ref<ISysAttachment>()
const handleUploadSuccess = (file: IResult<ISysAttachment>) => {
    templateFile.value = file.data
    selectedTemplateForm.value.fileId = file.data?.id
}

const defaultProps = {
    children: 'children',
    label: 'label',
}


const rules = reactive<FormRules>({
    name: [{ required: true, message: '请输入资料名称', trigger: 'blur' }],
    isRequire: [{ required: true, message: '请选择是否必须', trigger: 'blur' }],
    seqNo: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
})

const loading = ref(false)
const formRef = ref<FormInstance>()
const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            const data = Object.assign({}, selectedTemplateForm.value) as FundAuditTemplateDto
            loading.value = true
            saveAuditTemplate(data).then(() => {
                ElMessage.success('保存成功')
                getDetail()
                selectedTemplateForm.value = {
                    id: genSnowId(),
                    typeId: props.typeId,
                    isRequire: '1'
                }
            }).finally(() => {
                loading.value = false
            })
        }
    })
}

</script>

<style scoped>
:deep(.el-input__inner) {
    text-align: left;
}

:deep(.el-tree-node__content) {
    height: auto;
}

.node:hover .hidden {
    display: block;
    /* 鼠标悬浮时显示 */
}
/* .border{solid 1px #cddcfd} */
</style>