<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="20%" border>
            <el-descriptions-item width="80%">
                <template #label>
                    类别名称 <span class="text-red">*</span>
                </template>
                <el-form-item prop="name" class="!mb-0">
                    <el-input v-model="form.name" placeholder="请输入类别名称" maxlength="200" />
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    显示顺序 <span class="text-red">*</span>
                </template>
                <el-form-item prop="seqNo" class="!mb-0">
                    <el-input-number v-model="form.seqNo" placeholder="请输入显示顺序" controls-position="right" class="w-full" />
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    状态 <span class="text-red">*</span>
                </template>
                <el-form-item prop="isEnable" class="!mb-0">
                    <el-select v-model="form.isEnable" placeholder="请选择状态">
                        <el-option label="正常" value="1" />
                        <el-option label="禁用" value="0" />
                    </el-select>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="描述">
                <el-form-item prop="remark" class="!mb-0">
                    <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" :rows="3" maxlength="1000" />
                </el-form-item>
            </el-descriptions-item>
        </el-descriptions>
        <!-- 操作按钮 -->
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" :loading="loading" @click="submitForm">保存</el-button>
            <el-button @click="emit('cancel')">关闭窗口</el-button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from 'element-plus'
import { saveAuditType, getAuditTypeDetail } from '@/api/fund/auditTypeManage';


const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    formId: string
}>()


const form = reactive<FundAuditTypeDto>({
    isEnable: '1'
} as any)
if (props.formId) {
    getAuditTypeDetail(props.formId).then(res => {
        Object.assign(form, res.data)
    })
}

const rules = {
    name: [{ required: true, message: '请输入类别名称', trigger: 'blur' }],
    seqNo: [{ required: true, message: '请输入显示顺序', trigger: 'blur' }],
    isEnable: [{ required: true, message: '请选择状态', trigger: 'blur' }]
}

const loading = ref(false)
const formRef = ref<FormInstance>()
const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            loading.value = true
            saveAuditType(form).then(() => {
                ElMessage.success('保存成功')
                emit('close')
            }).finally(() => {
                loading.value = false
            })
        }
    })
}

</script>

<style scoped>
:deep(.el-input__inner) {
    text-align: left;
}
</style>