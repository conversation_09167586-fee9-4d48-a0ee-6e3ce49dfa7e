<template>
    <div class="app-container">
        <el-tabs type="border-card">
            <el-tab-pane label="参数设置">
                <ParamSeting></ParamSeting>
            </el-tab-pane>
            <el-tab-pane label="支付帐户管理">
                <PayAccount></PayAccount>
            </el-tab-pane>
            <el-tab-pane label="重点单位设置">
                <ImpUnit></ImpUnit>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script lang="tsx" setup>
import ParamSeting from './component/paramSeting.vue';
import ImpUnit from './component/impUnit.vue';
import PayAccount from './component/payAccount.vue';
</script>