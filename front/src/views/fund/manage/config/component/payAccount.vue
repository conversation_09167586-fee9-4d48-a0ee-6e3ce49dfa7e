<template>
    <el-form :model="form" :rules="rules" ref="formRef" label-position="top" label-width="0" :inline-message="true">
        <el-descriptions :column="1" border label-width="15%">
            <el-descriptions-item>
                <template #label>开户银行<span class="text-red">*</span></template>
                <el-form-item prop="bankName" class="!mb-0">
                    <el-input v-model="form.bankName" maxlength="50" />
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>户名<span class="text-red">*</span></template>
                <el-form-item prop="name" class="!mb-0">
                    <el-input v-model="form.name" maxlength="50" />
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>户(卡)号<span class="text-red">*</span></template>
                <el-form-item prop="cardNumber" class="!mb-0">
                    <el-input v-model="form.cardNumber" maxlength="50" />
                </el-form-item>
            </el-descriptions-item>
        </el-descriptions>
    </el-form>

    <div class="text-center mt-3">
        <el-button type="primary" @click="submitForm">保存</el-button>
    </div>

</template>

<script setup lang="ts">
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { getBankAccountList, addBankAccount, editBankAccount } from '@/api/fund/bankAccount'

const form = reactive<FundBankAccount>({
    bankName: '',
    name: '',
    cardNumber: '',
    isSpecial: '1'
})
const accountRes = await getBankAccountList({isSpecial: '1'}, {pageNum: 1, pageSize: 1})
if (accountRes.rows) {
    Object.assign(form, accountRes.rows.at(0))
}

const rules = reactive<FormRules<typeof form>>({
    name: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
    bankName: [{ required: true, message: '请输入户名', trigger: 'blur' }],
    cardNumber: [{ required: true, message: '请输入户(卡)号', trigger: 'blur' }],
})

const formRef = ref<FormInstance>()
const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
           let save = addBankAccount
           if (accountRes.data?.id) {
            save = editBankAccount
           }
           save(form).then(() => {
            ElMessage.success('保存成功');
        });
        }
    })
}
</script>