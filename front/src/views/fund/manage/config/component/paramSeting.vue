<template>
    <el-descriptions :column="1" label-width="15%" border>
        <el-descriptions-item label="拨款单申请通道" width="85%">
            <el-radio-group v-model="billApply">
                <el-radio value="0">关闭</el-radio>
                <el-radio value="1">开启</el-radio>
            </el-radio-group>
        </el-descriptions-item>
    </el-descriptions>
    <div class="text-center mt-5">
        <el-button type="primary" @click="save">保存设置</el-button>
    </div>
</template>

<script setup lang="ts">
import { useConfigStore } from '@/store/modules/config'
import { useConfig } from '@/utils/config'
// @ts-ignore
import { adjustConfig } from '@/api/system/config'
import { ElMessage } from 'element-plus'

const {
    ["sys.fund.bill.apply"]: sysFundBillApply,
} = useConfig('sys.fund.bill.apply',)

const billApply = ref<string>()

watchEffect(() => {
    if (sysFundBillApply.value) {
        billApply.value = sysFundBillApply.value
    }
})



const save = () => {
    const data = [
        {
            key: 'sys.fund.bill.apply',
            value: billApply.value
        }
    ]
    adjustConfig(data).then(() => {
        ElMessage.success('保存成功');
        useConfigStore().configMap = {}
    })

}
</script>