<template>
  <div v-loading="loading">
    <!-- 表单区域 -->
    <div class="form-controls">
      <el-form :inline="true">
        <el-form-item label="年度">
          <el-date-picker v-model="selectedYear" type="year" value-format="YYYY" clearable
            style="width: 120px"></el-date-picker>
        </el-form-item>
        <el-form-item label="">
          数量：{{ tableData.filter(t => t.ifImportant == '1').length ?? 0 }}
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" border>
      <el-table-column type="index" label="序号" width="80" align="center" />
      <el-table-column prop="orgName" label="单位名称" align="center" />
      <el-table-column prop="seqNo" label="编号" align="center" />
      <el-table-column prop="ifImportant" label="重点单位" align="center">
        <template #default="{ row }">
          <el-switch v-model="row.ifImportant" active-value="1" inactive-value="0"
            @change="handleImportantChange(row)" />
        </template>
      </el-table-column>
      <el-table-column label="高质量项目" align="center">
        <template #default="{ row }">
          <el-select :model-value="row.projectHqdps?.at(0)?.projId" @change="val => handleProjChange(row, val)"
            clearable>
            <el-option v-for="item in row.projectIds" :label="item.projName" :value="item.projId"></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="seqNo" label="高质量经办人" align="center">
        <template #default="{ row }">
          <el-select :model-value="row.projectHqdps?.at(0)?.userId" @change="val => handleUserChange(row, val)"
            clearable>
            <el-option v-for="item in row.fundImportantDeptUsers" :label="item.userName"
              :value="item.userId"></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { getImportantorgList, hqdpswitch, hqdpUserswitch, importswitch } from '@/api/fund/importantorg'
import { dayjs, ElMessage } from 'element-plus'


const selectedYear = ref(dayjs().format('YYYY'))

const loading = ref(false)
const tableData = ref<FundImportantOrgVo[]>([])
const handleSearch = () => {
  loading.value = true
  getImportantorgList(selectedYear.value).then(res => {
    tableData.value = res.data ?? []
  }).finally(() => {
    loading.value = false
  })
}

watch(() => selectedYear.value, () => {
  handleSearch()
}, { immediate: true })

const handleProjChange = (row: FundImportantOrgVo, val: string) => {
  if (row.orgId) {
    hqdpswitch(selectedYear.value, row.orgId, val ?? '').then(res => {
      ElMessage.success('设置成功')
      handleSearch()
    })
  } else {
    ElMessage.error('未查询到机构')
  }

}

const handleUserChange = (row: FundImportantOrgVo, val: string) => {
  if (row.orgId) {
    hqdpUserswitch(selectedYear.value, row.orgId, val ?? '').then(res => {
      ElMessage.success('设置成功')
      handleSearch()
    })
  } else {
    ElMessage.error('未查询到机构')
  }

}


const handleImportantChange = (row: FundImportantOrgVo) => {
  if (row.orgId) {
    importswitch(selectedYear.value, row.orgId, row.ifImportant ?? '1').then(res => {
      ElMessage.success('设置成功')
      handleSearch()
    })
  } else {
    ElMessage.error('未查询到机构')
  }

}

</script>
