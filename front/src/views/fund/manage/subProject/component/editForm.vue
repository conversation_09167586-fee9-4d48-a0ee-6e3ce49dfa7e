<template>
    <div>
        <el-descriptions :column="2" border label-width="20%">
            <el-descriptions-item label="项目名称" :span="2" width="60%">
                {{ form.name }}
            </el-descriptions-item>
            <el-descriptions-item label="项目单位" width="30%">
                {{ form.unitName }}
            </el-descriptions-item>
            <el-descriptions-item label="项目类型" width="30%">
                {{ form.typeName }}
            </el-descriptions-item>
            <el-descriptions-item label="项目用途" :span="2">
                {{jsonStrParseToJson<INameValue[]>(form.purpose)?.map((item: any) => item.name).join('、')}}
            </el-descriptions-item>
            <el-descriptions-item label="项目金额">
                {{ numberFormat(BigNumber(form.amount ?? 0).multipliedBy(10000).toNumber(), 2) }}
            </el-descriptions-item>
            <el-descriptions-item label="2025预算金额">
                <template #label>
                    <div class="flex items-center">
                        <el-dropdown @command="handleCommand">
                            <el-link type="primary">
                                {{ selectYearBudeget.year }}预算金额<el-icon class="el-icon--right"><arrow-down /></el-icon>
                            </el-link>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item v-for="item in form.projectPlanBudgetVos" :command="item">{{
                                        item.year }}预算金额</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>

                </template>
                {{ numberFormat(BigNumber(selectYearBudeget.bugetAmount ?? 0).multipliedBy(10000).toNumber(), 2) }}
            </el-descriptions-item>
        </el-descriptions>

        <el-table :data="dataList" border height="300" class="mt-4">
            <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
            <el-table-column label="子项目名称" align="center">
                <template #default="{ row }">
                    <el-input v-model="row.name" maxlength="200"></el-input>
                </template>
            </el-table-column>
            <el-table-column label="项目（合同）金额" align="center">
                <template #default="{ row }">
                    <el-input-number v-model="row.amount" :min="0" controls-position="right" class="!w-full" />
                </template>
            </el-table-column>
            <el-table-column label="当年预算" align="center">
                <template #default="{ row }">
                    <el-input-number v-model="row.bugetAmount" :min="0" controls-position="right" class="!w-full" />
                </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
                <template #default="{ row }">
                    <el-button type="danger" link @click="handleRemoveSubProject(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 操作按钮 -->
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="success" @click="handleAddSubProject">添加子项目</el-button>
            <el-button type="primary" :loading="loading" @click="handleSubmit">保存</el-button>
            <!-- <el-button @click="emit('cancel')">关闭窗口</el-button> -->
        </div>
    </div>
</template>

<script setup lang="ts">
import { getSubProjectDetailByProjId, getSubProjectListByProjId, removeSubProject, removeSubProjectCheck, saveSubProject } from '@/api/fund/projectChildren';
import { jsonStrParseToJson, numberFormat } from '@/utils/common';
import { BigNumber } from 'bignumber.js';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { groupBy } from 'lodash';


const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    formId: string,
    year: number
}>()

const dataList = ref<ProjectChildrenBudgetVo[]>([])
const form = reactive<ProjectChildrenVo>({})

const selectYearBudeget = reactive<ProjectChildrenBudgetVo>({})
const budgetList = ref<ProjectChildrenBudgetVo[]>([])
const getDetail = async () => {
    if (!selectYearBudeget.year) {
        selectYearBudeget.year = props.year
    }
    const res = await getSubProjectDetailByProjId(props.formId, selectYearBudeget.year)
    Object.assign(form, res.data)

    selectYearBudeget.bugetAmount = form.projectPlanBudgetVos?.find(t => t.year == props.year)?.bugetAmount
}
await getDetail()
const yearGroup = groupBy(budgetList.value ?? [], 'year')
dataList.value = Object.values(yearGroup).at(0) ?? []
watch(() => selectYearBudeget.year, async () => {
    const res = await getSubProjectListByProjId(props.formId, selectYearBudeget.year!)
    dataList.value = res.data ?? []
}, { immediate: true })


const handleCommand = (item: ProjectChildrenBudgetVo) => {
    selectYearBudeget.year = item.year
    selectYearBudeget.bugetAmount = item.bugetAmount
}

const handleAddSubProject = () => {
    dataList.value.push({
        projId: props.formId,
        year: form.year
    })
}
const handleRemoveSubProject = (row: any) => {
    ElMessageBox.confirm('将会同步删除历年子项目数据，确定要删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        if (row.id) {
            await removeSubProjectCheck(row.id);
            await removeSubProject(row.id);

            getDetail().then(() => {
                ElMessage.success('删除成功')
                dataList.value.splice(dataList.value.findIndex(t => t == row), 1)
            })

        } else {
            dataList.value.splice(dataList.value.findIndex(t => t == row), 1)
        }
    })

}


const loading = ref(false)
const formRef = ref<FormInstance>()
const handleSubmit = async () => {
    dataList.value.forEach(item => {
        item.year = selectYearBudeget.year
    })
    saveSubProject(dataList.value).then(res => {
        ElMessage.success('保存成功')

        getDetail()
        // emit('close')

    })
}
</script>