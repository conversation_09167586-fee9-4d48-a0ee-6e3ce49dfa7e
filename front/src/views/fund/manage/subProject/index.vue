<template>
    <div class="app-container">
        <!-- 搜索区域 -->
        <el-form label-width="auto" :inline="true">
            <el-form-item label="项目名称">
                <el-input v-model="searchForm.name" placeholder="请输入项目名称" clearable class="w-40" />
            </el-form-item>
            <el-form-item label="项目类别">
                <ProjectTypeSelect v-model="searchForm.typeId" placeholder="请选择项目类别"  class="!w-40" clearable></ProjectTypeSelect>
            </el-form-item>
            <el-form-item label="项目用途">
                <el-select v-model="searchForm.purposeIds" multiple collapse-tags placeholder="请选择项目用途" clearable class="w-40">
                    <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.orgId" clearable class="!w-40"></DeptSelect>
            </el-form-item>
            <el-form-item label="执行年度">
                <el-date-picker v-model="searchForm.year" type="year" value-format="YYYY" placeholder="选择日期"
                    class="!w-40"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-checkbox v-model="searchForm.ifcontain" true-value="1" false-value="0" class="ml-2">含有子项目</el-checkbox>
            </el-form-item>
        </el-form>

        <div v-loading="loading">
            <!-- 表格区域 -->
            <el-table :data="tableData" border>
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="项目名称" header-align="center" align="left" min-width="300px" fixed="left" />
                <el-table-column prop="typeName" label="项目类型" align="center" width="150" />
                <el-table-column prop="purpose" label="项目用途" align="center" width="200">
                    <template #default="{ row }">
                        <span>{{ jsonStrParseToJson<INameValue[]>(row.purpose)?.map((item: any) => item.name).join('、') }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="amount" label="项目金额" header-align="center" align="right" width="150"
                    :formatter="(row: any) => numberFormat(BigNumber(row.amount).multipliedBy(10000).toNumber(), 2)" />
                <el-table-column prop="unitName" label="项目单位" align="center" width="150" />
                <el-table-column prop="subprojcount" label="子项目数量" align="center" width="130" />
                <el-table-column label="操作" width="100" align="center" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link icon="setting" @click="handleEdit(row)">设置</el-button>

                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>

        <el-dialog title="子项目设置" v-model="subProjectSettingShow" width="800px" :close-on-click-modal="false"
            destroy-on-close @close="handleSearch()">
            <EditForm :form-id="currentProjectId!" :year="parseInt(searchForm.year!)"></EditForm>
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="tsx">
import { dayjs, ElMessage, ElMessageBox, TableColumnCtx } from 'element-plus'
import { dateFormat, jsonStrParseToJson, numberFormat } from '@/utils/common'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { EBillStatus } from '@/utils/constants'
import EditForm from './component/editForm.vue';
import { getSubProjectList } from '@/api/fund/projectChildren';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import { BigNumber } from 'bignumber.js';


const { proxy } = getCurrentInstance() as { proxy: any };
const {project_purpose, bill_status } = proxy.useDict( "project_purpose", 'bill_status');

const searchForm = reactive<ProjectChildrenConditionVo>({
    year: String(dayjs().year()),
})

const page = reactive({
    pageNum: 1,
    pageSize: 15
})

const total = ref(0)
const tableData = ref<ProjectChildrenVo[]>([])
const tableAmount = ref<{ totalApplyAmount: number, totalCheckAmount: number }>()

const loading = ref(false)
const handleSearch = () => {
    loading.value = true
    Promise.all([
        getSubProjectList(searchForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        // getPayBillListAmount(searchForm).then(res => {
        //     tableAmount.value = res.data
        // })
    ]).finally(() => {
        loading.value = false
    })

}
watch(() => searchForm.ifcontain, handleSearch, {immediate: true})



const currentProjectId = ref<string>()
const subProjectSettingShow = ref(false)
const handleEdit = (row: FundPaybillVo) => {
    currentProjectId.value = row.id
    subProjectSettingShow.value = true
}


interface SummaryMethodProps<T = FundPaybillVo> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = (
                <div class="text-left">
                    {`合计`}
                </div>
            )
        }
        if (column.property == 'applyAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalApplyAmount ?? 0, 2)
        }
        if (column.property == 'checkAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalCheckAmount ?? 0, 2)
        }

    })
    return sums as any
}
</script>
