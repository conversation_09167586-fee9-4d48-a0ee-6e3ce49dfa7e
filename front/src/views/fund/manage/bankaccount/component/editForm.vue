<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true" label-position="top">
        <el-descriptions :column="1" border label-width="25%">
            <!-- 开户银行（必填） -->
            <el-descriptions-item :span="2">
                <template #label>
                    开户银行 <span class="text-red">*</span>
                </template>
                <el-form-item prop="bankName" class="!mb-0">
                    <el-select v-model="form.bankName" filterable remote reserve-keyword placeholder="请输入开户银行"
                        remote-show-suffix :remote-method="remoteMethod" :loading="bankSelectLoading"
                         @change="handleBankChange">
                        <el-option v-for="item in bankOptions" :label="item.name" :value="item.id!" />
                    </el-select>
                </el-form-item>
            </el-descriptions-item>

            <!-- 开户网点代码（可留空） -->
            <el-descriptions-item :span="2">
                <template #label>
                    开户网点代码 <span class="text-red">*</span>
                </template>
                <el-form-item prop="stationCode" class="!mb-0">
                    <el-input v-model="form.stationCode" placeholder="请输入开户网点代码" maxlength="50" />
                </el-form-item>
            </el-descriptions-item>

            <!-- 户名（必填） -->
            <el-descriptions-item :span="1">
                <template #label>
                    户名 <span class="text-red">*</span>
                </template>
                <el-form-item prop="name" class="!mb-0">
                    <el-input v-model="form.name" placeholder="请输入户名" maxlength="200" />
                </el-form-item>
            </el-descriptions-item>

            <!-- 户(卡)号（必填） -->
            <el-descriptions-item :span="1">
                <template #label>
                    户（卡）号 <span class="text-red">*</span>
                </template>
                <el-form-item prop="cardNumber" class="!mb-0">
                    <el-input v-model="form.cardNumber" placeholder="请输入账号或卡号" maxlength="200" />
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="1">
                <template #label>
                    账号类型
                </template>
                <el-form-item prop="typeName" class="!mb-0">
                    <el-radio-group v-model="form.typeName">
                        <el-radio label="机构">机构</el-radio>
                        <el-radio label="个人">个人</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-descriptions-item>

        </el-descriptions>
    </el-form>
    <!-- 操作按钮 -->
    <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
        <el-button type="primary" :loading="loading" @click="submitForm">保存</el-button>
        <el-button @click="emit('cancel')">关闭窗口</el-button>
    </div>
</template>

<script setup lang="ts">
import { type FormRules, type FormInstance, ElMessage } from 'element-plus'
import { getBankStationList, getBankAccountDetail, addBankAccount, editBankAccount } from '@/api/fund/bankAccount'
import { pa } from 'element-plus/es/locale'

const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
  formId: string
}>()

const formRef = ref<FormInstance>()
const form = reactive<FundBankAccount>({
    typeName: '机构'
} as any)

if (props.formId) {
    getBankAccountDetail(props.formId).then(res => {
        Object.assign(form, res.data)
    })
}

const rules: FormRules = {
    bankName: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
    name: [{ required: true, message: '请输入户名', trigger: 'blur' }],
    cardNumber: [{ required: true, message: '请输入账号或卡号', trigger: 'blur' }],
    stationCode: [{ required: true, message: '请输入开户网点代码', trigger: 'blur' }],
}
const loading = ref(false)
const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            let save = addBankAccount
            if (props.formId) { 
                save = editBankAccount
            }
            loading.value = true
            save(form).then(() => {
                ElMessage.success('保存成功');
                emit('close');
            }).finally(() => {
                loading.value = false
            });
        }
    })
}

const bankOptions = ref<FundBankStation[]>([])
const bankSelectLoading = ref(false)
const remoteMethod = (query: string) => {
    if (query) {
        bankSelectLoading.value = true
        getBankStationList({
            name: query,
        }, {
            pageNum: 1,
            pageSize: 10
        }).then(res => {
            bankOptions.value = res.rows ?? []
        }).finally(() => {
            bankSelectLoading.value = false
        })
    } else {
        bankOptions.value = []
    }
}
const handleBankChange = (val: string) => {
    const bank = bankOptions.value.find(item => item.id === val)
    if (bank) {
        form.stationCode = bank?.id
        form.bankName = bank?.name ?? ''
    }
}
</script>
