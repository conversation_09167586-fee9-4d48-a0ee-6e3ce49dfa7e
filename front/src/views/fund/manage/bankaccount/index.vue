<template>
    <div class="app-container">
        <!-- 搜索区域 -->
        <el-form :inline="true" :model="searchForm">
            <el-form-item label="管理单位">
                <DeptSelect v-model="searchForm.orgId" clearable class="!w-50"></DeptSelect>
            </el-form-item>
            <el-form-item label="银行名称">
                <el-input v-model="searchForm.bankName" placeholder="请输入银行名称" clearable class="!w-50" />
            </el-form-item>
            <el-form-item label="账户名称/账号">
                <el-input v-model="searchForm.cardNameNumber" placeholder="请输入账户名称或账号" clearable class="!w-50" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button v-hasPermi="['fund:bankAccount:add']" type="success" @click="handleCreate">新增收款账户</el-button>
                <my-file-upload v-hasPermi="['fund:bankAccount:import']" class="ml-3" accept=".xlsx,.xls"
                    action="/fund/bankAccount/importData" :before-upload="handleBeforeUpload"
                    :on-error="handleUploadError" @upload-success="handleUploadSuccess">
                    <el-button type="info">网点导入</el-button>
                </my-file-upload>

                <el-checkbox v-model="searchForm.isOpenAccount" true-value="1" false-value="0" class="ml-5">无开户网点代码</el-checkbox>
            </el-form-item>
        </el-form>

        <div v-loading="loading">
            <!-- 表格区域 -->
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column type="index" label="序号" width="60" align="center">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="orgName" label="帐户管理单位" width="150" align="center" />
                <el-table-column prop="bankName" label="开户银行" align="center" />
                <el-table-column prop="name" label="开户名称" align="center" />
                <el-table-column prop="cardNumber" label="开户卡号" width="200" align="center" />
                <el-table-column prop="stationCode" label="开户网点代码" width="150" align="center" />
                <el-table-column label="操作" width="150" align="center">
                    <template #default="{ row }">
                        <template v-if="userStore.deptId == 100 || userStore.deptId == row.orgId">
                            <el-button v-hasPermi="['fund:bankAccount:edit']" type="primary" icon="edit" link
                                @click="editRow(row)">编辑</el-button>
                            <el-button v-hasPermi="['fund:bankAccount:remove']" type="danger" icon="delete" link
                                @click="deleteRow(row)">删除</el-button>
                        </template>

                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页控件 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>

        <!-- 编辑对话框 -->
        <el-dialog title="账户编辑" v-model="formEditShow" width="600px" :close-on-click-modal="false" destroy-on-close>
            <EditForm :form-id="currentFormId" @cancel="formEditShow = false"
                @close="formEditShow = false; handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { getBankAccountList, removeBankAccount } from '@/api/fund/bankAccount'
import EditForm from './component/editForm.vue'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
// @ts-ignore
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

const page = reactive({
    pageNum: 1,
    pageSize: 15
})
const searchForm = reactive<FundBankAccountQuery>({
    isSpecial: '0'
})
const loading = ref(false)
const total = ref(0)
const tableData = ref<FundBankAccountVo[]>([])
const handleSearch = () => {
    loading.value = true
    getBankAccountList(searchForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false

    })
}
handleSearch()

watch(() => searchForm.isOpenAccount, handleSearch)

const formEditShow = ref(false)
const handleCreate = () => {
    currentFormId.value = ''
    formEditShow.value = true
}

const currentFormId = ref('')
const editRow = (row: any) => {
    currentFormId.value = row.id
    formEditShow.value = true
}

const deleteRow = (row: any) => {
    ElMessageBox.confirm('是否确认删除?', '提示', {})
        .then(() => {
            removeBankAccount([row.id]).then(res => {
                ElMessage.success('删除成功');
                handleSearch()
            })
        })
}

let loadingInstance: any = null
const handleBeforeUpload = () => {
    loadingInstance = ElLoading.service({
        text: '数据包导入中...',
        fullscreen: true
    })
}
const handleUploadError = () => {
    ElMessage.error('数据包导入失败')
    loadingInstance?.close()
}
const handleUploadSuccess = () => {
    ElMessage.success('数据包导入成功')
    loadingInstance?.close()
}
</script>
