<template>
    <el-table :data="auditNodeList" border height="500px" v-loading="loading">
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column prop="nodeName" label="流程节点" align="center" width="150"></el-table-column>
        <el-table-column prop="operatorName" label="审批人" align="center" width="120"></el-table-column>
        <el-table-column prop="operateTime" label="审批时间" align="center" width="120"
            :formatter="(row: any) => dateFormat(row.operateTime)"></el-table-column>
        <el-table-column label="审批结果" align="center" width="100">
            <template #default="{ row }">
                <span v-if="row.operateResult == EAuditType.pass">{{
                    getAuditTypeName(EAuditType.pass) }}</span>
                <span v-if="row.operateResult == EAuditType.reject">{{
                    getAuditTypeName(EAuditType.reject) }}</span>
            </template>
        </el-table-column>
        <el-table-column prop="operateReason" label="审批意见" align="center"></el-table-column>
    </el-table>
</template>

<script setup lang="ts">
import { getPayBillAuditNodeList } from '@/api/fund/paybillAudit';
import { dateFormat } from '@/utils/common';
import { EAuditType, getAuditTypeName } from '@/utils/constants';



const props = defineProps<{
    billId: string
}>()

const loading = ref(true)
const auditNodeList = ref<FundPaybillNode[]>([])
getPayBillAuditNodeList(props.billId).then(res => {
    auditNodeList.value = res.data?.filter(item => item.isMultiClaim != true) ?? []
    const isMultiClaimList = res.data?.filter(item => item.isMultiClaim)
    if (isMultiClaimList && isMultiClaimList.length > 0) {
        auditNodeList.value.push({
            ...isMultiClaimList.at(0),
            operatorName: isMultiClaimList.map(t => t.operatorName).join('，')
        })
    }
}).finally(() => {
    loading.value = false
})
</script>