<template>
  <div class="monthly-plan">
    <!-- 年度选择 -->
    <div class="flex items-center gap-2">
        <span>年度</span>
        <el-select v-model="year" placeholder="选择年度" class="!w-40">
      <el-option label="2025" value="2025" />
      <el-option label="2024" value="2024" />
    </el-select>
    </div>

    <!-- 标题 -->
    <h3 class="my-2">
      化工区发展资金项目管理及资金支付子系统国产化升级改造月度资金计划
    </h3>

    <!-- 表格 -->
    <el-table :data="tableData" border>
      <el-table-column prop="month" label="月度" width="100" align="center" />
      <el-table-column label="计划金额 (元)" align="center">
        <template #default="scope">
          <el-input v-model="scope.row.plannedAmount" placeholder="请输入金额" />
        </template>
      </el-table-column>
      <el-table-column label="调整后金额 (元)" align="center">
        <template #default="scope">
          <el-input v-model="scope.row.adjustedAmount" placeholder="请输入金额" />
        </template>
      </el-table-column>
    </el-table>

    <!-- 保存按钮 -->
    <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
      <el-button type="primary" @click="save">保存</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">

const emit = defineEmits(['close', 'cancel'])
const year = ref('2025')

const tableData = ref([
  { month: '1月', plannedAmount: 1210500.00, adjustedAmount: '' },
  { month: '2月', plannedAmount: '', adjustedAmount: '' },
  { month: '3月', plannedAmount: '', adjustedAmount: '' },
  { month: '4月', plannedAmount: '', adjustedAmount: '' },
  { month: '5月', plannedAmount: '', adjustedAmount: '' },
  { month: '6月', plannedAmount: '', adjustedAmount: '' },
  { month: '7月', plannedAmount: '', adjustedAmount: '' },
  { month: '8月', plannedAmount: '', adjustedAmount: '' },
  { month: '9月', plannedAmount: '', adjustedAmount: '' },
  { month: '10月', plannedAmount: '', adjustedAmount: '' },
  { month: '11月', plannedAmount: '', adjustedAmount: '' },
  { month: '12月', plannedAmount: '', adjustedAmount: '' }
])

const save = () => {
  console.log('保存数据：', tableData.value)
  // 可添加后端提交逻辑
}
</script>