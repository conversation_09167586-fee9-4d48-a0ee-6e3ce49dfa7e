<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :inline="true" :model="form" label-width="auto">
      <el-form-item label="项目名称">
        <el-input v-model="form.projectName" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="考试主体">
        <el-select v-model="form.examSubject" placeholder="请选择考试主体" class="w-40">
          <el-option label="主体A" value="A" />
          <el-option label="主体B" value="B" />
        </el-select>
      </el-form-item>
      <el-form-item label="年份">
        <el-input v-model="form.year" placeholder="2025" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">查询</el-button>
        <el-button type="danger" @click="handleExport">数据导出</el-button>
      </el-form-item>
      <el-form-item>
        <!-- 状态筛选 -->
        <el-radio-group v-model="form.status" class="status-radio">
          <el-radio-button label="全部" />
          <el-radio-button label="未结果" />
          <el-radio-button label="已结果" />
          <el-radio-button label="全年资金计划为0" />
        </el-radio-group>
      </el-form-item>
    </el-form>


    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="填报" name="1">
        <!-- 表格数据 -->
        <el-table :data="tableData" border>
          <el-table-column prop="index" label="序号" width="60" align="center">
            <template #default="{ row, $index }">
              <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="projectName" label="项目名称" align="center" />
          <el-table-column prop="examSubject" label="考核主体" align="center" />
          <el-table-column prop="projectDate" label="项目时间" align="center" />
          <el-table-column prop="annualBudget" label="全年资金计划" align="center" />
          <el-table-column prop="annualBudget" label="月度资金计划" align="center" />
          <el-table-column prop="paidDate" label="操作" align="center" width="150">
            <template #default="{ row }">
              <el-button type="primary" link @click="editFormShow = true">填制</el-button>
              <el-button type="success" link>查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
          @pagination="handleSearch" />
      </el-tab-pane>
      <el-tab-pane label="年度一览" name="2">
        <!-- 表格数据 -->
        <el-table :data="tableData" border>
          <el-table-column prop="index" label="序号" width="60" align="center">
            <template #default="{ row, $index }">
              <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="projectName" label="项目名称" align="center" />
          <el-table-column prop="examSubject" label="考核主体" align="center" />
          <el-table-column prop="projectDate" label="项目期限" align="center" />
          <el-table-column prop="annualBudget" label="全年资金计划" align="center" />
          <el-table-column prop="annualBudget" label="月度资金计划" align="center" />
          <el-table-column v-for="month in 12" :key="month" :prop="`month${month}`" :label="`${month}月`"
            align="center" />
        </el-table>
        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
          @pagination="handleSearch" />
      </el-tab-pane>
    </el-tabs>

    <el-dialog title="月度资金计划" v-model="editFormShow" width="800px" :close-on-click-modal="false" destroy-on-close>
      <EditForm />
      <footer class="el-dialog__footer h-10"></footer>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import EditForm from './component/editForm.vue'
import { genSnowId } from '@/utils/common'

const activeTab = ref('1')
const form = ref({
  projectName: '',
  examSubject: '',
  year: '',
  status: '全部'
})
const page = reactive({
  pageNum: 1,
  pageSize: 10
})

const total = ref(0)
const tableData = ref([
  // 示例数据
  {
    index: 1,
    projectName: '项目A',
    examSubject: '主体A',
    projectDate: '2025-03-15',
    annualBudget: '¥100,000',
    month1: '¥8,000',
    month2: '¥9,000',
    month3: '¥10,000',
    month4: '¥8,000',
    month5: '¥9,000',
    month6: '¥10,000',
    month7: '¥8,000',
    month8: '¥9,000',
    month9: '¥10,000',
    month10: '¥8,000',
    month11: '¥9,000',
    month12: '¥12,000'
  }
])

const handleSearch = () => {
  console.log('查询条件：', form.value)
  // 查询逻辑
}

const handleExport = () => {
  console.log('导出数据')
  // 导出逻辑
}

const editFormShow = ref(false)
</script>

<style lang="css" scoped>
:deep(.el-tabs__header) {
  margin-bottom: 0;
}
</style>