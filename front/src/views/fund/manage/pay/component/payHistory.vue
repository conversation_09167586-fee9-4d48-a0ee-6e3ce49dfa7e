<template>

  <div class="relative">
    <div class="absolute right-1 top-1 z-10">
      <el-date-picker v-model="date" type="month" value-format="YYYY-MM-DD" format="YYYY-MM" placeholder="选择日期" />
    </div>
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 导入历史 -->
      <el-tab-pane label="导入历史" name="import">
        <el-table :data="importData" height="500px" border>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="type" label="操作类型" align="center"  width="80" :formatter="() => '导入'" />
          <el-table-column prop="createName" label="操作人" align="center" width="80" />
          <el-table-column prop="createTime" label="操作时间" align="center" width="100"
            :formatter="(row: any) => dateFormat(row.createTime, 'YYYY-MM-DD HH:mm:ss')" />
          <el-table-column prop="remark" label="描述" align="center" />
          <el-table-column label="下载" align="center" width="100">
            <template #default="scope">
              <el-link type="primary" @click="download(scope.row)">下载Excel</el-link>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据" />
          </template>
        </el-table>
      </el-tab-pane>
      <!-- 导出历史 -->
      <el-tab-pane label="导出历史" name="export">
        <el-table :data="exportData" height="500px" border>
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="type" label="操作类型" align="center" width="80" :formatter="() => '导出'" />
          <el-table-column prop="createName" label="操作人" align="center" width="80" />
          <el-table-column prop="createTime" label="操作时间" align="center" width="100"
            :formatter="(row: any) => dateFormat(row.createTime, 'YYYY-MM-DD HH:mm:ss')" />
          <el-table-column prop="remark" label="描述" align="center" />
          <el-table-column label="下载" align="center" width="100">
            <template #default="scope">
              <el-link type="primary" @click="download(scope.row)">下载Excel</el-link>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据" />
          </template>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { getAppropriationLogList } from '@/api/fund/appropriation'
import { downloadFile } from '@/api/lib/project/file'
import { dateFormat } from '@/utils/common'
import { dayjs } from 'element-plus'
import saveAs from 'file-saver'
import { ref } from 'vue'

const activeTab = ref('import')

const date = ref(dayjs().format('YYYY-MM-DD'))
const importData = ref<FundBankIe[]>([])
const exportData = ref<FundBankIe[]>([])


watch(() => date.value, () => {
  getAppropriationLogList(1, date.value).then(res => {
    importData.value = res.data ?? []
  })
  getAppropriationLogList(2, date.value).then(res => {
    exportData.value = res.data ?? []
  })
}, { immediate: true })

const download = (row: any) => {
  downloadFile(row.fileId!).then(res => {
    saveAs(new Blob([res]), row.fileName ?? '文件')
  })
}
</script>

<style scoped>
:deep(.el-tabs__content) {
  padding: 0;
}
</style>
