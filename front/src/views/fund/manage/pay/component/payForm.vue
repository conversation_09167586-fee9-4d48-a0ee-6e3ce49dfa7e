<template>
    <el-form ref="formRef" :model="form" :rules="rules" :inline-message="true">
        <el-descriptions :column="1" label-width="30%" border>
            <el-descriptions-item label="开户行" width="80%">
                {{ formData.bankName }}
            </el-descriptions-item>
            <el-descriptions-item label="银行账号">
                {{ formData.accountNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="收款人（单位）">
                {{ formData.accountName }}
            </el-descriptions-item>
            <el-descriptions-item label="拨付金额">
                <template #label>
                    拨付金额 <span class="text-red">*</span>
                </template>
                <el-form-item prop="payAmount" class="!mb-0">
                    {{ numberFormat(form.payAmount, 2) }}
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="拨付日期">
                <template #label>
                    拨付日期 <span class="text-red">*</span>
                </template>
                <el-form-item prop="payDate" class="!mb-0">
                    <el-date-picker v-model="form.payDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择拨付日期"
                        class="!w-60" />
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="凭证号">
                <el-form-item prop="payVoucher" class="!mb-0">
                    <el-input v-model="form.payVoucher" placeholder="请输入凭证号" class="!w-60" maxlength="200" />
                </el-form-item>

            </el-descriptions-item>
        </el-descriptions>
    </el-form>

    <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
        <el-button v-if="formData.status == 1" type="primary" :loading="loading" @click="handleSave">保存</el-button>
        <el-button v-else type="primary" :loading="loading" @click="handlePay">确认拨付</el-button>
        <el-button @click="emit('cancel')">关闭窗口</el-button>
    </div>
</template>

<script setup lang="ts">
import { editAppropriationInfo, updateAppropriation } from '@/api/fund/appropriation';
import { numberFormat } from '@/utils/common';
import { ElMessage, FormInstance, FormRules } from 'element-plus';


const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    formData: FundAppropriationInput
}>()

const form = reactive({
    id: props.formData.id,
    payAmount: props.formData.amount,
    payDate: props.formData.payDate,
    payVoucher: props.formData.payVoucher
})
const rules = reactive<FormRules<typeof form>>({
    payAmount: [{ required: true, message: "请输入拨付金额", trigger: "blur" }],
    payDate: [{ required: true, message: "请输入拨付日期", trigger: "blur" }],
});

const loading = ref(false)
const formRef = ref<FormInstance>()
const handleSave = () => {
    formRef.value?.validate().then(() => {
        loading.value = true
        editAppropriationInfo({
            id: form.id,
            payDate: form.payDate,
            payVoucher: form.payVoucher
        }).then(res => {
            ElMessage.success('保存成功')
            emit('close')
        }).finally(() => {
            loading.value = false
        })

    })
}

const handlePay = () => {
    formRef.value?.validate().then(() => {
        loading.value = true
        updateAppropriation(form).then(res => {
            ElMessage.success('保存成功')
            emit('close')
        }).finally(() => {
            loading.value = false
        })

    })
}

</script>