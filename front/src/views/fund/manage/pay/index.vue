<template>
    <div class="app-container">
        <!-- 查询表单 -->
        <el-form :model="searchForm" label-width="auto" :inline="true">
            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="w-60" />
            </el-form-item>
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.applyOrgid" clearable class="!w-60"></DeptSelect>
            </el-form-item>
            <el-form-item label="项目管理单位">
                <el-select v-model="searchForm.mgrOrgid" clearable class="!w-60">
                    <el-option v-for="item in projectManagerDeptList" :label="item.deptName" :value="item.deptId" />
                </el-select>
            </el-form-item>
            <el-form-item label="收款账号">
                <el-input v-model="searchForm.accountName" placeholder="请输入收款账号" clearable class="w-60" />
            </el-form-item>
            <el-form-item label="申请日期">
                <DateRangePicker v-model:begin-date="searchForm.applyDateBegin"
                    v-model:end-date="searchForm.applyDateEnd" type="daterange" start-placeholder="开始日期"
                    end-placeholder="结束日期" range-separator="至" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    class="!w-60" clearable>
                </DateRangePicker>
            </el-form-item>
            <el-form-item label="拨付状态">
                <el-select v-model="searchForm.payStatus" placeholder="请选择" class="w-60">
                    <el-option v-for="item in pay_status" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="拨付日期">
                <DateRangePicker v-model:begin-date="searchForm.payDateBegin" v-model:end-date="searchForm.payDateEnd"
                    type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" range-separator="至"
                    format="YYYY/MM/DD" value-format="YYYY-MM-DD" class="!w-60" clearable>
                </DateRangePicker>
            </el-form-item>
            <el-form-item label="拨款凭证">
                <el-input v-model="searchForm.payVoucher" placeholder="请输入拨款凭证" clearable class="w-60" />
            </el-form-item>
            <el-form-item label="拨款单编号">
                <el-input v-model="searchForm.serialNumber" placeholder="请输入拨款单编号" clearable class="w-60" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleExport">数据导出</el-button>
            </el-form-item>
        </el-form>

        <!-- 查询结果 -->
        <div>
            <div class="mb-2">
                <div class="flex gap-3">
                    <el-button type="primary" @click="handleExportPackage">数据包导出</el-button>
                    <my-file-upload action="/fund/appropriation/import" @upload-success="handleUploadSuccess">
                        <el-button type="primary">数据包导入</el-button>
                    </my-file-upload>
                    <el-button @click="handleViewHistory">导入导出历史</el-button>
                </div>
            </div>
            <div v-loading="loading">
                <el-table ref="tableRef" :data="tableData" border show-summary :summary-method="getSummaries"
                    @select="handleSelect" @select-all="handleSelectAll" @cell-click="handleCellClick">
                    <el-table-column type="selection" width="60" align="center" fixed="left" :selectable="(row) => row.status == 0" />
                    <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
                        <template #default="{ row, $index }">
                            <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="serialNumber" label="拨款单编号" align="center" width="120" fixed="left">
                        <template #default="{ row }">
                            <el-link type="primary" @click="handleView(row)">{{ row.serialNumber }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="projName" label="所属项目" align="center" fixed="left" min-width="300px">
                        <template #default="{ row }">
                            <div class="text-left">
                                {{ row.projName }}
                            </div>
                            <div class="flex gap-1">
                                <el-tag v-if="row.hqdpId" type="success">高质量扶持</el-tag>
                                <el-tag v-if="row.hasFundPurchase" type="warning">政府采购</el-tag>

                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="accountName" label="收款单位" align="center" min-width="200px" />
                    <el-table-column prop="amount" label="核准金额" header-align="center" align="right" width="120"
                        :formatter="(row: any) => numberFormat(row.amount, 2)" />
                    <el-table-column prop="applyDate" label="申请日期" align="center" width="150" />
                    <el-table-column prop="payAmount" label="拨款金额" header-align="center" align="right" width="120"
                        :formatter="(row: any) => numberFormat(row.payAmount, 2)" />
                    <el-table-column prop="payDate" label="拨款日期" align="center" width="150" />
                    <el-table-column prop="payVoucher" label="财务凭证" align="center" width="150">
                        <template #default="{ row }">
                            <el-link @click="handlePay(row)">{{ row.payVoucher }}</el-link>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="拨付状态" align="center" width="100">
                        <template #default="{ row }">
                            <dict-tag :options="pay_status" :value="row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="80" fixed="right">
                        <template #default="{ row }">
                            <el-button type="primary" link :disabled="row.status == 1"
                                @click="handlePay(row)">拨付</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <!-- 分页 -->
                <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                    @pagination="handleSearch" />
            </div>

        </div>

        <el-dialog title="资金拨付" v-model="payFormShow" width="500px" :close-on-click-modal="false" destroy-on-close>
            <PayForm :form-data="currentRow!" @cancel="payFormShow = false"
                @close="payFormShow = false; handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog title="拨款单查看" v-model="formViewShow" width="1200px" :close-on-click-modal="false" destroy-on-close>
            <payBillFormView :form-id="currentBillId!" :show-audit="false" @cancel="formViewShow = false"
                @close="formViewShow = false;" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog title="导入和导出历史" v-model="payHistoryShow" width="800px" :close-on-click-modal="false"
            destroy-on-close>
            <PayHistory />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>

</template>

<script setup lang="tsx">
import { dayjs, ElMessage, TableColumnCtx, TableInstance } from 'element-plus'
import PayForm from './component/payForm.vue'
import PayHistory from './component/payHistory.vue'
import { getAppropriationList, getAppropriationListCount } from '@/api/fund/appropriation'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
import { projectManagerDeptList } from '@/utils/constants'
import { numberFormat } from '@/utils/common'
import payBillFormView from '../apply/component/payBillFormView.vue'
import { el } from 'element-plus/es/locale'
import { Row } from 'vxe-pc-ui'

const { proxy } = getCurrentInstance() as { proxy: any };
const { pay_status } = proxy.useDict("pay_status",);


const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const searchForm = reactive<FundAppropriationInputQuery>({
    payStatus: '0'
} as any)
const tableRef = ref<TableInstance>()
const total = ref(0)
const tableData = ref<FundAppropriationInput[]>([])
const tableAmount = ref<{ amountCount: number, payAmountCount: number }>()
const loading = ref(false)
const tableSelectIds = ref<Set<string>>(new Set())

const handleSearch = () => {
    loading.value = true
    Promise.all([
        getAppropriationList(searchForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getAppropriationListCount(searchForm).then(res => {
            tableAmount.value = res.data
        })
    ]).finally(() => {
        nextTick(() => {
            tableData.value.forEach(item => {
                tableRef.value?.toggleRowSelection(item, tableSelectIds.value.has(item.id!))
            })
        })
        loading.value = false
    })
}
handleSearch()

const handleSelect = (selection: FundAppropriationInput[], row: FundAppropriationInput) => {
    // 单行选择/取消
    if (row.id) {
        if (selection.some(item => item.id === row.id)) {
            tableSelectIds.value.add(row.id)
        } else {
            tableSelectIds.value.delete(row.id)
        }
    }
}

const handleSelectAll = (selection: FundAppropriationInput[]) => {
    // 当前页全选/取消
    tableData.value.forEach(item => {
        if (item.id) {
            if (selection.some(sel => sel.id === item.id)) {
                tableSelectIds.value.add(item.id)
            } else {
                tableSelectIds.value.delete(item.id)
            }
        }
    })
}

const handleCellClick = (row: FundAppropriationInput, column: TableColumnCtx) => {
    if (['payAmount', 'payDate', 'payVoucher',].includes(column.property) && row.status == 1) {
        payFormShow.value = true
        currentRow.value = row
    }
}

const handleExport = () => {
    proxy.download('/fund/appropriation/exportList', {
        ...searchForm
    }, `资金拨付录入${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}

const handleExportPackage = () => {
    proxy.download('/fund/appropriation/export', {
        ids: Array.from(tableSelectIds.value.values())
    }, `package_export_${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}

const handleUploadSuccess = (res: IResult<string>) => {
    if (res.code != 200) {
        ElMessage.error(res.msg)
    } else {
        ElMessage.success('数据包导入成功')
    }
    handleSearch()
}

const currentBillId = ref<string>()
const formViewShow = ref(false)
const handleView = (row: FundAppropriationInput) => {
    if (row.billId) {
        currentBillId.value = row.billId
        formViewShow.value = true
    }

}

const handleViewHistory = () => {
    payHistoryShow.value = true
}

const currentRow = ref<FundAppropriationInput>()
const payFormShow = ref(false)
const handlePay = (row: FundAppropriationInput) => {
    payFormShow.value = true
    currentRow.value = row
}

const payHistoryShow = ref(false)

interface SummaryMethodProps<T = any> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = (
                <div class="text-left">
                    {`合计`}
                </div>
            )
        }
        if (column.property == 'amount') {
            sums[index] = numberFormat(tableAmount.value?.amountCount ?? 0, 2)
        }
        if (column.property == 'payAmount') {
            sums[index] = numberFormat(tableAmount.value?.payAmountCount ?? 0, 2)
        }

    })
    return sums as any
}
</script>
