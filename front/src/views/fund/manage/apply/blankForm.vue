<template>
  <div class="mt-[50px]">
    <PayBillFormEdit formClass="ml-[30px] mb-[50px]" @close="$router.back()"
      bottomClass="py-3 fixed bottom-0 bg-[#f6fbfd]/90 z-100 w-full border-t-solid border-[#dfeaf4] pl-[30px]"></PayBillFormEdit>


  </div>
</template>

<script setup lang="ts" name="Manage/bill/apply/form">
import PayBillFormEdit from './component/payBillFormEdit.vue';

const emit = defineEmits(['close', 'cancel'])
</script>