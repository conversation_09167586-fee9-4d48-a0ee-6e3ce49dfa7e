<template>
    <el-form ref="formRef" :model="form" :rules="formRules" class="flex flex-col gap-3" :inline-message="true">
        <el-descriptions :column="1" label-width="30%" border>
            <el-descriptions-item width="70%">
                <template #label>
                    收款人（单位） <span class="text-red">*</span>
                </template>
                <el-form-item prop="accountName" class="!mb-0">
                    <el-input v-model="form.accountName" maxlength="100"></el-input>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    开户银行 <span class="text-red">*</span>
                </template>
                <el-form-item prop="bankName" class="!mb-0">
                    <div class="grid grid-cols-[60%_1fr] gap-2 w-full">
                        <el-select v-model="form.bankName" filterable remote reserve-keyword placeholder="请输入开户银行"
                            remote-show-suffix :remote-method="remoteMethod" :loading="bankSelectLoading"
                            @change="handleBankChange">
                            <el-option v-for="item in bankOptions" :label="item.name" :value="item.id!" />
                        </el-select>
                        <el-input v-model="form.stationCode" placeholder="网点代号，选填" maxlength="50"></el-input>
                    </div>

                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    银行账号 <span class="text-red">*</span>
                </template>
                <el-form-item prop="accountNumber" class="!mb-0">
                    <el-input v-model="form.accountNumber" maxlength="50"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="修改说明">
                <el-form-item prop="amount" class="!mb-0">
                    <el-input v-model="form.changeReason" type="textarea" :rows="2" maxlength="200"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="修改凭据">
                <my-file-upload :accept="fileType" :data="{
                    sourceId: form.id,
                    sourceType: EFileSoureType.PAYBILL,
                    primaryType: '修改凭据'
                }" @upload-success="fileTableRef?.updateTable"></my-file-upload>
                <div class="mt-2">
                    <FileTable ref="fileTableRef" height="120px" :source-id="form.id" :primary-type="'修改凭据'"
                        :hiddenColumns="['index', 'primaryType']"></FileTable>
                </div>
            </el-descriptions-item>
        </el-descriptions>
    </el-form>
    <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
        <el-button type="primary" @click="save">确认修改</el-button>
        <el-button type="warning" @click="updateHistoryShow = true">修改历史</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>

    <el-dialog title="收款信息修改历史" v-model="updateHistoryShow" width="1000px" :close-on-click-modal="false"
        destroy-on-close>
        <AccountUpdateHistory :skr-id="accountInfo.id!"></AccountUpdateHistory>
    </el-dialog>
</template>

<script setup lang="ts">
import { EFileSoureType } from '@/utils/constants';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import FileTable from '@/components/Table/FileTable.vue';
import { updateSkr } from '@/api/fund/paybillAudit';
import AccountUpdateHistory from './accountUpdateHistory.vue';
import { genSnowId } from '@/utils/common';
import { getBankStationList } from '@/api/fund/bankAccount';

const emit = defineEmits(['close', 'cancel']);
const props = defineProps<{
    billId: string
    accountInfo: setAmountType
}>()

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const updateHistoryShow = ref(false)
const form = reactive({
    id: genSnowId(),
    skrId: props.accountInfo.id,
    accountName: props.accountInfo.name,
    accountNumber: props.accountInfo.cardNumber,
    bankName: props.accountInfo.bankName,
    stationCode: props.accountInfo.stationCode,
    changeReason: '',
    fileIds: [] as string[]

})
const formRules = reactive<FormRules<typeof form>>({
    accountName: [
        { required: true, message: '请输入收款人（单位）', trigger: 'blur' }
    ],
    bankName: [
        { required: true, message: '请输入开户银行', trigger: 'blur' }
    ],
    accountNumber: [
        { required: true, message: '请输入银行账号', trigger: 'blur' }
    ],
    stationCode: [
        { required: true, message: '请输入网点代号', trigger: 'blur' }
    ],
})

const bankOptions = ref<FundBankStation[]>([])
const bankSelectLoading = ref(false)
const remoteMethod = (query: string) => {
    if (query) {
        bankSelectLoading.value = true
        getBankStationList({
            name: query,
        }, {
            pageNum: 1,
            pageSize: 10
        }).then(res => {
            bankOptions.value = res.rows ?? []
        }).finally(() => {
            bankSelectLoading.value = false
        })
    } else {
        bankOptions.value = []
    }
}
const handleBankChange = (val: string) => {
    const bank = bankOptions.value.find(item => item.id === val)
    if (bank) {
        form.stationCode = bank?.id
        form.bankName = bank?.name ?? ''
    }
}

const formRef = ref<FormInstance>()
const fileTableRef = ref<FileTableExposeType | null>(null)

const save = () => {
    form.fileIds = fileTableRef.value?.getFileList().map(t => t.id!) ?? []
    updateSkr(form).then(res => {
        ElMessage.success('入库成功');
        emit('close')
    })
}
</script>