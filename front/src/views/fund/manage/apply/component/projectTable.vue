<template>
    <div class="">
        <!-- 查询表单 -->
        <el-form :inline="true" :model="queryForm" label-width="auto">
            <el-form-item label="项目名称">
                <el-input v-model="queryForm.name" placeholder="请输入项目名称" clearable class="w-40" />
            </el-form-item>
            <el-form-item label="项目类型">
                <ProjectTypeSelect v-model="queryForm.typeId" placeholder="请选择项目类别" class="!w-40" clearable>
                </ProjectTypeSelect>
            </el-form-item>
            <el-form-item label="项目用途">
                <el-select v-model="queryForm.purposeIds" multiple collapse-tags placeholder="请选择项目用途" clearable class="w-40">
                    <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="执行年度">
                <el-date-picker v-model="queryForm.year" type="year" placeholder="请选择年度" clearable class="!w-40" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
            </el-form-item>
        </el-form>

        <div v-loading="loading">
            <!-- 项目数据表格 -->
            <el-table :data="tableData" height="500px" border>
                <el-table-column type="index" label="序号" width="60" align="center">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="项目名称" header-align="center" align="left">
                    <template #default="{ row }">
                        <el-link type="primary" @click="handleSelectProject(row)">{{ row.name }}</el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="applyOrgname" label="项目单位" align="center" width="200" />
                <el-table-column prop="estAmount" label="项目金额（万元）" header-align="center" align="right" width="150"
                    :formatter="(row: any) => numberFormat(row.estAmount)" />
                <el-table-column prop="duration" label="项目期限" align="center" width="150">
                    <template #default="{ row }">
                        <div>起：{{ getDateRange(row).minDate }}</div>
                        <div>止：{{ getDateRange(row).maxDate }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="leader" label="负责人" align="center" width="100" />
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
    </div>
    <el-dialog title="子项目选择" v-model="subProjectTableShow" width="1000px" :close-on-click-modal="false"
        destroy-on-close>
        <el-table :data="childrenProjectList" row-key="id" height="500px" default-expand-all border style="width: 100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="name" label="项目名称" header-align="center" align="left">
                <template #default="{ row }">
                    <el-link @click="handleSubProjectSelect(row)">
                        {{ row.name }}
                        <span v-if="row.childName">{{ ` -- ${row.childName}` }}</span>
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column prop="amount" label="项目金额" header-align="center" align="right" width="150"
                :formatter="(row: any) => numberFormat(row.amount)" />
        </el-table>
    </el-dialog>
</template>

<script setup lang="ts">
import { getProjectList } from '@/api/fund/feeShare'
import { getPaybillProjectList } from '@/api/fund/paybill'
import { getProjectChildrenList } from '@/api/fund/children'
import { checkIsNumber, getMinMaxDate, numberFormat } from '@/utils/common'
import { BigNumber } from 'bignumber.js'
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import { dayjs } from 'element-plus'

const emit = defineEmits(['select'])

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose } = proxy.useDict("project_purpose");

const page = reactive({
    pageNum: 1,
    pageSize: 10
})
const queryForm = reactive<ProjectManageConditionVo>({
    year: dayjs().format('YYYY'),
})

const loading = ref(false)
const tableData = ref<IProjectInfoVo[]>([])
const total = ref(0)
const handleSearch = () => {
    loading.value = true
    getPaybillProjectList(queryForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0

    }).finally(() => {
        loading.value = false
    })
}
handleSearch()

const getDateRange = (row: IProjectInfoVo) => {
    return getMinMaxDate([
        row.buildBegin,
        row.buildEnd,
        row.fundBegin,
        row.fundEnd,
    ])
}

interface IChildrenProject {
    id: string
    projId: string,
    name: string
    projName: string,
    childName?: string
    leaderName: string,
    leaderMobile: string,
    projType: string,
    amount: number,
    projAmount: number,
    planYear: number,
    applyOrgid?: number,
    assessOrgid?: number,
    cooperateOrgid?: number,
    children?: IChildrenProject[]
}
const childrenProjectList = ref<IChildrenProject[]>([])
const handleSelectProject = async (row: IProjectInfoVo) => {
    // 项目批准金额 按照优先级获取 合同金额 > 核定金额 > 入库金额 > 项目估算
    const projAmount = BigNumber(checkIsNumber(row.contractAmount) ? row.contractAmount :
        (checkIsNumber(row.checkAmount) ? row.checkAmount :
            (checkIsNumber(row.libAmount) ? row.libAmount : row.estAmount)) ?? 0).multipliedBy(10000).toNumber()
    // 当年下达用款计划 按照优先级获取 调整计划金额 > 正式计划金额 > 年初计划金额 > 上报计划金额
    const planYear = BigNumber(checkIsNumber(row.fundPlan?.adjustAmount) ? row.fundPlan?.adjustAmount :
        (checkIsNumber(row.fundPlan?.formalAmount) ? row.fundPlan?.formalAmount :
            (checkIsNumber(row.fundPlan?.earlyAmount) ? row.fundPlan?.earlyAmount : row.fundPlan?.declareAmount)) ?? 0).multipliedBy(10000).toNumber()
    const childrenRes = await getProjectChildrenList(row.id!)
    if (childrenRes.data && childrenRes.data.length > 0) {
        subProjectTableShow.value = true
        childrenProjectList.value = [{
            id: row.id!,
            projId: row.id!,
            name: row.name!,
            projName: row.name!,
            leaderName: row.leader!,
            leaderMobile: row.leaderTel!,
            projType: row.typeName!,
            amount: projAmount,
            projAmount: projAmount,
            planYear: planYear,
            applyOrgid: row.applyOrgid,
            assessOrgid: row.assessOrgid,
            cooperateOrgid: row.cooperateOrgid,
            children: childrenRes.data.map((item, index) => {
                return {
                    id: item.id!,
                    projId: row.id!,
                    name: item.name!,
                    projName: row.name!,
                    childName: item.name,
                    leaderName: row.leader!,
                    leaderMobile: row.leaderTel!,
                    projType: row.typeName!,
                    amount: item.amount!,
                    projAmount: projAmount,
                    planYear: planYear,
                    applyOrgid: row.applyOrgid,
                    assessOrgid: row.assessOrgid,
                    cooperateOrgid: row.cooperateOrgid,
                }
            }),
        }]
    } else {
        emit('select', {
            projId: row.id,
            projName: row.name,
            leaderName: row.leader,
            leaderMobile: row.leaderTel,
            projType: row.typeName,
            projAmount: projAmount,
            planYear: planYear,
            applyOrgid: row.applyOrgid,
            assessOrgid: row.assessOrgid,
            cooperateOrgid: row.cooperateOrgid,
        })
    }

}

const handleSubProjectSelect = (row: IChildrenProject) => {
    // 如果选中的是主项目，那么不传childId
    if (row.id == childrenProjectList.value.at(0)?.id) {
        emit('select', {
            projId: row.projId,
            projName: row.projName,
            leaderName: row.leaderName,
            leaderMobile: row.leaderMobile,
            projType: row.projType,
            projAmount: row.projAmount,
            planYear: row.planYear,
            applyOrgid: row.applyOrgid,
            assessOrgid: row.assessOrgid,
            cooperateOrgid: row.cooperateOrgid,
        })
    } else {
        emit('select', {
            projId: row.projId,
            childId: row.id,
            childName: row.childName,
            projName: row.projName,
            leaderName: row.leaderName,
            leaderMobile: row.leaderMobile,
            projType: row.projType,
            projAmount: row.projAmount,
            planYear: row.planYear,
            applyOrgid: row.applyOrgid,
            assessOrgid: row.assessOrgid,
            cooperateOrgid: row.cooperateOrgid,
        })
    }

}

const subProjectTableShow = ref(false)
</script>

<style scoped lang="less">
// :deep(.el-table__indent) {
//     display: none;
// }
// :deep(.el-table__placeholder) {
//     display: none;
// }
// :deep(.el-table__expand-icon) </style>