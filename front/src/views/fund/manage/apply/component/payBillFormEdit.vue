<template>
    <div :class="$attrs.formClass">
        <el-scrollbar height="700px">
            <div class="flex gap-3">
                <div class="apply-table" style="margin-bottom: 20px;">
                    <div class="header">
                        上海化学工业区专项发展资金拨款申请表
                    </div>
                    <div class="date">
                        <span>申请单位：{{ form.applyOrgname }}</span>
                        <span style="float: right;">填表日期：{{ dateFormat(form.applyDate, 'YYYY年M月D日') }}</span>
                    </div>
                    <table class="mt-1">
                        <tbody>
                            <tr>
                                <td rowspan="6" style="width: 14%;">项目<br>基本<br>情况</td>
                                <td>项目名称<span class="star">*</span></td>
                                <td width="42%" colspan="3" style="text-align:left; padding-left:5px;">
                                    <div class="flex items-center gap-2">
                                        <el-icon class="cursor-pointer" color="#409EFF"
                                            @click="projectTableShow = true">
                                            <Search></Search>
                                        </el-icon>
                                        <span class="text-black">{{ form.projName }}</span>
                                        <span v-if="form.childName">{{ ` -- ` + form.childName }}</span>
                                    </div>
                                    <span v-if="form.projSn" class="text-black text-3 ml-7">{{ form.projSn }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td width="1%">
                                    <div style="width: 180px;">
                                        项目负责人<span class="star">*</span>
                                    </div>
                                </td>
                                <td width="42%">
                                    <input type="text" v-model="form.leaderName" maxlength="50">
                                </td>
                                <td width="1%">
                                    <div style="width: 150px;">
                                        联系电话<span class="star">*</span>
                                    </div>
                                </td>
                                <td width="42%">
                                    <input type="text" v-model="form.leaderMobile" maxlength="50">
                                </td>
                            </tr>
                            <tr>
                                <td width="1%">
                                    <div style="width: 180px;">
                                        项目批准总额<span class="star">*</span>
                                    </div>
                                </td>
                                <td width="42%">
                                    <input :value="numberFormat(form.projAmount, 2)" type="text" readonly
                                        style="outline: none; cursor: pointer;">
                                </td>
                                <td width="1%">
                                    <div style="width: 150px;">
                                        支出分类
                                    </div>
                                </td>
                                <td width="42%">
                                    <input :value="form.projType" type="text" readonly
                                        style="outline: none; cursor: pointer;">
                                </td>
                            </tr>

                            <tr>
                                <td>当年下达用款计划<span class="star">*</span></td>
                                <td>
                                    <input :value="numberFormat(form.planYear, 2)" type="text" readonly
                                        style="outline: none; cursor: pointer;">
                                </td>
                                <td>项目属性</td>
                                <td class="!pl-2px">
                                    <select v-model="form.typeId">
                                        <option selected :value="undefined">请选择</option>
                                        <option v-for="item in project_attr" :value="item.value">{{ item.label }}
                                        </option>

                                    </select>
                                </td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr>
                                <td rowspan="9" style="width: 14%;">资金<br>拨款<br>申请</td>
                                <td>收款单位<span class="star">*</span></td>
                                <td colspan="3" style="position: relative;padding-left:5px;">
                                    <div class="flex items-center gap-2">
                                        <el-icon class="cursor-pointer" color="#409EFF"
                                            @click="bankAccountTableShow = true">
                                            <Search></Search>
                                        </el-icon>
                                        <span id="lblProjectSN">
                                            <template v-if="accountList.length > 1">
                                                {{ `${accountList.at(0)?.name} 等${accountList.length}人 <见附表>` }}
                                            </template>
                                            <template v-else>
                                                {{ accountList.at(0)?.name }}
                                            </template>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>单位开户银行<span class="star">*</span></td>
                                <td colspan="3" style="text-align: left;">
                                    <input :value="accountList.length > 1 ? '<见附表>' : accountList.at(0)?.bankName"
                                        type="text" readonly style="outline: none; cursor: pointer;">
                                </td>
                            </tr>
                            <tr>
                                <td>单位银行账号<span class="star">*</span></td>
                                <td colspan="3">
                                    <input :value="accountList.length > 1 ? '<见附表>' : accountList.at(0)?.cardNumber"
                                        type="text" readonly style="outline: none; cursor: pointer;">
                                </td>
                            </tr>

                            <tr>
                                <td>本次申请拨款<span class="star">*</span></td>
                                <td>
                                    <input v-model="form.applyAmount" type="text" :readonly="accountList.length > 1">

                                </td>
                                <td>拨款类型<span class="star">*</span></td>
                                <td class="!pl-2px">
                                    <select v-model="form.applyType" style="width: 100%;">
                                        <option selected :value="undefined">请选择</option>
                                        <option v-for="item in bill_type" :value="item.value">{{ item.label }}</option>

                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td>申请拨款说明<span class="star">*</span></td>
                                <td colspan="3" style="text-align: left;">
                                    <textarea v-model="form.applyReason" rows="2" cols="20"
                                        :style="{ height: applyReasonHeight + 'px' }"
                                        @input="evt => applyReasonHeight = (evt.target as any)?.scrollHeight"
                                        maxlength="8000"></textarea>
                                </td>
                            </tr>
                            <tr>
                                <td>核准金额</td>
                                <td style="text-align: left;">
                                    <input :value="numberFormat(form.checkAmount)" type="text" readonly
                                        style="outline: none; cursor: pointer;">
                                </td>
                                <td>项目累计拨款<span class="star">*</span></td>
                                <td>
                                    <div class="flex justify-between">
                                        <input :value="numberFormat(getProjectTotalPayedAmount, 2)" type="text"
                                            readonly>
                                        <el-button type="primary" link
                                            @click="projectPayedTableShow = true">查看明细</el-button>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                        <tbody>
                            <tr>
                                <td rowspan="6" style="width: 14%;">证明<br>材料</td>
                                <td>合同名称</td>
                                <td width="42%" colspan="3" style="text-align:left; padding-left:5px;">
                                    <div class="flex items-center gap-2">
                                        <el-icon class="cursor-pointer" color="#409EFF"
                                            @click="handleOpenContractTable">
                                            <Search></Search>
                                        </el-icon>
                                        <span>{{ form?.contractName }}</span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td width="1%">
                                    <div style="width: 180px;">
                                        合同金额
                                    </div>
                                </td>
                                <td width="42%">
                                    <input :value="numberFormat(form?.contractAmount, 2)" type="text" readonly>
                                </td>
                                <td width="1%">
                                    <div style="width: 150px;">
                                        合同累计拨款
                                    </div>
                                </td>
                                <td width="42%">
                                    <div class="flex justify-between">
                                        <input :value="numberFormat(form?.contractPayed, 2)" type="text" readonly>
                                        <el-button type="primary" link
                                            @click="contractPayedTableShow = true">查看明细</el-button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td width="1%">
                                    <div style="width: 180px;">
                                        财务监理意见<span class="star">*</span>
                                    </div>
                                </td>
                                <td width="42%">
                                    <div class="text-left pl-2">
                                        <el-radio-group v-model="form.fundOpinion">
                                            <el-radio value="1">是</el-radio>
                                            <el-radio value="0">否</el-radio>
                                        </el-radio-group>
                                    </div>
                                </td>
                                <td width="1%">
                                    <div style="width: 150px;">
                                        附件
                                    </div>
                                </td>
                                <td width="42%">
                                    <div class="flex items-center ml-1">
                                        <el-button v-if="form.fundOpinion != '1'" type="primary" icon="DocumentAdd" text
                                            link @click="ElMessage.warning('财务监理意见为“否”，无需上传附件')"></el-button>
                                        <my-file-upload v-else :accept="fileType" :data="{
                                            sourceId: form.id,
                                            sourceType: EFileSoureType.PAYBILL,
                                            primaryType: 财务监理意见,
                                        }" :before-upload="handleFinanceSupervisionBeforeUpload"
                                            @upload-success="handleFinanceSupervisionUploadSuccess">
                                            <el-icon color="#409EFF">
                                                <DocumentAdd></DocumentAdd>
                                            </el-icon>
                                        </my-file-upload>
                                        <DownloadA v-if="FinanceSupervisionFile" class="ml-2 break-all"
                                            :file="FinanceSupervisionFile">
                                        </DownloadA>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                        <tbody>
                            <tr>
                                <td rowspan="6" style="width: 14%;">项目<br>管理<br>部门</td>
                                <td width="42%" colspan="2" style="text-align:left; padding-left:8px;">
                                    <el-radio-group v-model="form.mgrOrgid">
                                        <el-radio v-for="item in newProjectManagerDeptList" :value="item.deptId"
                                            :disabled="item.disabled">{{
                                                item.deptName
                                            }}</el-radio>
                                    </el-radio-group>
                                </td>
                                <td width="1%">
                                    <div style="width: 150px;">
                                        审批处室<span class="star">*</span>
                                    </div>
                                </td>
                                <td width="42%" style="text-align:left; padding-left:2px;">
                                    <select v-model="form.auditOrgid" style="width: 100%;">
                                        <option selected :value="undefined">请选择</option>
                                        <option v-for="item in auditDeptList" :value="item.deptId">{{ item.deptName }}
                                        </option>
                                    </select>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                    <div class="page-break" style="clear:both;">&nbsp;</div>
                    <div v-if="(accountList?.length ?? 0) > 1">
                        <div class="header" style="text-align:left;">附表：{{ form.serialNumber }}收款人清单</div>
                        <table class="!border-[3px] border-solid border-black">
                            <thead>
                                <tr>
                                    <td width="1%">
                                        <div style="width:30px; text-align:center;">#</div>
                                    </td>
                                    <td width="25%">收款人</td>
                                    <td width="30%">开户银行</td>
                                    <td width="30%">银行帐号</td>
                                    <td width="15%">金额</td>
                                </tr>
                            </thead>
                            <tbody class="!border-[1px]">

                                <tr v-for="(item, index) in accountList">
                                    <td>{{ index + 1 }}</td>
                                    <td>{{ item.name }}</td>
                                    <td>{{ item.bankName }}</td>
                                    <td>{{ item.cardNumber }}</td>
                                    <td>{{ item.amount }}</td>
                                </tr>



                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4">合计（共 {{ accountList.length }} 人）</td>
                                    <td>{{sumAmount(accountList.map(t => t.amount))}}</td>
                                </tr>
                            </tfoot>

                        </table>
                    </div>
                </div>
                <div class="w-[300px]">
                    <div class="mt-13">
                        <FileManage :bill-form="form" type="edit"></FileManage>

                    </div>
                </div>
            </div>
        </el-scrollbar>
    </div>

    <div :class="$attrs.bottomClass">
        <el-popover v-if="formId" placement="top" :width="800" trigger="click">
            <template #reference>
                <el-button style="margin-right: 14px" round type="primary">审核流程</el-button>
            </template>
            <el-table :data="auditNodeList" border>
                <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
                <el-table-column prop="nodeName" label="流程节点" align="center" width="150"></el-table-column>
                <el-table-column prop="operatorName" label="审批人" align="center" width="120"></el-table-column>
                <el-table-column prop="operateTime" label="审批时间" align="center" width="120"
                    :formatter="(row: any) => dateFormat(row.operateTime)"></el-table-column>
                <el-table-column label="审批结果" align="center" width="100">
                    <template #default="{ row }">
                        <span v-if="row.operateResult == EAuditType.pass">{{
                            getAuditTypeName(EAuditType.pass) }}</span>
                        <span v-if="row.operateResult == EAuditType.reject">{{
                            getAuditTypeName(EAuditType.reject) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="operateReason" label="审批意见" align="center"></el-table-column>
            </el-table>
        </el-popover>
        <el-button type="warning" class="w-20" :loading="loading" @click="handleTempSave">暂存</el-button>
        <el-button v-if="form.canDoSubmit || !formId" type="success" class="w-20" :loading="loading"
            @click="handleSubmit">提交</el-button>
        <el-button type="danger" class="w-20 ml-2" @click="emit('close')">关闭</el-button>
        <el-checkbox class="ml-3" v-model="sendSms" true-value="1" false-value="0">短信通知</el-checkbox>
    </div>

    <el-dialog title="项目选择" v-model="projectTableShow" width="1000px" :close-on-click-modal="false" destroy-on-close>
        <ProjectTable @select="handleProjectSelect"></ProjectTable>
    </el-dialog>
    <el-dialog title="账户快速选择" v-model="bankAccountTableShow" width="1000px" :close-on-click-modal="false"
        destroy-on-close>
        <BankAccountTable :activate-tab="accountList.length > 1 ? 'private' : 'public'"
            :select-list="accountList.filter(item => item.isPublic == false)" @select="handleAccountSelect"
            @cancel="bankAccountTableShow = false">
        </BankAccountTable>
    </el-dialog>
    <el-dialog title="合同选择" v-model="contractTableShow" width="1000px" :close-on-click-modal="false" destroy-on-close>
        <ContractTable :proj-id="form.projId!" @select="handleContraceSelect"></ContractTable>
    </el-dialog>
    <el-dialog title="合同累计拨款查询" v-model="contractPayedTableShow" width="800px" :close-on-click-modal="false"
        destroy-on-close>
        <PayedTable :pay-list="getContractDetailList"></PayedTable>
    </el-dialog>
    <el-dialog title="项目累计拨款查询" v-model="projectPayedTableShow" width="800px" :close-on-click-modal="false"
        destroy-on-close>
        <PayedTable :pay-list="getProjectDetailList"></PayedTable>
    </el-dialog>
</template>

<script setup lang="tsx">
import { EFileSoureType, projectManagerDeptList } from '@/utils/constants';
import { Search, DocumentAdd } from '@element-plus/icons-vue'
import ProjectTable from './projectTable.vue';
import BankAccountTable from './bankAccountTable.vue';
import ContractTable from './contractTable.vue';
import PayedTable from './payedTable.vue';
import { savePayBill, getBillPaidInfoByContractId, getBillPaidInfoByProjId, getPayBillDetail, submitPayBill, getBillAttachmentsByProjId, getBillProjAttachments } from '@/api/fund/paybill';
import { dayjs, ElMessage, ElMessageBox } from 'element-plus';
// @ts-ignore
import useUserStore from '@/store/modules/user'
import { checkIsNumber, dateFormat, genSnowId, numberFormat, sumAmount } from '@/utils/common';
import { getFileList1, removeFile, updateAttachment } from '@/api/lib/project/file';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
import { getPayBillAuditNodeList } from '@/api/fund/paybillAudit';
import { EAuditType, getAuditTypeName } from '@/utils/constants';
import FileManage from './fileManage.vue';
import { useDeptStore } from '@/components/Select/deptStore';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_attr, bill_type, bill_file_type, project_file_type } = proxy.useDict("project_attr", "bill_type", 'bill_file_type', 'project_file_type');
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    formId?: string;
}>()

/** 申请拨款说明 高度*/
const applyReasonHeight = ref(46)

// 审批处室
const deptStore = useDeptStore()

const auditDeptList = ref<SysDept[]>([])
await new Promise(resolve => {
    watch(() => deptStore.deptList, () => {
        if (deptStore.deptList.length > 0) {
            auditDeptList.value = deptStore.deptList.filter(t => t.orderNum! >= 11 && t.orderNum! <= 17)
            resolve(true)
        }
    }, {immediate: true})
});

const userStore = useUserStore()
const accountList = ref<setAmountType[]>([])
const projectPaidInfoList = ref<FundPaybillPaidInfoVo[]>([])
const contractPaidInfoList = ref<FundPaybillPaidInfoVo[]>([])
const auditNodeList = ref<FundPaybillNode[]>([])
const form = reactive<FundPaybillVo>({
    id: genSnowId(),
    fundOpinion: '0',
    applyDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    applyOrgid: userStore.deptId,
    applyOrgname: userStore.deptName,
    // auditOrgid: deptList.value.map(t => t.deptId).includes(userStore.user.dept.parentId) || deptList.value.map(t => t.deptId).includes(userStore.user.dept.deptId)
})
const auditDeptIds = auditDeptList.value.map(t => t.deptId)
if (auditDeptIds.includes(userStore.user.dept.deptId)) {
    form.auditOrgid = userStore.user.dept.deptId
} else {
    if (auditDeptIds.includes(userStore.user.dept.parentId)) {
        form.auditOrgid = userStore.user.dept.parentId
    }
}


if (props.formId) {
    getPayBillDetail(props.formId).then(res => {
        Object.assign(form, res.data)
        form.planYear = form.planYear
        form.projAmount = form.projAmount
        accountList.value = form.skrs?.map(item => {
            return {
                accountId: item.accountId ?? '',
                name: item.accountName ?? '',
                bankName: item.bankName ?? '',
                cardNumber: item.accountNumber ?? '',
                amount: item.amount ?? 0,
                isPublic: (form.skrs?.length ?? 0) > 1
            }
        }) ?? []
        // projectPaidInfoList.value = form.projPaybillList?.map(item => {
        //     return {
        //         id: item.id ?? '',
        //         serialNumber: item.serialNumber ?? '',
        //         payAmount: item.amount,
        //         payDate: item.details?.at(0)?.payDate ?? '',
        //         payVoucher: item.details?.at(0)?.payVoucher ?? '',
        //     }
        // }) ?? []
        // contractPaidInfoList.value = form.contractPaybillList?.map(item => {
        //     return {
        //         id: item.id ?? '',
        //         serialNumber: item.serialNumber ?? '',
        //         payAmount: item.amount,
        //         payDate: item.details?.at(0)?.payDate ?? '',
        //         payVoucher: item.details?.at(0)?.payVoucher ?? '',
        //     }
        // }) ?? []

        getFileList1(form.id!, 财务监理意见).then(res => {
            FinanceSupervisionFile.value = res.data?.at(0);
        })

        getPayBillAuditNodeList(form.id!).then(res => {

            auditNodeList.value = res.data?.filter(item => item.isMultiClaim != true) ?? []
            const isMultiClaimList = res.data?.filter(item => item.isMultiClaim)
            if (isMultiClaimList && isMultiClaimList.length > 0) {
                auditNodeList.value.push({
                    ...isMultiClaimList.at(0),
                    operatorName: isMultiClaimList.map(t => t.operatorName).join('，')
                })
            }

        })


    }).then(() => {
        // 获取项目累计拨款明细
        watch(() => form.projId, () => {
            if (form.projId) {
                getBillPaidInfoByProjId(form.projId!).then(res => {
                    projectPaidInfoList.value = res.data ?? []
                })
            }
        })
        // 获取合同累计拨款
        watch(() => form.contractId, () => {
            if (form.contractId) {
                getBillPaidInfoByContractId(form.contractId).then(res => {
                    contractPaidInfoList.value = res.data ?? []
                })
            }
        })
    }).then(() => {
        // 因为el-radio无法绑定null值，所以这里默认给他转换一下
        form.mgrOrgid = form.mgrOrgid ?? undefined
        projectManagerListUpdate(false)
    })
}


// 获取项目累计拨款
const getProjectTotalPayedAmount = computed(() => {
    return sumAmount([
        ...projectPaidInfoList.value.map(t => t.payAmount),
        form.applyAmount
    ])
})
const getProjectDetailList = computed(() => {
    return [
        {
            id: form.paidInfo?.id,
            serialNumber: form.serialNumber ?? dayjs(form.applyDate).format('YYYYMMDDHHmmss'),
            payAmount: checkIsNumber(form.checkAmount) ? form.checkAmount : form.applyAmount,
            payVoucher: form.paidInfo?.payVoucher,
        },
        ...projectPaidInfoList.value,

    ]
})

// 项目管理单位联动
const newProjectManagerDeptList = ref<{
    deptId?: number;
    deptName: string;
    disabled: boolean
}[]>([{ deptId: undefined, deptName: '无', disabled: false }, ...projectManagerDeptList.value.map(t => ({ ...t, disabled: false }))])
const projectManagerListUpdate = (isUpdateAuditOrgid: boolean) => {
    let mgrOrgid: number | undefined = form.mgrOrgid
    // 拨款单申请单位是发展公司的下属公司
    if (userStore.user.dept.parentId == 5483) {
        // ① 选择的项目，申报单位、配合单位、考核单位三者有任何一个是“公共事务中心”，默认选择“无”；
        if ([form.projApplyOrgid, form.projAssessOrgid, form.projCooperateOrgid].includes(399)) {
            mgrOrgid = undefined
        } else {
            // 除①外的情况，禁用“公共事务中心”，默认选择“无”
            newProjectManagerDeptList.value.find(t => t.deptId == 399)!.disabled = true
            mgrOrgid = undefined
        }
    }
    // 拨款单申请单位是驻区单位： 禁用“发展公司”，默认选择“无”
    else if (userStore.user.dept.parentId == 200) {
        newProjectManagerDeptList.value.find(t => t.deptId == 5483)!.disabled = true
        mgrOrgid = undefined
    }
    // 拨款单申请单位是公共事务中心及机关处室：默认选择“无”，全部禁用；
    else if (userStore.user.dept.parentId == 5482 || userStore.user.dept.parentId == 100) {
        newProjectManagerDeptList.value.forEach(t => t.disabled = true)
    }
    // 拨款单申请单位是其它情况
    else {
        // ① 选择的项目，申报单位、配合单位、考核单位三者有任何一个是“公共事务中心”，默认选择“无”，禁用“发展公司”
        if ([form.projApplyOrgid, form.projAssessOrgid, form.projCooperateOrgid].includes(399)) {
            newProjectManagerDeptList.value.find(t => t.deptId == 399)!.disabled = true
            mgrOrgid = undefined
        } else {
            // 其它：默认选择“无”，三项全部禁用
            newProjectManagerDeptList.value.forEach(t => t.disabled = true)
            mgrOrgid = undefined
        }
    }

    if (isUpdateAuditOrgid) {
        form.mgrOrgid = mgrOrgid
    }
}
// 获取累计拨款
// const getContranctTotalPayedAmount = computed(() => {
//     return sumAmount([
//         form.applyAmount,
//         ...contractPaidInfoList.value.map(t => t.payAmount),
//     ])
// })
const getContractDetailList = computed(() => {
    return [
        {
            id: form.paidInfo?.id,
            serialNumber: form.serialNumber ?? dayjs(form.applyDate).format('YYYYMMDDHHmmss'),
            payAmount: checkIsNumber(form.checkAmount) ? form.checkAmount : form.applyAmount,
            payVoucher: form.paidInfo?.payVoucher,
        },
        ...contractPaidInfoList.value,

    ]
})


const projectTableShow = ref(false);
const handleProjectSelect = (data: any) => {
    projectTableShow.value = false
    form.projId = data.projId
    form.childId = data.childId
    form.childName = data.childName
    form.projName = data.projName
    form.leaderName = data.leaderName
    form.leaderMobile = data.leaderMobile
    form.projType = data.projType
    form.projAmount = data.projAmount
    form.planYear = data.planYear

    form.projApplyOrgid = data.applyOrgid
    form.projAssessOrgid = data.assessOrgid
    form.projCooperateOrgid = data.cooperateOrgid

    projectManagerListUpdate(true)
}

// 设置项目属性的中文名
watch(() => form.typeId, () => {
    if (form.typeId) {
        form.typeName = project_attr.value.find((t: any) => t.value == form.typeId)?.label ?? ''
    }
})



const bankAccountTableShow = ref(false);
const handleAccountSelect = (data: setAmountType[]) => {
    accountList.value = data
    bankAccountTableShow.value = false
    form.applyAmount = sumAmount(accountList.value.map((t) => t.amount)) ?? undefined
}

type contractSelectType = {
    id: string,
    name: string,
    amount: number,
    addUpAmount: number,
    /** 乙方 */
    partb: string
}
const contractTableShow = ref(false);
const handleOpenContractTable = () => {
    if (!form.projId) {
        ElMessage.warning('请先选择项目')
        return
    }
    contractTableShow.value = true
}
const handleContraceSelect = (data: contractSelectType) => {
    contractTableShow.value = false
    form.contractId = data.id
    form.contractName = data.name
    form.contractAmount = data.amount
    form.contractPayed = data.addUpAmount
    // 用于判断收款人和合同乙方是否一致
    form.contractPartb = data.partb
}
const contractPayedTableShow = ref(false);
const projectPayedTableShow = ref(false);

const 财务监理意见 = '财务监理意见'
const FinanceSupervisionFile = ref<ISysAttachment>();
const handleFinanceSupervisionBeforeUpload = () => {
    // if (form.fundOpinion != '1') {
    //     ElMessage.warning('财务监理意见为“否”，无需上传附件')
    //     return false
    // }
    if (form.fundFile) {
        removeFile(form.fundFile)
    }

}
const handleFinanceSupervisionUploadSuccess = (res: { data: ISysAttachment }) => {
    FinanceSupervisionFile.value = res.data;
    form.fundFile = res.data.id
}

const loading = ref(false)
const handleTempSave = () => {
    if (!form.projId) {
        ElMessage.error('请选择项目')
        return
    }
    // if (!form.leaderName) {
    //     ElMessage.error('请输入项目负责人')
    //     return
    // }
    // if (!form.leaderMobile) {
    //     ElMessage.error('请输入联系电话')
    //     return
    // }
    // if (accountList.value.length < 1) {
    //     ElMessage.error('请选择收款单位')
    //     return
    // }
    if (!checkIsNumber(form.applyAmount) || form.applyAmount < 0) {
        ElMessage.error('请输入正确的拨款金额')
        return
    }
    // if (!form.applyType) {
    //     ElMessage.error('请输入拨款类型')
    //     return
    // }
    // if (!form.applyReason) {
    //     ElMessage.error('请输入申请拨款说明')
    //     return
    // }
    // if (!form.contractId) {
    //     ElMessage.error('请选择合同')
    //     return
    // }
    const data: FundPaybillDto = {
        id: form.id!,
        projId: form.projId ?? '',
        childId: form.childId,
        typeId: form.typeId,
        typeName: form.typeName,
        leaderName: form.leaderName ?? '',
        leaderMobile: form.leaderMobile ?? '',
        contractId: form.contractId ?? '',
        applyAmount: form.applyAmount!,
        applyType: form.applyType!,
        applyReason: form.applyReason ?? '',
        fundOpinion: form.fundOpinion ?? '',
        mgrOrgid: form.mgrOrgid ?? undefined,
        auditOrgid: form.auditOrgid ?? '',
        fundFile: form.fundFile,
        hqdpId: form.hqdpId,
        skrs: accountList.value.map((t) => ({ accountId: t.accountId, amount: accountList.value.length > 1 ? t.amount : form.applyAmount! }))

    }
    loading.value = true
    savePayBill(data).then(res => {
        ElMessage.success('暂存成功')
    }).finally(() => {
        loading.value = false
    })
}

const sendSms = ref('1')
const handleSubmit = async () => {
    if (!checkIsNumber(form.applyAmount)) {
        ElMessage.error('请输入正确的拨款金额')
        return
    }
    const data: FundPaybillDto = {
        id: form.id!,
        projId: form.projId ?? '',
        childId: form.childId,
        typeId: form.typeId,
        typeName: form.typeName,
        leaderName: form.leaderName ?? '',
        leaderMobile: form.leaderMobile ?? '',
        contractId: form.contractId ?? '',
        applyAmount: form.applyAmount!,
        applyType: form.applyType!,
        applyReason: form.applyReason ?? '',
        fundOpinion: form.fundOpinion ?? '',
        mgrOrgid: form.mgrOrgid ?? undefined,
        auditOrgid: form.auditOrgid ?? '',
        fundFile: form.fundFile,
        hqdpId: form.hqdpId,
        skrs: accountList.value.map((t) => ({ accountId: t.accountId, amount: accountList.value.length > 1 ? t.amount : form.applyAmount! }))

    }
    if (form.contractId && !accountList.value.map(t => t.name).includes(form.contractPartb ?? 'aaa')) {
        await ElMessageBox.confirm(
            <div>
                <div class="text-red">所选合同乙方与当前收款单位不一致，请添加附件说明</div>

                <div class="mt-5">是否确认提交？</div>
            </div>,
            {
                // type: 'warning',
                title: '提示'
            }
        )
    }
    loading.value = true
    submitPayBill(data, sendSms.value).then(res => {
        ElMessage.success('提交成功')
        emit('close')
    }).finally(() => {
        loading.value = false
    })
}

</script>

<style scoped>
.apply-table {
    width: 800px;

}

.apply-table .header {
    font-size: 24px;
    font-family: 黑体;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
}

.apply-table .date {
    font-size: 16px;
    /* font-family: 宋体; */
}

.blink {
    margin: 10px 0;
    color: red;
    font-weight: bold;
    animation: blink 2s infinite;
}

.apply-table table {
    /* border: 5px solid #000; */
    width: 800px;
    border-collapse: collapse;
    font-size: 16px;
}

.apply-table tbody {
    border: 3px solid #000;
    background-color: #fff;
}

.apply-table td {
    border: 1px solid #000;

    text-align: center;
    padding: 5px 0;
}

span.star {
    color: Red;
    font-weight: bold;
    font-size: 18px;
    vertical-align: middle;
}

input[type="text"] {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    text-indent: 5px;
    font-size: 16px;
    outline: none;
}

textarea {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100px - 4px);
    padding: 5px 8px;
    font-family: Arial;
    /* text-indent: 5px; */
    resize: none;
    font-size: 16px;
    outline: none;
}

select {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    font-size: 16px;
    outline: none;
}

:deep(.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th) {
    background-image: linear-gradient(to bottom, #f7f7f7, #f7f7f7) !important
}
</style>