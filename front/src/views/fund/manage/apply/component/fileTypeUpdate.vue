<template>
    <el-form ref="formRef" :model="form" class="flex flex-col gap-3 h-50" :inline-message="true">
        <el-descriptions :column="1" label-width="30%" border>
            <el-descriptions-item width="70%" label="文件">
                {{ props.file.fileName }}
            </el-descriptions-item>
            <el-descriptions-item width="70%">
                <template #label>
                    类型 <span class="text-red">*</span>
                </template>
                <el-form-item prop="accountName" class="!mb-0">
                    <el-select v-model="form.primaryType" placeholder="文件类型">
                        <el-option v-for="item in bill_file_type" :label="item.label" :value="item.label"></el-option>
                    </el-select>
                </el-form-item>
            </el-descriptions-item>
        </el-descriptions>
    </el-form>
    <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
        <el-button type="primary" @click="save">确认变更</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { updateAttachment } from '@/api/lib/project/file';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['close', 'cancel']);
const props = defineProps<{
    file: ISysAttachment
}>()

const { proxy } = getCurrentInstance() as { proxy: any };
const { bill_file_type } = proxy.useDict('bill_file_type');

const form = reactive({
    id: props.file.id,
    primaryType: props.file.primaryType
})

const save = () => {
    updateAttachment(form).then(res => {
        ElMessage.success('隐藏成功')
        emit('close')
    })
}
</script>