<template>
    <el-tabs type="border-card">
        <el-tab-pane label="附件">
            <el-scrollbar height="520px">
                <div v-if="canDoUpload" class="flex gap-3 mb-3">
                    <div class="flex gap-3">
                        <el-select v-model="uploadFileType" class="!w-30" placeholder="文件类型">
                            <el-option v-for="item in bill_file_type" :label="item.label"
                                :value="item.label"></el-option>
                        </el-select>
                        <el-dropdown split-button type="primary">
                            <div>
                                <span v-if="!uploadFileType" @click="ElMessage.warning('请选择上传类型')">选择文件</span>
                                <my-file-upload v-else :accept="fileType" :data="{
                                    sourceId: props.billForm.id,
                                    sourceType: EFileSoureType.PAYBILL,
                                    primaryType: uploadFileType,
                                    primaryTypeName: getUploadFileType?.label
                                }" @upload-success="searchUploadFile">
                                    <span class="text-white">选择文件</span>
                                </my-file-upload>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>添加OA附件</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
                <div class="flex flex-col">
                    <el-table :data="fileList.concat(...hqdpFileList)" row-key="id" :expand-row-keys="range(0, 101, 1).map(t => String(t))"
                        :indent="2" border>
                        <el-table-column label="序号" width="60" align="center"></el-table-column>
                        <el-table-column prop="fileName" label="文件名称">
                            <template #default="{ row }">
                                <div v-if="row.primaryType" class="flex gap-1 items-center justify-between">
                                    <DownloadA :file="row" class="break-all"></DownloadA>
                                    <div class="flex flex-col">
                                        <el-button v-if="canDoEdit" class="p-1" type="warning" text icon="Edit"
                                            @click="handleUpdateFileType(row)"></el-button>
                                        <RemoveFile v-if="type == 'edit'" class="p-1 !ml-0" :file="row"
                                            @success="() => searchUploadFile()">
                                        </RemoveFile>
                                    </div>
                                </div>
                                <div v-if="row.isHqdp">
                                    <el-link type="primary" :href="hqdpBaseUrl + row.fileUrl"
                                        :download="row.fileName">{{ row.fileName }}</el-link>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="项目">
            <el-scrollbar height="500px">
                <div v-if="canDoUpload" class="flex gap-3 mb-3">
                    <div class="flex gap-3">
                        <el-select v-model="uploadProjFileType" class="!w-30" placeholder="文件类型">
                            <el-option v-for="item in project_file_type" :label="item.label"
                                :value="item.label"></el-option>
                        </el-select>
                        <el-dropdown split-button type="primary">
                            <div>
                                <span v-if="!uploadProjFileType" @click="ElMessage.warning('请选择上传类型')">选择文件</span>
                                <my-file-upload v-else :accept="fileType" :data="{
                                    sourceId: props.billForm.projId,
                                    sourceType: EFileSoureType.PROJECT,
                                    primaryType: uploadProjFileType,
                                    primaryTypeName: getUploadProjFileType?.label
                                }" @upload-success="searchProjFileList">
                                    <span class="text-white">选择文件</span>
                                </my-file-upload>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item>添加OA附件</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
                <div class="flex flex-col">
                    <el-table :data="projectFileList" row-key="id"
                        :expand-row-keys="range(0, 50, 1).map(t => String(t))" :indent="2" border>
                        <el-table-column label="序号" width="60" align="center"></el-table-column>
                        <el-table-column prop="fileName" label="文件名称">
                            <template #default="{ row }">
                                <div v-if="row.primaryType" class="flex items-center justify-between">
                                    <DownloadA :file="row" class="break-all"></DownloadA>
                                    <el-button v-if="canDoEdit" class="p-1" type="danger" text icon="CircleClose"
                                        @click="handleHiddenFile(row, '2')"></el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

            </el-scrollbar>

        </el-tab-pane>
        <el-tab-pane label="其它拨款单">
            <el-scrollbar height="500px">
                <div v-for="item in otherBillFileList" class="mt-0">
                    <p class="my-2 text-4">申请单编号：{{ item.serialNumber }}</p>

                    <el-table :data="item.fileList" row-key="id" :expand-row-keys="range(0, 50, 1).map(t => String(t))"
                        :indent="2" border>
                        <el-table-column label="序号" width="60" align="center"></el-table-column>
                        <el-table-column prop="fileName" label="文件名称">
                            <template #default="{ row }">
                                <div v-if="row.primaryType" class="grid grid-cols-[1fr_10%] items-center">
                                    <DownloadA :file="row" class="break-all"></DownloadA>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-scrollbar>

        </el-tab-pane>
    </el-tabs>

    <el-dialog title="变更附件类型" v-model="fileTypeUpdateShow" width="500px" :close-on-click-modal="false" destroy-on-close>
        <FileTypeUpdate :file="currentFile!" @close="fileTypeUpdateShow = false; searchUploadFile()"
            @cancel="fileTypeUpdateShow = false"></FileTypeUpdate>
        <footer class="el-dialog__footer h-10"></footer>
    </el-dialog>
</template>

<script setup lang="ts">
import { getFileList1, updateAttachment } from '@/api/lib/project/file';
import FileTypeUpdate from './fileTypeUpdate.vue';
import { EBillStatus, EFileSoureType } from '@/utils/constants';
import { groupBy, range } from 'lodash';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getBillAttachmentsByProjId, getBillProjAttachments } from '@/api/fund/paybill';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
import RemoveFile from '@/components/FileUpload/RemoveFile.vue';
import auth from '@/plugins/auth';

const props = withDefaults(defineProps<{
    billForm: FundPaybillVo,
    type: 'edit' | 'view',
    hqdpFileList?: ISysAttachment[]
}>(), {
    hqdpFileList: () => [] as any
})

const hqdpBaseUrl = import.meta.env.VITE_HQDP_BASE_URL

const { proxy } = getCurrentInstance() as { proxy: any };
const { bill_file_type, project_file_type } = proxy.useDict('bill_file_type', 'project_file_type');
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const canDoUpload = computed(() => {
    return ![EBillStatus.ysh, EBillStatus.ybf].includes(props.billForm.status??-10)
})
const canDoEdit = computed(() => {
    return ![EBillStatus.ysh, EBillStatus.ybf].includes(props.billForm.status??-10) && auth.hasPermi('fund:paybillAudit:updateAttachment')
})

const projectFileList = ref<(ISysAttachment & { children: ISysAttachment[] })[]>([]);
const otherBillFileList = ref<{ serialNumber: string, fileList: { id: string, fileName: string, children: ISysAttachment[] }[] }[]>([])

const uploadFileType = ref("");
const getUploadFileType = computed(() => {
    return bill_file_type.value.find((item: any) => item.value === uploadFileType.value);
});
const uploadProjFileType = ref("");
const getUploadProjFileType = computed(() => {
    return project_file_type.value.find((item: any) => item.value === uploadProjFileType.value);
});
const fileList = ref<(ISysAttachment & { children: ISysAttachment[] })[]>([]);
const searchUploadFile = () => {
    uploadFileType.value = ""
    getFileList1(props.billForm.id!, getUploadFileType.value).then(res => {
        fileList.value.length = 0
        const groupData = groupBy(res.data, 'primaryType')
        let audoIndex = 0
        Object.keys(groupData).forEach((key, index) => {
            fileList.value.push({
                id: String(index),
                fileName: key,
                children: groupData[key].map(t => {
                    audoIndex++
                    return {
                        ...t,
                        index: audoIndex
                    }
                })
            })
        })
    });
}

const searchProjFileList = () => {
    uploadProjFileType.value = ""
    // 查询当前项目附件
    getBillProjAttachments(props.billForm.projId!).then(res => {
        projectFileList.value.length = 0
        const groupData = groupBy(res.data, 'primaryType')
        let audoIndex = 0
        Object.keys(groupData).forEach((key, index) => {
            projectFileList.value.push({
                id: String(index),
                fileName: key,
                children: groupData[key].map(t => {
                    audoIndex++
                    return {
                        ...t,
                        index: audoIndex
                    }
                })
            })
        })
    })
}

watch(() => props.billForm.projId, () => {
    if (props.billForm.projId) {
        searchProjFileList()

        // 查询当前项目其它拨款单附件
        getBillAttachmentsByProjId(props.billForm.projId, props.billForm.id).then(res => {
            const serialGroup = groupBy(res.data, 'serialNumber')
            otherBillFileList.value = Object.keys(serialGroup).map(key => {
                const fileGroup = groupBy(serialGroup[key], 'primaryType')
                let autoIndex = 0
                return {
                    serialNumber: key,
                    fileList: Object.keys(fileGroup).map((key, index) => {
                        return {
                            id: String(index),
                            fileName: key,
                            children: fileGroup[key].map(t => {
                                autoIndex++
                                return {
                                    ...t,
                                    index: autoIndex
                                }
                            })
                        }
                    })
                }
            }) as any
        })
    }
}, { immediate: true })

watch(() => props.billForm.id, () => {
    searchUploadFile()
}, { immediate: true })

/**
 * 隐藏附件
 * @param file 
 * @param type 1 是拨款单 2 是项目
 */
const handleHiddenFile = (file: ISysAttachment, type: '1' | '2') => {
    ElMessageBox.confirm('是否确认隐藏文件？', '提示', {
        type: 'warning'
    }).then(() => {
        updateAttachment({ id: file.id, hideFlag: 'PB' }).then(res => {
            ElMessage.success('隐藏成功')
            searchProjFileList()
        })
    })
}

const fileTypeUpdateShow = ref(false)
const currentFile = ref<ISysAttachment>()
const handleUpdateFileType = (file: ISysAttachment) => {
    fileTypeUpdateShow.value = true
    currentFile.value = file
}
</script>