<template>
    <el-scrollbar height="700px">
        <div class="flex gap-3">
            <div id="printTable" class="apply-table" style="margin-bottom: 20px;">
                <div class="header">
                    上海化学工业区专项发展资金拨款申请表
                </div>
                <!-- <div v-if="form.serialNumber" class="text-right text-4 font-bold">
                    No. {{ form.serialNumber }}
                </div> -->
                <div class="date flex items-baseline justify-between">
                    <span>申请单位：{{ form.applyOrgname }}</span>
                    <span v-if="form.serialNumber">No. {{ form.serialNumber }}</span>
                    <span>填表日期：{{ dateFormat(form.applyDate, 'YYYY年M月D日') }}</span>
                </div>
                <table>
                    <tbody>
                        <tr>
                            <td rowspan="6" style="width: 14%;">项目<br>基本<br>情况</td>
                            <td>项目名称<span class="star">*</span></td>
                            <td width="42%" colspan="3" style="text-align:left; padding-left:5px;">
                                <div class="text-black py-1">
                                    <div class="text-4">
                                         {{ form.projName }} 
                                         <span v-if="form.childName">
                                            {{ ` -- ` + form.childName }}
                                         </span>
                                        </div>
                                    <span class="text-3">{{ form.projSn }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td width="1%">
                                <div style="width: 180px;">
                                    项目负责人<span class="star">*</span>
                                </div>
                            </td>
                            <td width="42%" class="!text-left !pl-5px">
                                <span class="text-black">{{ form.leaderName }}</span>
                            </td>
                            <td width="1%">
                                <div style="width: 150px;">
                                    联系电话<span class="star">*</span>
                                </div>
                            </td>
                            <td width="42%" class="!text-left !pl-5px">
                                <span class="text-black">{{ form.leaderMobile }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td width="1%">
                                <div style="width: 180px;">
                                    项目批准总额<span class="star">*</span>
                                </div>
                            </td>
                            <td width="42%" class="!text-left !pl-5px">
                                <span class="text-black">{{ numberFormat(form.projAmount, 2) }}</span>
                            </td>
                            <td width="1%">
                                <div style="width: 150px;">
                                    支出分类
                                </div>
                            </td>
                            <td width="42%" class="!text-left !pl-5px">
                                <span class="text-black">{{ form.projType }}</span>
                            </td>
                        </tr>

                        <tr>
                            <td>当年下达用款计划<span class="star">*</span></td>
                            <td class="!text-left !pl-5px">
                                <span class="text-black">{{ numberFormat(form.planYear, 2) }}</span>
                            </td>
                            <td>项目属性</td>
                            <td class="!text-left !pl-5px">
                                <span class="text-black">{{ form.typeName }}</span>
                            </td>
                        </tr>
                    </tbody>
                    <tbody>
                        <tr>
                            <td rowspan="9" style="width: 14%;">资金<br>拨款<br>申请</td>
                            <td>收款单位<span class="star">*</span></td>
                            <td colspan="3" class="node" style="position: relative;padding-left:5px;">
                                <div class="flex items-center gap-2">
                                    <span class="text-black">
                                        <template v-if="accountList.length > 1">
                                            {{ `${accountList.at(0)?.name} 等${accountList.length}人 <见附表>` }}
                                        </template>
                                        <template v-else>
                                            {{ accountList.at(0)?.name }}
                                        </template>
                                    </span>
                                    <div v-if="accountList.length == 1 && auth.hasPermi('fund:paybillAudit:updateSkr')"
                                        class="inline-block hidden hover:block">
                                        <el-button type="danger" link size="default"
                                            @click="handleUpdateAccount(accountList.at(0)!)">修改</el-button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>单位开户银行<span class="star">*</span></td>
                            <td colspan="3" class="node" style="text-align: left;padding-left:5px;">
                                <div class="flex items-center gap-2">
                                    <span class="text-black">{{ accountList.length > 1 ? '<见附表>' :
                                            accountList.at(0)?.bankName }}</span>
                                    <div v-if="accountList.length == 1 && auth.hasPermi('fund:paybillAudit:updateSkr')"
                                        class="inline-block hidden hover:block">
                                        <el-button type="danger" link size="default"
                                            @click="handleUpdateAccount(accountList.at(0)!)">修改</el-button>
                                    </div>
                                </div>

                            </td>
                        </tr>
                        <tr>
                            <td>单位银行账号<span class="star">*</span></td>
                            <td colspan="3" class="node" style="text-align: left;padding-left:5px;">
                                <div class="flex items-center gap-2">
                                    <span class="text-black">
                                        {{ accountList.length > 1 ? '<见附表>' : accountList.at(0)?.cardNumber }}
                                    </span>
                                    <div v-if="accountList.length == 1 && auth.hasPermi('fund:paybillAudit:updateSkr')"
                                        class="inline-block hidden hover:block">
                                        <el-button type="danger" link size="default"
                                            @click="handleUpdateAccount(accountList.at(0)!)">修改</el-button>
                                    </div>
                                </div>

                            </td>
                        </tr>

                        <tr>
                            <td>本次申请拨款<span class="star">*</span></td>
                            <td class="!text-left !pl-5px">
                                <span class="text-black">{{ numberFormat(form.applyAmount, 2) }}</span>

                            </td>
                            <td>拨款类型<span class="star">*</span></td>
                            <td class="!text-left !pl-5px">
                                <span class="text-black">{{ bill_type.find((t: any) => t.value == form.applyType)?.label }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td>申请拨款说明<span class="star">*</span></td>
                            <td colspan="3" style="text-align: left;">
                                <div class="p-1 pl-5px">
                                    <span class="text-4 text-black">{{ form.applyReason }}</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>核准金额</td>
                            <td class="!text-left !pl-5px">
                                <span class="text-black">{{ numberFormat(form.checkAmount, 2) }}</span>
                            </td>
                            <td>项目累计拨款<span class="star">*</span></td>
                            <td class="relative">
                                <div class="flex justify-between pl-5px">
                                    <span class="text-black">{{ numberFormat(form.projPayed, 2) }}</span>
                                    <el-button class="noprint" type="primary" link
                                        @click="projectPayedTableShow = true">查看明细</el-button>
                                </div>

                                <div v-if="form.status == 3" class="absolute bottom-5 right-10 noprint">
                                    <img class="w-50" src="@/assets/images/pay-complete.png"></img>
                                </div>
                            </td>
                        </tr>

                    </tbody>
                    <tbody>
                        <tr>
                            <td rowspan="6" style="width: 14%;">证明<br>材料</td>
                            <td>合同名称</td>
                            <td width="42%" colspan="3" class="!text-left !pl-5px">
                                <span class="text-black">{{ form?.contractName }}</span>
                            </td>
                        </tr>
                        <tr>
                            <td width="1%">
                                <div style="width: 180px;">
                                    合同金额
                                </div>
                            </td>
                            <td width="42%" class="!text-left !pl-5px">
                                <span class="text-black">{{ numberFormat(form.contractAmount, 2) }}</span>
                            </td>
                            <td width="1%">
                                <div style="width: 150px;">
                                    合同累计拨款
                                </div>
                            </td>
                            <td width="42%" class="!pl-5px">
                                <div class="flex justify-between">
                                    <span class="text-black">{{ numberFormat(form.contractPayed, 2) }}</span>
                                    <el-button class="noprint" type="primary" link
                                        @click="contractPayedTableShow = true">查看明细</el-button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td width="1%">
                                <div style="width: 180px;">
                                    财务监理意见<span class="star">*</span>
                                </div>
                            </td>
                            <td width="42%" class="!text-left !pl-5px">
                                <span class="text-black">{{ form.fundOpinion == '1' ? '是' : '否' }}</span>
                            </td>
                            <td width="1%">
                                <div style="width: 150px;">
                                    附件
                                </div>
                            </td>
                            <td width="42%" class="text-left">
                                <div class="flex items-center ml-1">
                                    <DownloadA v-if="FinanceSupervisionFile"
                                        :file="FinanceSupervisionFile">
                                    </DownloadA>
                                </div>
                            </td>
                        </tr>

                    </tbody>

                    <tbody>
                        <tr>
                            <td rowspan="3" style="width: 14%;">审批<br>情况
                            </td>
                            <td colspan="4" style="text-align: left; vertical-align: top; height: 50px;"> 项目管理部门意见
                                {{ getMgrOrgname ? `(${getMgrOrgname})` : '' }}：
                                <template v-for="item in [getAuditInfoBySeqNo(2)]">
                                    <template v-if="item">
                                        <div style="padding: 20px; text-align: center;">
                                            {{ item?.operateReason ?? '同意' }}
                                        </div>
                                        <div style="text-align:right;">
                                            {{ item?.operateTime ? dateFormat(item.operateTime, 'YYYY年MM月DD日') : ''
                                            }}
                                        </div>
                                    </template>
                                </template>


                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left; vertical-align: top; height: 50px;"> 业务处室意见：
                                <div style="padding: 20px; text-align: center;">
                                    <template v-for="item in [getAuditInfoBySeqNo(4)]">
                                        <template v-if="item">
                                            {{ item.operateReason ?? '同意' }}
                                            <div style="text-align:right;">
                                                签字
                                                <img v-if="userSignMap.get(item.operatorUserName ?? '')"
                                                    class="w-80px h-50px"
                                                    :src="userSignMap.get(item.operatorUserName ?? '')" />
                                                <span v-else style="font-size:24px;">{{ item?.operatorName }}</span>
                                            </div>
                                            <div style="text-align:right;">
                                                {{ item.operateTime ? dateFormat(item.operateTime, 'YYYY年MM月DD日') : ''
                                                }}
                                            </div>
                                        </template>

                                    </template>

                                </div>
                            </td>
                            <td colspan="2" style="text-align: left; vertical-align: top; height: 50px;"> 计财处意见：
                                <div style="padding: 20px; text-align: center;">
                                    <template v-for="item in [getAuditInfoBySeqNo(7)]">
                                        <template v-if="item">
                                            {{ item.operateReason ?? '同意' }}
                                            <div style="text-align:right;">
                                                签字 
                                                <img v-if="userSignMap.get(item.operatorUserName ?? '')"
                                                    class="w-80px h-50px"
                                                    :src="userSignMap.get(item.operatorUserName ?? '')" />
                                                <span v-else style="font-size:24px;">{{ item?.operatorName }}</span>
                                            </div>
                                            <div style="text-align:right;">
                                                {{ item.operateTime ? dateFormat(item.operateTime, 'YYYY年MM月DD日') : ''
                                                }}
                                            </div>
                                        </template>

                                    </template>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" style="text-align: left; vertical-align: top; height: 50px;"> 分管领导审核意见：
                                <div style="padding: 20px; text-align: center;">
                                    <template v-for="item in [getAuditInfoBySeqNo(8)]">
                                        <template v-if="item">
                                            {{ item.operateReason ?? '同意' }}
                                            <div style="text-align:right;">
                                                签字 
                                                <img v-if="userSignMap.get(item.operatorUserName ?? '')"
                                                    class="w-80px h-50px"
                                                    :src="userSignMap.get(item.operatorUserName ?? '')" />
                                                <span v-else style="font-size:24px;">{{ item?.operatorName }}</span>
                                            </div>
                                            <div style="text-align:right;">
                                                {{ item.operateTime ? dateFormat(item.operateTime, 'YYYY年MM月DD日') : ''
                                                }}
                                            </div>
                                        </template>

                                    </template>
                                </div>
                            </td>
                            <td colspan="2" style="text-align: left; vertical-align: top; height: 50px;"> 管委会主任审批意见：
                                <div style="padding: 20px; text-align: center;">
                                    <template v-for="item in [getAuditInfoBySeqNo(9)]">
                                        <template v-if="item">
                                            {{ item.operateReason ?? '同意' }}
                                            <div style="text-align:right;">
                                                签字 
                                                <img v-if="userSignMap.get(item.operatorUserName ?? '')"
                                                    class="w-80px h-50px"
                                                    :src="userSignMap.get(item.operatorUserName ?? '')" />
                                                <span v-else style="font-size:24px;">{{ item?.operatorName }}</span>
                                            </div>
                                            <div style="text-align:right;">
                                                {{ item.operateTime ? dateFormat(item.operateTime, 'YYYY年MM月DD日') : ''
                                                }}
                                            </div>
                                        </template>

                                    </template>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="page-break" style="clear:both;">&nbsp;</div>
                <div v-if="(accountList?.length ?? 0) > 1">
                    <div class="header" style="text-align:left;">附表：{{ form.serialNumber }}收款人清单</div>
                    <table class="!border-[3px] border-solid border-black">
                        <thead>
                            <tr>
                                <td width="1%">
                                    <div style="width:30px; text-align:center;">#</div>
                                </td>
                                <td width="25%">收款人</td>
                                <td width="30%">开户银行</td>
                                <td width="30%">银行帐号</td>
                                <td width="15%">金额</td>
                            </tr>
                        </thead>
                        <tbody class="!border-[1px]">

                            <tr v-for="(item, index) in accountList">
                                <td>{{ index + 1 }}</td>
                                <td>
                                    <el-link v-if="auth.hasPermi('fund:paybillAudit:updateSkr')" type="primary"
                                        class="cursor-pointer text-4" @click="handleUpdateAccount(item)">{{ item.name
                                        }}</el-link>
                                    <span v-else>{{ item.name }}</span>
                                </td>
                                <td>{{ item.bankName }}</td>
                                <td>{{ item.cardNumber }}</td>
                                <td>{{ item.amount }}</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4">合计（共 {{ accountList.length }} 人）</td>
                                <td>{{sumAmount(accountList.map(t => t.amount))}}</td>
                            </tr>
                        </tfoot>

                    </table>
                </div>

            </div>
            <div class="w-[300px]">

                <div class="mt-13">
                    <FileManage :bill-form="form" :hqdp-file-list="hqdpFileList" type="view"></FileManage>
                </div>
            </div>
        </div>
    </el-scrollbar>
    <!-- <div class="absolute bottom-0 left-10 h-[60px] flex items-center z-100">
       
    </div> -->
    <div
        :class="$attrs.bottomClass ?? 'absolute bottom-0 left-0 h-[60px] flex justify-center items-center bg-[#f6fbfd]/90 w-full z-100 border-t-solid border-[#dfeaf4]'">
        <el-popover placement="top" :width="800" trigger="click">
            <template #reference>
                <el-button style="margin-right: 16px">审核流程</el-button>
            </template>
            <el-table :data="hqdpAuditList.concat(auditNodeList)" max-height="500px" border>
                <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
                <el-table-column prop="nodeName" label="流程节点" align="center" width="150"></el-table-column>
                <el-table-column prop="operatorName" label="审批人" align="center" width="120"></el-table-column>
                <el-table-column prop="operateTime" label="审批时间" align="center" width="120"
                    :formatter="(row: any) => dateFormat(row.operateTime)"></el-table-column>
                <el-table-column label="审批结果" align="center" width="100">
                    <template #default="{ row }">
                        <span v-if="row.operateResult == EAuditType.pass">{{
                            getAuditTypeName(EAuditType.pass) }}</span>
                        <span v-if="row.operateResult == EAuditType.reject">{{
                            getAuditTypeName(EAuditType.reject) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="operateReason" label="审批意见" align="center"></el-table-column>
            </el-table>
        </el-popover>
        <template v-if="showAudit">
            <el-button v-if="form.canDoRecall" type="warning" class="w-20" :loading="loading"
                @click="handleRecall">错发收回</el-button>
            <el-button v-if="form.canDoAudit" type="warning" class="w-20" @click="handleAudit">审批</el-button>
        </template>

        <el-button type="success" class="w-20" v-print="{ id: '#printTable' }">打印</el-button>
        <el-button type="danger" class="w-20 ml-2" @click="emit('cancel')">关闭</el-button>
    </div>
    <el-dialog title="合同累计拨款查询" v-model="contractPayedTableShow" width="800px" :close-on-click-modal="false"
        destroy-on-close>
        <PayedTable :pay-list="getContractDetailList"></PayedTable>
    </el-dialog>
    <el-dialog title="项目累计拨款查询" v-model="projectPayedTableShow" width="800px" :close-on-click-modal="false"
        destroy-on-close>
        <PayedTable :pay-list="getProjectDetailList"></PayedTable>
    </el-dialog>
    <el-dialog title="拨款单审批" v-model="auditFormShow" width="500px" :close-on-click-modal="false" destroy-on-close>
        <AuditForm :audit-info="auditInfo!" :show-audit="true" @close="emit('close')" @to-next="handleToNext" />
        <footer class="el-dialog__footer h-10"></footer>
    </el-dialog>
    <el-dialog title="收款信息修改" v-model="accountUpdateShow" width="700px" :close-on-click-modal="false" destroy-on-close>
        <AccountUpdate :billId="formId!" :accountInfo="accountInfo!"
            @close="accountUpdateShow = false; getDetail(formId!)" @cancel="accountUpdateShow = false"></AccountUpdate>
        <footer class="el-dialog__footer h-10"></footer>
    </el-dialog>
</template>

<script setup lang="ts">
import PayedTable from './payedTable.vue';
import { savePayBill, getBillPaidInfoByContractId, getBillPaidInfoByProjId, getPayBillDetail, submitPayBill, getBillAttachmentsByProjId, getHqdpContent } from '@/api/fund/paybill';
import { dayjs, ElMessage, ElMessageBox } from 'element-plus';
// @ts-ignore
import useUserStore from '@/store/modules/user'
import { checkIsNumber, dateFormat, genSnowId, numberFormat, sumAmount } from '@/utils/common';
import { getFileList1 } from '@/api/lib/project/file';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
import { BigNumber } from 'bignumber.js';
import { auditPayBill, getPayBillAuditNodeList, recallPayBill } from '@/api/fund/paybillAudit';
import AuditForm from './auditForm.vue';
import { EAuditType, getAuditTypeName, projectManagerDeptList } from '@/utils/constants';
import { WatchStopHandle } from 'vue';
import { useUserSignStore } from '@/store/modules/userSign';
import { groupBy, range } from 'lodash';
import AccountUpdate from './accountUpdate.vue';
import auth from '@/plugins/auth';
import FileManage from './fileManage.vue';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_attr, bill_type, project_type, bill_file_type } = proxy.useDict("project_attr", "bill_type", 'project_type', 'bill_file_type');

const { userSignMap } = useUserSignStore()

const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    formId?: string;
    showAudit?: boolean;
}>()

const userStore = useUserStore()
const accountList = ref<setAmountType[]>([])
const projectPaidInfoList = ref<FundPaybillPaidInfoVo[]>([])
const contractPaidInfoList = ref<FundPaybillPaidInfoVo[]>([])
const auditNodeList = ref<FundPaybillNode[]>([])
const form = reactive<FundPaybillVo>({
    id: genSnowId(),
    fundOpinion: '0',
    applyDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    applyOrgid: userStore.deptId,
    applyOrgname: userStore.deptName,
    auditOrgid: userStore.deptId,
})

let projectWatch: WatchStopHandle | null = null;
let contractWatch: WatchStopHandle | null = null;
const hqdpFileList = ref<(ISysAttachment & { children: ISysAttachment[] })[]>([]);
const hqdpAuditList = ref<FundPaybillNode[]>([])
const getDetail = async (billId: string) => {
    if (projectWatch) projectWatch()
    if (contractWatch) contractWatch()
    getPayBillDetail(billId).then(res => {
        Object.assign(form, res.data)
        form.planYear = form.planYear
        form.projAmount = form.projAmount
        accountList.value = form.skrs?.map(item => {
            return {
                id: item.id,
                accountId: item.accountId ?? '',
                name: item.accountName ?? '',
                bankName: item.bankName ?? '',
                cardNumber: item.accountNumber ?? '',
                amount: item.amount ?? 0,
                stationCode: item.stationCode,
                isPublic: (form.skrs?.length ?? 0) > 1
            }
        }) ?? []
        // projectPaidInfoList.value = form.projPaybillList?.map(item => {
        //     return {
        //         id: item.id ?? '',
        //         serialNumber: item.serialNumber ?? '',
        //         payAmount: item.amount,
        //         payDate: item.details?.at(0)?.payDate ?? '',
        //         payVoucher: item.details?.at(0)?.payVoucher ?? '',
        //     }
        // }) ?? []
        // contractPaidInfoList.value = form.contractPaybillList?.map(item => {
        //     return {
        //         id: item.id ?? '',
        //         serialNumber: item.serialNumber ?? '',
        //         payAmount: item.amount,
        //         payDate: item.details?.at(0)?.payDate ?? '',
        //         payVoucher: item.details?.at(0)?.payVoucher ?? '',
        //     }
        // }) ?? []

        getFileList1(form.id!, 财务监理意见).then(res => {
            FinanceSupervisionFile.value = res.data?.at(0);
        })

        getPayBillAuditNodeList(form.id!).then(res => {
            auditNodeList.value = res.data?.filter(item => item.isMultiClaim != true) ?? []
            const isMultiClaimList = res.data?.filter(item => item.isMultiClaim)
            if (isMultiClaimList && isMultiClaimList.length > 0) {
                auditNodeList.value.push({
                    ...isMultiClaimList.at(0),
                    operatorName: isMultiClaimList.map(t => t.operatorName).join('，')
                })
            }
        })


    }).then(() => {
        // 获取项目累计拨款明细
        projectWatch = watch(() => form.projId, () => {
            if (form.projId) {
                getBillPaidInfoByProjId(form.projId!).then(res => {
                    projectPaidInfoList.value = res.data ?? []
                })
            }
        }, {immediate: true})
        // 获取合同累计拨款
        contractWatch = watch(() => form.contractId, () => {
            if (form.contractId) {
                getBillPaidInfoByContractId(form.contractId).then(res => {
                    contractPaidInfoList.value = res.data ?? []
                })
            }
        }, {immediate: true})
    })
    getHqdpContent(billId).then(res => {
        if (res.data?.auditInfo) {
            res.data?.auditInfo.forEach(item => {
                hqdpAuditList.value.push({
                    nodeName: item.opName,
                    operatorName: item.opUser,
                    operateTime: item.opTime,
                    operateReason: item.opReason,
                })
            })
        }
        if (res.data?.attachInfo && res.data.attachInfo.length > 0) {
            hqdpFileList.value.push({
                id: '100',
                fileName: '高质量附件',
                children: res.data?.attachInfo.map(t => {
                    return {
                        isHqdp: true,
                        fileName: t.fileName,
                        fileUrl: t.fileUrl,
                        index: t.sortIndex
                    }
                })
            })
        }
    })
}
if (props.formId) {
    getDetail(props.formId)
}

const getProjectDetailList = computed(() => {
    return [
        {
            id: form.paidInfo?.id,
            serialNumber: form.serialNumber ?? dayjs(form.applyDate).format('YYYYMMDDHHmmss'),
            payAmount: checkIsNumber(form.checkAmount) ? form.checkAmount : form.applyAmount,
            payVoucher: form.paidInfo?.payVoucher,
        },
        ...projectPaidInfoList.value,

    ]
})

const getContractDetailList = computed(() => {
    return [
        {
            id: form.paidInfo?.id,
            serialNumber: form.serialNumber ?? dayjs(form.applyDate).format('YYYYMMDDHHmmss'),
            payAmount: checkIsNumber(form.checkAmount) ? form.checkAmount : form.applyAmount,
            payVoucher: form.paidInfo?.payVoucher,
        },
        ...contractPaidInfoList.value,

    ]
})




/** 项目管理单位 name */
const getMgrOrgname = computed(() => {
    return projectManagerDeptList.value.find(t => t.deptId == form.mgrOrgid)?.deptName
})

// 设置项目属性的中文名
watch(() => form.typeId, () => {
    if (form.typeId) {
        form.typeName = project_attr.value.find((t: any) => t.value == form.typeId)?.label ?? ''
    }
})

const contractPayedTableShow = ref(false);
const projectPayedTableShow = ref(false);

const 财务监理意见 = '财务监理意见'
const FinanceSupervisionFile = ref<ISysAttachment>();

const loading = ref(false)
const handleRecall = () => {
    ElMessageBox.confirm('是否确认错发收回？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
    }).then(res => {
        loading.value = true
        recallPayBill(form.id!).then(res => {
            ElMessage.success('收回成功！')
            emit('close')
        }).finally(() => {
            loading.value = false
        })
    })
}


const auditFormShow = ref(false)
const auditInfo = ref<{ billId: string, checkAmount: number }>()
const handleAudit = () => {
    auditInfo.value = {
        billId: form.id!,
        checkAmount: checkIsNumber(form.checkAmount) ? form.checkAmount : form.applyAmount!
    }
    auditFormShow.value = true
}

const handleToNext = (nextId: string) => {
    getDetail(nextId)
    auditFormShow.value = false
}

const getAuditInfoBySeqNo = (seqNo: number) => {
    for (let i = auditNodeList.value.length - 1; i >= 0; i--) {
        const info = auditNodeList.value[i]
        if ((info?.nodeSeq ?? 0) < seqNo) {
            return
        }
        if (info?.nodeSeq == seqNo && info.operatorUserName && info.operateTime) {
            return info
        }
    }
}

const accountUpdateShow = ref(false)
const accountInfo = ref<setAmountType>()
const handleUpdateAccount = (account: setAmountType) => {
    accountUpdateShow.value = true
    accountInfo.value = account
}
</script>

<style scoped>
.apply-table {
    width: 800px;
}

.apply-table .header {
    font-size: 24px;
    font-family: 黑体;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
}

.apply-table .date {
    font-size: 16px;
    /* font-family: 宋体; */
}

.blink {
    margin: 10px 0;
    color: red;
    font-weight: bold;
    animation: blink 2s infinite;
}

.apply-table table {
    /* border: 5px solid #000; */
    width: 800px;
    border-collapse: collapse;
    font-size: 16px;
}

.apply-table tbody {
    border: 3px solid #000;
    background-color: #fff;
}

.apply-table td {
    border: 1px solid #000;
    padding: 5px 0;
    text-align: center;
}

span.star {
    color: Red;
    font-weight: bold;
    font-size: 18px;
    vertical-align: middle;
}

input[type="text"] {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    text-indent: 5px;
    font-size: 16px;
    outline: none;
}

textarea {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100px - 4px);
    padding: 5px 8px;
    font-family: Arial;
    text-indent: 5px;
    resize: none;
    font-size: 16px;
    outline: none;
}

select {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    font-size: 16px;
    outline: none;
}

.node:hover .hidden {
    display: block;
    /* 鼠标悬浮时显示 */
}

@media print {
    .noprint {
        display: none;
    }

    .print {
        display: block;
    }

    .page-break {
        page-break-after: always;
        /* 在该元素之后强制分页 */
    }
}
</style>