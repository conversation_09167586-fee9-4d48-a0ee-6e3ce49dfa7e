<template>
  <el-table :data="dataList" height="500px" border show-summary :summary-method="getSummaries" v-loading="loading">
    <!-- 序号 -->
    <el-table-column type="index" label="序号" width="100" align="center" />

    <!-- 拨款单编号（带链接样式） -->
    <el-table-column prop="serialNumber" label="拨款单编号" align="center">
      <template #default="scope">
        <el-link v-if="scope.row.serialNumber" type="primary" underline="hover" @click="handleView(scope.row)">
          {{ scope.row.serialNumber }}
        </el-link>
        <el-link v-else type="primary" underline="hover" @click="handleView(scope.row)">
          {{ dateFormat( scope.row.applyDate, 'YYYYMMDDHHmmss') }}
        </el-link>
      </template>
    </el-table-column>

    <!-- 拨付金额 -->
    <el-table-column prop="payAmount" label="拨付金额" width="120" header-align="center" align="right"
      :formatter="(row: any) => numberFormat(row.payAmount, 2)" />

    <!-- 拨付日期 -->
    <el-table-column prop="payDate" label="拨付日期" width="160" align="center"
      :formatter="(row: any) => dateFormat(row.payDate)" />

    <!-- 财务凭证 -->
    <el-table-column prop="payVoucher" label="财务凭证" width="160" align="center" />
  </el-table>

  <el-dialog title="拨款单查看" v-model="formViewShow" width="1200px" :close-on-click-modal="false" destroy-on-close
    @close="currentFormId = ''">
    <payBillFormView :form-id="currentFormId!" :show-audit="false" @cancel="formViewShow = false"
      @close="formViewShow = false;" />
    <footer class="el-dialog__footer h-10"></footer>
  </el-dialog>
</template>

<script setup lang="tsx">
import { getBillPaidInfoByProjId } from '@/api/fund/paybill';
import { dateFormat, numberFormat, sumAmount } from '@/utils/common';
import { TableColumnCtx } from 'element-plus';
import payBillFormView from './payBillFormView.vue';


const props = defineProps<{
  projId?: string
  payList?: FundPaybillPaidInfoVo[]
}>()
const loading = ref(false)
const dataList = ref<FundPaybillPaidInfoVo[]>(props.payList ?? [])
if (props.projId) {
  loading.value = true
  getBillPaidInfoByProjId(props.projId).then(res => {
    dataList.value = res.data ?? []
  }).finally(() => loading.value = false)
}

const currentFormId = ref<string>()
const formViewShow = ref(false)
const handleView = (row: FundPaybillVo) => {
  if (row.id) {
    currentFormId.value = row.id
    formViewShow.value = true
  }

}

interface SummaryMethodProps<T = FundPaybillVo> {
  columns: TableColumnCtx[]
  data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns, data } = param
  const sums: (string | VNode | number | null)[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = (
        <div class="text-center">
          {`合计`}
        </div>
      )
    }
    if (index === 2) {
      sums[index] = numberFormat(sumAmount(dataList.value?.map(item => item.payAmount)), 2)

    }

  })
  return sums as any
}
</script>
