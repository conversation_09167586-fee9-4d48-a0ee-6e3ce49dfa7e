<template>
    <div>
        <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true" label-position="top">

            <el-descriptions :column="1" label-width="25%" border>
                <el-descriptions-item label="核准金额（元）" width="80%">
                    <el-input-number v-model="form.checkAmount" :min="0" controls-position="right" />
                </el-descriptions-item>
                <el-descriptions-item label="理由" width="80%">
                    <el-form-item prop="operateReason" class="!mb-0">
                        <el-input type="textarea" v-model="form.operateReason" :rows="5" maxlength="1000"></el-input>
                    </el-form-item>

                </el-descriptions-item>
            </el-descriptions>
        </el-form>


        <div>
            <el-checkbox v-model="form.sendSms" :true-value="1" :false-value="0">短信通知</el-checkbox>
            <el-checkbox v-model="form.auditNext" :true-value="1" :false-value="0">继续下一个</el-checkbox>
        </div>

        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="success" class="w-20" :loading="loading" @click="handleAgreen">同意</el-button>
            <el-button type="danger" class="w-20" :loading="loading" @click="handleReject">退回</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { auditPayBill } from '@/api/fund/paybillAudit';
import { ElLoading, ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';

const emit = defineEmits(['close', 'toNext']);
const props = defineProps<{
    auditInfo: {
        billId: string;
        checkAmount: number
    }
}>();

const loading = ref(false)
const form = reactive<FundPaybillDoAuditDto>({
    billId: props.auditInfo.billId,
    checkAmount: props.auditInfo.checkAmount,
    operateResult: 1,
    operateReason: '',
    sendSms: 1,
    auditNext: 1
})

const rules: FormRules = {
    operateReason: [{ required: true, message: '请输入理由', trigger: 'blur' }],
}
const formRef = ref<FormInstance>()

const handleAgreen = () => {
    ElMessageBox.confirm(`您的意见为“同意”，是否确认？`, '提示', {
        type: 'warning'
    }).then(() => {
        form.operateResult = 1
        loading.value = true
        auditPayBill(form).then(res => {
            ElMessage.success('审核成功');
            if (form.auditNext && res.data) {
                const loadingInstance1 = ElLoading.service({ fullscreen: true, text: '当前拨款单审批完成，3秒后进入下一个待审批拨款单！' })
                setTimeout(() => {
                    emit('toNext', res.data)
                    loadingInstance1.close()
                }, 3000);
            } else {
                emit('close')
            }
        }).finally(() => loading.value = false)
    })
}
const handleReject = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            ElMessageBox.confirm(`您的意见为“退回”，是否确认？`, '提示', {
                type: 'warning'
            }).then(() => {
                form.operateResult = 0

                loading.value = true
                auditPayBill(form).then(res => {
                    ElMessage.success('退回成功');
                    if (form.auditNext && res.data) {
                        const loadingInstance1 = ElLoading.service({ fullscreen: true, text: '当前拨款单审批完成，3秒后进入下一个待审批拨款单！' })
                        setTimeout(() => {
                            emit('toNext', res.data)
                            loadingInstance1.close()
                        }, 3000);
                    } else {
                        emit('close')
                    }

                }).finally(() => loading.value = false)
            })
        }
    })

}
</script>