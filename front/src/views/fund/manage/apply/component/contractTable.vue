<template>
    <div>
        <!-- 查询区域 -->
        <el-form :inline="true" :model="searchForm" class="query-form">
            <el-form-item label="甲方">
                <el-input v-model="searchForm.parta" placeholder="请输入甲方" />
            </el-form-item>
            <el-form-item label="乙方">
                <el-input v-model="searchForm.partb" placeholder="请输入乙方" />
            </el-form-item>
            <el-form-item>
                <el-button type="success" @click="handleSearch">查询</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleCreate">新建合同</el-button>
            </el-form-item>
        </el-form>

        <!-- 表格区域 -->
        <el-table :data="tableData" v-loading="loading" height="500px" border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="code" label="合同编号" align="center" />
            <el-table-column prop="name" label="合同名称" align="center" />
            <el-table-column prop="projName" label="所属项目" align="center" />
            <el-table-column prop="partb" label="乙方" align="center" />
            <el-table-column prop="amount" label="合同金额" header-align="center" align="right" width="100"
                :formatter="(row: any) => numberFormat(row.amount)" />
        </el-table>

        <!-- 注释 -->
        <div class="text-red">
            注：点击行内复选框即可选择该合同
        </div>
    </div>
    <el-dialog title="合同编辑" v-model="formEditShow" width="800px" :close-on-click-modal="false" destroy-on-close>
        <EditForm ref="editFormRef" @cancel="formEditShow = false">
        </EditForm>
        <footer class="el-dialog__footer h-10"></footer>
    </el-dialog>
</template>

<script setup lang="tsx">
import { getContractList } from '@/api/fund/contractInfo'
import { numberFormat } from '@/utils/common';
import EditForm from '@/views/fund/contract/component/editForm.vue'

const emit = defineEmits(['select', 'cancel'])
const props = defineProps<{
    projId: string
}>()

const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const searchForm = reactive<FundContractInfoQuery>({
    projId: props.projId
})
const loading = ref(false)
const total = ref(0)
const tableData = ref<FundContractInfoVo[]>([])

const handleSearch = () => {
    loading.value = true
    getContractList(searchForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()

const handleCreate = () => {
    formEditShow.value = true
}

const handleSelectionChange = (val: FundContractInfoVo[]) => {
    const con = val[0]
    emit('select', {
        id: con.id,
        name: con.name,
        amount: con.amount,
        addUpAmount: con.addUpAmount,
        partb: con.partb
    })
}

const formEditShow = ref(false)
</script>
