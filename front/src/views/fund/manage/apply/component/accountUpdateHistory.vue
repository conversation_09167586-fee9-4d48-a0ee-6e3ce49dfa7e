<template>

    <el-table :data="tableData" v-loading="loading" height="500px" border>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="changeUsername" label="修改人" align="center" width="100px" />
        <el-table-column prop="changeTime" label="修改时间" align="center" width="100px"
            :formatter="(row) => dateFormat(row.changeTime, 'YYYY-MM-DD HH:mm:ss')" />
        <el-table-column prop="changeReason" label="修改原因" align="center" />
        <el-table-column label="修改前" header-align="center" align="left">
            <template #default="{ row }">
                收款人(单位)：{{ row.originalInfo.accountName }} <br />
                开户银行：{{ row.originalInfo.bankName }} <br />
                网点代号：{{ row.originalInfo.stationCode }} <br />
                银行账号：{{ row.originalInfo.accountNumber }} <br />
            </template>
        </el-table-column>
        <el-table-column label="修改后" header-align="center" align="left">
            <template #default="{ row }">
                收款人(单位)：{{ row.changeInfo.accountName }} <br />
                开户银行：{{ row.changeInfo.bankName }} <br />
                网点代号：{{ row.changeInfo.stationCode }} <br />
                银行账号：{{ row.changeInfo.accountNumber }} <br />
            </template>
        </el-table-column>
        <el-table-column prop="files" label="修改凭据" align="center">
            <template #default="{ row }">
                <div class="flex flex-col gap-2 " >
                     <DownloadA v-for="(file, index) in row.files" :file="file" class="break-all border-b border-b-solid"></DownloadA>
                </div>

            </template>
        </el-table-column>
    </el-table>
</template>

<script setup lang="ts">
import { skrChangelist } from '@/api/fund/paybillAudit';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
import { dateFormat } from '@/utils/common';

const props = defineProps<{
    skrId: string
}>()

const loading = ref(false)
const tableData = ref<FundPaybillSkrChangeVo[]>([])

skrChangelist(props.skrId).then(res => {
    tableData.value = res.data ?? []
})
</script>