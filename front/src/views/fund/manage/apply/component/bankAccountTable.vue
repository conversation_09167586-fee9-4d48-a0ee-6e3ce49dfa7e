<template>
    <div class="relative">
        <span class="text-red absolute right-0 top-3">注：“对公帐号”仅可选择一个；“对私帐号”可选择多个，需填写每个帐号的收款金额！</span>
        <el-tabs v-model="activeTab">
            <!-- 对公账号 -->
            <el-tab-pane label="对公账号" name="public">
                <div class="mb-3">
                    <el-input v-model="searchKeyword" placeholder="请输入收款人或银行账号" style="width: 300px" clearable
                        @change="handleSearch" />
                </div>
                <div class="mb-2">
                    <el-table :data="publicAccounts" style="width: 100%" height="500px" border
                        @selection-change="handlePublicSelection">
                        <el-table-column type="radio" width="50" align="center">
                            <template #default="{ row }">
                                <el-radio :modelValue="false" @click="handleSelectPublicAccount(row)"> </el-radio>
                            </template>
                        </el-table-column>
                        <el-table-column type="index" label="序号" width="60" align="center">
                            <template #default="{ row, $index }">
                                {{ (page.pageNum - 1) * page.pageSize + $index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="收款单位" align="center" />
                        <el-table-column prop="bankName" label="开户银行" align="center" />
                        <el-table-column prop="cardNumber" label="银行账号" align="center" width="240" />
                        <el-table-column prop="stationCode" label="网点代码" align="center" width="150" />
                    </el-table>
                    <pagination :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                        @pagination="handleSearch" />
                </div>
            </el-tab-pane>

            <!-- 对私账号 -->
            <el-tab-pane label="对私账号" name="private">
                <div class="mb-3 flex justify-between">
                    <el-input v-model="searchKeywordPerson" placeholder="请输入收款人或银行账号" style="width: 300px" clearable
                        @change="handleSearchPerson" />
                    <div>
                        <el-button @click="clearPrivateSelection">清空</el-button>
                        <el-button type="primary" @click="handleConfirmPrivateSelection">
                            选择完成 ({{ selectedPrivateMap.size }})
                        </el-button>
                    </div>
                </div>
                <div class="mb-2">
                    <el-table ref="privateTable" :data="privateAccounts" style="width: 100%" height="500px" border
                        row-key="id" highlight-current-row>
                        <el-table-column width="50" align="center">
                            <template #default="{ row }">
                                <el-checkbox :modelValue="selectedPrivateMap.has(row.id)"
                                    @change="handleSelectPrivateAccount(row)"></el-checkbox>
                            </template>

                        </el-table-column>
                        <el-table-column type="index" label="序号" width="60" align="center">
                            <template #default="{ row, $index }">
                                {{ (pagePerson.pageNum - 1) * pagePerson.pageSize + $index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="收款人" align="center" />
                        <el-table-column prop="bankName" label="开户银行" align="center" />
                        <el-table-column prop="cardNumber" label="银行账号" align="center" />
                        <el-table-column prop="stationCode" label="网点代码" align="center" width="200" />
                    </el-table>
                    <pagination :total="totalPerson" v-model:page="pagePerson.pageNum"
                        v-model:limit="pagePerson.pageSize" @pagination="handleSearchPerson" />
                </div>
            </el-tab-pane>
        </el-tabs>

        <el-dialog title="收款账户" v-model="payAccountAmountEditShow" width="1000px" :close-on-click-modal="false"
            destroy-on-close>
            <el-table :data="setAmountList" height="500px" border>
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="name" label="收款人" align="center" />
                <el-table-column prop="bankName" label="开户银行" align="center" />
                <el-table-column prop="cardNumber" label="银行账号" align="center" />
                <el-table-column prop="bankName" label="开户银行" align="center" />
                <el-table-column label="金额(单位：元)" align="center">
                    <template #default="{ row }">
                        <el-input-number v-model="row.amount" :min="0" controls-position="right" />
                    </template>
                </el-table-column>
            </el-table>
            <template #footer>
                <div class="flex items-center justify-between">
                    <div>
                        <span>共 {{ setAmountList.length }} 人，总金额：{{ numberFormat(sumAmount(setAmountList.map(t => t.amount))??0) }} 元</span>
                    </div>
                    <div>
                        <el-button @click="$emit('cancel')">取消</el-button>
                        <el-button type="primary" @click="handleConfirmAmount">确定</el-button>
                    </div>
                </div>

            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { getBankAccountList } from '@/api/fund/paybill'
import { numberFormat, sumAmount } from '@/utils/common';
import { ElMessage, TableInstance } from 'element-plus'
import { ref, computed } from 'vue'

const emit = defineEmits(['select', 'cancel'])
const props = defineProps<{
    activateTab: 'public' | 'private'
    selectList: setAmountType[]
}>()

const activeTab = ref(props.activateTab ?? 'public')

const page = reactive({
    pageNum: 1,
    pageSize: 10
})

const searchKeyword = ref('')
const total = ref(0)
// 对公账号数据（单选）
const publicAccounts = ref<FundBankAccountVo[]>([])
const handleSearch = () => {
    getBankAccountList({ isSpecial: '0', typeName: '机构', cardNameNumber: searchKeyword.value, ...page }).then(res => {
        publicAccounts.value = res.rows ?? []
        total.value = res.total ?? 0
    })
}
handleSearch()

const selectedPublic = ref<any[]>([])

function handlePublicSelection(selection: any[]) {
    selectedPublic.value = selection
}

const privateTable = ref<TableInstance>()
const pagePerson = reactive({
    pageNum: 1,
    pageSize: 10
})
const totalPerson = ref(0)
// 对私账号数据（多选）
const privateAccounts = ref<FundBankAccountVo[]>([])
const searchKeywordPerson = ref('')
const loading = ref(false)
const handleSearchPerson = () => {
    loading.value = true
    getBankAccountList({ typeName: '个人', cardNameNumber: searchKeywordPerson.value, ...pagePerson }).then(res => {
        privateAccounts.value = res.rows ?? []
        totalPerson.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
handleSearchPerson()

const selectedPrivateMap = ref<Map<string, setAmountType>>(
    new Map(props.selectList.filter(t => props.activateTab == 'private').map(item => {
        return [item.accountId, { ...item }]
    }))
)
const handleSelectPrivateAccount = (val: FundBankAccountVo) => {
    if (selectedPrivateMap.value.has(val.id!)) {
        selectedPrivateMap.value.delete(val.id!)
    } else {
        selectedPrivateMap.value.set(val.id!, {
            accountId: val.id!,
            name: val.name!,
            bankName: val.bankName!,
            cardNumber: val.cardNumber!,
            amount: null,
            isPublic: false
        })
    }
}
const clearPrivateSelection = () => {
    selectedPrivateMap.value.clear()
}

const handleSelectPublicAccount = (val: FundBankAccountVo) => {
    emit('select', [
        {
            accountId: val.id!,
            name: val.name!,
            bankName: val.bankName!,
            cardNumber: val.cardNumber!,
            isPublic: true
        }
    ])
}

const setAmountList = ref<setAmountType[]>([])
const payAccountAmountEditShow = ref(false)
const handleConfirmPrivateSelection = () => {
    console.log(selectedPrivateMap.value.size)
    if (selectedPrivateMap.value.size > 1) {
        payAccountAmountEditShow.value = true
        setAmountList.value = Array.from(selectedPrivateMap.value.values())
    } else {
        if (selectedPrivateMap.value.size == 1) {
            emit('select', Array.from(selectedPrivateMap.value.values()))
        } else {
            ElMessage.warning('至少选择一个收款人！')
        }

    }
}

const handleConfirmAmount = () => {
    emit('select', setAmountList.value)
}

</script>
