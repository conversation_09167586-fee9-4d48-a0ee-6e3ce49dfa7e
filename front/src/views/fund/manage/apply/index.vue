<template>
    <div class="app-container">
        <!-- 搜索区域 -->
        <el-form label-width="auto" :inline="true">
            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="w-40" />
            </el-form-item>
            <el-form-item label="项目类别">
                <ProjectTypeSelect v-model="searchForm.projType" placeholder="请选择项目类别" class="!w-40" clearable>
                </ProjectTypeSelect>
            </el-form-item>
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.applyOrgid" clearable class="!w-40"></DeptSelect>
            </el-form-item>
            <el-form-item label="收款单位">
                <el-input v-model="searchForm.accountName" placeholder="请输入收款单位" clearable class="w-40" />
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择" clearable class="w-40">
                    <el-option v-for="item in bill_status" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="申请日期">
                <DateRangePicker v-model:begin-date="searchForm.applyDateStart"
                    v-model:end-date="searchForm.applyDateEnd" type="daterange" start-placeholder="开始日期"
                    end-placeholder="结束日期" range-separator="至" format="YYYY/MM/DD" value-format="YYYY-MM-DD"
                    class="!w-60" clearable>
                </DateRangePicker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="success" @click="handleCreate">新建拨款单</el-button>
                <el-button type="danger" @click="handleExport">数据导出</el-button>
            </el-form-item>
        </el-form>

        <div v-loading="loading">
            <!-- 表格区域 -->
            <el-table :data="tableData" border show-summary :summary-method="getSummaries">
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="projName" label="项目名称" align="center" min-width="300px" fixed="left">
                    <template #default="{ row }">
                        <div class="text-left">
                            <div>
                                <el-link type="primary" @click="handleProjectPayedShow(row)">{{ row.projName }}</el-link>
                            </div>
                            <el-tag>{{ row.applyOrgname }}</el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="projType" label="支付分类" align="center" width="150" />
                <el-table-column prop="applyType" label="拨款类型" align="center" width="150">
                    <template #default="{ row }">
                        <dict-tag :options="bill_type" :value="row.applyType" />
                    </template>
                </el-table-column>
                <el-table-column prop="receivingUnit" label="收款单位" align="center" min-width="150px">
                    <template #default="{ row }">
                        <template v-if="row.skrs && row.skrs.length > 1">
                            {{ `${row.skrs.at(0).accountName} 等${row.skrs.length}人 <见附表>` }}
                        </template>
                        <template v-else>
                            {{ row.skrs.at(0)?.accountName }}
                        </template>
                    </template>
                </el-table-column>
                <el-table-column prop="applyAmount" label="申请金额" header-align="center" align="right" width="150"
                    :formatter="(row: any) => numberFormat(row.applyAmount, 2)" />
                <el-table-column prop="checkAmount" label="核准金额" header-align="center" align="right" width="150"
                    :formatter="(row: any) => numberFormat(row.checkAmount, 2)" />
                <el-table-column prop="applyDate" label="申请日期" align="center" width="150"
                    :formatter="(row: any) => dateFormat(row.applyDate)" />
                <!-- <el-table-column prop="status" label="状态" align="center" width="100">
                    <template #default="{ row }">
                        <dict-tag :options="bill_status" :value="row.status" />
                    </template>
                </el-table-column> -->
                <el-table-column prop="statusInfo" label="状态" align="center" width="100">
                </el-table-column>
                <el-table-column label="操作" width="150" align="center" fixed="right">
                    <template #default="{ row }">
                        <template v-if="[EBillStatus.draft, EBillStatus.reject].includes(row.status)">
                            <el-button type="primary" link icon="view" @click="handleView(row)">查看</el-button>
                            <el-button v-if="row.applyUserid == userStore.id" type="primary" link icon="edit" @click="handleEdit(row)">编辑</el-button>
                            <el-button v-if="EBillStatus.draft == row.status && row.applyUserid == userStore.id" type="danger" link icon="delete"
                                @click="handleDelete(row)">删除</el-button>
                        </template>
                        <template v-else>
                            <el-button type="primary" link icon="view" @click="handleView(row)">查看</el-button>
                        </template>

                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>

        <!-- 编辑弹窗 -->
        <el-dialog title="拨款单编辑" v-model="formEditShow" width="1200px" :close-on-click-modal="false" destroy-on-close
            @close="currentFormId = ''">
            <PayBillFormEdit :form-id="currentFormId" @cancel="formEditShow = false; handleSearch()"
                @close="formEditShow = false; handleSearch()"
                bottomClass="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center" />


            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog title="拨款单查看" v-model="formViewShow" width="1200px" :close-on-click-modal="false" destroy-on-close
            @close="currentFormId = ''">
            <payBillFormView :form-id="currentFormId!" :show-audit="false" @cancel="formViewShow = false"
                @close="formViewShow = false; handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog title="项目拨付明细" v-model="projectPayedTableShow" width="800px" :close-on-click-modal="false"
            destroy-on-close>
            <PayedTable :proj-id="currentProjectId"></PayedTable>
        </el-dialog>
    </div>
</template>

<script setup lang="tsx">
import PayBillFormEdit from './component/payBillFormEdit.vue'
import payBillFormView from './component/payBillFormView.vue'
import { dayjs, ElMessage, ElMessageBox, TableColumnCtx } from 'element-plus'
import { getPayBillList, getPayBillListAmount, removePayBill } from '@/api/fund/paybill'
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
import { format } from 'echarts'
import { dateFormat, numberFormat } from '@/utils/common'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { EBillStatus } from '@/utils/constants'
import PayedTable from './component/payedTable.vue';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
// @ts-ignore
import useUserStore from '@/store/modules/user'

const { proxy } = getCurrentInstance() as { proxy: any };
const { bill_type, bill_status } = proxy.useDict("bill_type", 'bill_status');

const router = useRouter()
const userStore = useUserStore()

const searchForm = reactive<FundPaybillQuery>({
    applyDateStart: dayjs().year() + '-01-01',
    applyDateEnd: dayjs().year() + '-12-31',
})

const page = reactive({
    pageNum: 1,
    pageSize: 10
})

const total = ref(0)
const tableData = ref<FundPaybillVo[]>([])
const tableAmount = ref<{ totalApplyAmount: number, totalCheckAmount: number }>()

const loading = ref(false)
const handleSearch = () => {
    loading.value = true
    Promise.all([
        getPayBillList(searchForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getPayBillListAmount(searchForm).then(res => {
            tableAmount.value = res.data
        })
    ]).finally(() => {
        loading.value = false
    })

}
handleSearch()

const handleCreate = () => {
    router.push({ path: 'apply/form' })
    // formEditShow.value = true
}



const currentFormId = ref<string>()
const formEditShow = ref(false)
const handleEdit = (row: FundPaybillVo) => {
    currentFormId.value = row.id
    formEditShow.value = true
}

const formViewShow = ref(false)
const handleView = (row: FundPaybillVo) => {
    currentFormId.value = row.id
    formViewShow.value = true
}

const currentProjectId = ref<string>()
const projectPayedTableShow = ref(false);
const handleProjectPayedShow = (row: FundPaybillVo) => {
    currentProjectId.value = row.projId
    projectPayedTableShow.value = true
}

const handleDelete = (row: FundPaybillVo) => {
    ElMessageBox.confirm('是否确认删除拨款单?', '提示', {
        type: 'warning'
    })
        .then(() => {
            removePayBill([row.id!]).then(res => {
                ElMessage.success('删除成功');
                handleSearch()
            })
        })
}

const handleExport = () => {
    proxy.download("/fund/paybill/export", {
        ...searchForm,
    }, `拨款单_${new Date().getTime()}.xlsx`);
}

interface SummaryMethodProps<T = FundPaybillVo> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {
        if (index === 0) {
            sums[index] = (
                <div class="text-left">
                    {`合计`}
                </div>
            )
        }
        if (column.property == 'applyAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalApplyAmount ?? 0, 2)
        }
        if (column.property == 'checkAmount') {
            sums[index] = numberFormat(tableAmount.value?.totalCheckAmount ?? 0, 2)
        }

    })
    return sums as any
}
</script>
