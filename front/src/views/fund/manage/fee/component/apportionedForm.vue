<template>
    <el-form ref="formRef" :model="form" :rules="rules" :inline-message="true">
        <el-descriptions :column="1" label-width="20%" border>
            <el-descriptions-item width="80%">
                <template #label>
                    组号 <span class="text-red">*</span>
                </template>
                <el-form-item prop="groupName" class="!mb-0">
                    <el-select v-model="form.groupName" placeholder="请选择组号">
                        <el-option v-for="item in 10" :label="item" :value="item" />
                    </el-select>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    分摊单位 <span class="text-red">*</span>
                </template>
                <el-form-item prop="orgId" class="!mb-0">
                    <DeptSelect ref="deptSelectRef" v-model="form.orgId" class="!w-50"></DeptSelect>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    分摊份额 <span class="text-red">*</span>
                </template>
                <el-form-item prop="shareWeight" class="!mb-0">
                    <el-input-number v-model="form.shareWeight" :min="0" controls-position="right" placeholder="请输入分摊份额" class="!w-50" />
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    挂接项目 <span class="text-red">*</span>
                </template>
                <el-form-item prop="projId" class="!mb-0">
                    <el-select v-model="form.projId">
                        <el-option v-for="item in projectList" :label="item.name" :value="item.id!"></el-option>
                    </el-select>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="预算金额">
                <el-form-item prop="estTotal" class="!mb-0">
                    <el-input-number v-model="form.estTotal" :min="0" controls-position="right" placeholder="请输入预算金额" class="!w-50" />
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="备注">
                <el-form-item prop="remark" class="!mb-0">
                    <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" :rows="3" maxlength="1000" />
                </el-form-item>
            </el-descriptions-item>
        </el-descriptions>
        <!-- 操作按钮 -->
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" :loading="loading" @click="submitForm">保存</el-button>
            <el-button @click="emit('cancel')">关闭窗口</el-button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { addFeeShare, editFeeShare, getFeeShareDetail, getProjectList } from '@/api/fund/feeShare';
import { ElMessage, FormInstance } from 'element-plus'
import DeptSelect from '@/components/Select/DeptSelect.vue'


const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    formId: string,
    year: string
}>()

const projectList = ref<IProjectInfoVo[]>([])
const form = reactive<FundFeeShare>({
    year: props.year
} as any)
if (props.formId) {
    const res = await getFeeShareDetail(props.formId)
    Object.assign(form, res.data)

    projectList.value.push({name: form.projName, id: form.projId})
}


watch(() => form.orgId, () => {
    getProjectList(props.year, form.orgId).then(res => {
        projectList.value = res.data ?? []
    })
})

const rules = {
    groupName: [{ required: true, message: '请输入组号', trigger: 'blur' }],
    orgId: [{ required: true, message: '请输入分摊单位', trigger: 'blur' }],
    shareWeight: [{ required: true, message: '请输入分摊份额', trigger: 'blur' }],
    projId: [{ required: true, message: '请输入挂接项目', trigger: 'blur' }]
}

const deptSelectRef = ref()
const loading = ref(false)
const formRef = ref<FormInstance>()
const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            let save = addFeeShare
            if (props.formId) {
                save = editFeeShare
            }
            form.orgName = deptSelectRef.value.getDeptList().find((item: any) => item.deptId === form.orgId)?.deptName
            form.projName = projectList.value.find((item: any) => item.id === form.projId)?.name
            loading.value = true
            save(form).then(() => {
                ElMessage.success('保存成功');
                emit('close');
            }).finally(() => {
                loading.value = false
            })
        }
    })
}

</script>
