<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="20%" border>
            <el-descriptions-item width="80%" label="组">
                {{ updateFeeData.groupName }}
            </el-descriptions-item>
            <el-descriptions-item label="分摊单位">
                {{ updateFeeData.orgName }}
            </el-descriptions-item>
            <el-descriptions-item label="月份">
                {{ updateFeeData.shareMonth }}月
            </el-descriptions-item>
            <el-descriptions-item label="分摊金额">
                <el-form-item prop="budget" class="!mb-0">
                    <el-input-number v-model="form.budget" :min="0" controls-position="right" placeholder="请输入预算金额" />
                </el-form-item>
                <span v-if="updateFeeData.changeInfo" class="text-orange">
                    原分摊金额为{{updateFeeData.changeInfo.oldValue}}，修改金额为{{updateFeeData.changeInfo.newValue}}，修改时间为{{dateFormat(updateFeeData.changeInfo.updateTime, 'YYYY-MM-DD HH:mm:ss')}}【<span
                        class="text-[#409EFF] cursor-pointer" @click="recoverAmount">点击还原</span>】
                </span>
            </el-descriptions-item>
        </el-descriptions>
        <!-- 操作按钮 -->
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" :loading="loading" @click="submitForm">保存</el-button>
            <el-button @click="emit('cancel')">关闭窗口</el-button>
        </div>
    </el-form>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus'
import { editFeeShare } from '@/api/fund/feeShare';
import { editFees, recoveryAmount } from '@/api/fund/shareDetail';
import { dateFormat } from '@/utils/common';

const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    year: string
    updateFeeData: {
        shareMonth: string,
        groupName: string,
        orgId: number,
        orgName: string,
        feeAmount: number
        changeInfo: {
            newValue: number,
            oldValue: number,
            updateTime: string,
        }
    }
}>()


const form = ref({
    budget: props.updateFeeData.feeAmount,
})

const rules = {
    budget: [{ required: true, message: '请输入预算', trigger: 'blur' }]
}

const formRef = ref<FormInstance>()
const loading = ref(false)
const recoverAmount = () => {
    ElMessageBox.confirm('是否确认还原？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        loading.value = true
        recoveryAmount({
            year: props.year,
            month: props.updateFeeData.shareMonth,
            groupName: props.updateFeeData.groupName,
            orgId: props.updateFeeData.orgId,
        }).then(res => {
            ElMessage.success('还原成功')
            emit('close')
        }).finally(() => {
            loading.value = false
        })
    })
    
}
const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            loading.value = true
            editFees({
                year: props.year,
                month: props.updateFeeData.shareMonth,
                groupName: props.updateFeeData.groupName,
                orgId: props.updateFeeData.orgId,
                feeAmount: form.value.budget
            }).then(res => {
                ElMessage.success('修改成功')
                emit('close')
            }).finally(() => {
                loading.value = false
            })
        }
    })
}

</script>
