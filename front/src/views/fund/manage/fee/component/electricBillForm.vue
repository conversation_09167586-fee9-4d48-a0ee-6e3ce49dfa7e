<template>
    <div>
        <el-scrollbar height="500px">
        <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
            <el-descriptions :column="1" label-width="20%" border>
                <el-descriptions-item width="80%" label="组">
                    <template #label>
                        月份 <span class="text-red">*</span>
                    </template>
                    <el-date-picker v-model="form.month" placeholder="请选择" value-format="YYYY-MM-DD" type="month"
                        class="!w-50" />
                </el-descriptions-item>
                <el-descriptions-item v-for="item in dataList">
                    <template #label>
                        组{{item.groupName}}总额 <span class="text-red">*</span>
                    </template>
                    <el-form-item class="!mb-0">
                        <el-input-number v-model="item.shareTotal" :min="0" controls-position="right" placeholder="请输入预算金额"
                            class="w-50" />
                    </el-form-item>
                    <el-table :data="groupData[item.groupName]" border class="mt-1">
                        <el-table-column prop="orgName" label="单位" align="center"></el-table-column>
                        <el-table-column prop="shareRate" label="比例" align="center"></el-table-column>
                        <el-table-column label="金额" align="center">
                            <template #default="{row}">
                                {{ div(item.shareTotal, row.shareRate) }}
                            </template>
                        </el-table-column>
                    </el-table>
                </el-descriptions-item>
            </el-descriptions>

        </el-form>
    </el-scrollbar>
    <!-- 操作按钮 -->
    <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
        <el-button type="primary" :loading="loading" @click="submitForm">保存</el-button>
        <el-button @click="emit('cancel')">关闭窗口</el-button>
    </div>
    </div>
</template>

<script setup lang="ts">
import { allocationFees, getElectricityFees } from '@/api/fund/shareDetail'
import { ElMessage, FormInstance } from 'element-plus'
import { groupBy } from 'lodash';
import { BigNumber } from 'bignumber.js';



const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    year: string
}>()

const form = reactive({
    month: props.year + '-01' + '-01',
})

const dataList = ref<any[]>([])
const groupData = ref<any>({})
watch(() => form.month, () => {
    getElectricityFees(form.month).then(res => {
       groupData.value = groupBy(res.data, 'groupName')
       dataList.value = Object.keys(groupData.value).map(key => {
           return {
               groupName: key,
               shareTotal: groupData.value[key]?.at(0).shareTotal
           }
       })
    })
}, {immediate: true})

const div = (num:number, num1: number) => {
    return BigNumber(num??0).multipliedBy(num1/100).toFixed(2)
}

const rules = {
    shareTotal: [{ required: true, message: '请输入总额', trigger: 'blur' }]
}

const loading = ref(false)
const formRef = ref<FormInstance>()
const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            const data = dataList.value.map(item => {
                return {
                    groupName: item.groupName,
                    shareMonth: form.month,
                    shareTotal: item.shareTotal
                }
            })
            loading.value = true
            allocationFees(data as any).then(res => {
                ElMessage.success('分配成功')
                emit('close')

            }).finally(() => {
                loading.value = false
            })
        }
    })
}

</script>
<style scoped>
/* 深度选择器 */
.equal-width-columns :deep(.el-descriptions__table) {
    table-layout: fixed;
}
</style>