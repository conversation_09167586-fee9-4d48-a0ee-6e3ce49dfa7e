<template>
    <div class="app-container">
        <!-- 查询表单 -->
        <el-form label-width="auto" :inline="true">
            <el-form-item label="年度">
                <el-date-picker v-model="searchForm.year" placeholder="请选择年度" type="year"
                    value-format="YYYY"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button v-if="activate == '1'" type="primary" @click="handleAddApportionedForm">增加分摊单位</el-button>
                <el-button v-if="activate == '2'" type="primary" @click="electricBillFormShow = true">电费分摊</el-button>
                <el-button v-if="activate == '2'" type="" @click="handleExport">数据导出</el-button>
            </el-form-item>
        </el-form>

        <el-tabs v-model="activate" type="border-card">
            <el-tab-pane name="1" label="分摊设置">
                <!-- 表格展示 -->
                <el-table :data="tableData" :span-method="objectSpanMethod" border>
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="groupName" label="组" align="center" width="80" />
                    <el-table-column prop="orgName" label="分摊单位" align="center" width="150" />
                    <el-table-column prop="shareWeightStr" label="分摊份额（占比）" align="center" width="150" />
                    <el-table-column prop="projName" label="挂接项目" align="center" min-width="200px" />
                    <el-table-column prop="estTotal" label="预算" header-align="center" align="right" width="130"
                        :formatter="(row: any) => numberFormat(row.estTotal, 2)" />
                    <el-table-column prop="remark" label="备注" align="center" min-width="150px" />
                    <el-table-column label="操作" width="150" align="center">
                        <template #default="{ row }">
                            <el-button type="primary" icon="edit" link
                                @click="handleEditAddApportionedForm(row)">编辑</el-button>
                            <el-button type="danger" icon="delete" link
                                @click="handleAddApportionedRemove(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>
            <el-tab-pane name="2" label="分摊统计">
                <!-- 表格展示 -->
                <el-table :data="tableDetailData" :span-method="objectSpanMethod1"
                    :cell-class-name="bindCellClassName as any" @cell-click="handleCellClick" border>
                    <el-table-column type="index" label="序号" width="60" align="center" fixed="left" />
                    <el-table-column prop="groupName" label="组" align="center" width="80" fixed="left" />
                    <el-table-column prop="orgName" label="分摊单位" align="center" width="100" fixed="left" />
                    <el-table-column prop="january" label="1月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.january, 2)" />
                    <el-table-column prop="february" label="2月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.february, 2)" />
                    <el-table-column prop="march" label="3月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.march, 2)" />
                    <el-table-column prop="april" label="4月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.april, 2)" />
                    <el-table-column prop="may" label="5月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.may, 2)" />
                    <el-table-column prop="june" label="6月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.june, 2)" />
                    <el-table-column prop="july" label="7月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.july, 2)" />
                    <el-table-column prop="august" label="8月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.august, 2)" />
                    <el-table-column prop="september" label="9月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.september, 2)" />
                    <el-table-column prop="october" label="10月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.october, 2)" />
                    <el-table-column prop="november" label="11月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.november, 2)" />
                    <el-table-column prop="december" label="12月" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.december, 2)" />
                    <el-table-column prop="shareAmount" label="分摊" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.shareAmount, 2)" />
                    <el-table-column prop="remark" align="center" width="150" />
                    <el-table-column prop="estTotal" label="预算" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.estTotal, 2)" />
                    <el-table-column prop="overspend" label="超支" header-align="center" align="right" width="100"
                        :formatter="(row: any) => numberFormat(row.overspend, 2)" />
                </el-table>
            </el-tab-pane>
        </el-tabs>


        <el-dialog :title="`分摊单位（年度${searchForm.year}）`" v-model="apportionedFormShow" width="600px"
            :close-on-click-modal="false" destroy-on-close @close="currentFormId = ''">
            <ApportionedForm :year="searchForm.year" :form-id="currentFormId!" @cancel="apportionedFormShow = false"
                @close="apportionedFormShow = false; handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog :title="`分摊金额修改`" v-model="billAmountChangeShow" width="600px" :close-on-click-modal="false"
            destroy-on-close>
            <BillAmountChange :year="searchForm.year" :updateFeeData="updateFeeData!"
                @cancel="billAmountChangeShow = false" @close="billAmountChangeShow = false; handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog :title="`电费分摊（年度${searchForm.year}）`" v-model="electricBillFormShow" width="800px"
            :close-on-click-modal="false" destroy-on-close>
            <ElectricBillForm :year="searchForm.year" @cancel="electricBillFormShow = false"
                @close="electricBillFormShow = false; handleSearch()" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>

</template>

<script setup lang="tsx">
import { dayjs, ElMessage, ElMessageBox } from 'element-plus'
import ApportionedForm from './component/apportionedForm.vue'
import BillAmountChange from './component/billAmountChange.vue'
import ElectricBillForm from './component/electricBillForm.vue'
import { getFeeShareList, removeFeeShare } from '@/api/fund/feeShare'
import { editFees, getFeeShareDetailList } from '@/api/fund/shareDetail'
import { checkIsNumber, numberFormat } from '@/utils/common'

const activate = ref('1')
const searchForm = reactive({
    year: String(dayjs().year()),
})

const tableData = ref<FundFeeShare[]>([])
const tableDetailData = ref<FundShareStatistics[]>([])
const handleSearch = () => {
    getFeeShareList(searchForm.year).then(res => {
        tableData.value = res.data ?? []
    })

    getFeeShareDetailList(searchForm.year).then(res => {
        tableDetailData.value = res.data ?? []
    })
}
watch(() => searchForm.year, handleSearch, { immediate: true })

const currentFormId = ref<string | undefined>('')
const apportionedFormShow = ref(false)
const handleEditAddApportionedForm = (row: FundFeeShare) => {
    apportionedFormShow.value = true
    currentFormId.value = row.id
}

const handleAddApportionedForm = () => {
    apportionedFormShow.value = true
}

const handleAddApportionedRemove = (row: FundFeeShare) => {
    ElMessageBox.confirm('确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        removeFeeShare([row.id!]).then(() => {
            ElMessage.success('删除成功');
            handleSearch();
        });
    })
}

const updateFeeData = ref<{
    shareMonth: string,
    groupName: string,
    orgId: number,
    orgName: string,
    feeAmount: number
    changeInfo: {
        newValue: number,
        oldValue: number,
        updateTime: string,
    }
}>()
const billAmountChangeShow = ref(false)

const monthMap = new Map([
    ['january', '1'],
    ['february', '2'],
    ['march', '3'],
    ['april', '4'],
    ['may', '5'],
    ['june', '6'],
    ['july', '7'],
    ['august', '8'],
    ['september', '9'],
    ['october', '10'],
    ['november', '11'],
    ['december', '12']
])
const handleCellClick = (row: FundShareStatistics, column: { property: keyof FundShareStatistics }) => {
    if (row.orgId && monthMap.has(column.property)) {
        if (checkIsNumber(row[column.property])) {
            updateFeeData.value = {
                shareMonth: monthMap.get(column.property)!,
                groupName: row.groupName!,
                orgId: row.orgId!,
                orgName: row.orgName!,
                feeAmount: row[column.property] as number,
                changeInfo: row.changeMap[column.property]
            }
            billAmountChangeShow.value = true
        }
    }

}
const bindCellClassName = (data: { row: FundShareStatistics, column: { property: keyof FundShareStatistics }, rowIndex: number, columnIndex: number }) => {
    if (data.row.orgId && monthMap.has(data.column.property)) {
        if (data.row.changeMap?.[data.column.property]) {
            return 'text-#F56C6C'
        }
        return 'text-#409EFF'
    }
    return ''
}

const electricBillFormShow = ref(false)

const { proxy } = getCurrentInstance() as { proxy: any };
const handleExport = () => {
    proxy.download("/fund/shareDetail/download", {
        year: searchForm.year,
    }, `分摊统计${new Date().getTime()}.xlsx`);
}

/**
 * 由chatgpt生成
 * @param param0 
 */
const objectSpanMethod = ({
    row,
    column,
    rowIndex,
    columnIndex,
}: {
    row: FundFeeShare
    column: any
    rowIndex: number
    columnIndex: number
}) => {
    // 仅对 indexLevel1 列（第 0 列）进行合并
    if (columnIndex !== 1) return;

    const spanMap = new Map<string, { startIndex: number; count: number }>();
    const valueCount: Record<string, number> = {};

    // 遍历一次，记录每种 indexLevel1 出现的位置和次数
    tableData.value.forEach((item, idx) => {
        const key = item.groupName as any;
        if (!valueCount[key]) {
            valueCount[key] = 1;
            spanMap.set(key, { startIndex: idx, count: 1 });
        } else {
            valueCount[key]++;
            const spanInfo = spanMap.get(key);
            if (spanInfo) {
                spanInfo.count++;
            }
        }
    });

    const key = row.groupName;
    const spanInfo = spanMap.get(key as any);

    if (spanInfo?.startIndex === rowIndex) {
        return {
            rowspan: spanInfo.count,
            colspan: 1,
        };
    } else {
        return {
            rowspan: 0,
            colspan: 0,
        };
    }
};

/**
 * 由chatgpt生成
 * @param param0 
 */
const objectSpanMethod1 = ({
    row,
    column,
    rowIndex,
    columnIndex,
}: {
    row: FundFeeShare
    column: any
    rowIndex: number
    columnIndex: number
}) => {
    // 仅对 indexLevel1 列（第 0 列）进行合并
    if (columnIndex !== 1) return;

    const spanMap = new Map<string, { startIndex: number; count: number }>();
    const valueCount: Record<string, number> = {};

    // 遍历一次，记录每种 indexLevel1 出现的位置和次数
    tableDetailData.value.forEach((item, idx) => {
        const key = item.groupName as any;
        if (!valueCount[key]) {
            valueCount[key] = 1;
            spanMap.set(key, { startIndex: idx, count: 1 });
        } else {
            valueCount[key]++;
            const spanInfo = spanMap.get(key);
            if (spanInfo) {
                spanInfo.count++;
            }
        }
    });

    const key = row.groupName;
    const spanInfo = spanMap.get(key as any);

    if (spanInfo?.startIndex === rowIndex) {
        return {
            rowspan: spanInfo.count,
            colspan: 1,
        };
    } else {
        return {
            rowspan: 0,
            colspan: 0,
        };
    }
};

</script>
