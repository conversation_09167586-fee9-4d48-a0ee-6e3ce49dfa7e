<template>
    <div class="app-container">
        <!-- 查询表单 -->
        <el-form :inline="true" label-width="auto" :model="searchForm" class="filter-form">
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.orgId" clearable class="!w-40"></DeptSelect>
            </el-form-item>

            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="!w-40" />
            </el-form-item>

            <el-form-item label="采购主体">
                <el-select v-model="searchForm.purchaseOrgid" placeholder="请选择" clearable class="!w-40">
                    <el-option v-for="item in purchaseDeptList" :label="item.deptName" :value="item.deptId" />
                </el-select>
            </el-form-item>

            <el-form-item label="采购品目">
                <el-select v-model="searchForm.itemId" placeholder="请选择" clearable class="!w-40">
                    <el-option v-for="item in purchase_catalog" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="采购方式">
                <el-select v-model="searchForm.modeId" placeholder="请选择" clearable class="!w-40">
                    <el-option v-for="item in purchase_mode" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="采购组织形式">
                <el-select v-model="searchForm.styleId" placeholder="请选择" clearable class="!w-40">
                    <el-option v-for="item in purchase_form" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>



            <el-form-item label="采购编码">
                <el-input v-model="searchForm.purchaseCode" placeholder="请输入采购编码" class="!w-40" />
            </el-form-item>

            <el-form-item label="中标单位">
                <el-input v-model="searchForm.winOrg" placeholder="请输入中标单位" class="!w-40" />
            </el-form-item>

            <el-form-item label="采购进展">
                <el-select v-model="searchForm.nodeSeq" placeholder="请选择" clearable class="w-40">
                    <el-option v-for="item in flowNodeOptions" :label="item.nodeName" :value="item.nodeSeq!" />
                </el-select>
            </el-form-item>



            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="info" @click="handleSearch">数据导出</el-button>
                <el-button v-hasPermi="['fund:purchase:save']" type="success" @click="handleApply">政府采购申请</el-button>
            </el-form-item>
        </el-form>

        <div>
            <!-- 数据表格 -->
            <el-table :data="tableData" style="width: 100%" @cell-click="handleCellClick" v-loading="loading">
                <el-table-column type="index" label="序号" width="60" align="center" fixed>
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="项目信息" align="center" fixed>
                    <el-table-column prop="projName" label="项目名称" header-align="center" align="left" width="200" />
                    <el-table-column prop="orgName" label="项目单位" align="center" width="120" />
                    <el-table-column prop="projAmount" label="项目批准金额(万元)" header-align="center" align="right"
                        width="120" :formatter="(row) => numberFormat(row.projAmount, 4)">
                        <template #header>
                            <div>项目批准金额</div>
                            <div>(万元)</div>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="政府采购预算" align="center">
                    <el-table-column prop="purchaseName" label="采购项目名称" header-align="center" align="left"
                        width="200" />
                    <el-table-column prop="purchaseOrgname" label="采购人" align="center" width="120" />
                    <el-table-column prop="budgetAmount" label="采购预算金额(元)" header-align="center" align="right"
                        width="120" :formatter="(row) => numberFormat(row.budgetAmount, 2)">
                        <template #header>
                            <div>采购预算金额</div>
                            <div>(元)</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="itemText" label="采购品目" align="center" width="200" />
                    <el-table-column prop="styleText" label="采购组织形式" align="center" width="120" />
                    <el-table-column prop="modeText" label="采购方式" align="center" width="120" />
                    <el-table-column prop="scaleText" label="预留中小企业类型" align="center" width="120" />
                    <el-table-column prop="track.purchaseCode" label="采购编码" align="center" width="120" />
                    <el-table-column prop="createTime" label="申请日期" align="center" width="130"
                        :formatter="(row: any) => dateFormat(row.createTime)" />
                </el-table-column>

                <el-table-column label="政府采购活动" align="center">
                    <el-table-column prop="track.agencyOrg" label="采购代理机构" align="center" width="120" />
                    <el-table-column prop="track.winOrg" label="中标单位" align="center" width="120" />
                    <el-table-column prop="track.winType" label="中标单位类型" align="center" width="120" />
                    <el-table-column prop="track.winAmount" label="中标金额" header-align="center" align="right" width="120"
                        :formatter="(row) => numberFormat(row.track?.winAmount, 2)" />
                    <el-table-column prop="track.winDate" label="成交日期" align="center" width="120"
                        :formatter="(row: any) => dateFormat(row.track?.winDate)" />

                </el-table-column>

                <el-table-column label="项目实施" align="center">
                    <el-table-column prop="track.acceptanceResult" label="验收结果" align="center" width="100">
                        <template #default="{ row }">
                            <el-tag v-if="row.track?.acceptanceResult == '1'" type="success">通过</el-tag>
                            <el-tag v-if="row.track?.acceptanceResult == '0'" type="danger">未通过</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="payedAmount" label="累计拨款(元)" header-align="center" align="right" width="120"
                        :formatter="(row) => numberFormat(row.payedAmount, 2)">
                        <template #header>
                            <div>累计拨款</div>
                            <div>(元)</div>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column prop="nodeName" label="采购进展" align="center" width="120" fixed="right" />
                <el-table-column label="操作" align="center" width="180" fixed="right">
                    <template #default="{ row }">
                        <template v-if="[EPurchaseStatus.reject, EPurchaseStatus.draft].includes(row.status)">
                            <el-button v-hasPermi="['fund:purchase:save']" type="primary" icon="edit" link
                                @click="handleEdit(row)">编辑</el-button>
                            <el-button v-hasPermi="['fund:purchase:remove']" type="danger" link icon="delete"
                                @click="handleRemove(row)">删除</el-button>
                        </template>
                        <template v-else>
                            <el-button type="primary" icon="view" link @click="handleView(row)">查看</el-button>
                            <el-button v-hasPermi="['fund:purchase:forceRecall']" type="danger" link
                                @click="handleRecall(row)">错发收回</el-button>
                        </template>

                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
        <el-dialog title="采购审核" v-model="auditDetailShow" fullscreen :close-on-click-modal="false" destroy-on-close>
            <AuditDetail @cancel="auditDetailShow = false;" />
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="Purchase/table">
import { forceRecall, getPurchaseList, removePurchase } from '@/api/fund/purchase'
import AuditDetail from '../../component/auditDetail.vue'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { dateFormat, numberFormat } from '@/utils/common';
import { EFlowType, EPurchaseStatus, purchaseDeptList } from '@/utils/constants'
import { getFlowNodeOption } from '@/api/system/flowNode';
import { ElMessage, ElMessageBox, TableColumnCtx } from 'element-plus';
import auth from '@/plugins/auth';

const { proxy } = getCurrentInstance() as { proxy: any };
const { purchase_catalog, purchase_form, purchase_mode } = proxy.useDict("purchase_catalog", 'purchase_form', 'purchase_mode');

const flowNodeOptions = ref<SysFlowNodeOptionVo[]>([]);
getFlowNodeOption(EFlowType.F03).then(res => {
    flowNodeOptions.value = res.data ?? []
})
const router = useRouter()


const page = reactive({
    pageNum: 1,
    pageSize: 10
})

const searchForm = reactive<FundPurchaseQuery>({})
const total = ref(0)
const tableData = ref<FundPurchaseVo[]>([])
const loading = ref(false)

const handleSearch = () => {
    loading.value = true
    getPurchaseList(searchForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()
onActivated(() => handleSearch())

const auditDetailShow = ref(false)

const handleApply = () => {
    router.push({ path: 'apply', query: { t: Date.now() } })
}
const handleView = (row: FundAuditListVo) => {
    router.push({ path: 'audit/form', query: { id: row.id } })
}

const handleEdit = (row: FundAuditListVo) => {
    router.push({ path: 'apply', query: { id: row.id } })
}

const handleCellClick = (row: FundPurchaseVo, column: TableColumnCtx<FundAuditListVo>) => {
    if (column.property == 'projName') {
        if (auth.hasPermi('fund:audit:hook') && !row.projId && row.doEarly == '1') {
            router.push({ path: 'hook', query: { id: row.id } })
        }
    }
}

const handleRecall = (row: FundPurchaseAuditListVo) => {
    ElMessageBox.confirm('确定要撤回吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        forceRecall(row.id!).then(() => {
            ElMessage.success('撤回成功')
            handleSearch()
        })
    })
}

const handleRemove = (row: FundAuditListVo) => {
    ElMessageBox.confirm('是否确认删除？', '提示', {
        type: 'warning'
    })
        .then(() => {
            removePurchase([row.id!]).then(res => {
                ElMessage.success('操作成功')
                handleSearch()
            })
        })
}
</script>