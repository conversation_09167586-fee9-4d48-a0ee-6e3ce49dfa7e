<template>
    <div class="bg-white flex flex-col ">
        <ApplyTableView ref="applyTableRef" class="mb-[50px] ml-[30px]"></ApplyTableView>
        <div
            class="form-foot ue-clear no-print py-3 fixed bottom-0 bg-[#f6fbfd]/90 z-100 w-full border-t-solid border-[#dfeaf4] pl-[30px]">
            <el-button type="primary" :loading="loading" @click="handleConfirmHook">确认挂接</el-button>
            <el-button type="info" @click="operateHistoryShow = true">操作历史</el-button>
            <el-button type="danger" @click="router.replace({ path: 'table' })">返回列表</el-button>

        </div>

        <el-dialog title="操作历史" v-model="operateHistoryShow" width="600px" :close-on-click-modal="false"
            destroy-on-close>
            <AuditHistory :purchase-id="formId" />
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="Purchase/apply">
import { canUserSubmit, savePurchase, submitPurchase, updateProjInfo } from '@/api/fund/purchase';
import ApplyTableView from '../../component/applyTableView.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import AuditHistory from '../../component/auditHistory.vue';
import { checkIsNumber } from '@/utils/common';

const router = useRouter()
const formId = useRoute().query.id as string

const operateHistoryShow = ref(false)

const loading = ref(false)
const applyTableRef = ref()



const handleConfirmHook = () => {
    ElMessageBox.confirm('确定挂接吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const data = await applyTableRef.value.getData()
        loading.value = true
        updateProjInfo(formId, data.projId).then(() => {
            ElMessage.success('挂接成功')
            router.replace({ path: 'table' })
        }).finally(() => {
            loading.value = false
        })
    })
}
</script>