<template>
    <div class="bg-white flex flex-col ">
        <ApplyTable ref="applyTableRef" class="mb-[50px]"></ApplyTable>
        <div class="form-foot ue-clear no-print py-3 fixed bottom-0 bg-[#f6fbfd]/90 z-100 w-full border-t-solid border-[#dfeaf4] pl-[30px]" >
            <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
            <el-button v-if="canSubmit" type="success" :loading="loading" @click="handleSubmit">提交</el-button> 
            <el-button type="info" :disabled="!formId" @click="operateHistoryShow = true">操作历史</el-button>
            <el-button type="danger" @click="router.replace({ path: 'table' })">返回列表</el-button>

        </div>

        <el-dialog title="操作历史" v-model="operateHistoryShow" width="700px" :close-on-click-modal="false"
            destroy-on-close>
            <AuditHistory :purchase-id="formId" />
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="Purchase/apply">
import { canUserSubmit, savePurchase, submitPurchase } from '@/api/fund/purchase';
import ApplyTable from '../../component/applyTable.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import AuditHistory from '../../component/auditHistory.vue';
import { checkIsNumber } from '@/utils/common';

const router = useRouter()
const formId = useRoute().query.id as string

const canSubmit = (await canUserSubmit(formId ?? '1')).data
const operateHistoryShow = ref(false)
const loading = ref(false)
const applyTableRef = ref()
const handleSave = async () => {
    const data = await applyTableRef.value.getData()
    if (!data.purchaseOrgid) {
        ElMessage.error('请输入选择采购主体')
        return
    }
    if (!checkIsNumber(data.budgetAmount) || data.budgetAmount < 0) {
        ElMessage.error('请输入正确的预算金额')
        return
    }
    loading.value = true
    savePurchase(data).then(() => {
        ElMessage.success('保存成功')
    }).finally(() => {
        loading.value = false
    })
}



const handleSubmit = async () => {
    ElMessageBox.confirm('确定提交吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const data = await applyTableRef.value.getData()
        loading.value = true
        submitPurchase(data).then(() => {
            ElMessage.success('提交成功')
            router.replace({ path: 'table' })
        }).finally(() => {
            loading.value = false
        })
    })
}


</script>