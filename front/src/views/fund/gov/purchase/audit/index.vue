<template>
    <div class="app-container">
        <!-- 查询表单 -->
        <el-form :inline="true" label-width="auto" :model="searchForm" class="filter-form">
            <el-form-item label="项目单位">
                <DeptSelect v-model="searchForm.orgId" clearable class="!w-40"></DeptSelect>
            </el-form-item>

            <el-form-item label="项目名称">
                <el-input v-model="searchForm.projName" placeholder="请输入项目名称" clearable class="!w-40" />
            </el-form-item>

            <el-form-item label="采购主体">
                <el-select v-model="searchForm.purchaseOrgid" placeholder="请选择" clearable class="!w-40">
                    <el-option v-for="item in purchaseDeptList" :label="item.deptName" :value="item.deptId" />
                </el-select>
            </el-form-item>

            <el-form-item label="采购品目">
                <el-select v-model="searchForm.itemId" placeholder="请选择" clearable class="!w-40">
                    <el-option v-for="item in purchase_catalog" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="采购方式">
                <el-select v-model="searchForm.modeId" placeholder="请选择" clearable class="!w-40">
                    <el-option v-for="item in purchase_mode" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="采购组织形式">
                <el-select v-model="searchForm.styleId" placeholder="请选择" clearable class="!w-40">
                    <el-option v-for="item in purchase_form" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
            </el-form-item>
        </el-form>

        <div>
            <!-- 数据表格 -->
            <el-table :data="tableData" style="width: 100%" v-loading="loading">
                <el-table-column type="index" label="序号" width="60" align="center" fixed >
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="项目信息" align="center" fixed>
                    <el-table-column prop="projName" label="项目名称" header-align="center" align="left" width="200" />
                    <el-table-column prop="orgName" label="项目单位" align="center" />
                    <el-table-column prop="projAmount" label="项目批准金额(万元)" header-align="center" align="right"
                        width="120" :formatter="(row) => numberFormat(row.projAmount, 4)">
                        <template #header>
                            <div>项目批准金额</div>
                            <div>(万元)</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="payedAmount" label="累计拨款(元)" header-align="center" align="right" width="120"
                        :formatter="(row) => numberFormat(row.payedAmount, 2)">
                        <template #header>
                            <div>累计拨款</div>
                            <div>(元)</div>
                        </template>
                    </el-table-column>
                </el-table-column>
                <el-table-column label="政府采购预算" align="center">
                    <el-table-column prop="purchaseName" label="采购项目名称" header-align="center" align="left" width="200" />
                    <el-table-column prop="purchaseOrgname" label="采购人" align="center" />
                    <el-table-column prop="budgetAmount" label="采购预算金额(元)" header-align="center" align="right"  width="120"
                        :formatter="(row) => numberFormat(row.budgetAmount, 2)">
                        <template #header>
                            <div>采购预算金额</div>
                            <div>(元)</div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="itemText" label="采购品目" align="center" width="200" />
                    <el-table-column prop="styleText" label="采购组织形式" align="center" width="120" />
                    <el-table-column prop="modeText" label="采购方式" align="center" width="120" />
                    <el-table-column prop="scaleText" label="预留中小企业类型" align="center" width="120" />
                </el-table-column>

                <el-table-column prop="createTime" label="申请日期" align="center" width="130"
                    :formatter="(row: any) => dateFormat(row.createTime)" />
                <el-table-column label="操作" align="center" width="100">
                    <template #default="{ row }">
                        <el-button v-if="row.canDoOperate" type="primary" link size="small" @click="handleAudit(row)">审核</el-button>
                        <el-button v-if="row.canDoRecall" type="danger" link size="small" @click="handleRecall(row)">错发收回</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>
    </div>
</template>

<script setup lang="ts" name="Purchase/audit">
import { getPurchaseAuditList, getPurchaseList, recall } from '@/api/fund/purchase'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { dateFormat, numberFormat } from '@/utils/common';
import { purchaseDeptList } from '@/utils/constants'
import { ElMessage, ElMessageBox } from 'element-plus';

const { proxy } = getCurrentInstance() as { proxy: any };
const { purchase_catalog, purchase_form, purchase_mode } = proxy.useDict("purchase_catalog", 'purchase_form', 'purchase_mode');

const router = useRouter()
const page = reactive({
    pageNum: 1,
    pageSize: 10
})

const searchForm = reactive<FundPurchaseAuditQuery>({})
const total = ref(0)
const tableData = ref<FundPurchaseAuditListVo[]>([])
const loading = ref(false)

const handleSearch = () => {
    loading.value = true
    getPurchaseAuditList(searchForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()
onActivated(() => handleSearch())

const handleAudit = (row: FundPurchaseAuditListVo) => {
    router.push({ path: 'audit/form', query: { id: row.id } })
}

const handleRecall = (row: FundPurchaseAuditListVo) => {
    ElMessageBox.confirm('确定要撤回吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        recall(row.id!).then(() => {
            ElMessage.success('撤回成功')
            handleSearch()
        })
    })

}
</script>