<template>
    <div v-loading="loading">
        <el-table :data="dataList" height="400px" border>
            <el-table-column type="index" label="序号" align="center" width="60"></el-table-column>
            <el-table-column label="操作人" prop="operatorName" align="center" width="120"></el-table-column>
            <el-table-column label="操作时间" prop="operateTime" :formatter="(row: any) => dateFormat(row.operateTime)"
                align="center" width="130"></el-table-column>
                <el-table-column label="节点名称" prop="nodeName" align="center" width="120"></el-table-column>
            <el-table-column label="操作名称" prop="operateResult" align="center" width="120">
                <template #default="{row}">
                    <el-tag v-if="row.operateResult == 1" type="success">提交</el-tag>
                    <el-tag v-if="row.operateResult == 0" type="danger">退回</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作内容" prop="operateReason" align="center"></el-table-column>
        </el-table>
    </div>

</template>

<script lang="ts" setup>
import { getAuditHistoryList } from '@/api/fund/purchase';
import { dateFormat } from '@/utils/common';

const props = defineProps({
    purchaseId: {
        type: String,
        default: ''
    }
})

const loading = ref(true)
const dataList = ref<FundPurchaseNodeVo[]>([])
getAuditHistoryList(props.purchaseId).then(res => {
    dataList.value = res.data ?? []
}).finally(() => {
    loading.value = false
})
</script>