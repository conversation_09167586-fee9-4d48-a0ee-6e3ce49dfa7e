<template>
    <div id="printTable" class="apply-form">
        <div class="form-title">政府采购预算申请表</div>
        <div class="form-head">
            <table>
                <tbody>
                    <tr>
                        <td>
                            <div style="width:90px; text-align:left;">申请部门：</div>
                        </td>
                        <td>{{ form.orgName }}</td>
                    </tr>
                    <tr>
                        <td>采购主体：</td>
                        <td>
                            <ElCheckboxOne :model-value="form.purchaseOrgid"
                                :options="purchaseDeptList.map(t => ({ label: t.deptName, value: t.deptId }))">
                            </ElCheckboxOne>
                        </td>
                    </tr>
                    <tr>
                        <td>项目名称：</td>
                        <td>
                            <template v-if="auth.hasPermi('fund:audit:hook') && form.doEarly == '1'">
                                <el-select v-model="form.projName" allow-create filterable remote reserve-keyword
                                    placeholder="请输入项目名称" remote-show-suffix :remote-method="remoteMethod"
                                    :loading="projectSelectLoading" @change="handleProjectChange"
                                    class="w-600px border-b-1 border-b-solid proj-name">
                                    <el-option v-for="item in projectOptions" :label="item.name" :value="item.id!" />
                                </el-select>
                            </template>
                            <template v-else>
                                <input type="text" id="txtProjectName" :value="form.projName" readonly></input>
                            </template>
                        </td>
                        <td style="width:1%;">
                            <el-checkbox class="check-box-one" :model-value="form.doEarly" true-value="1" false-value="0"
                                disabled>提前实施</el-checkbox>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="form-body mt-1">
            <table class="table-info">
                <tbody>
                    <tr>
                        <td>
                            <div class="w-[160px]">政府采购项目名称</div>
                        </td>
                        <td class="w30p td-left">
                            {{ form.purchaseName }}
                        </td>
                        <td>
                            <div class="w-[160px]">政府采购品目</div>
                        </td>
                        <td class="w30p td-left">
                            <ElCheckboxOne :model-value="form.itemId" :options="purchase_catalog"
                                @click="handleOpenTree('1')">
                            </ElCheckboxOne>
                            <div>
                                <span style="font-size:12px; line-height:20px;" class="hh">{{ form.itemText }}</span>

                            </div>

                        </td>
                    </tr>
                    <tr>
                        <td>数量和计量单位</td>
                        <td class="w30p td-left">
                            <input :value="form.purchaseQty" type="text" readonly />
                        </td>
                        <td>预算金额（元）</td>
                        <td class="w30p td-left">
                            <input :value="numberFormat(form.budgetAmount, 2)" type="text" readonly />
                        </td>
                    </tr>
                    <tr>
                        <td>项目批准文件</td>
                        <td colspan="3" class="td-left">
                            <div class="flex flex-col gap-2 justify-start" style="float: left;">
                                <FileTable ref="fileTableRef" class="hidden" :source-id="form.id!" primaryType="项目批准文件"
                                    :hidden-columns="['primaryType', 'del']">
                                </FileTable>
                                <DownloadA v-for="file in fileTableRef?.getFileList() ?? []" :file="file"></DownloadA>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>预留采购份额措施</td>
                        <td colspan="3">
                            <div class="text-left">
                                <ElCheckboxOne :model-value="form.measureId" :options="purchase_quota"></ElCheckboxOne>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>中小企业规模类型</td>
                        <td class="w30p !text-left" colspan="3">
                            <ElCheckboxOne :model-value="form.scaleId" :options="purchase_sme"></ElCheckboxOne>
                        </td>
                    </tr>
                    <tr>
                        <td>不适宜由中小企业<br>提供的具体原因</td>
                        <td class="w30p td-left" colspan="3">
                            <div class="hh">
                                <ElCheckboxOne :model-value="form.unfitId" :options="purchase_reason"></ElCheckboxOne>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>采购需求概况</td>
                        <td colspan="3" class="td-left">
                            <div class="hh"> {{ form.requireText }}</div>
                        </td>
                    </tr>
                    <tr>
                        <td>预计采购时间</td>
                        <td class="w30p td-left">
                            <div class="inline relative">
                                <span ref="auditStartRef" contenteditable="true"
                                    :innerHTML="dateFormat(form.purchaseDate, 'YYYY年MM月DD日')"></span>
                            </div>
                        </td>
                        <td>采购合同跨年使用</td>
                        <td class="w30p !text-left">
                            <ElCheckboxOne :model-value="form.acrossYear" :options="yerOrNoOption"></ElCheckboxOne>
                        </td>
                    </tr>
                    <tr>
                        <td>采购组织形式</td>
                        <td class="w30p !text-left">
                            <ElCheckboxOne :model-value="form.styleId" :options="purchase_form"></ElCheckboxOne>
                        </td>
                        <td>政府购买服务目录</td>
                        <td class="w30p td-left">
                            <ElCheckboxOne :model-value="form.catalogFlag" :options="yerOrNoOption"
                                @click="handleOpenTree('2')"></ElCheckboxOne>
                            <span style="font-size:12px; line-height:20px;">{{ form.catalogText }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td>采购方式</td>
                        <td colspan="3">
                            <div class="text-left hh">
                                <ElCheckboxOne :model-value="form.modeId" :options="purchase_mode"></ElCheckboxOne>
                            </div>

                        </td>
                    </tr>
                </tbody>
            </table>
            <table class="table-audit">
                <tbody>
                    <tr>
                        <td class="td-left" title="">审核人：{{ form.auditUsername }}</td>
                        <td>填报人：{{ form.applyUsername }}</td>
                        <td class="td-right">填报日期：{{ dateFormat(form.createTime) || dayjs().format('YYYY-MM-DD') }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script lang="tsx" setup>
import { EFileSoureType, purchaseDeptList } from '@/utils/constants';
import { foreach, fromArray } from 'tree-lodash';
import { purchaseItems } from './data/purchaseItems';
import { purchaseService } from './data/purchaseService';
import { TreeWithParentKey } from 'tree-lodash/dist/esm/fromArray';
import { TreeKey } from 'tree-lodash/dist/esm/types';
import { dayjs, ElMessage, TreeInstance } from 'element-plus';
import { dateFormat, genSnowId, numberFormat } from '@/utils/common';
import { getPurchase, getPurchaseProjectOptions, savePurchase } from '@/api/fund/purchase';
import FileTable from '@/components/Table/FileTable.vue';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
import auth from '@/plugins/auth';

const { proxy } = getCurrentInstance() as { proxy: any };
const {
    purchase_catalog,
    purchase_quota,
    purchase_sme,
    purchase_reason,
    purchase_form,
    purchase_mode
} = proxy.useDict("purchase_catalog", "purchase_quota", 'purchase_sme', 'purchase_reason', 'purchase_form', 'purchase_mode');
const yerOrNoOption = [{ label: '是', value: '1' }, { label: '否', value: '0' }]

const fileTableRef = ref<FileTableExposeType | null>(null)

const purchaseItemTree = fromArray(purchaseItems as any, { itemKey: 'id', parentKey: 'pId' })
const purchaseServiceTree = fromArray(purchaseService as any, { itemKey: 'id', parentKey: 'pId' })

const formId = useRoute().query.id as string
const form = reactive<FundPurchaseVo>({
    id: genSnowId()
} as FundPurchaseVo)

defineExpose({
    async getData() {
        return {
            ...form,
        }
    }
})

if (formId) {
    const res = await getPurchase(formId)
    Object.assign(form, res.data)
}

/** 1 品类 2 服务 */
const treeType = ref('1')
const treeShow = ref(false)
const currentTree = ref<TreeWithParentKey<string, string, TreeKey>[]>([])
const handleOpenTree = (type: string) => {
    setTimeout(() => {
        treeType.value = type

        if (type == '1') {
            if (form.itemId == '10') {
                currentTree.value = purchaseItemTree.filter(item => item.id == 'A')
                treeShow.value = true
            }
            if (form.itemId == '20') {
                currentTree.value = purchaseItemTree.filter(item => item.id == 'B')
                treeShow.value = true
            }
            if (form.itemId == '30') {
                currentTree.value = purchaseItemTree.filter(item => item.id == 'C')
                treeShow.value = true
            }
        }
        if (type == '2') {
            if (form.catalogFlag == '1') {
                currentTree.value = purchaseServiceTree
                treeShow.value = true
            }
        }
    }, 1)
}

const projectOptions = ref<IProjectInfoVo[]>([])
const projectSelectLoading = ref(false)
const remoteMethod = (query: string) => {
    if (query) {
        projectSelectLoading.value = true
        getPurchaseProjectOptions(query).then(res => {
            projectOptions.value = res.data ?? []
        }).finally(() => {
            projectSelectLoading.value = false
        })
    } else {
        projectOptions.value = []
    }
}
const handleProjectChange = (val: string) => {
    const project = projectOptions.value.find(item => item.id === val)
    if (project) {
        form.projId = project.id!
        form.projName = project.name
    }
}



const ElCheckboxOne = defineComponent({
    name: 'ElCheckboxOne',
    props: {
        modelValue: {
            type: [String, Number],
            required: false,
        },
        options: {
            type: Array as PropType<{ label: string; value: string | number }[]>,
            required: true,
        },
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
        const handleChange = (val: string | number) => {
            emit('update:modelValue', val)
        }

        return () => (
            <div class={'check-box-one'}>
                {props.options.map((opt) => (
                    <el-checkbox
                        key={opt.value}
                        label={opt.value}
                        modelValue={props.modelValue === opt.value}
                        onChange={() => handleChange(opt.value)}
                        disabled={true}
                    >
                        {opt.label}
                    </el-checkbox>
                ))}
            </div>
        )
    },
})


</script>

<style scoped>
.apply-form {
    width: 1000px;
    background-color: white;
}

.apply-form .form-title {
    font-size: 32px;
    text-align: center;
    margin: 15px 0;
}

#txtProjectName {
    padding: 8px 0;
    font-size: 16px;
    font-family: 微软雅黑;
    border: 0;
    border-bottom: 1px solid #000;
    outline: none;
    width: 600px;
}

.apply-form .form-body .table-info {
    width: 100%;
    border: 2px solid #000;
    border-collapse: collapse;
}

.apply-form .form-body .table-info td {
    text-align: center;
    border: 1px solid #000;
    border-collapse: collapse;
    width: 20%;
    padding: 15px 10px;
}

.w30p {
    width: 45% !important;
}

.apply-form .form-body .table-audit {
    width: 100%;
}

.apply-form .form-body .table-audit td {
    text-align: center;
    width: 33.33%;
    padding: 15px 10px;
}

.td-left {
    text-align: left !important;
}

.td-left .flex {
    flex-wrap: wrap !important;
    /* 强制换行 */
    gap: 8px;
    /* 控制文件项间距 */
}

/* 文件项样式（确保宽度可换行） */
:deep(.el-link__inner) {
    width: 100%;
    /* 防止单个文件项溢出 */
    word-wrap: break-word;
    /* 长单词在边界处换行 */
    word-break: break-all;
    /* 任意字符处强制换行（适合中文/长字符串） */
    white-space: normal;
    text-align: left;
}

:deep(.hh) {
    width: 100%;
    /* 防止单个文件项溢出 */
    word-wrap: break-word;
    /* 长单词在边界处换行 */
    word-break: break-all;
    /* 任意字符处强制换行（适合中文/长字符串） */
    white-space: normal;
}

.td-right {
    text-align: right !important;
}

span[contenteditable="true"] {
    outline: none;
    display: inline-block;
    width: 100%;
}

input[type="text"] {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    text-indent: 5px;
    font-size: 16px;
    outline: none;
}

input[type="number"] {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    text-indent: 5px;
    font-size: 16px;
    outline: none;
}

textarea {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    text-indent: 5px;
    font-size: 16px;
    outline: none;
}

:deep(.proj-name .el-select__wrapper) {
    box-shadow: none
}
</style>
<style scoped>
:deep(.check-box-one .el-checkbox__inner) {
    border: 1px solid black!important;
    background-color: white!important;
}
:deep(.check-box-one .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner:after) {
    border-color: black!important;
}
:deep(.check-box-one .el-checkbox__label) {
    color: black!important;
    font-weight: 450!important;
    /* font-size: 16px!important; */
}
</style>