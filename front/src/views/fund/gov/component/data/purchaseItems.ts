export const purchaseItems = [
    { id: "A", pId: "0", name: "货物", open: true },
    { id: "A02000000", pId: "A", name: "设备", open: true },
    { id: "A02010000", pId: "A02000000", name: "信息化设备" },
    { id: "A02010100", pId: "A02010000", name: "计算机" },
    { id: "A02010101", pId: "A02010100", name: "巨型计算机" },
    { id: "A02010102", pId: "A02010100", name: "大型计算机" },
    { id: "A02010103", pId: "A02010100", name: "中型计算机" },
    { id: "A02010104", pId: "A02010100", name: "服务器" },
    { id: "A02010105", pId: "A02010100", name: "台式计算机" },
    { id: "A02010106", pId: "A02010100", name: "移动工作站" },
    { id: "A02010107", pId: "A02010100", name: "图形工作站" },
    { id: "A02010108", pId: "A02010100", name: "便携式计算机" },
    { id: "A02010109", pId: "A02010100", name: "平板式计算机" },
    { id: "A02010199", pId: "A02010100", name: "其他计算机" },
    { id: "A02010200", pId: "A02010000", name: "网络设备" },
    { id: "A02010201", pId: "A02010200", name: "路由器" },
    { id: "A02010202", pId: "A02010200", name: "交换设备" },
    { id: "A02010203", pId: "A02010200", name: "集线器" },
    { id: "A02010204", pId: "A02010200", name: "光端机" },
    { id: "A02010205", pId: "A02010200", name: "终端接入设备" },
    { id: "A02010206", pId: "A02010200", name: "通信(控制)处理机" },
    { id: "A02010207", pId: "A02010200", name: "通信控制器" },
    { id: "A02010208", pId: "A02010200", name: "集中器" },
    { id: "A02010209", pId: "A02010200", name: "终端控制器" },
    { id: "A02010210", pId: "A02010200", name: "集群控制器" },
    { id: "A02010211", pId: "A02010200", name: "多站询问单位" },
    { id: "A02010212", pId: "A02010200", name: "网络接口" },
    { id: "A02010213", pId: "A02010200", name: "通信适配器" },
    { id: "A02010214", pId: "A02010200", name: "接口适配器" },
    { id: "A02010215", pId: "A02010200", name: "光纤转换器" },
    { id: "A02010216", pId: "A02010200", name: "网络收发器" },
    { id: "A02010217", pId: "A02010200", name: "网络转发器" },
    { id: "A02010218", pId: "A02010200", name: "网络分配器" },
    { id: "A02010219", pId: "A02010200", name: "网关" },
    { id: "A02010220", pId: "A02010200", name: "网桥" },
    { id: "A02010221", pId: "A02010200", name: "协议分析器" },
    { id: "A02010222", pId: "A02010200", name: "协议测试设备" },
    { id: "A02010223", pId: "A02010200", name: "差错检测设备" },
    { id: "A02010224", pId: "A02010200", name: "负载均衡设备" },
    { id: "A02010299", pId: "A02010200", name: "其他网络设备" },
    { id: "A02010300", pId: "A02010000", name: "信息安全设备" },
    { id: "A02010301", pId: "A02010300", name: "防火墙" },
    { id: "A02010302", pId: "A02010300", name: "入侵检测设备" },
    { id: "A02010303", pId: "A02010300", name: "入侵防御设备" },
    { id: "A02010304", pId: "A02010300", name: "漏洞扫描设备" },
    { id: "A02010305", pId: "A02010300", name: "容灾备份设备" },
    { id: "A02010306", pId: "A02010300", name: "网络隔离设备" },
    { id: "A02010307", pId: "A02010300", name: "安全审计设备" },
    { id: "A02010308", pId: "A02010300", name: "安全路由器" },
    { id: "A02010309", pId: "A02010300", name: "计算机终端安全设备" },
    { id: "A02010310", pId: "A02010300", name: "网闸" },
    { id: "A02010311", pId: "A02010300", name: "网上行为管理设备" },
    { id: "A02010312", pId: "A02010300", name: "密码产品" },
    { id: "A02010313", pId: "A02010300", name: "虚拟专用网（VPN）设备" },
    { id: "A02010399", pId: "A02010300", name: "其他信息安全设备" },
    { id: "A02010400", pId: "A02010000", name: "终端设备" },
    { id: "A02010401", pId: "A02010400", name: "触摸式终端设备" },
    { id: "A02010402", pId: "A02010400", name: "终端机" },
    { id: "A02010499", pId: "A02010400", name: "其他终端设备" },
    { id: "A02010500", pId: "A02010000", name: "存储设备" },
    { id: "A02010501", pId: "A02010500", name: "磁盘机" },
    { id: "A02010502", pId: "A02010500", name: "磁盘阵列" },
    { id: "A02010503", pId: "A02010500", name: "存储用光纤交换机" },
    { id: "A02010504", pId: "A02010500", name: "光盘库" },
    { id: "A02010505", pId: "A02010500", name: "磁带机" },
    { id: "A02010506", pId: "A02010500", name: "磁带库" },
    { id: "A02010507", pId: "A02010500", name: "网络存储设备" },
    { id: "A02010508", pId: "A02010500", name: "移动存储设备" },
    { id: "A02010599", pId: "A02010500", name: "其他存储设备" },
    { id: "A02010600", pId: "A02010000", name: "机房辅助设备" },
    { id: "A02010601", pId: "A02010600", name: "机柜" },
    { id: "A02010602", pId: "A02010600", name: "机房环境监控设备" },
    { id: "A02010699", pId: "A02010600", name: "其他机房辅助设备" },
    { id: "A02010700", pId: "A02010000", name: "信息化设备零部件" },
    { id: "A02019900", pId: "A02010000", name: "其他信息化设备" },
    { id: "A02020000", pId: "A02000000", name: "办公设备" },
    { id: "A02020100", pId: "A02020000", name: "复印机" },
    { id: "A02020200", pId: "A02020000", name: "投影仪" },
    { id: "A02020300", pId: "A02020000", name: "投影幕" },
    { id: "A02020400", pId: "A02020000", name: "多功能一体机" },
    { id: "A02020500", pId: "A02020000", name: "照相机及器材" },
    { id: "A02020501", pId: "A02020500", name: "数字照相机" },
    { id: "A02020502", pId: "A02020500", name: "通用照相机" },
    { id: "A02020503", pId: "A02020500", name: "静态视频照相机" },
    { id: "A02020504", pId: "A02020500", name: "专用照相机" },
    { id: "A02020505", pId: "A02020500", name: "特殊照相机" },
    { id: "A02020506", pId: "A02020500", name: "镜头及器材" },
    { id: "A02020599", pId: "A02020500", name: "其他照相机及器材" },
    { id: "A02020600", pId: "A02020000", name: "执法记录仪" },
    { id: "A02020700", pId: "A02020000", name: "电子白板" },
    { id: "A02020800", pId: "A02020000", name: "触控一体机" },
    { id: "A02020900", pId: "A02020000", name: "刻录机" },
    { id: "A02021000", pId: "A02020000", name: "打印机" },
    { id: "A02021001", pId: "A02021000", name: "A3 黑白打印机" },
    { id: "A02021002", pId: "A02021000", name: "A3 彩色打印机" },
    { id: "A02021003", pId: "A02021000", name: "A4 黑白打印机" },
    { id: "A02021004", pId: "A02021000", name: "A4 彩色打印机" },
    { id: "A02021005", pId: "A02021000", name: "3D 打印机" },
    { id: "A02021006", pId: "A02021000", name: "票据打印机" },
    { id: "A02021007", pId: "A02021000", name: "条码打印机" },
    { id: "A02021008", pId: "A02021000", name: "地址打印机" },
    { id: "A02021099", pId: "A02021000", name: "其他打印机" },
    { id: "A02021100", pId: "A02020000", name: "输入输出设备" },
    { id: "A02021101", pId: "A02021100", name: "绘图设备" },
    { id: "A02021102", pId: "A02021100", name: "光电设备" },
    { id: "A02021103", pId: "A02021100", name: "LED 显示屏" },
    { id: "A02021104", pId: "A02021100", name: "液晶显示器" },
    { id: "A02021105", pId: "A02021100", name: "阴极射线管显示器" },
    { id: "A02021106", pId: "A02021100", name: "等离子显示器" },
    { id: "A02021107", pId: "A02021100", name: "KVM 设备" },
    { id: "A02021108", pId: "A02021100", name: "综合输入设备" },
    { id: "A02021109", pId: "A02021100", name: "键盘" },
    { id: "A02021110", pId: "A02021100", name: "鼠标器" },
    { id: "A02021111", pId: "A02021100", name: "控制杆" },
    { id: "A02021112", pId: "A02021100", name: "刷卡机" },
    { id: "A02021113", pId: "A02021100", name: "纸带输入机" },
    { id: "A02021114", pId: "A02021100", name: "磁卡读写器" },
    { id: "A02021115", pId: "A02021100", name: "集成电路(IC)卡读写器" },
    { id: "A02021116", pId: "A02021100", name: "非接触式智能卡读写机" },
    { id: "A02021117", pId: "A02021100", name: "触摸屏" },
    { id: "A02021118", pId: "A02021100", name: "扫描仪" },
    { id: "A02021119", pId: "A02021100", name: "条码扫描器" },
    { id: "A02021120", pId: "A02021100", name: "高拍仪" },
    { id: "A02021121", pId: "A02021100", name: "图形板" },
    { id: "A02021122", pId: "A02021100", name: "光笔" },
    { id: "A02021123", pId: "A02021100", name: "坐标数字化仪" },
    { id: "A02021124", pId: "A02021100", name: "语音输入设备" },
    { id: "A02021125", pId: "A02021100", name: "手写式输入设备" },
    { id: "A02021126", pId: "A02021100", name: "数据录入设备" },
    { id: "A02021199", pId: "A02021100", name: "其他输入输出设备" },
    { id: "A02021200", pId: "A02020000", name: "文印设备" },
    { id: "A02021201", pId: "A02021200", name: "速印机" },
    { id: "A02021202", pId: "A02021200", name: "胶印机" },
    { id: "A02021203", pId: "A02021200", name: "装订机" },
    { id: "A02021204", pId: "A02021200", name: "配页机" },
    { id: "A02021205", pId: "A02021200", name: "折页机" },
    { id: "A02021206", pId: "A02021200", name: "油印机" },
    { id: "A02021299", pId: "A02021200", name: "其他文印设备" },
    { id: "A02021300", pId: "A02020000", name: "销毁设备" },
    { id: "A02021301", pId: "A02021300", name: "碎纸机" },
    { id: "A02021302", pId: "A02021300", name: "光盘粉碎机" },
    { id: "A02021303", pId: "A02021300", name: "硬盘粉碎机" },
    { id: "A02021304", pId: "A02021300", name: "芯片粉碎机" },
    { id: "A02021305", pId: "A02021300", name: "综合销毁设备" },
    { id: "A02021399", pId: "A02021300", name: "其他销毁设备" },
    { id: "A02021400", pId: "A02020000", name: "会计机械" },
    { id: "A02021401", pId: "A02021400", name: "计算器" },
    { id: "A02021499", pId: "A02021400", name: "其他会计机械" },
    { id: "A02021500", pId: "A02020000", name: "制图机械" },
    { id: "A02021600", pId: "A02020000", name: "打字机" },
    { id: "A02021700", pId: "A02020000", name: "办公设备零部件" },
    { id: "A02029900", pId: "A02020000", name: "其他办公设备" },
    { id: "A02060000", pId: "A02000000", name: "电气设备" },
    { id: "A02061800", pId: "A02060000", name: "生活用电器" },
    { id: "A02061801", pId: "A02061800", name: "电冰箱" },
    { id: "A02061802", pId: "A02061800", name: "风扇" },
    { id: "A02061803", pId: "A02061800", name: "通风机" },
    { id: "A02061804", pId: "A02061800", name: "空调机" },
    { id: "A02061805", pId: "A02061800", name: "空气滤洁器" },
    { id: "A02061806", pId: "A02061800", name: "空气净化设备" },
    { id: "A02061807", pId: "A02061800", name: "排烟系统" },
    { id: "A02061808", pId: "A02061800", name: "取暖器" },
    { id: "A02061809", pId: "A02061800", name: "调湿调温机" },
    { id: "A02061810", pId: "A02061800", name: "洗衣机" },
    { id: "A02061811", pId: "A02061800", name: "吸尘器" },
    { id: "A02061812", pId: "A02061800", name: "洗碗机" },
    { id: "A02061813", pId: "A02061800", name: "厨房电动废物处理器" },
    { id: "A02061814", pId: "A02061800", name: "泔水处理器" },
    { id: "A02061815", pId: "A02061800", name: "熨烫电器" },
    { id: "A02061816", pId: "A02061800", name: "烹调电器" },
    { id: "A02061817", pId: "A02061800", name: "食品制备电器" },
    { id: "A02061818", pId: "A02061800", name: "饮水器" },
    { id: "A02061819", pId: "A02061800", name: "热水器" },
    { id: "A02061820", pId: "A02061800", name: "美容电器" },
    { id: "A02061821", pId: "A02061800", name: "保健器具" },
    { id: "A02061822", pId: "A02061800", name: "电热卧具、服装" },
    { id: "A02061899", pId: "A02061800", name: "其他生活用电器" },
    { id: "A02061900", pId: "A02060000", name: "照明设备" },
    { id: "A02061901", pId: "A02061900", name: "矿灯" },
    { id: "A02061902", pId: "A02061900", name: "建筑用灯具" },
    { id: "A02061903", pId: "A02061900", name: "车、船用灯" },
    { id: "A02061904", pId: "A02061900", name: "水下照明灯" },
    { id: "A02061905", pId: "A02061900", name: "民用机场灯具" },
    { id: "A02061906", pId: "A02061900", name: "防爆灯具" },
    { id: "A02061907", pId: "A02061900", name: "农业用灯具" },
    { id: "A02061908", pId: "A02061900", name: "室内照明灯具" },
    { id: "A02061909", pId: "A02061900", name: "场地用灯" },
    { id: "A02061910", pId: "A02061900", name: "路灯" },
    { id: "A02061911", pId: "A02061900", name: "移动照明灯塔" },
    { id: "A02061912", pId: "A02061900", name: "除害虫用灯" },
    { id: "A02061913", pId: "A02061900", name: "应急照明灯" },
    { id: "A02061914", pId: "A02061900", name: "体育比赛用灯" },
    { id: "A02061915", pId: "A02061900", name: "手电筒" },
    { id: "A02061916", pId: "A02061900", name: "发光标志、铭牌" },
    { id: "A02061917", pId: "A02061900", name: "摄影专用灯" },
    { id: "A02061999", pId: "A02061900", name: "其他照明设备" },
    { id: "A02080000", pId: "A02000000", name: "通信设备" },
    { id: "A02080700", pId: "A02080000", name: "电话通信设备" },
    { id: "A02080701", pId: "A02080700", name: "普通电话机" },
    { id: "A02080702", pId: "A02080700", name: "特种电话机" },
    { id: "A02080703", pId: "A02080700", name: "移动电话" },
    { id: "A02080704", pId: "A02080700", name: "电话交换设备" },
    { id: "A02080705", pId: "A02080700", name: "会议电话调度设备及市话中继设" },
    { id: "A02080799", pId: "A02080700", name: "其他电话通信设备" },
    { id: "A02080800", pId: "A02080000", name: "视频会议系统设备" },
    { id: "A02080801", pId: "A02080800", name: "视频会议控制台" },
    { id: "A02080802", pId: "A02080800", name: "视频会议多点控制器" },
    { id: "A02080803", pId: "A02080800", name: "视频会议会议室终端" },
    { id: "A02080804", pId: "A02080800", name: "音视频矩阵" },
    { id: "A02080805", pId: "A02080800", name: "视频会议系统及会议室音频系统" },
    { id: "A02080899", pId: "A02080800", name: "其他视频会议系统设备" },
    { id: "A02320000", pId: "A02000000", name: "医疗设备" },
    { id: "A02430000", pId: "A02000000", name: "航空器及其配套设备" },
    { id: "A02430900", pId: "A02430000", name: "无人机" },
    { id: "A05000000", pId: "A", name: "家具和用具", open: true },
    { id: "A05010000", pId: "A05000000", name: "家具" },
    { id: "A05010100", pId: "A05010000", name: "床类" },
    { id: "A05010101", pId: "A05010100", name: "钢木床类" },
    { id: "A05010102", pId: "A05010100", name: "钢塑床类" },
    { id: "A05010103", pId: "A05010100", name: "轻金属床类" },
    { id: "A05010104", pId: "A05010100", name: "木制床类" },
    { id: "A05010105", pId: "A05010100", name: "塑料床类" },
    { id: "A05010106", pId: "A05010100", name: "竹制床类" },
    { id: "A05010107", pId: "A05010100", name: "藤床类" },
    { id: "A05010199", pId: "A05010100", name: "其他床类" },
    { id: "A05010200", pId: "A05010000", name: "台、桌类" },
    { id: "A05010201", pId: "A05010200", name: "办公桌" },
    { id: "A05010202", pId: "A05010200", name: "会议桌" },
    { id: "A05010203", pId: "A05010200", name: "教学、实验用桌" },
    { id: "A05010204", pId: "A05010200", name: "茶几" },
    { id: "A05010299", pId: "A05010200", name: "其他台、桌类" },
    { id: "A05010300", pId: "A05010000", name: "椅凳类" },
    { id: "A05010301", pId: "A05010300", name: "办公椅" },
    { id: "A05010302", pId: "A05010300", name: "桌前椅" },
    { id: "A05010303", pId: "A05010300", name: "会议椅" },
    { id: "A05010304", pId: "A05010300", name: "教学、实验椅凳" },
    { id: "A05010399", pId: "A05010300", name: "其他椅凳类" },
    { id: "A05010400", pId: "A05010000", name: "沙发类" },
    { id: "A05010401", pId: "A05010400", name: "三人沙发" },
    { id: "A05010402", pId: "A05010400", name: "单人沙发" },
    { id: "A05010499", pId: "A05010400", name: "其他沙发类" },
    { id: "A05010500", pId: "A05010000", name: "柜类" },
    { id: "A05010501", pId: "A05010500", name: "书柜" },
    { id: "A05010502", pId: "A05010500", name: "文件柜" },
    { id: "A05010503", pId: "A05010500", name: "更衣柜" },
    { id: "A05010504", pId: "A05010500", name: "保密柜" },
    { id: "A05010505", pId: "A05010500", name: "茶水柜" },
    { id: "A05010599", pId: "A05010500", name: "其他柜类" },
    { id: "A05010600", pId: "A05010000", name: "架类" },
    { id: "A05010601", pId: "A05010600", name: "木质架类" },
    { id: "A05010602", pId: "A05010600", name: "金属质架类" },
    { id: "A05010699", pId: "A05010600", name: "其他架类" },
    { id: "A05010700", pId: "A05010000", name: "屏风类" },
    { id: "A05010701", pId: "A05010700", name: "木质屏风类" },
    { id: "A05010702", pId: "A05010700", name: "金属质屏风类" },
    { id: "A05010799", pId: "A05010700", name: "其他屏风类" },
    { id: "A05010800", pId: "A05010000", name: "组合家具" },
    { id: "A05019900", pId: "A05010000", name: "其他家具" },
    { id: "A05020000", pId: "A05000000", name: "用具" },
    { id: "A05020100", pId: "A05020000", name: "厨卫用具" },
    { id: "A05020101", pId: "A05020100", name: "厨房操作台" },
    { id: "A05020102", pId: "A05020100", name: "炊事机械" },
    { id: "A05020103", pId: "A05020100", name: "煤气罐（液化气罐）" },
    { id: "A05020104", pId: "A05020100", name: "水池" },
    { id: "A05020105", pId: "A05020100", name: "便器" },
    { id: "A05020106", pId: "A05020100", name: "水嘴" },
    { id: "A05020107", pId: "A05020100", name: "便器冲洗阀" },
    { id: "A05020108", pId: "A05020100", name: "水箱配件" },
    { id: "A05020109", pId: "A05020100", name: "阀门" },
    { id: "A05020110", pId: "A05020100", name: "淋浴器" },
    { id: "A05020111", pId: "A05020100", name: "淋浴房" },
    { id: "A05020112", pId: "A05020100", name: "餐具" },
    { id: "A05020199", pId: "A05020100", name: "其他厨卫用具" },
    { id: "A05029900", pId: "A05020000", name: "其他用具" },
    { id: "A05030000", pId: "A05000000", name: "装具" },
    { id: "A05040000", pId: "A05000000", name: "办公用品" },
    { id: "A05040100", pId: "A05040000", name: "纸制文具" },
    { id: "A05040101", pId: "A05040100", name: "复印纸" },
    { id: "A05040102", pId: "A05040100", name: "信纸" },
    { id: "A05040103", pId: "A05040100", name: "信封" },
    { id: "A05040104", pId: "A05040100", name: "单证" },
    { id: "A05040105", pId: "A05040100", name: "票据" },
    { id: "A05040106", pId: "A05040100", name: "本册" },
    { id: "A05040199", pId: "A05040100", name: "其他纸制文具" },
    { id: "A05040200", pId: "A05040000", name: "硒鼓、粉盒" },
    { id: "A05040201", pId: "A05040200", name: "鼓粉盒" },
    { id: "A05040202", pId: "A05040200", name: "墨粉盒" },
    { id: "A05040203", pId: "A05040200", name: "喷墨盒" },
    { id: "A05040204", pId: "A05040200", name: "墨水盒" },
    { id: "A05040205", pId: "A05040200", name: "色带" },
    { id: "A05040299", pId: "A05040200", name: "其他硒鼓、粉盒" },
    { id: "A05040300", pId: "A05040000", name: "墨、颜料" },
    { id: "A05040301", pId: "A05040300", name: "墨水" },
    { id: "A05040302", pId: "A05040300", name: "颜料" },
    { id: "A05040399", pId: "A05040300", name: "其他墨、颜料" },
    { id: "A05040400", pId: "A05040000", name: "文教用品" },
    { id: "A05040401", pId: "A05040400", name: "文具" },
    { id: "A05040402", pId: "A05040400", name: "笔" },
    { id: "A05040403", pId: "A05040400", name: "教具" },
    { id: "A05040499", pId: "A05040400", name: "其他文教用品" },
    { id: "A05040500", pId: "A05040000", name: "清洁用品" },
    { id: "A05040501", pId: "A05040500", name: "卫生用纸制品" },
    { id: "A05040502", pId: "A05040500", name: "消毒杀菌用品" },
    { id: "A05040503", pId: "A05040500", name: "肥(香)皂和合成洗涤剂" },
    { id: "A05040504", pId: "A05040500", name: "口腔清洁护理用品" },
    { id: "A05040599", pId: "A05040500", name: "其他清洁用品" },
    { id: "A05040600", pId: "A05040000", name: "信息化学品" },
    { id: "A05040601", pId: "A05040600", name: "胶片胶卷" },
    { id: "A05040602", pId: "A05040600", name: "录音录像带" },
    { id: "A05040699", pId: "A05040600", name: "其他信息化学品" },
    { id: "A05049900", pId: "A05040000", name: "其他办公用品" },
    { id: "A07000000", pId: "A", name: "物资", open: true },
    { id: "A07020000", pId: "A07000000", name: "医药品" },
    { id: "A08000000", pId: "A", name: "无形资产", open: true },
    { id: "A08060000", pId: "A08000000", name: "信息数据类无形资产" },
    { id: "A08060300", pId: "A08060000", name: "计算机软件" },
    { id: "A08060301", pId: "A08060300", name: "基础软件" },
    { id: "A08060302", pId: "A08060300", name: "支撑软件" },
    { id: "A08060303", pId: "A08060300", name: "应用软件" },
    { id: "A0806030301", pId: "A08060303", name: "信息安全软件" },
    { id: "A0806030302", pId: "A08060303", name: "其他应用软件" },
    { id: "A08060399", pId: "A08060300", name: "其他计算机软件" },
    { id: "B", pId: "0", name: "工程", open: true },
    { id: "B01000000", pId: "B", name: "房屋施工", open: true },
    { id: "B01010000", pId: "B01000000", name: "办公用房施工" },
    { id: "B01020000", pId: "B01000000", name: "业务用房施工" },
    { id: "B02000000", pId: "B", name: "构筑物施工", open: true },
    { id: "B03000000", pId: "B", name: "施工工程准备", open: true },
    { id: "B05000000", pId: "B", name: "专业施工", open: true },
    { id: "B06000000", pId: "B", name: "安装工程", open: true },
    { id: "B07000000", pId: "B", name: "装修工程", open: true },
    { id: "B08000000", pId: "B", name: "修缮工程", open: true },
    { id: "B99000000", pId: "B", name: "其他建筑工程", open: true },
    { id: "C", pId: "0", name: "服务", open: true },
    { id: "C04000000", pId: "C", name: "医疗卫生服务" },
    { id: "C04010000", pId: "C04000000", name: "医院服务" },
    { id: "C04030000", pId: "C04000000", name: "门诊服务" },
    { id: "C04070000", pId: "C04000000", name: "健康检查服务" },
    { id: "C04090000", pId: "C04000000", name: "预防接种服务" },
    { id: "C04990000", pId: "C04000000", name: "其他医疗卫生服务" },
    { id: "C05000000", pId: "C", name: "社会服务" },
    { id: "C05020000", pId: "C05000000", name: "社会治理服务" },
    { id: "C05029900", pId: "C05020000", name: "其他社会治理服务" },
    { id: "C05030000", pId: "C05000000", name: "灾害防治和应急管理服务" },
    { id: "C05030100", pId: "C05030000", name: "防灾减灾预警预报服务" },
    { id: "C05030200", pId: "C05030000", name: "防灾救灾物资储备供应服务" },
    { id: "C05030300", pId: "C05030000", name: "灾害救援救助服务" },
    { id: "C05030400", pId: "C05030000", name: "灾后管理服务" },
    { id: "C05030500", pId: "C05030000", name: "应急救治服务" },
    { id: "C05039900", pId: "C05030000", name: "其他灾害防治和应急管理服务" },
    { id: "C05040000", pId: "C05000000", name: "安全服务" },
    { id: "C05040100", pId: "C05040000", name: "公共安全服务" },
    { id: "C05040300", pId: "C05040000", name: "保安服务" },
    { id: "C05040400", pId: "C05040000", name: "特种保安服务" },
    { id: "C05040500", pId: "C05040000", name: "道路交通协管服务" },
    { id: "C05040600", pId: "C05040000", name: "社会治安协管服务" },
    { id: "C05049900", pId: "C05040000", name: "其他安全保护服务" },
    { id: "C05990000", pId: "C05000000", name: "其他社会服务" },
    { id: "C06000000", pId: "C", name: "文化、体育、娱乐服务" },
    { id: "C06040000", pId: "C06000000", name: "体育服务" },
    { id: "C06040100", pId: "C06040000", name: "体育组织服务" },
    { id: "C06040200", pId: "C06040000", name: "体育场馆服务" },
    { id: "C06049900", pId: "C06040000", name: "其他体育服务" },
    { id: "C06050000", pId: "C06000000", name: "娱乐服务" },
    { id: "C06050100", pId: "C06050000", name: "室内娱乐服务" },
    { id: "C06050200", pId: "C06050000", name: "游乐园服务" },
    { id: "C06050300", pId: "C06050000", name: "休闲健身娱乐服务" },
    { id: "C06059900", pId: "C06050000", name: "其他娱乐服务" },
    { id: "C06990000", pId: "C06000000", name: "其他文化、体育、娱乐服务" },
    { id: "C07000000", pId: "C", name: "生态环境保护和治理服务" },
    { id: "C07010000", pId: "C07000000", name: "生态环境保护服务" },
    { id: "C07010100", pId: "C07010000", name: "生态资源调查与监测服务" },
    { id: "C07010200", pId: "C07010000", name: "碳汇监测与评估服务" },
    { id: "C07010300", pId: "C07010000", name: "生态环境舆情监控服务" },
    { id: "C07010400", pId: "C07010000", name: "生态环境成果交流与管理服务" },
    { id: "C07019900", pId: "C07010000", name: "其他生态环境保护服务" },
    { id: "C07020000", pId: "C07000000", name: "生态环境治理服务" },
    { id: "C07020100", pId: "C07020000", name: "水污染治理服务" },
    { id: "C07020200", pId: "C07020000", name: "空气污染治理服务" },
    { id: "C07020300", pId: "C07020000", name: "噪声与振动污染治理服务" },
    { id: "C07020400", pId: "C07020000", name: "危险废物治理服务" },
    { id: "C07020500", pId: "C07020000", name: "无害固体废物处理服务" },
    { id: "C07029900", pId: "C07020000", name: "其他生态环境治理服务" },
    { id: "C07990000", pId: "C07000000", name: "其他生态环境保护和治理服务" },
    { id: "C13000000", pId: "C", name: "公共设施管理服务" },
    { id: "C13010000", pId: "C13000000", name: "区域规划和设计服务" },
    { id: "C13020000", pId: "C13000000", name: "市政公用设施管理服务" },
    { id: "C13030000", pId: "C13000000", name: "园林绿化管理服务" },
    { id: "C13040000", pId: "C13000000", name: "市容管理服务" },
    { id: "C13050000", pId: "C13000000", name: "城镇公共卫生服务" },
    { id: "C13990000", pId: "C13000000", name: "其他公共设施管理服务" },
    { id: "C16000000", pId: "C", name: "信息技术服务" },
    { id: "C16010000", pId: "C16000000", name: "软件开发服务" },
    { id: "C16010100", pId: "C16010000", name: "基础软件开发服务" },
    { id: "C16010200", pId: "C16010000", name: "支撑软件开发服务" },
    { id: "C16010300", pId: "C16010000", name: "应用软件开发服务" },
    { id: "C16010301", pId: "C16010300", name: "通用应用软件开发服务" },
    { id: "C16010302", pId: "C16010300", name: "行业应用软件开发服务" },
    { id: "C16010400", pId: "C16010000", name: "嵌入式软件开发服务" },
    { id: "C16010500", pId: "C16010000", name: "信息安全软件开发服务" },
    { id: "C16020000", pId: "C16000000", name: "信息系统集成实施服务" },
    { id: "C16020100", pId: "C16020000", name: "基础环境集成实施服务" },
    { id: "C16020200", pId: "C16020000", name: "硬件集成实施服务" },
    { id: "C16020300", pId: "C16020000", name: "软件集成实施服务" },
    { id: "C16020400", pId: "C16020000", name: "安全集成实施服务" },
    { id: "C16029900", pId: "C16020000", name: "其他系统集成实施服务" },
    { id: "C16030000", pId: "C16000000", name: "数据处理服务" },
    { id: "C16030100", pId: "C16030000", name: "存储服务" },
    { id: "C16030200", pId: "C16030000", name: "数据加工处理服务" },
    { id: "C16030300", pId: "C16030000", name: "数字内容加工处理服务" },
    { id: "C16039900", pId: "C16030000", name: "其他数据处理服务" },
    { id: "C16040000", pId: "C16000000", name: "云计算服务" },
    { id: "C16050000", pId: "C16000000", name: "信息化工程监理服务" },
    { id: "C16060000", pId: "C16000000", name: "测试评估认证服务" },
    { id: "C16070000", pId: "C16000000", name: "运行维护服务" },
    { id: "C16070100", pId: "C16070000", name: "基础环境运维服务" },
    { id: "C16070200", pId: "C16070000", name: "硬件运维服务" },
    { id: "C16070300", pId: "C16070000", name: "软件运维服务" },
    { id: "C16070400", pId: "C16070000", name: "安全运维服务" },
    { id: "C16079900", pId: "C16070000", name: "其他运行维护服务" },
    { id: "C16080000", pId: "C16000000", name: "运营服务" },
    { id: "C16080100", pId: "C16080000", name: "软件运营服务" },
    { id: "C16080200", pId: "C16080000", name: "平台运营服务" },
    { id: "C16080300", pId: "C16080000", name: "基础设施运营服务" },
    { id: "C16089900", pId: "C16080000", name: "其他运营服务" },
    { id: "C16090000", pId: "C16000000", name: "信息技术咨询服务" },
    { id: "C16090100", pId: "C16090000", name: "信息化规划服务" },
    { id: "C16090200", pId: "C16090000", name: "信息系统设计服务" },
    { id: "C16090300", pId: "C16090000", name: "信息技术管理咨询服务" },
    { id: "C16099900", pId: "C16090000", name: "其他信息技术咨询服务" },
    { id: "C16100000", pId: "C16000000", name: "呼叫中心服务" },
    { id: "C16990000", pId: "C16000000", name: "其他信息技术服务" },
    { id: "C17000000", pId: "C", name: "电信和其他信息传输服务" },
    { id: "C17010000", pId: "C17000000", name: "电信服务" },
    { id: "C17010100", pId: "C17010000", name: "基础电信服务" },
    { id: "C17010200", pId: "C17010000", name: "网络接入服务" },
    { id: "C17010300", pId: "C17010000", name: "其他增值电信服务" },
    { id: "C17020000", pId: "C17000000", name: "互联网信息服务" },
    { id: "C17030000", pId: "C17000000", name: "卫星传输服务" },
    { id: "C17990000", pId: "C17000000", name: "其他电信和信息传输服务" },
    { id: "C19000000", pId: "C", name: "专业技术服务" },
    { id: "C19010000", pId: "C19000000", name: "技术测试和分析服务" },
    { id: "C19040000", pId: "C19000000", name: "测绘服务" },
    { id: "C19060000", pId: "C19000000", name: "地质勘测服务" },
    { id: "C19070000", pId: "C19000000", name: "合同能源管理服务" },
    { id: "C19990000", pId: "C19000000", name: "其他专业技术服务" },
    { id: "C20000000", pId: "C", name: "鉴证咨询服务" },
    { id: "C20020000", pId: "C20000000", name: "鉴证服务" },
    { id: "C20020100", pId: "C20020000", name: "会计鉴证服务" },
    { id: "C20020300", pId: "C20020000", name: "法律鉴证服务" },
    { id: "C20020500", pId: "C20020000", name: "工程造价鉴定服务" },
    { id: "C20020600", pId: "C20020000", name: "工程监理服务" },
    { id: "C20020700", pId: "C20020000", name: "资产评估服务" },
    { id: "C20020800", pId: "C20020000", name: "环境评估服务" },
    { id: "C20020900", pId: "C20020000", name: "房地产土地评估服务" },
    { id: "C20021000", pId: "C20020000", name: "建筑图纸审核服务" },
    { id: "C20021100", pId: "C20020000", name: "医疗事故鉴定服务" },
    { id: "C20029900", pId: "C20020000", name: "其他鉴证服务" },
    { id: "C20030000", pId: "C20000000", name: "咨询服务" },
    { id: "C20030100", pId: "C20030000", name: "会计咨询服务" },
    { id: "C20030200", pId: "C20030000", name: "税务咨询服务" },
    { id: "C20030300", pId: "C20030000", name: "法律咨询服务" },
    { id: "C20030400", pId: "C20030000", name: "社会与管理咨询服务" },
    { id: "C20030500", pId: "C20030000", name: "工程设计前咨询服务" },
    { id: "C20030600", pId: "C20030000", name: "工程政策咨询服务" },
    { id: "C21000000", pId: "C", name: "房地产服务" },
    { id: "C21040000", pId: "C21000000", name: "物业管理服务" },
    { id: "C21050000", pId: "C21000000", name: "土地管理服务" },
    { id: "C21990000", pId: "C21000000", name: "其他房地产服务" },
    { id: "C22000000", pId: "C", name: "会议、展览、住宿和餐饮服务" },
    { id: "C22010000", pId: "C22000000", name: "会议服务" },
    { id: "C22010100", pId: "C22010000", name: "大型会议服务" },
    { id: "C22010200", pId: "C22010000", name: "一般会议服务" },
    { id: "C22990000", pId: "C22000000", name: "其他会议、展览、住宿和餐饮服务" },
    { id: "C23000000", pId: "C", name: "商务服务" },
    { id: "C23010000", pId: "C23000000", name: "法律服务" },
    { id: "C23010100", pId: "C23010000", name: "法律诉讼服务" },
    { id: "C23019900", pId: "C23010000", name: "其他法律服务" },
    { id: "C23020000", pId: "C23000000", name: "会计服务" },
    { id: "C23020100", pId: "C23020000", name: "财务报表编制服务" },
    { id: "C23020200", pId: "C23020000", name: "记账服务" },
    { id: "C23029900", pId: "C23020000", name: "其他会计服务" },
    { id: "C23030000", pId: "C23000000", name: "审计服务" },
    { id: "C23040000", pId: "C23000000", name: "税务服务" },
    { id: "C23060000", pId: "C23000000", name: "调查和民意测验服务" },
    { id: "C23070000", pId: "C23000000", name: "公共信息与宣传服务" },
    { id: "C23080000", pId: "C23000000", name: "行业管理服务" },
    { id: "C23090000", pId: "C23000000", name: "印刷和出版服务" },
    { id: "C23110000", pId: "C23000000", name: "租赁服务（不带操作员）" },
    { id: "C23110100", pId: "C23110000", name: "计算机设备和软件租赁服务" },
    { id: "C23110200", pId: "C23110000", name: "办公设备租赁服务" },
    { id: "C23110300", pId: "C23110000", name: "车辆及其他运输机械租赁服务" },
    { id: "C23110700", pId: "C23110000", name: "家具、用具和装具租赁服务" },
    { id: "C23119900", pId: "C23110000", name: "其他租赁服务" },
    { id: "C23120000", pId: "C23000000", name: "维修和保养服务" },
    { id: "C23120100", pId: "C23120000", name: "计算机设备维修和保养服务" },
    { id: "C23120200", pId: "C23120000", name: "办公设备维修和保养服务" },
    { id: "C23120300", pId: "C23120000", name: "车辆维修和保养服务" },
    { id: "C23120302", pId: "C23120300", name: "车辆加油、添加燃料服务" },
    { id: "C23120303", pId: "C23120300", name: "车辆充换电服务" },
    { id: "C23120399", pId: "C23120300", name: "其他车辆维修和保养服务" },
    { id: "C23120500", pId: "C23120000", name: "医疗设备维修和保养服务" },
    { id: "C23120600", pId: "C23120000", name: "家具维修和保养服务" },
    { id: "C23120700", pId: "C23120000", name: "空调维修和保养服务" },
    { id: "C23120800", pId: "C23120000", name: "电梯维修和保养服务" },
    { id: "C23121000", pId: "C23120000", name: "安保设备维修和保养服务" },
    { id: "C23121100", pId: "C23120000", name: "消防设备维修和保养服务" },
    { id: "C23129900", pId: "C23120000", name: "其他维修和保养服务" },
    { id: "C23150000", pId: "C23000000", name: "广告宣传服务" },
    { id: "C23160000", pId: "C23000000", name: "建筑物清洁服务" },
    { id: "C23170000", pId: "C23000000", name: "摄影服务" },
    { id: "C23180000", pId: "C23000000", name: "包装服务" },
    { id: "C23190000", pId: "C23000000", name: "翻译服务" },
    { id: "C23200000", pId: "C23000000", name: "档案管理服务" },
    { id: "C23210000", pId: "C23000000", name: "外事服务" },
    { id: "C24000000", pId: "C", name: "政府和社会资本合作服务" },
    { id: "C99000000", pId: "C", name: "其他服务" }
];