<template>
    <div class="apply-form ml-[30px] ">
        <div class="form-title">政府采购预算申请表</div>
        <div class="form-head">
            <table>
                <tbody>
                    <tr>
                        <td>
                            <div style="width:90px; text-align:left;">申请部门：</div>
                        </td>
                        <td>{{ form.orgName }}</td>
                    </tr>
                    <tr>
                        <td>采购主体：</td>
                        <td>
                            <ElCheckboxOne v-model="form.purchaseOrgid"
                                :options="purchaseDeptList.map(t => ({ label: t.deptName, value: t.deptId }))">
                            </ElCheckboxOne>
                        </td>
                    </tr>
                    <tr>
                        <td>项目名称：</td>
                        <td>
                            <el-select v-model="form.projName" allow-create filterable remote reserve-keyword
                                placeholder="请输入项目名称" remote-show-suffix :remote-method="remoteMethod"
                                :loading="projectSelectLoading" @change="handleProjectChange"
                                class="w-600px border-b-1 border-b-solid proj-name">
                                <el-option v-for="item in projectOptions" :label="item.name" :value="item.id!" />
                            </el-select>
                            <!-- <input type="text" id="txtProjectName" value="" autocomplete="off"> -->
                        </td>
                        <td style="width:1%;">
                            <el-checkbox v-model="form.doEarly" true-value="1" false-value="0">提前实施</el-checkbox>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="form-body mt-1">
            <table class="table-info">
                <tbody>
                    <tr>
                        <td class="!w-100px">
                            <div>政府采购项目名称</div>
                        </td>
                        <td class="w30p td-left">
                            <input v-model="form.purchaseName" type="text" maxlength="200" />
                        </td>
                        <td class="!w-100px">
                            <div>政府采购项目名称</div>
                        </td>
                        <td class="w30p td-left">
                            <ElCheckboxOne v-model="form.itemId" :options="purchase_catalog"
                                @click="handleOpenTree('1')">
                            </ElCheckboxOne>
                            <div>
                                <span style="font-size:12px; line-height:20px;">{{ form.itemText }}</span>

                            </div>

                        </td>
                    </tr>
                    <tr>
                        <td>数量和计量单位</td>
                        <td class="w30p td-left">
                            <input v-model="form.purchaseQty" type="text" maxlength="50" />
                        </td>
                        <td>预算金额（元）</td>
                        <td class="w30p td-left">
                            <input v-model="form.budgetAmount" type="number" min="0" max="10" />
                        </td>
                    </tr>
                    <tr>
                        <td>项目批准文件</td>
                        <td colspan="3" class="td-left">
                            <div class="flex gap-2">
                                <my-file-upload :accept="fileType" :data="{
                                    sourceId: form.id,
                                    sourceType: EFileSoureType.FUND_PURCHASE,
                                    primaryType: '项目批准文件'
                                }" @upload-success="fileTableRef?.updateTable">
                                    <el-button type="primary">本地上传</el-button>
                                </my-file-upload>
                                <!-- <my-file-upload class="file-upload">
                                    <el-button type="primary">OA上传</el-button>
                                </my-file-upload> -->
                            </div>
                            <div class="mt-2">
                                <!-- <FileTable ref="fileTableRef" :source-id="form.id!" primaryType="项目批准文件"
                                    :hidden-columns="['primaryType']">
                                </FileTable> -->
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>预留采购份额措施</td>
                        <td colspan="3">
                            <div class="text-left">
                                <ElCheckboxOne v-model="form.measureId" :options="purchase_quota"></ElCheckboxOne>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>中小企业规模类型</td>
                        <td class="w30p !text-left" colspan="3">
                            <ElCheckboxOne v-model="form.scaleId" :options="purchase_sme"></ElCheckboxOne>
                        </td>
                    </tr>
                    <tr>
                        <td>不适宜由中小企业<br>提供的具体原因</td>
                        <td class="w30p td-left" colspan="3">
                            <div class="w-200px">
                                <ElCheckboxOne v-model="form.unfitId" :options="purchase_reason" :is-un-checked="true">
                                </ElCheckboxOne>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>采购需求概况</td>
                        <td colspan="3" class="td-left">
                            <textarea v-model="form.requireText" rows="5" class="w-full resize-none"
                                :style="{ height: requireTextHeight + 'px' }"
                                @input="evt => requireTextHeight = (evt.target as any)?.scrollHeight"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td>预计采购时间</td>
                        <td class="w30p td-left">
                            <div class="inline relative">
                                <span ref="auditStartRef" contenteditable="true"
                                    :innerHTML="dateFormat(form.purchaseDate, 'YYYY年MM月DD日')"></span>
                                <div class="absolute top--1 left-0 opacity-0">
                                    <el-date-picker v-model="form.purchaseDate" type="date" value-format="YYYY-MM-DD"
                                        class="!w-37"></el-date-picker>
                                </div>
                            </div>
                        </td>
                        <td>采购合同跨年使用</td>
                        <td class="w30p !text-left">
                            <ElCheckboxOne v-model="form.acrossYear" :options="yerOrNoOption"></ElCheckboxOne>
                        </td>
                    </tr>
                    <tr>
                        <td>采购组织形式</td>
                        <td class="w30p !text-left">
                            <ElCheckboxOne v-model="form.styleId" :options="purchase_form"></ElCheckboxOne>
                        </td>
                        <td>政府购买服务目录</td>
                        <td class="w30p td-left">
                            <ElCheckboxOne v-model="form.catalogFlag" :options="yerOrNoOption"
                                @click="handleOpenTree('2')"></ElCheckboxOne>
                            <span style="font-size:12px; line-height:20px;">{{ form.catalogText }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td>采购方式</td>
                        <td colspan="3">
                            <div class="text-left">
                                <ElCheckboxOne v-model="form.modeId" :options="purchase_mode"></ElCheckboxOne>
                            </div>

                        </td>
                    </tr>
                </tbody>
            </table>
            <table class="table-audit">
                <tbody>
                    <tr>
                        <td class="td-left" title="">审核人：</td>
                        <td>填报人：庄彬英</td>
                        <td class="td-right">填报日期：{{ dateFormat(form.createTime) || dayjs().format('YYYY-MM-DD') }}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <el-dialog :title="treeType == '1' ? '上海市市级政府采购品目' : '上海市市级政府购买服务指导性目录'" v-model="treeShow" width="500px"
            :close-on-click-modal="false" destroy-on-close>
            <el-scrollbar height="500px">
                <el-tree ref="treeRef" :data="currentTree" :default-expanded-keys="['A', 'B', 'C']" node-key="id"
                    :props="{ label: 'name' }"></el-tree>
            </el-scrollbar>
            <template #footer>
                <div class="text-center">
                    <el-button @click="treeShow = false">取消</el-button>
                    <el-button type="primary" @click="handleTreeConfirm">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="tsx" setup>
import { EFileSoureType, purchaseDeptList } from '@/utils/constants';
import { foreach, fromArray } from 'tree-lodash';
import { purchaseItems } from './data/purchaseItems';
import { purchaseService } from './data/purchaseService';
import { TreeWithParentKey } from 'tree-lodash/dist/esm/fromArray';
import { TreeKey } from 'tree-lodash/dist/esm/types';
import { CheckboxValueType, dayjs, ElMessage, TreeInstance } from 'element-plus';
import { dateFormat, genSnowId } from '@/utils/common';
import { getPurchase, getPurchaseProjectOptions, savePurchase } from '@/api/fund/purchase';
import FileTable from '@/components/Table/FileTable.vue';
// @ts-ignore
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");
const {
    purchase_catalog,
    purchase_quota,
    purchase_sme,
    purchase_reason,
    purchase_form,
    purchase_mode
} = proxy.useDict("purchase_catalog", "purchase_quota", 'purchase_sme', 'purchase_reason', 'purchase_form', 'purchase_mode');
const yerOrNoOption = [{ label: '是', value: '1' }, { label: '否', value: '0' }]

const fileTableRef = ref<FileTableExposeType | null>(null)
const requireTextHeight = ref(46)
const purchaseItemTree = fromArray(purchaseItems as any, { itemKey: 'id', parentKey: 'pId' })
const purchaseServiceTree = fromArray(purchaseService as any, { itemKey: 'id', parentKey: 'pId' })

const formId = useRoute().query.id as string
const form = reactive<FundPurchaseVo>({
    id: genSnowId(),
    doEarly: '0',
    orgName: userStore.deptName
} as FundPurchaseVo)

defineExpose({
    async getData() {
        return {
            ...form,
            attachments: []
        }
    }
})

if (formId) {
    const res = await getPurchase(formId)
    Object.assign(form, res.data)
}

watch(() => form.scaleId, () => {
    form.unfitId = ''
})

/** 1 品类 2 服务 */
const treeType = ref('1')
const treeShow = ref(false)
const currentTree = ref<TreeWithParentKey<string, string, TreeKey>[]>([])
const handleOpenTree = (type: string) => {
    setTimeout(() => {
        treeType.value = type

        if (type == '1') {
            if (form.itemId == '10') {
                currentTree.value = purchaseItemTree.filter(item => item.id == 'A')
                treeShow.value = true
            }
            if (form.itemId == '20') {
                currentTree.value = purchaseItemTree.filter(item => item.id == 'B')
                treeShow.value = true
            }
            if (form.itemId == '30') {
                currentTree.value = purchaseItemTree.filter(item => item.id == 'C')
                treeShow.value = true
            }
        }
        if (type == '2') {
            if (form.catalogFlag == '1') {
                currentTree.value = purchaseServiceTree
                treeShow.value = true
            }
        }
    }, 1)
}
const treeRef = ref<TreeInstance | null>()
const handleTreeConfirm = () => {
    const currentNode = treeRef.value?.getCurrentNode()
    if (currentNode?.children?.length > 0) {
        ElMessage.warning('请选择具体项目')
        return
    }
    const getText = () => {
        const list: string[] = [currentNode?.name]
        let currentPid = currentNode?.pId
        foreach(currentTree.value, (node) => {
            if (node.id == currentPid) {
                list.push(node.name)
                currentPid = node.pId
            }
        }, { strategy: 'post' })
        return list.reverse().join('>')
    }
    if (treeType.value == '1') {
        form.itemText = getText()
    }
    if (treeType.value == '2') {
        form.catalogText = getText()
    }
    treeShow.value = false
}

const projectOptions = ref<IProjectInfoVo[]>([])
const projectSelectLoading = ref(false)
const remoteMethod = (query: string) => {
    if (query) {
        projectSelectLoading.value = true
        getPurchaseProjectOptions(query).then(res => {
            projectOptions.value = res.data ?? []
        }).finally(() => {
            projectSelectLoading.value = false
        })
    } else {
        projectOptions.value = []
    }
}
const handleProjectChange = (val: string) => {
    const project = projectOptions.value.find(item => item.id === val)
    if (project) {
        form.projId = project.id!
        form.projName = project.name
    }
}



const ElCheckboxOne = defineComponent({
    name: 'ElCheckboxOne',
    props: {
        modelValue: {
            type: [String, Number],
            required: false,
        },
        options: {
            type: Array as PropType<{ label: string; value: string | number }[]>,
            required: true,
        },
        isUnChecked: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
        const handleChange = (val: string | number) => {
            if (props.isUnChecked && props.modelValue === val) {
                emit('update:modelValue', null)
            } else {
                emit('update:modelValue', val)
            }

        }

        return () => (
            <div>
                {props.options.map((opt) => (
                    <el-checkbox
                        key={opt.value}
                        label={opt.value}
                        modelValue={props.modelValue === opt.value}
                        onChange={() => handleChange(opt.value)}
                    >
                        {opt.label}
                    </el-checkbox>
                ))}
            </div>
        )
    },
})


</script>

<style scoped>
.apply-form {
    width: 1000px;
    background-color: white;
}

.apply-form .form-title {
    font-size: 32px;
    text-align: center;
    margin: 15px 0;
}

#txtProjectName {
    padding: 8px 0;
    font-size: 16px;
    font-family: 微软雅黑;
    border: 0;
    border-bottom: 1px solid #000;
    outline: none;
    width: 600px;
}

.apply-form .form-body .table-info {
    width: 100%;
    border: 2px solid #000;
    border-collapse: collapse;
}

.apply-form .form-body .table-info td {
    text-align: center;
    border: 1px solid #000;
    border-collapse: collapse;
    width: 16%;
    padding: 15px 10px;
}

.w30p {
    width: 30% !important;
}

.apply-form .form-body .table-audit {
    width: 100%;
}

.apply-form .form-body .table-audit td {
    text-align: center;
    width: 33.33%;
    padding: 15px 10px;
}

.td-left {
    text-align: left !important;
}

.td-right {
    text-align: right !important;
}

span[contenteditable="true"] {
    outline: none;
    display: inline-block;
    width: 100%;
}

input[type="text"] {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    text-indent: 5px;
    font-size: 16px;
    outline: none;
}

input[type="number"] {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    text-indent: 5px;
    font-size: 16px;
    outline: none;
}

textarea {
    border: 0;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    text-indent: 5px;
    font-size: 16px;
    outline: none;
}

:deep(.proj-name .el-select__wrapper) {
    box-shadow: none
}
</style>