<template>
    <el-descriptions :column="1" label-width="15%" border>
        <el-descriptions-item label="中标单位类型">
            <template #label>
                中标单位类型 <span class="text-red text-4">*</span>
            </template>
            {{ auditForm.currentNodeData?.operateDataResult?.winType }}
        </el-descriptions-item>
        <el-descriptions-item label="中标单位名称">
            <template #label>
                中标单位名称 <span class="text-red text-4">*</span>
            </template>
            {{ auditForm.currentNodeData?.operateDataResult?.winOrg }}
        </el-descriptions-item>
        <el-descriptions-item label="成交日期">
            <template #label>
                成交日期 <span class="text-red text-4">*</span>
            </template>
            {{ dateFormat(auditForm.currentNodeData?.operateDataResult?.winDate) }}
        </el-descriptions-item>
        <el-descriptions-item label="中标金额">
            <template #label>
                中标金额 <span class="text-red text-4">*</span>
            </template>
            {{ numberFormat(auditForm.currentNodeData?.operateDataResult?.winAmount, 2) }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
            {{ auditForm.currentNodeData?.operateDataResult?.winDescribe }}
        </el-descriptions-item>
        <el-descriptions-item label="附件">
            <div class="w-full max-w-200">
                <FileTable ref="fileTableRef" :source-id="auditForm.getPurchaseId!" primary-type="中标文件"
                    :hidden-columns="['primaryType', 'del']"></FileTable>
            </div>

        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang="ts" setup>
import { dateFormat, numberFormat } from '@/utils/common';
import { useAuditStore } from '../../store/formStore';
import FileTable from '@/components/Table/FileTable.vue'


const auditForm = useAuditStore()
</script>