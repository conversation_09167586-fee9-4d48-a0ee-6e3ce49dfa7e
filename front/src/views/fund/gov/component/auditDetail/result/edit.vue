<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item label="中标单位类型">
                <template #label>
                    中标单位类型 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="winType" class="!mb-0">
                    <el-select v-model="form.winType" placeholder="请选择中标单位类型" clearable class="w-50">
                        <el-option label="小型" value="小型"></el-option>
                        <el-option label="中型" value="中型"></el-option>
                        <el-option label="大型" value="大型"></el-option>
                    </el-select>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="中标单位名称">
                <template #label>
                    中标单位名称 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="winOrg" class="!mb-0">
                    <el-input v-model="form.winOrg" class="w-50"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="成交日期">
                <template #label>
                    成交日期 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="winDate" class="!mb-0">
                    <el-date-picker v-model="form.winDate" type="date" value-format="YYYY-MM-DD" placeholder="选择日期"
                        class="!w-50"></el-date-picker>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="中标金额">
                <template #label>
                    中标金额 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="winAmount" class="!mb-0">
                    <el-input-number v-model="form.winAmount" :min="0" controls-position="right" class="w-50"></el-input-number>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="备注">
                <el-input v-model="form.winDescribe" type="textarea" :rows="5" class="w-200"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="附件">
                <div class="mb-2">
                    <MyFileUpload :accept="fileType" :data="{
                        sourceId: auditForm.getPurchaseId,
                        sourceType: EFileSoureType.FUND_PURCHASE,
                        primaryType: '中标文件'
                    }" @upload-success="fileTableRef?.updateTable"></MyFileUpload>
                </div>
                <div class="w-full max-w-200">
                    <FileTable ref="fileTableRef" :source-id="auditForm.getPurchaseId!" primary-type="中标文件"
                        :hidden-columns="['primaryType']"></FileTable>
                </div>

            </el-descriptions-item>
        </el-descriptions>
    </el-form>
</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus';
import { useAuditStore } from '../../store/formStore';
import { EFileSoureType } from '@/utils/constants';
import FileTable from '@/components/Table/FileTable.vue'

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const auditForm = useAuditStore()
const form = reactive({
    winType: auditForm.currentNodeData?.operateDataResult?.winType ?? '',
    winOrg: auditForm.currentNodeData?.operateDataResult?.winOrg ?? '',
    winDate: auditForm.currentNodeData?.operateDataResult?.winDate ?? '',
    winAmount: auditForm.currentNodeData?.operateDataResult?.winAmount ?? null,
    winDescribe: auditForm.currentNodeData?.operateDataResult?.winDescribe ?? '',
})
const rules = {
    noticeDate: [{ required: true, message: '请选择公告发布日期', trigger: 'blur' }]
}
const fileTableRef = ref<FileTableExposeType | null>(null)
const formRef = ref<FormInstance>()
defineExpose({
    async getData() {
        return {
            ...form,
            files: fileTableRef.value?.getFileList().map(item => ({ name: item.fileName, fileId: item.id }))
        }
    },
    async getDataAndCheck() {
        await formRef.value?.validate()
        return {
            ...form,
            files: fileTableRef.value?.getFileList().map(item => ({ name: item.fileName, fileId: item.id }))
        }
    }
})
</script>