<template>
    <el-descriptions :column="1" label-width="15%" border>
        <el-descriptions-item label="公告发布日期">
            <template #label>
                    公告发布日期 <span class="text-red text-4">*</span>
                </template>
            {{ auditForm.currentNodeData?.operateDataResult?.noticeDate }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
            {{ auditForm.currentNodeData?.operateDataResult?.noticeDescribe }}
        </el-descriptions-item>
        <el-descriptions-item label="附件">
            <div class="w-full max-w-200">
                <FileTable ref="fileTableRef" :source-id="auditForm.getPurchaseId!" primary-type="采购文件"
                    :hidden-columns="['primaryType', 'del']"></FileTable>
            </div>

        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang="ts" setup>
import { useAuditStore } from '../../store/formStore';
import FileTable from '@/components/Table/FileTable.vue'


const auditForm = useAuditStore()
</script>