<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item label="采购代理单位名称">
                <template #label>
                    采购代理单位名称 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="purchaseCode" class="!mb-0">
                    <el-input v-model="form.agencyOrg" placeholder="请输入采购代理单位名称" class="!w-50"></el-input>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="备注">
                <el-input v-model="form.agencyDescribe" type="textarea" :rows="5" class="w-200"></el-input>

            </el-descriptions-item>
        </el-descriptions>
    </el-form>

</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus';
import { useAuditStore } from '../../store/formStore';

const auditForm = useAuditStore()
const form = reactive({
    agencyOrg: auditForm.currentNodeData?.operateDataResult?.agencyOrg ?? '',
    agencyDescribe: auditForm.currentNodeData?.operateDataResult?.agencyDescribe ?? '',
})
const rules = {
    agencyOrg: [{ required: true, message: '请输入采购代理单位名称', trigger: 'blur' }],
}

const formRef = ref<FormInstance>()
defineExpose({
    async getData() {
        return {
            ...form
        }
    },
    async getDataAndCheck() {
        await formRef.value?.validate()
        return {
            ...form
        }
    }
})
</script>