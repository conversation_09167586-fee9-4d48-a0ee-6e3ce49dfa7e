<template>
    <el-descriptions :column="1" label-width="15%" border>
        <el-descriptions-item label="采购代理单位名称">
            <template #label>
                采购代理单位名称 <span class="text-red text-4">*</span>
            </template>
            {{ auditForm.currentNodeData?.operateDataResult?.agencyOrg }}
        </el-descriptions-item>
        <el-descriptions-item label="备注">
            {{ auditForm.currentNodeData?.operateDataResult?.agencyDescribe }}
        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang="ts" setup>
import { useAuditStore } from '../../store/formStore';


const auditForm = useAuditStore()
</script>