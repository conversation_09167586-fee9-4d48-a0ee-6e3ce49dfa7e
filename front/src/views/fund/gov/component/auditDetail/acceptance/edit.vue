<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item label="验收日期">
                <template #label>
                    验收日期 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="acceptanceDate" class="!mb-0">
                    <el-date-picker v-model="form.acceptanceDate" type="date" value-format="YYYY-MM-DD"
                        placeholder="选择日期" class="!w-50"></el-date-picker>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="验收结果">
                <template #label>
                    验收结果 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="acceptanceResult" class="!mb-0">
                    <el-select v-model="form.acceptanceResult" clearable placeholder="请选择" class="w-50">
                        <el-option label="通过" value="1"></el-option>
                        <el-option label="不通过" value="0"></el-option>
                    </el-select>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="备注">
                <el-input v-model="form.acceptanceDescribe" type="textarea" :rows="5" class="w-full max-w-200"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="附件">
                 <div class="mb-2">
                    <MyFileUpload :accept="fileType" :data="{
                        sourceId: auditForm.getPurchaseId,
                        sourceType: EFileSoureType.FUND_PURCHASE,
                        primaryType: '验收文件'
                    }" @upload-success="fileTableRef?.updateTable"></MyFileUpload>
                 </div>
                <div class="w-full max-w-200">
                   <FileTable ref="fileTableRef" :source-id="auditForm.getPurchaseId!" primary-type="验收文件"
                    :hidden-columns="['primaryType']"></FileTable>
                </div>
                
            </el-descriptions-item>
        </el-descriptions>
    </el-form>
</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus';
import { useAuditStore } from '../../store/formStore';
import { EFileSoureType } from '@/utils/constants';
import FileTable from '@/components/Table/FileTable.vue'

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const auditForm = useAuditStore()
const form = reactive({
    acceptanceDate: auditForm.currentNodeData?.operateDataResult?.acceptanceDate ?? '',
    acceptanceResult: auditForm.currentNodeData?.operateDataResult?.acceptanceResult ?? '',
    acceptanceDescribe: auditForm.currentNodeData?.operateDataResult?.acceptanceDescribe ?? '',
})
const rules = {
    acceptanceDate: [{ required: true, message: '请选择验收日期', trigger: 'blur' }],
    acceptanceResult: [{ required: true, message: '请选择验收结果', trigger: 'blur' }]
}
const fileTableRef = ref<FileTableExposeType | null>(null)
const formRef = ref<FormInstance>()
defineExpose({
    async getData() {
        return {
            ...form,
            files: fileTableRef.value?.getFileList().map(item => ({name: item.fileName, fileId: item.id}))
        }
    },
    async getDataAndCheck() {
        await formRef.value?.validate()
        return {
            ...form,
            files: fileTableRef.value?.getFileList().map(item => ({name: item.fileName, fileId: item.id}))
        }
    }
})
</script>