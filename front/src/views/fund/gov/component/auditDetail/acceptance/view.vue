<template>
    <el-descriptions :column="1" label-width="15%" border>
        <el-descriptions-item width="85%">
            <template #label>
                验收日期 <span class="text-red text-4">*</span>
            </template>
            {{ auditForm.currentNodeData?.operateDataResult?.acceptanceDate }}
        </el-descriptions-item>
        <el-descriptions-item>
            <template #label>
                验收结果 <span class="text-red text-4">*</span>
            </template>
            <span v-if="auditForm.currentNodeData?.operateDataResult?.acceptanceResult == '1'" class="text-green">通过</span>
            <span v-if="auditForm.currentNodeData?.operateDataResult?.acceptanceResult == '0'" class="text-red">不通过</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注">
            {{ auditForm.currentNodeData?.operateDataResult?.acceptanceDescribe }}
        </el-descriptions-item>
        <el-descriptions-item label="附件">
            <div class="w-full max-w-200">
                <FileTable ref="fileTableRef" :source-id="auditForm.getPurchaseId!" primary-type="验收文件"
                    :hidden-columns="['primaryType', 'del']"></FileTable>
            </div>

        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang="ts" setup>
import { useAuditStore } from '../../store/formStore';
import FileTable from '@/components/Table/FileTable.vue'


const auditForm = useAuditStore()
</script>