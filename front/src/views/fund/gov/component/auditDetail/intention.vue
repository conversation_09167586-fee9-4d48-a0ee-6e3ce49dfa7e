<template>
    <div class="relative h-full">
        <div class="absolute top-0 left-0 z-10 w-full bg-white">
            <p class="my-0 text-5 font-bold">意向公开</p>
            <el-divider class="my-3" />
        </div>

        <el-scrollbar height="100%">
            <div class="w-full py-15">
                <Edit ref="editFormRef" v-if="auditForm.userCanOperate"></Edit>
                <View v-else></View>
            </div>

        </el-scrollbar>


        <!-- 操作按钮 -->
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <template v-if="auditForm.userCanOperate">
                    <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
                    <el-button type="success" :loading="loading" @click="handleSubmit">提交</el-button>
                </template>
                <el-button @click="$router.back()">返回列表</el-button>
            </div>

        </div>
    </div>
</template>

<script setup lang="ts">
import { doOperate } from '@/api/fund/purchase';
import Edit from './intention/edit.vue';
import View from './intention/view.vue';
import { useAuditStore } from '../store/formStore';
import { ElMessage } from 'element-plus';

const auditForm = useAuditStore()

const loading = ref(false)
const editFormRef = ref()
const handleSave = async () => {
    const data = await editFormRef.value?.getData()
    loading.value = true
    doOperate({
        isSubmit: false,
        purchaseId: auditForm.getPurchaseId!,
        operateData: data
    }).then(res => {
        ElMessage.success('保存成功')
        auditForm.fetchFundAudit()
    }).finally(() => {
        loading.value = false
    })
}

const handleSubmit = async () => {
    const data = await editFormRef.value?.getDataAndCheck()
    loading.value = true
    doOperate({
        isSubmit: true,
        purchaseId: auditForm.getPurchaseId!,
        operateData: data
    }).then(res => {
        ElMessage.success('保存成功')
        auditForm.fetchFundAudit()
        auditForm.toNextNode()
    }).finally(() => {
        loading.value = false
    })
}
</script>