<template>
    <el-descriptions :column="1" label-width="10%" border>
            <el-descriptions-item label="公开日期" width="90%">
                <template #label>
                    公开日期 <span class="text-red text-4">*</span>
                </template>
                {{ dateFormat(auditForm.currentNodeData?.operateDataResult?.intendDate, 'YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item label="描述">
                {{ auditForm.currentNodeData?.operateDataResult?.intendDescribe }}

            </el-descriptions-item>
            <el-descriptions-item label="提示">
                （1）该项目政府采购意向已公开，可以到上海政府采购网http://www.zfcg.sh.gov.cn/中“市级采购公告-采购意向公开”查阅。<br />
                （2）采购意向公开时间原则上不得晚于采购活动开始前30日。<br />
                （3）请项目单位尽快编制采购招标文件，请采购人规范委托政府采购代理机构。

            </el-descriptions-item>
        </el-descriptions>
</template>

<script setup lang="ts">
import { dateFormat } from '@/utils/common';
import { useAuditStore } from '../../store/formStore';

const auditForm = useAuditStore()
</script>