<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="10%" border>
            <el-descriptions-item label="公开日期" width="90%">
                <template #label>
                    公开日期 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="intendDate" class="!mb-0">
                    <el-date-picker v-model="form.intendDate" type="date" value-format="YYYY-MM-DD" placeholder="选择日期"
                        class="w-50"></el-date-picker>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="描述">
                <el-input v-model="form.intendDescribe" type="textarea" :rows="5" class="w-200"></el-input>

            </el-descriptions-item>
            <el-descriptions-item label="提示">
                （1）该项目政府采购意向已公开，可以到上海政府采购网<a
                    href="http://www.zfcg.sh.gov.cn/">http://www.zfcg.sh.gov.cn/</a>中“市级采购公告-采购意向公开”查阅。<br />
                （2）采购意向公开时间原则上不得晚于采购活动开始前30日。<br />
                （3）请项目单位尽快编制采购招标文件，请采购人规范委托政府采购代理机构。

            </el-descriptions-item>
        </el-descriptions>
    </el-form>

</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus';
import { useAuditStore } from '../../store/formStore';

const auditForm = useAuditStore()
const form = reactive({
    intendDate: auditForm.currentNodeData?.operateDataResult?.intendDate ?? '',
    intendDescribe: auditForm.currentNodeData?.operateDataResult?.intendDescribe ?? ''
})
const rules = {
    intendDate: [{ required: true, message: '请选择公开日期', trigger: 'blur' }]
}

const formRef = ref<FormInstance>()
defineExpose({
     async getData() {
        return {
            ...form
        }
    },
    async getDataAndCheck() {
        await formRef.value?.validate()
        return {
            ...form
        }
    }
})
</script>