<template>
    <div class="relative h-full">

        <el-scrollbar height="100%">
            <div class="flex justify-center pb-10">
                <ApplyTableView></ApplyTableView>
            </div>
        </el-scrollbar>
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <el-button v-if="auditForm.userCanOperate" type="danger" :loading="loading" @click="handleReject">退回</el-button>
                <el-button v-if="auditForm.userCanOperate" type="success" :loading="loading" @click="handlePass">通过</el-button>
                <el-button type="primary" v-print="{ id: '#printTable', exclude: '.no-print' }">打印</el-button>
                <el-button type="info" @click="operateHistoryShow = true">操作历史</el-button>
                <el-button @click="$router.back()">返回列表</el-button>
            </div>
        </div>

        <el-dialog title="操作历史" v-model="operateHistoryShow" width="800px" :close-on-click-modal="false" destroy-on-close>
            <AuditHistory :purchase-id="auditForm.getPurchaseId" />
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { doAudit } from '@/api/fund/purchase';
import ApplyTableView from '../applyTableView.vue';
import AuditHistory from '../auditHistory.vue';
import { useAuditStore } from '../store/formStore';
import { ElMessage, ElMessageBox } from 'element-plus';

const auditForm = useAuditStore()

const operateHistoryShow = ref(false)
const loading = ref(false)
const handlePass = () => {
    ElMessageBox.confirm('是否确认审核通过？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then((res) => {
        loading.value = true
        doAudit({
            purchaseId: auditForm.getPurchaseId,
            operateResult: 1,
        }).then(res => {
            ElMessage.success('审核成功')
            auditForm.fetchFundAudit()
        }).finally(() => {
            loading.value = false
        })
    })
}

const handleReject = () => {
    ElMessageBox.prompt('请输入驳回理由', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async ({value}) => {
        loading.value = true
        doAudit({
            purchaseId: auditForm.getPurchaseId,
            operateResult: 0,
            operateReason: value
        }).then(res => {
            ElMessage.success('退回成功')
            auditForm.fetchFundAudit()
        }).finally(() => {
            loading.value = false
        })

    })
}
</script>