<template>
    <div class="relative h-full ">

        <el-scrollbar height="100%">
            <div class="flex justify-center pb-10">
                <ApplyTableView ref="applyTableRef"></ApplyTableView>
            </div>
        </el-scrollbar>
        <div class="absolute bottom-0  w-full z-10 bg-white">
            <el-divider class="my-3" />
            <div class="w-full flex justify-center items-center">
                <el-button v-if="canSubmit" type="primary" :loading="loading" @click="handleSave">保存</el-button>
                <el-button v-if="canSubmit" type="success" :loading="loading" @click="handleSubmit">提交</el-button>
                <el-button type="primary" v-print="{ id: '#printTable', exclude: '.no-print' }">打印</el-button>
                <el-button type="info" @click="operateHistoryShow = true">操作历史</el-button>
                <el-button @click="$router.back()">返回列表</el-button>
            </div>
        </div>

        <el-dialog title="操作历史" v-model="operateHistoryShow" width="800px" :close-on-click-modal="false"
            destroy-on-close>
            <AuditHistory :purchase-id="auditForm.getPurchaseId" />
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { canUserSubmit, savePurchase, submitPurchase } from '@/api/fund/purchase';
import ApplyTableView from '../applyTableView.vue';
import AuditHistory from '../auditHistory.vue';
import { useAuditStore } from '../store/formStore';
import { ElMessage, ElMessageBox } from 'element-plus';

const auditForm = useAuditStore()

const canSubmit = (await canUserSubmit(auditForm.getPurchaseId!)).data

const operateHistoryShow = ref(false)
const loading = ref(false)
const applyTableRef = ref()
const handleSave = async () => {
    const data = await applyTableRef.value.getData()
    loading.value = true
    savePurchase(data).then(() => {
        ElMessage.success('保存成功')
    }).finally(() => {
        loading.value = false
    })
}



const handleSubmit = async () => {
    ElMessageBox.confirm('确定提交吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const data = await applyTableRef.value.getData()
        loading.value = true
        submitPurchase(data).then(() => {
            ElMessage.success('提交成功')
            auditForm.fetchFundAudit()
        }).finally(() => {
            loading.value = false
        })
    })
}

</script>