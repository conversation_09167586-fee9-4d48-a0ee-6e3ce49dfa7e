<template>
    <el-descriptions :column="1" label-width="15%" border>
        <el-descriptions-item width="85%">
            <template #label>
                政府采购实施 <br />
                计划发送日期 <span class="text-red">*</span>
            </template>
            {{ dateFormat(auditForm.currentNodeData?.operateDataResult?.planDate, 'YYYY-MM-DD') }}
        </el-descriptions-item>
        <el-descriptions-item label="描述">
            <template #label>
                采购编码 <span class="text-red">*</span>
            </template>
            {{ auditForm.currentNodeData?.operateDataResult?.purchaseCode }}
        </el-descriptions-item>
        <el-descriptions-item label="描述">
            {{ auditForm.currentNodeData?.operateDataResult?.planDescribe }}
        </el-descriptions-item>
        <el-descriptions-item label="提示">
            政府采购实施计划已发送，请采购人到政府采购云平台查看。

        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang="ts" setup>
import { dateFormat } from '@/utils/common';
import { useAuditStore } from '../../store/formStore';


const auditForm = useAuditStore()
</script>