<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item width="85%">
                <template #label>
                    政府采购实施 <br />
                    计划发送日期 <span class="text-red">*</span>
                </template>
                <el-form-item prop="planDate" class="!mb-0">
                    <el-date-picker v-model="form.planDate" type="date" value-format="YYYY-MM-DD" placeholder="选择日期"
                        class="!w-50"></el-date-picker>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="描述">
                <template #label>
                    采购编码 <span class="text-red">*</span>
                </template>
                <el-form-item prop="purchaseCode" class="!mb-0">
                    <el-input v-model="form.purchaseCode" placeholder="请输入采购编码" class="!w-50"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="描述">
                <el-input v-model="form.planDescribe" type="textarea" :rows="5" class="w-200"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="提示">
                政府采购实施计划已发送，请采购人到政府采购云平台查看。

            </el-descriptions-item>
        </el-descriptions>
    </el-form>

</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus';
import { useAuditStore } from '../../store/formStore';

const auditForm = useAuditStore()
const form = reactive({
    planDate: auditForm.currentNodeData?.operateDataResult?.planDate ?? '',
    purchaseCode: auditForm.currentNodeData?.operateDataResult?.purchaseCode ?? '',
    planDescribe: auditForm.currentNodeData?.operateDataResult?.planDescribe ?? ''
})
const rules = {
    planDate: [{ required: true, message: '请选择政府采购实施计划发送日期', trigger: 'blur' }],
    purchaseCode: [{ required: true, message: '请输入采购编码', trigger: 'blur' }],
}

const formRef = ref<FormInstance>()
defineExpose({
     async getData() {
        return {
            ...form
        }
    },
    async getDataAndCheck() {
        await formRef.value?.validate()
        return {
            ...form
        }
    }
})
</script>