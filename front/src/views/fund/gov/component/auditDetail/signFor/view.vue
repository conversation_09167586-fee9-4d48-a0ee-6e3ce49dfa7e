<template>
    <el-descriptions :column="1" label-width="15%" border>
        <el-descriptions-item label="(网签)合同编号" width="85%">
            <template #label>
                (网签)合同编号 <span class="text-red text-4">*</span>
            </template>
            {{ auditForm.currentNodeData?.operateDataResult?.contractCode }}

        </el-descriptions-item>
        <el-descriptions-item label="签订日期">
            <template #label>
                签订日期 <span class="text-red text-4">*</span>
            </template>
            {{ dateFormat(auditForm.currentNodeData?.operateDataResult?.contractDate) }}
        </el-descriptions-item>
        <el-descriptions-item label="合同金额(元) ">
            <template #label>
                合同金额(元) <span class="text-red text-4">*</span>
            </template>
            {{ numberFormat(auditForm.currentNodeData?.operateDataResult?.contractAmount, 2) }}
        </el-descriptions-item>
        <el-descriptions-item label="付款条件">
            <el-table :data="auditForm.currentNodeData?.operateDataResult?.rules ?? []" border class="w-full max-w-200">
                <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                <el-table-column prop="percent" label="支付比例" width="150" align="center"></el-table-column>
                <el-table-column prop="remark" label="支付说明" header-align="center" align="left"></el-table-column>
                <el-table-column prop="amount" label="支付金额(元)" width="150" align="center"></el-table-column>
            </el-table>

        </el-descriptions-item>
        <el-descriptions-item label="服务期">
            <template #label>
                服务期 <span class="text-red text-4">*</span>
            </template>
            {{ auditForm.currentNodeData?.operateDataResult?.contractPeriod }}

        </el-descriptions-item>
        <el-descriptions-item label="附件">
            <div class="w-full max-w-200">
                <FileTable ref="fileTableRef" :source-id="auditForm.getPurchaseId!" primary-type="采购合同"
                    :hidden-columns="['primaryType', 'del']"></FileTable>
            </div>

        </el-descriptions-item>
    </el-descriptions>
</template>

<script lang="ts" setup>
import { dateFormat, numberFormat } from '@/utils/common';
import { useAuditStore } from '../../store/formStore';
import FileTable from '@/components/Table/FileTable.vue'


const auditForm = useAuditStore()
</script>