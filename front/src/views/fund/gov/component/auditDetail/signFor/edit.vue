<template>
    <el-form :model="form" :rules="rules" ref="formRef" :inline-message="true">
        <el-descriptions :column="1" label-width="15%" border>
            <el-descriptions-item label="(网签)合同编号" width="85%">
                <template #label>
                    (网签)合同编号 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="contractCode" class="!mb-0">
                    <el-input v-model="form.contractCode" class="w-50"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="签订日期">
                <template #label>
                    签订日期 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="contractDate" class="!mb-0">
                    <el-date-picker v-model="form.contractDate" type="date" value-format="YYYY-MM-DD" placeholder="选择日期"
                        class="!w-50"></el-date-picker>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="合同金额(元) ">
                <template #label>
                    合同金额(元)  <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="contractAmount" class="!mb-0">
                    <el-input-number v-model="form.contractAmount" :min="0" controls-position="right" class="w-50"></el-input-number>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="付款条件">
                <el-table :data="form.rules" border class="w-full max-w-200">
                    <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                    <el-table-column prop="index" label="支付比例" width="150" align="center">
                        <template #default="{row}">
                            <el-input-number v-model="row.percent" :min="0" controls-position="right" class="w-full"></el-input-number>
                        </template>
                    </el-table-column>
                    <el-table-column prop="index" label="支付说明" align="center">
                        <template #default="{row}">
                            <el-input v-model="row.remark" type="textarea" :rows="1" autosize class="w-full"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="amount" label="支付金额(元)" width="150" align="center">
                        <template #default="{row}">
                            {{ BigNumber(form.contractAmount??0).multipliedBy(BigNumber(row.percent??0).dividedBy(100)).toFixed(2) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="140" align="center">
                        <template #default="{row, $index}">
                            <el-button type="primary" v-if="$index == 0" link @click="form.rules.push({})">添加付款次数</el-button>
                            <el-button type="danger" v-else link @click="form.rules.splice($index, 1)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>

            </el-descriptions-item>
            <el-descriptions-item label="服务期">
                <template #label>
                    服务期 <span class="text-red text-4">*</span>
                </template>
                <el-form-item prop="contractPeriod" class="!mb-0">
                    <el-input v-model="form.contractPeriod" type="textarea" :rows="5" class="w-full max-w-200"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="附件">
                <div class="mb-2">
                    <MyFileUpload :accept="fileType" :data="{
                        sourceId: auditForm.getPurchaseId,
                        sourceType: EFileSoureType.FUND_PURCHASE,
                        primaryType: '采购合同'
                    }" @upload-success="fileTableRef?.updateTable"></MyFileUpload>
                </div>
                <div class="w-full max-w-200">
                    <FileTable ref="fileTableRef" :source-id="auditForm.getPurchaseId!" primary-type="采购合同"
                        :hidden-columns="['primaryType']"></FileTable>
                </div>

            </el-descriptions-item>
        </el-descriptions>
    </el-form>
</template>

<script setup lang="ts">
import { FormInstance } from 'element-plus';
import { useAuditStore } from '../../store/formStore';
import { EFileSoureType } from '@/utils/constants';
import FileTable from '@/components/Table/FileTable.vue'
import { BigNumber } from 'bignumber.js';

const { proxy } = getCurrentInstance() as { proxy: any };
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const auditForm = useAuditStore()
const form = reactive({
    contractCode: auditForm.currentNodeData?.operateDataResult?.contractCode ?? '',
    contractDate: auditForm.currentNodeData?.operateDataResult?.contractDate ?? '',
    contractAmount: auditForm.currentNodeData?.operateDataResult?.contractAmount ?? null,
    contractPeriod: auditForm.currentNodeData?.operateDataResult?.contractPeriod ?? '',
    rules: (auditForm.currentNodeData?.operateDataResult?.rules) as any[] ?? [
        {
            id: '',
            percent: null,
            amount: '',
            remark: ''
        }
    ],
    files: []
})
const rules = {
    contractCode: [{ required: true, message: '请输入(网签)合同编号 ', trigger: 'blur' }],
    contractDate: [{ required: true, message: '请选择签订日期', trigger: 'blur' }],
    contractAmount: [{ required: true, message: '请输入合同金额(元) ', trigger: 'blur' }],
    contractPeriod: [{ required: true, message: '请输入服务期', trigger: 'blur' }]
}
const fileTableRef = ref<FileTableExposeType | null>(null)
const formRef = ref<FormInstance>()
defineExpose({
    async getData() {
        return {
            ...form,
            files: fileTableRef.value?.getFileList().map(item => ({ name: item.fileName, fileId: item.id }))
        }
    },
    async getDataAndCheck() {
        await formRef.value?.validate()
        return {
            ...form,
            rules: form.rules.map((rule, index) => {
                return {
                    ...rule,
                    id: index +1,
                    amount: BigNumber(form.contractAmount??0).multipliedBy(BigNumber(rule.percent??0).dividedBy(100)).toFixed(2)
                }
                
            }),
            files: fileTableRef.value?.getFileList().map(item => ({ name: item.fileName, fileId: item.id }))
        }
    }
})
</script>