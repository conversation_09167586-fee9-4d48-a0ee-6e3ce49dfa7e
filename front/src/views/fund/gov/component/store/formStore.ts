import { getPurchase } from "@/api/fund/purchase"

export const useAuditStore = defineStore('purchaseStore', () => {

    const activate = ref(0)

    const setActivate = (nodeSeq: number) => {
        activate.value = nodeSeq
    }

    const toNextNode = () => {
        activate.value++
    }

    const userCanOperate = ref(false)

    /**
     * 主表单
     */
    const mainForm = ref<FundPurchaseVo>()
    const fetchFundAudit = async (formId?: string) => {
        const formRes = await getPurchase(formId ?? mainForm.value?.id!)
        mainForm.value = formRes.data
        activate.value = mainForm.value!.nodeSeq
    }



    /**
     * 当前节点数据
     */
    const currentNodeData = ref<FundPurchaseNodeDataResultVo>()

    /**
     * 当前节点的序号与主表单是否一致，用于判断是否可以提交
     */
    const isNodeSeqEqualMainForm = computed(() => {
        return mainForm.value?.nodeSeq === currentNodeData.value?.nodeSeq
    })

    const getPurchaseId = computed(() => {
        return mainForm.value?.id
    })

    return {
        activate,
        userCanOperate,
        setActivate,
        toNextNode,
        fetchFundAudit,
        mainForm,
        currentNodeData,
        isNodeSeqEqualMainForm,
        getPurchaseId
    }
})