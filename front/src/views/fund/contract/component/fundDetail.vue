<template>
  <div class="">
    <el-table :data="tableData" height="500px" border style="width: 100%">
      <el-table-column type="index" label="序号" width="60">
        <template #default="{ row, $index }">
          <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="projectName" label="项目名称" min-width="200" />
      <el-table-column prop="contractName" label="合同名称" min-width="200" />
      <el-table-column prop="contractAmount" label="合同金额" />
      <el-table-column prop="totalPaid" label="拨付总额" />
      <el-table-column prop="receivingUnit" label="收款单位" />
      <el-table-column prop="paymentOrderNo" label="拨款单号" />
      <el-table-column prop="paymentAmount" label="拨款金额" />
      <el-table-column prop="paymentDate" label="拨款日期" />
      <el-table-column prop="voucherNo" label="凭证号码" />
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
      @pagination="handleSearch" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const page = reactive({
  pageNum: 1,
  pageSize: 10,
})
const tableData = ref([
  {
    projectName: '上海化工区开发建设图片展设计布展施工合同',
    contractName: '上海化工区开发建设图片展设计布展施工合同',
    contractAmount: '0',
    totalPaid: '0',
    receivingUnit: '巴博忆',
    paymentOrderNo: '',
    paymentAmount: '0.00',
    paymentDate: '',
    voucherNo: ''
  },
])
const total = ref(0)
const handleSearch = () => {
  // console.log('搜索条件：', searchForm.value)
}
</script>
