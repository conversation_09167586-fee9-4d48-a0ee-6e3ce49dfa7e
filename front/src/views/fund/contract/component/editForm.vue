<template>
    <el-scrollbar height="500px">
        <el-form :model="form" :rules="rules" ref="formRef" label-position="top" label-width="0" :inline-message="true">
            <el-descriptions :column="2" border label-width="15%" class="equal-width-columns">

                <!-- 合同编号（自动生成） -->
                <el-descriptions-item width="35%">
                    <template #label>合同编号</template>
                    <el-form-item prop="contractNo" class="!mb-0">
                        <el-input v-model="form.code" disabled />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 合同名称（必填） -->
                <el-descriptions-item width="35%">
                    <template #label><span class="text-red">*</span> 合同名称</template>
                    <el-form-item prop="name" class="!mb-0">
                        <el-input v-model="form.name" placeholder="请输入合同名称" maxlength="200" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 甲方（固定） -->
                <el-descriptions-item :span="1">
                    <template #label>甲方</template>
                    <el-form-item prop="partyA" class="!mb-0">
                        <el-input v-model="form.parta" disabled />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 乙方（必填） -->
                <el-descriptions-item :span="1">
                    <template #label>
                        <span class="text-red">*</span> 乙方
                    </template>
                    <el-form-item prop="partb" class="!mb-0">
                        <el-input v-model="form.partb" placeholder="请输入乙方名称" maxlength="200" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 所属项目（必填，可弹窗选择） -->
                <el-descriptions-item :span="1">
                    <template #label><span class="text-red">*</span> 所属项目</template>
                    <el-form-item prop="projId" class="!mb-0">
                        <el-input :model-value="form.projName" placeholder="请选择所属项目" readonly>
                            <template #suffix>
                                <el-icon class="cursor-pointer" @click="projectSelectShow  =true">
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-descriptions-item>

                <!-- 合同金额（必填） -->
                <el-descriptions-item :span="1">
                    <template #label><span class="text-red">*</span> 合同金额 (元)</template>
                    <el-form-item prop="amount" class="!mb-0">
                        <el-input-number v-model="form.amount" :min="0" controls-position="right" placeholder="请输入合同金额"
                            class="!w-full" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 签订日期 -->
                <el-descriptions-item :span="1">
                    <template #label>签订日期</template>
                    <el-form-item prop="signDate" class="!mb-0">
                        <el-date-picker v-model="form.signDate" type="date" placeholder="选择签订日期" style="width: 100%" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 起止日期 -->
                <el-descriptions-item :span="1">
                    <template #label>起止日期</template>
                    <el-form-item prop="dateRange" class="!mb-0">
                        <DateRangePicker v-model:begin-date="form.validatyBegin" v-model:end-date="form.validatyEnd"
                            type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD" class="!w-full">
                        </DateRangePicker>
                    </el-form-item>
                </el-descriptions-item>

                <!-- 合同内容 -->
                <el-descriptions-item :span="2">
                    <template #label><span class="text-red">*</span> 合同内容</template>
                    <el-form-item prop="content" class="!mb-0">
                        <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入合同主要内容" />
                    </el-form-item>
                </el-descriptions-item>

                <!-- 合同附件 -->
                <el-descriptions-item :span="2">
                    <template #label>合同附件</template>
                    <my-file-upload accept=".doc,.docx,.pdf" :data="{
                        sourceId: form.id,
                        sourceType: EFileSoureType.CONTRACT,
                        primaryType: EFileSoureType.CONTRACT,
                    }" @upload-success="fileTableRef?.updateTable"></my-file-upload>
                    <div class="mt-2">
                        <FileTable ref="fileTableRef" :source-id="form.id" :primaryType="EFileSoureType.CONTRACT"
                            :hiddenColumns="['index', 'primaryType']">
                        </FileTable>
                    </div>
                </el-descriptions-item>
            </el-descriptions>

        </el-form>
    </el-scrollbar>
    <!-- 操作按钮 -->
    <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
        <el-button type="success" @click="submitForm">保存</el-button>
        <el-button @click="emit('cancel')">关闭窗口</el-button>
    </div>
    <el-dialog title="项目选择" v-model="projectSelectShow" width="1000px" :close-on-click-modal="false" destroy-on-close>
        <ProjectTable @select="handleProjectSelect"></ProjectTable>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { EFileSoureType } from '@/utils/constants'
import { getFileList1 } from '@/api/lib/project/file';
import FileTable from '@/components/Table/FileTable.vue';
import { genSnowId } from '@/utils/common';
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
// @ts-ignore
import useUserStore from '@/store/modules/user'
import { addContract, editContract, getContractDetail } from '@/api/fund/contractInfo';
import ProjectTable from './projectTable.vue';

const emit = defineEmits(['close', 'cancel'])
const props = defineProps<{
    contractId?: string;
}>()

const userStore = useUserStore()


const fileTableRef = ref<FileTableExposeType | null>(null)
const formRef = ref<FormInstance>()
const form = reactive<FundContractInfoDto>({
    id: genSnowId(),
    code: '自动生成',
    parta: userStore.deptName,
} as FundContractInfoDto)
if (props.contractId) {
    const res = await getContractDetail(props.contractId)
    Object.assign(form, res.data)
}

const rules = reactive<FormRules<typeof form>>({
    name: [{ required: true, message: '请输入合同名称', trigger: 'blur' }],
    partb: [{ required: true, message: '请输入乙方', trigger: 'blur' }],
    projId: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
    amount: [{ required: true, message: '请输入合同金额', trigger: 'blur' }],
    content: [{ required: true, message: '请输入合同内容', trigger: 'blur' }],
})

const projectSelectShow = ref(false)
const handleProjectSelect = (data: {projId: string, projName: string}) => {
    projectSelectShow.value = false
    form.projId = data.projId
    form.projName = data.projName
}

const submitForm = () => {
    formRef.value?.validate((valid) => {
        if (valid) {
            let save = addContract
            if (props.contractId) {
                save = editContract
            }
            save(form).then(() => {
                ElMessage.success('保存成功');
                emit('close');
            });
        }
    })
}

</script>


<style scoped>
/* 深度选择器 */
.equal-width-columns :deep(.el-descriptions__table) {
    table-layout: fixed;
}
</style>
