<template>
    <div class="app-container">
        <!-- 搜索区域 -->
        <el-form :inline="true" :model="searchForm">
            <el-form-item label="甲方">
                <el-input v-model="searchForm.parta" placeholder="请输入甲方" class="w-40" clearable />
            </el-form-item>
            <el-form-item label="乙方">
                <el-input v-model="searchForm.partb" placeholder="请输入乙方" class="w-40" clearable />
            </el-form-item>
            <el-form-item label="所属项目">
                <el-input v-model="searchForm.projId" placeholder="请输入所属项目" class="w-40" clearable />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button type="success" @click="handleCreate">新建合同</el-button>
            </el-form-item>

        </el-form>

        <div v-loading="loading">
            <!-- 合同表格 -->
            <el-table :data="tableData" border @cell-click="handleCellClick">
                <el-table-column type="index" label="序号" width="60" align="center" fixed="left" >
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="code" label="合同编号" width="150" align="center" fixed="left" />
                <el-table-column prop="name" label="合同名称" header-align="center" align="left" min-width="200" fixed="left" />
                <el-table-column prop="projName" label="所属项目" min-width="300" header-align="center" align="left" />
                <el-table-column prop="partb" label="乙方" align="center" min-width="150px" />
                <el-table-column prop="amount" label="合同金额" width="150" header-align="center" align="right"
                    :formatter="(row: any) => numberFormat(row.amount, 2)" />
                <el-table-column prop="amount2019" label="截至2019年底累计拨付" width="170" header-align="center" align="right"
                    :formatter="(row: any) => numberFormat(row.amount2019, 2)" />
                <el-table-column prop="addUpAmount" label="累计拨付总额" width="150" header-align="center" align="right"
                    :formatter="(row: any) => numberFormat(row.addUpAmount, 2)" />
                <el-table-column label="操作" width="150" align="center" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" icon="edit" link @click="editRow(scope.row)">编辑</el-button>
                        <el-button type="danger" icon="delete" link @click="deleteRow(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>

        <el-dialog title="合同编辑" v-model="formEditShow" width="800px" :close-on-click-modal="false" destroy-on-close
            @close="currentId = ''">
            <EditForm :contract-id="currentId" @cancel="formEditShow = false"
                @close="formEditShow = false; handleSearch()">
            </EditForm>
            <footer class="el-dialog__footer h-10"></footer>
        </el-dialog>
        <el-dialog title="合同拨款明细" v-model="fundDetailShow" width="1000px" :close-on-click-modal="false"
            destroy-on-close>
            <FundDetail></FundDetail>
        </el-dialog>
    </div>



</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import EditForm from './component/editForm.vue'
import FundDetail from './component/fundDetail.vue'
import { editContract, getContractList, removeContract } from '@/api/fund/contractInfo'
import { numberFormat } from '@/utils/common'

const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const searchForm = reactive<FundContractInfoQuery>({})
const loading = ref(false)
const total = ref(0)
const tableData = ref<FundContractInfoVo[]>([])

const handleSearch = () => {
    loading.value = true
    getContractList(searchForm, page).then(res => {
        tableData.value = res.rows ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()

const fundDetailShow = ref(false)
const handleCellClick = (row: FundContractInfoVo, column: any,) => {
    if (column.property === 'totalPaid') {
        fundDetailShow.value = true
    }
    if (column.property === 'amount2019') {
        ElMessageBox.prompt('请输入累计拨付金额', '', {
            type: 'info',
            inputPattern: /^[0-9]+.?[0-9]*$/,
            inputType: 'number',
            inputValue: String(row.amount2019 ?? 0),
            inputErrorMessage: '请输入数字'
        }).then(res => {
            editContract({ ...row, amount2019: res.value as any }).then(res => {
                ElMessage.success('修改成功')
                handleSearch()
            })
        })
    }
}

const currentId = ref('')
const formEditShow = ref(false)
const handleCreate = () => {
    formEditShow.value = true
}

const editRow = (row: FundContractInfoVo) => {
    formEditShow.value = true
    currentId.value = row.id!
}

const deleteRow = (row: FundContractInfoVo) => {
    ElMessageBox.confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        removeContract([row.id!]).then(res => {
            ElMessage.success('删除成功');
            handleSearch()
        })
    })

}
</script>
