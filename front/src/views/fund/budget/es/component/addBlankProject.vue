<template>
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="auto">
        <el-form-item prop="projName" label="项目名称">
            <el-input v-model="form.projName"></el-input>
        </el-form-item>
        <el-form-item label="考核主体">
            <DeptSelect ref="deptRef" v-model="form.khorgId" placeholder="请选择考核主体"></DeptSelect>
        </el-form-item>
    </el-form>
    <div class="flex justify-center mt-5">
        <el-button type="primary" @click="save">确定</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { v4 as uuidv4 } from 'uuid'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { EUsageFundPlanItemType } from '@/utils/constants';
import { addFormulateTwo, editProjectMessageTwo } from '@/api/fund/budgetTwo';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['close', 'cancel', 'add']);
const props = defineProps<{
    budgetId: string;
    typeCode: string;
    data?: FundBudgetFormulateVo
}>();
const form = reactive({
    id: props.data?.id,
    projName: props.data?.projName ?? '',
    khorgId: props.data?.khorgId ?? undefined,
    // seqNo: props.data?.seqNo ?? ''
});
const formRules = {
    projName: [
        { required: true, message: '请输入项目名称', trigger: 'blur' }
    ],
    assessOrgid: [
        { required: true, message: '请选择考核主体', trigger: 'change' }
    ],
};

const deptRef = ref()

const formRef = ref();
const save = () => {
    formRef.value.validate((valid: boolean) => {
        if (valid) {
            if (!form.id) {
                addFormulateTwo({
                    budgetId: props.budgetId,
                    projName: form.projName,
                    typeCode: props.typeCode,
                    khorgId: form.khorgId,
                    khorgName: deptRef.value?.getDeptList()?.find((item: any) => item.deptId === form.khorgId)?.deptName ?? ''
                }).then(res => {
                    ElMessage.success('保存成功')
                    // 关闭弹窗
                    emit('close');
                })
            } else {
                editProjectMessageTwo({
                    id: form.id,
                    projName: form.projName,
                    khorgId: form.khorgId,
                    khorgName: deptRef.value?.getDeptList()?.find((item: any) => item.deptId === form.khorgId)?.deptName ?? ''
                }).then(res => {
                    ElMessage.success('保存成功')
                    // 关闭弹窗
                    emit('close');
                })
            }

        } else {
            console.log('表单验证失败');
            return false;
        }
    });
};
</script>