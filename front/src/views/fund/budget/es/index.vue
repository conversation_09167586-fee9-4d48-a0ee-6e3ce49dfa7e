<template>
    <div class="app-container">
        <div class="flex gap-2">
            <el-date-picker v-model="year" type="year" placeholder="选择年份" format="YYYY年" value-format="YYYY"
                class="mb-4" />
            <el-button type="primary" class="mb-4" @click="handleAdd">新增预算</el-button>
        </div>
        <el-table :data="dataList" border row-key="id">
            <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
            <el-table-column prop="year" label="年度" align="center" width="120"></el-table-column>
            <el-table-column prop="title" label="标题" align="center"></el-table-column>
            <el-table-column prop="projectCount" label="项目数量" align="center" width="120"></el-table-column>
            <el-table-column prop="snapCount" label="快照数量" align="center" width="120"></el-table-column>
            <el-table-column prop="updateTime" label="更新时间" align="center" width="180"
                :formatter="(row: any) => dateFormat(row.updateTime)"></el-table-column>
            <el-table-column prop="status" label="开放状态" align="center" width="150">
                <template #default="{ row }">
                    <el-switch v-model="row.status" active-value="0" inactive-value="1"
                        @change="handleClosedChange(row)"></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="220">
                <template #default="{ row }">
                    <el-button type="primary" link icon="edit"
                        @click="$router.push({ path: 'budget-es/table', query: { year: row.year } })">编辑</el-button>
                    <el-button type="danger" link icon="delete" @click="handleRemove(row)">删除</el-button>
                    <el-button type="primary" link icon="picture" @click="handleSnapListShow(row)">快照</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-dialog title="快照" v-model="snapListShow" width="800px" :close-on-click-modal="false" destroy-on-close>
            <el-table :data="snapList" border height="700">
                <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
                <el-table-column label="快照名称" header-align="center" align="left">
                    <template #default="{ row }">
                        <el-link type="primary" @click="handleClickSnap(row)">

                            <span class="ml-3">{{ row.snapName }}</span>
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column label="快照时间" width="170" align="center">
                    <template #default="{ row }">
                        <span>{{ dateFormat(row.createTime, 'YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>

                <template #empty>
                    <el-empty description="暂无数据"></el-empty>
                </template>
            </el-table>
        </el-dialog>
        <el-dialog title="快照" v-model="snapViewShow" fullscreen :close-on-click-modal="false" destroy-on-close>
            <Preview :snapId="snapData!.id!" @close="snapViewShow = false"></Preview>
        </el-dialog>
    </div>

</template>

<script setup lang="ts">
import { editBudgeTwo, formulateTwoRelease, getBudgetTwoList, getBudgetTwoSnapList, hasSecondFormulate, removeBudgeTwo } from '@/api/fund/budgetTwo';
import { getList, updateTableClose, delByYear, getListSnap } from '@/api/lib/report/usagePlan';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox, formatter } from 'element-plus';
import Preview from './preview.vue';

const { proxy } = getCurrentInstance() as { proxy: any };
const { snap_type } = proxy.useDict('snap_type');

const year = ref<number>()
const router = useRouter()

const dataList = ref<FundBudget[]>([]);
const loading = ref(false);
const search = () => {
    loading.value = true;
    getBudgetTwoList().then(res => {
        dataList.value = res.data ?? [];
    }).finally(() => {
        loading.value = false;
    });
}
search()
const handleAdd = async () => {
    if (!year.value) {
        ElMessage.error('请选择年份');
        return
    }
    const has = await hasSecondFormulate(year.value)
    if (has.data) {
        router.push({ path: 'budget-es/table', query: { year: year.value } });
    } else {
        ElMessageBox.confirm('未查询到二上快照，是否确认新增预算？', '提示', {
            type: 'warning'
        }).then(() => {
            router.push({ path: 'budget-es/table', query: { year: year.value } });
        })
    }
    
}

const handleClosedChange = (row: FundBudget) => {
    formulateTwoRelease(row.id!).then(res => {
        ElMessage.success('操作成功')
    })
}

const snapListShow = ref(false)
const snapList = ref<FundBudget[]>([])
const handleSnapListShow = (row: FundBudget) => {
    snapListShow.value = true

    getBudgetTwoSnapList(row.year!).then(res => {
        snapList.value = res.data ?? []
    })
}

const snapData = ref<FundBudget>()
const snapViewShow = ref(false)
const handleClickSnap = (row: FundBudget) => {
    snapViewShow.value = true
    snapData.value = row
}

const handleRemove = (row: FundBudget) => {
    ElMessageBox.confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        removeBudgeTwo(row.id!).then(res => {
            ElMessage.success('操作成功')
            search()
        })
    })
}
</script>