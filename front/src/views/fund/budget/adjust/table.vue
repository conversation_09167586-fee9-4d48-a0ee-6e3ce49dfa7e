<template>
    <div class="app-container relative" v-loading="loading">
        <h1 class="text-center cursor-pointer py-2 mt-0 mb-1" :class="{ 'hover:bg-blue': budgetInfo?.status == '0' }"
            @click="handleChangeTableTitle">
            {{ budgetInfo?.title }}
        </h1>
        <div class="flex justify-end">
            <div class="flex items-center gap-2">
                <span class="cursor-pointer" :class="{ 'hover:bg-blue': budgetInfo?.status == '0' }"
                    @click="handleChangeTableUnit">{{ budgetInfo?.unit }}</span>
                <right-toolbar :search="false" :columns="columns" @queryTable="handleSearch"></right-toolbar>
            </div>
        </div>
        <div class="mt-2">
            <vxe-table ref="tableRef" :data="dataList" :height="tableHeight" :tree-config="bindTreeConfig" border
                header-cell-class-name="project-header-custom"
                :row-drag-config="{ isCrossDrag: true, visibleMethod: ({ row }) => row.typeCode, dragEndMethod: handleRowDragEnd }"
                :row-config="{ drag: true }" :virtual-y-config="{ enabled: true, gt: 0 }"
                :virtual-x-config="{ enabled: true, gt: 0 }"
                @cell-click="handleCellClick"
                @row-dragend="handleRowDragEnd1">
                <vxe-column field="node" width="120px" align="center" tree-node drag-sort fixed="left">
                    <template #default="{ row, $rowIndex }">
                        <template v-if="!row.typeCode">
                            <el-dropdown v-if="$rowIndex != 0" placement="bottom" @command="handleCommand">
                                <el-button type="primary" icon="Plus" text plain size="small"></el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item
                                            :command="{ fun: handleAddBlankProject, args: { typeCode: categorySortMap[row.categorySort as 1 | 2 | 3] } }">空项目</el-dropdown-item>
                                        <el-dropdown-item
                                            :command="{ fun: handleAddExistProject, args: { typeCode: categorySortMap[row.categorySort as 1 | 2 | 3] } }">已有项目</el-dropdown-item>

                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                        <template v-else>
                            <el-button type="danger" icon="close" text plain size="small"
                                @click="handleRemove(row)"></el-button>
                        </template>

                    </template>
                </vxe-column>
                <vxe-column title="序号" width="60px" align="center" fixed="left">
                    <template #default="{ row }">
                        {{ row.typeCode ? row.index : '' }}
                    </template>
                </vxe-column>
                <vxe-column field="projName" title="项目名称" width="300px" fixed="left">
                    <template #default="{ row }">
                        <span :class="{ 'font-bold': !row.typeCode, 'cursor-pointer': !row.projId && row.typeCode }">{{ row.projName }}</span>
                    </template>
                </vxe-column>
                <vxe-column field="khorgName" title="考核主体" align="center" width="140px" fixed="left"
                    :visible="columnMap.get('khorgName')?.visible"></vxe-column>
                <vxe-colgroup title="上年结转" align="center"
                    :visible="columnMap.get('balanceAmount')?.visible || columnMap.get('balanceFund')?.visible || columnMap.get('balancePurchase')?.visible">
                    <vxe-column field="balanceAmount" title="小计" header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.balanceAmount, 2)"
                        :visible="columnMap.get('balanceAmount')?.visible"></vxe-column>
                    <vxe-column field="balanceFund" :title="`${year - 1}年资金结转`" header-align="center" align="right"
                        width="140px" :visible="columnMap.get('balanceFund')?.visible">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.balanceFund" :row="row" prop="balanceFund">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                    <vxe-column field="balancePurchase" :title="`${year - 1}年政府采购延期支付`" header-align="center"
                        align="right" width="140px" :visible="columnMap.get('balancePurchase')?.visible">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.balancePurchase" :row="row" prop="balancePurchase">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="二上预算" align="center"
                    :visible="columnMap.get('secondAmount')?.visible || columnMap.get('secondFund')?.visible || columnMap.get('secondPurchase')?.visible || columnMap.get('secondService')?.visible">
                    <vxe-column field="secondAmount" title="小计" header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.secondAmount, 2)"
                        :visible="columnMap.get('secondAmount')?.visible"></vxe-column>
                    <vxe-column field="secondFund" :title="`${year}年资金预算`" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.secondFund, 2)"
                        :visible="columnMap.get('secondFund')?.visible"></vxe-column>
                    <vxe-column field="secondPurchase" :title="`${year}年政府采购预算`" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.secondPurchase, 2)"
                        :visible="columnMap.get('secondPurchase')?.visible"></vxe-column>
                    <vxe-column field="secondService" :title="`其中：${year}年购买服务预算`" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.secondService, 2)"
                        :visible="columnMap.get('secondService')?.visible"></vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="年度用款计划" align="center" :visible="columnMap.get('scheduleAmount')?.visible">
                    <vxe-column field="scheduleAmount" :title="`${year}年用款计划`" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.scheduleAmount, 2)"
                        :visible="columnMap.get('scheduleAmount')?.visible"></vxe-column>
                </vxe-colgroup>
                <vxe-column field="budgetAdjust" title="预算调整" header-align="center" align="right" width="140px"
                    :formatter="t => numberFormat(t.row.budgetAdjust, 2)"
                    :visible="columnMap.get('budgetAdjust')?.visible"></vxe-column>
                <vxe-colgroup title="调整后预算" align="center"
                    :visible="columnMap.get('adjustAmount')?.visible || columnMap.get('adjustFund')?.visible || columnMap.get('adjustPurchase')?.visible || columnMap.get('adjustService')?.visible">
                    <vxe-column field="adjustAmount" title="小计" header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.adjustAmount, 2)"
                        :visible="columnMap.get('adjustAmount')?.visible"></vxe-column>
                    <vxe-column field="adjustFund" :title="`${year}年资金预算`" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.adjustFund, 2)"
                        :visible="columnMap.get('adjustFund')?.visible"></vxe-column>
                    <vxe-column field="adjustPurchase" :title="`${year}年政府采购预算`" header-align="center" align="right"
                        width="140px" :visible="columnMap.get('adjustPurchase')?.visible">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.adjustPurchase" :row="row" prop="adjustPurchase">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                    <vxe-column field="adjustService" :title="`其中：${year}年购买服务预算`" header-align="center" align="right"
                        width="140px" :visible="columnMap.get('adjustService')?.visible">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.adjustService" :row="row" prop="adjustService">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="资金到位" align="center"
                    :visible="columnMap.get('receivedAmount')?.visible || columnMap.get('received1st')?.visible || columnMap.get('received2nd')?.visible || columnMap.get('received3rd')?.visible || columnMap.get('received4rd')?.visible">
                    <vxe-column field="receivedAmount" title="小计" header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.receivedAmount, 2)"
                        :visible="columnMap.get('receivedAmount')?.visible"></vxe-column>
                    <vxe-column field="received1st" title="第一批" header-align="center" align="right" width="140px"
                        :visible="columnMap.get('received1st')?.visible">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.received1st" :row="row" prop="received1st">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                    <vxe-column field="received2nd" title="第二批" header-align="center" align="right" width="140px"
                        :visible="columnMap.get('received2nd')?.visible">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.received2nd" :row="row" prop="received2nd">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                    <vxe-column field="received3rd" title="第三批" header-align="center" align="right" width="140px"
                        :visible="columnMap.get('received3rd')?.visible">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.received3rd" :row="row" prop="received3rd">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                    <vxe-column field="received4th" title="第四批" header-align="center" align="right" width="140px"
                        :visible="columnMap.get('received4th')?.visible">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.received4th" :row="row" prop="received4th">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                </vxe-colgroup>


                <vxe-column field="remark" title="备注" align="center" width="140px"
                    :visible="columnMap.get('remark')?.visible">
                    <template #default="{ row }">
                        <template v-if="row.typeCode && budgetInfo?.status == '0'">
                            <el-input v-model="row.remark" type="textarea" :rows="1" placeholder="请输入" class="w-full" maxlength="1000"
                                @change="handleChange(row, 'remark')" />
                        </template>
                        <span v-show="isPrint">{{ row.remark }}</span>

                    </template>
                </vxe-column>
            </vxe-table>
        </div>

        <div class="fixed bottom-0 z-10 bg-white w-full p-2">
            
            <el-button type="primary" v-if="budgetInfo?.status == '0'" @click="handleCreateSnap">生成快照</el-button>
            <el-button type="primary" @click="handleRefreshAmount">刷新预算调整金额</el-button>
            <el-button type="primary" @click="handleRefreshAdjust">刷新预算调整明细</el-button>
            <el-button type="primary" @click="handlePrint">打印预览</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <el-button type="danger" @click="$router.back()">返回</el-button>
        </div>

        <el-dialog title="空项目" v-model="blankProjectShow" width="400px" align-center :close-on-click-modal="false"
            destroy-on-close>
            <AddBlankProject :budget-id="budgetInfo?.id!" :type-code="currentTypeCode" :data="currentRow"
                @close="blankProjectShow = false; handleSearch()" @cancel="blankProjectShow = false">
            </AddBlankProject>
        </el-dialog>
        <el-dialog title="项目选择" v-model="existProjectShow" width="800px" align-center :close-on-click-modal="false"
            destroy-on-close>
            <AddExistProject :budget-id="budgetInfo?.id!" :year="String(year)" :type-code="currentTypeCode"
                :data="currentRow" @close="existProjectShow = false; handleSearch()" @cancel="existProjectShow = false">
            </AddExistProject>
        </el-dialog>
    </div>
</template>
<script lang="tsx" setup>
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs, ElMessage, ElMessageBox, type TabsPaneContext } from 'element-plus'
import { checkIsNumber, dateFormat, numberFormat, sumAmount } from '@/utils/common';
import { v4 as uuidv4 } from 'uuid'
import { adjustResort, createBudgetAdjustSnap, editBudgeAdjust, editBudgeAdjustDetail, flushAdjustAmount, flushAdjustDetail, getBudgetAdjustdetailByYear, removeAdjustDetail } from '@/api/fund/budgetAdjust';
import { WatchStopHandle } from 'vue';
import { debounce } from 'lodash';
import AddBlankProject from './component/addBlankProject.vue'
import AddExistProject from './component/addExistProject.vue'

// @ts-ignore
VxeUI.use(VxePcUI)

const tableHeight = ref(window.innerHeight - 250)

const { proxy } = getCurrentInstance() as { proxy: any };

const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId',
    childrenField: 'childDetailList'
}

const categorySortMap = {
    1: 'A0',
    2: 'B0',
    3: 'C0',
}
const year = parseInt(useRoute().query.year as string)
const columnMap = ref<Map<string, typeof columns.value[0]>>(new Map())
const columns = ref([
    { label: '项目名称', prop: 'projName', width: 150, align: 'center', visible: true, disabled: true },
    { label: '考核主体', prop: 'khorgName', width: 100, align: 'center', visible: true },
    { label: '小计', prop: 'balanceAmount', align: 'center', visible: true },
    { label: `${year - 1}年资金结转`, prop: 'balanceFund', align: 'center', visible: true },
    { label: `${year - 1}年政府采购延期支付`, prop: 'balancePurchase', align: 'center', visible: true },
    { label: '小计', prop: 'secondAmount', align: 'center', visible: true },
    { label: `${year}年资金预算`, prop: 'secondFund', align: 'center', visible: true },
    { label: `${year}年政府采购预算`, prop: 'secondPurchase', align: 'center', visible: true },
    { label: `其中：${year}年购买服务预算`, prop: 'secondService', align: 'center', visible: true },
    { label: `${year}年用款计划`, prop: 'scheduleAmount', align: 'center', visible: true },
    { label: '预算调整', prop: 'budgetAdjust', align: 'center', visible: true },
    { label: '小计', prop: 'adjustAmount', align: 'center', visible: true },
    { label: `${year}年资金预算`, prop: 'adjustFund', align: 'center', visible: true },
    { label: `${year}年政府采购预算`, prop: 'adjustPurchase', align: 'center', visible: true },
    { label: `其中：${year}年购买服务预算`, prop: 'adjustService', align: 'center', visible: true },
    { label: '小计', prop: 'receivedAmount', align: 'center', visible: true },
    { label: '第一批', prop: 'received1st', align: 'center', visible: true },
    { label: '第二批', prop: 'received2nd', align: 'center', visible: true },
    { label: '第三批', prop: 'received3rd', align: 'center', visible: true },
    { label: '第四批', prop: 'received4th', align: 'center', visible: true },
    { label: '备注', prop: 'remark', align: 'center', visible: true },
]);
const updateFileds = columns.value.map(t => t.prop)



const loading = ref(false);
const budgetInfo = ref<FundBudget>()
const dataList = ref<(FundBudgetAdjust & { index?: number, parentId?: string, categorySort?: number })[]>([])
let stopColumnChangeWatch: WatchStopHandle | null
const handleSearch = () => {
    if (stopColumnChangeWatch != null) {
        stopColumnChangeWatch()
    }
    loading.value = true
    getBudgetAdjustdetailByYear(year, JSON.stringify(columns.value)).then(res => {
        budgetInfo.value = res.data?.budget
        if (budgetInfo.value?.columns) {
            columns.value = JSON.parse(budgetInfo.value?.columns)
        }
        const list: typeof dataList.value = []
        res.data?.budgetDetailList?.forEach(item => {
            let index = 1
            item.id = item.id ?? uuidv4()
            list.push({ ...item, childDetailList: [] })
            item.childDetailList?.forEach(t => {
                list.push({ ...t, index: index++, id: t.id ?? uuidv4(), parentId: item.id })
            })
        })
        dataList.value = list.sort((a, b) => a.categorySort! - b.categorySort!)
        dataList.value.unshift({ ...res.data?.total!, id: uuidv4() })

        nextTick(() => tableRef.value?.setAllTreeExpand(true))

        // if (!budgetInfo.value?.columns) {
        //     editBudgeAdjust({
        //         id: budgetInfo.value?.id,
        //         columns: JSON.stringify(columns.value)
        //     })
        // }
    }).then(() => {
        stopColumnChangeWatch = watch(columns, debounce(() => {
            // 当列发生变化时，重新计算表格数据
            editBudgeAdjust({
                id: budgetInfo.value?.id,
                columns: JSON.stringify(columns.value)
            })
        }, 3000), { deep: true })
        columns.value.forEach(col => {
            columnMap.value.set(col.prop, col)
        });
    })
        .finally(() => {
            loading.value = false
        })
}
handleSearch()


const handleChangeTableTitle = () => {
    if (budgetInfo.value?.status != '0') return
    ElMessageBox.prompt('请输入', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: budgetInfo.value?.title,
        inputValidator(value) {
            if (value.length > 200) {
                return '文字超长'
            }
            return true
        },
        type: 'info'
    }).then((res) => {
        loading.value = true
        editBudgeAdjust({ id: budgetInfo.value?.id, title: res.value }).then(() => {
            budgetInfo.value!.title = res.value
            ElMessage.success('修改成功')
        }).finally(() => loading.value = false)
    })
}

const handleChangeTableUnit = () => {
    if (budgetInfo.value?.status != '0') return
    ElMessageBox.prompt('请输入', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: budgetInfo.value?.unit,
        inputValidator(value) {
            if (value.length > 200) {
                return '文字超长'
            }
            return true
        },
        type: 'info'
    }).then((res) => {
        loading.value = true
        editBudgeAdjust({ id: budgetInfo.value?.id, unit: res.value }).then(() => {
            budgetInfo.value!.unit = res.value
            ElMessage.success('修改成功')
        }).finally(() => loading.value = false)
    })
};

const handleChange = (row: FundBudgetAdjust, prop: keyof FundBudgetAdjust) => {
    loading.value = true
    editBudgeAdjustDetail({ id: row.id!, columnName: prop, value: row[prop] }).then(res => {
        ElMessage.success('修改成功')
        const tableData = tableRef.value?.getFullData()
        getBudgetAdjustdetailByYear(year, '').then(res => {
            Object.keys(res.data!.total).forEach((k) => {
                if (updateFileds.includes(k)) {
                    tableData!.at(0)[k] = res.data!.total[k as keyof FundBudgetAdjust]
                }
            })
            res.data?.budgetDetailList.forEach((t, i) => {
                Object.keys(t).forEach((k, j) => {
                    if (updateFileds.includes(k)) {
                        tableData!.at(i+1)[k] = t[k as keyof FundBudgetAdjust]
                    }
                   
                })
            })
        })
    }).finally(() => {
        loading.value = false
    })
}

const handleCreateSnap = () => {
    if (budgetInfo.value?.status != '0') return
    ElMessageBox.prompt('请输入快照名称', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator(value) {
            if (value.length > 200) {
                return '文字超长'
            }
            return true
        },
        inputValue: budgetInfo.value?.title,
        type: 'info'
    }).then((res) => {
        loading.value = true
        createBudgetAdjustSnap({ id: budgetInfo.value?.id!, title: res.value }).then(() => {
            ElMessage.success('快照创建成功')
        }).finally(() => loading.value = false)
    })
}

const handleRemove = (row: FundBudgetAdjust) => {
    ElMessageBox.confirm('确定要删除吗？', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        loading.value = true
        removeAdjustDetail(row.id!).then(() => {
            ElMessage.success('删除成功')
            handleSearch()
        }).finally(() => loading.value = false)
    })
}

const categorySortMap1: any = {
    'A0': 1,
    'B0': 2,
    'C0': 3,
}
const handleRowDragEnd = (data: { newRow: FundBudgetAdjust, oldRow: FundBudgetAdjust, dragRow: FundBudgetAdjust, offsetIndex: number }) => {
    if (!data.dragRow.typeCode || data.dragRow.typeCode != data.newRow.typeCode) {
        return false
    }
    return true

}

const handleRowDragEnd1 = (data: { newRow: FundBudgetAdjust, oldRow: FundBudgetAdjust, dragRow: FundBudgetAdjust, offsetIndex: number }) => {
    const sortList = dataList.value.find(t => t.categorySort == categorySortMap1[data.dragRow.typeCode!])?.childDetailList?.map((item, index) => {
        return {
            id: item.id!,
            seqNo: index + 1
        }
    })

    if (sortList && sortList.length > 0) {
        adjustResort(sortList).then(res => {
            ElMessage.success('保存成功')
            handleSearch()
        })
    }
}

const handleCellClick = (data: {row: FundBudgetAdjust, rowIndex: number, column: any}) => {
    if (data.column.field == "projName" && !data.row.projId && data.row.typeCode) {
        blankProjectShow.value = true
        currentRow.value = data.row
    }
    
}

const handleCommand = (command: any) => {
    if (command.fun) {
        command.fun(command.args);
    }
}

const currentTypeCode = ref('')
const currentRow = ref<FundBudgetAdjust>()
const blankProjectShow = ref(false)
const handleAddBlankProject = (args: any) => {
    blankProjectShow.value = true
    currentTypeCode.value = args.typeCode
};

const existProjectShow = ref(false)
const handleAddExistProject = (args: any) => {
    existProjectShow.value = true
    currentTypeCode.value = args.typeCode
};

// 因为有输入框，导致数值无法打印，因此加这个让打印时直接把数字显示出来
const isPrint = ref(false)
const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    isPrint.value = true
    nextTick(() => {
        // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
        const map = new Map<string, any[]>()
        tableRef.value?.getPrintHtml({
            excludeFields: ['node'],
            dataFilterMethod: (data: { row: any }) => {
                map.set(data.row.id, data.row.childDetailList)
                data.row.childDetailList = []
                return true
            }
        }).then(res => {
            const headHtml = `
        <h1 style="text-align: center;">
        ${year}年度化工区专项发展资金预算编制明细表（调整）
        </h1>
        <div style="text-align: right;margin-bottom: 5px;">${budgetInfo.value?.unit}</div>`
            const html = res.html
            tableRef.value?.print({
                html: headHtml + html,
            })
        }).finally(() => {
            dataList.value.forEach((item) => {
                if (map.has(item.id!)) {
                    item.childDetailList = map.get(item.id!)
                }
            })
        }).finally(() => {
            isPrint.value = false
        })
    })

}

const handleRefreshAmount = () => {
    ElMessageBox.confirm('是否确认刷新预算调整金额？', '提示', {
        type: 'warning'
    }).then(() => {
        flushAdjustAmount(year).then(() => {
            ElMessage.success('刷新成功')
            handleSearch()
        })
    })
}

const handleRefreshAdjust = () => {
    ElMessageBox.confirm('是否确认刷新预算调整明细？', '提示', {
        type: 'warning'
    }).then(() => {
        flushAdjustDetail(year).then(() => {
            ElMessage.success('刷新成功')
            handleSearch()
        })
    })
}

const handleDownloadExcel = () => {
    proxy.download('/fund/budgetAdjust/export', {
        id: budgetInfo.value?.id,
    }, `${year}年度化工区专项发展资金预算编制明细表（调整）${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}





const InputNumberComponent = defineComponent({
    props: {
        modelValue: {
            type: [Number, null] as PropType<number | null>,
            required: true,
        },
        row: {
            type: Object as () => FundBudgetAdjust,
            required: true

        },
        prop: {
            type: String as () => keyof FundBudgetAdjust,
            required: true
        }
    },
    setup(props, { emit }) {
        const handleUpdate = (val: number) => {
            emit('update:modelValue', val)
        }

        return () => {
            if (props.row.typeCode && budgetInfo.value?.status == '0') {
                return (
                    <div>
                        <el-input-number modelValue={props.modelValue} onUpdate:modelValue={handleUpdate} precision={2} min={0} controls={false} placeholder="请输入" class="w-full"
                            onChange={() => handleChange(props.row, props.prop)} />
                        {
                            isPrint.value ? <span>{numberFormat(props.modelValue, 2)}</span> : ''
                        }
                    </div>
                )
            } else {
                return (<span>{numberFormat(props.modelValue, 2)}</span>)
            }
        }


    }
})
</script>

<style scoped>
:deep(.project-header-custom) {
    background: linear-gradient(to bottom, #fafdff, #deeefb) !important;
    /* 深蓝渐变 */
    text-align: center;
    border-right: 1px solid #d9e0eb !important;
}
</style>
