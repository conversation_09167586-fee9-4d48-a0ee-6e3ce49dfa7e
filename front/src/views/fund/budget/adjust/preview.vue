<template>
    <div class="app-container relative">
        <h1 class="text-center py-2 mt-0 mb-1">
            {{ budgetInfo?.title }}
        </h1>
        <div class="flex justify-end">
            <div class="flex items-center gap-2">
                <span>{{ budgetInfo?.unit }}</span>
                <!-- <right-toolbar :search="false" :columns="[]" @queryTable="handleSearch"></right-toolbar> -->
            </div>
        </div>
        <div class="mt-2" v-loading="loading">
            <vxe-table ref="tableRef" :data="dataList" :height="tableHeight" :tree-config="bindTreeConfig" border
                header-cell-class-name="project-header-custom"
                :row-drag-config="{ isCrossDrag: true, visibleMethod: ({ row }) => row.typeCode }"
                :row-config="{ drag: true }" :virtual-y-config="{ enabled: true, gt: 0 }" :virtual-x-config="{ enabled: true, gt: 0 }">
                <vxe-column field="node" width="120px" align="center" tree-node drag-sort fixed="left">
                    <template #default="{ row, $rowIndex }">
                        <template v-if="!row.typeCode">
                            <el-button v-if="$rowIndex != 0" type="primary" icon="Plus" text plain size="small"></el-button>
                        </template>
                        <template v-else>
                            <el-button type="danger" icon="close" text plain size="small"></el-button>
                        </template>

                    </template>
                </vxe-column>
                <vxe-column title="序号" width="60px" align="center" fixed="left">
                    <template #default="{ row }">
                        {{ row.typeCode ? row.index : '' }}
                    </template>
                </vxe-column>
                <vxe-column field="projName" title="项目名称" width="300px" fixed="left">
                    <template #default="{ row }">
                        <span :class="{ 'font-bold': !row.typeCode }">{{ row.projName }}</span>
                    </template>
                </vxe-column>
                <vxe-column field="khorgName" title="考核主体" align="center" width="140px" fixed="left"
                    :visible="columnMap.get('khorgName')?.visible"></vxe-column>
                <vxe-colgroup title="上年结转" align="center"
                    :visible="columnMap.get('balanceAmount')?.visible || columnMap.get('balanceFund')?.visible || columnMap.get('balancePurchase')?.visible">
                    <vxe-column field="balanceAmount" title="小计" header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.balanceAmount)"
                        :visible="columnMap.get('balanceAmount')?.visible"></vxe-column>
                    <vxe-column field="balanceFund" :title="columnMap.get('balanceFund')?.label" header-align="center" align="right"
                        width="140px" :visible="columnMap.get('balanceFund')?.visible">
                    </vxe-column>
                    <vxe-column field="balancePurchase" :title="columnMap.get('balancePurchase')?.label" header-align="center"
                        align="right" width="140px" :visible="columnMap.get('balancePurchase')?.visible">
                    </vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="二上预算" align="center"
                    :visible="columnMap.get('secondAmount')?.visible || columnMap.get('secondFund')?.visible || columnMap.get('secondPurchase')?.visible || columnMap.get('secondService')?.visible">
                    <vxe-column field="secondAmount" title="小计" header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.secondAmount)"
                        :visible="columnMap.get('secondAmount')?.visible"></vxe-column>
                    <vxe-column field="secondFund" :title="columnMap.get('secondFund')?.label" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.secondFund)"
                        :visible="columnMap.get('secondFund')?.visible"></vxe-column>
                    <vxe-column field="secondPurchase" :title="columnMap.get('secondPurchase')?.label" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.secondPurchase)"
                        :visible="columnMap.get('secondPurchase')?.visible"></vxe-column>
                    <vxe-column field="secondService" :title="columnMap.get('secondService')?.label" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.secondService)"
                        :visible="columnMap.get('secondService')?.visible"></vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="年度用款计划" align="center" :visible="columnMap.get('scheduleAmount')?.visible">
                    <vxe-column field="scheduleAmount" :title="columnMap.get('scheduleAmount')?.label" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.scheduleAmount)"
                        :visible="columnMap.get('scheduleAmount')?.visible"></vxe-column>
                </vxe-colgroup>
                <vxe-column field="budgetAdjust" title="预算调整" header-align="center" align="right" width="140px"
                    :formatter="t => numberFormat(t.row.budgetAdjust)"
                    :visible="columnMap.get('budgetAdjust')?.visible"></vxe-column>
                <vxe-colgroup title="调整后预算" align="center"
                    :visible="columnMap.get('adjustAmount')?.visible || columnMap.get('adjustFund')?.visible || columnMap.get('adjustPurchase')?.visible || columnMap.get('adjustService')?.visible">
                    <vxe-column field="adjustAmount" title="小计" header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.adjustAmount)"
                        :visible="columnMap.get('adjustAmount')?.visible"></vxe-column>
                    <vxe-column field="adjustFund" :title="columnMap.get('adjustFund')?.label" header-align="center" align="right"
                        width="140px" :formatter="t => numberFormat(t.row.adjustFund)"
                        :visible="columnMap.get('adjustFund')?.visible"></vxe-column>
                    <vxe-column field="adjustPurchase" :title="columnMap.get('adjustPurchase')?.label" header-align="center" align="right"
                        width="140px" :visible="columnMap.get('adjustPurchase')?.visible">
                    </vxe-column>
                    <vxe-column field="adjustService" :title="columnMap.get('adjustService')?.label" header-align="center" align="right"
                        width="140px" :visible="columnMap.get('adjustService')?.visible">
                    </vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="资金到位" align="center"
                    :visible="columnMap.get('receivedAmount')?.visible || columnMap.get('received1st')?.visible || columnMap.get('received2nd')?.visible || columnMap.get('received3rd')?.visible || columnMap.get('received4rd')?.visible">
                    <vxe-column field="receivedAmount" title="小计" header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.receivedAmount)"
                        :visible="columnMap.get('receivedAmount')?.visible"></vxe-column>
                    <vxe-column field="received1st" title="第一批" header-align="center" align="right" width="140px"
                        :visible="columnMap.get('received1st')?.visible">
                    </vxe-column>
                    <vxe-column field="received2nd" title="第二批" header-align="center" align="right" width="140px"
                        :visible="columnMap.get('received2nd')?.visible">
                    </vxe-column>
                    <vxe-column field="received3rd" title="第三批" header-align="center" align="right" width="140px"
                        :visible="columnMap.get('received3rd')?.visible">
                    </vxe-column>
                    <vxe-column field="received4th" title="第四批" header-align="center" align="right" width="140px"
                        :visible="columnMap.get('received4th')?.visible">
                    </vxe-column>
                </vxe-colgroup>


                <vxe-column field="remark" title="备注" align="center" width="140px"
                    :visible="columnMap.get('remark')?.visible">
                </vxe-column>
            </vxe-table>
        </div>

        <div class="fixed bottom-0 z-10 bg-white w-full p-2">
            
            <el-button type="primary" @click="handlePrint">打印预览</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <el-button type="danger" @click="$router.back()">返回</el-button>
        </div>
    </div>
</template>
<script lang="ts" setup>
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs, ElMessage, ElMessageBox, type TabsPaneContext } from 'element-plus'
import { checkIsNumber, dateFormat, numberFormat, sumAmount } from '@/utils/common';
import { v4 as uuidv4 } from 'uuid'
import { getBudgetAdjustdetailById } from '@/api/fund/budgetAdjust';

// @ts-ignore
VxeUI.use(VxePcUI)

const props = defineProps<{
    snapId: string
}>()

const tableHeight = ref(window.innerHeight - 200)

const { proxy } = getCurrentInstance() as { proxy: any };

const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId',
    childrenField: 'childDetailList'
}

const columnMap = ref<Map<string, any>>(new Map())
const loading = ref(true);
const budgetInfo = ref<FundBudget>()
const dataList = ref<(FundBudgetAdjust & { index?: number, parentId?: string, categorySort?: number })[]>([])
const handleSearch = () => {
    getBudgetAdjustdetailById(props.snapId).then(res => {
        budgetInfo.value = res.data?.budget
        const list: typeof dataList.value = []
        res.data?.budgetDetailList?.forEach(item => {
            let index = 1
            item.id = item.id ?? uuidv4()
            list.push({ ...item, childDetailList: [] })
            item.childDetailList?.forEach(t => {
                list.push({ ...t, index: index++, id: t.id ?? uuidv4(), parentId: item.id })
            })
        })
        dataList.value = list.sort((a,b) => a.categorySort! - b.categorySort!)
        dataList.value.unshift(res.data?.total!)
        JSON.parse(budgetInfo.value?.columns??'[]').forEach((col: any) => {
            columnMap.value.set(col.prop, col)
        });
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()


const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
    const map = new Map<string, any[]>()
    tableRef.value?.getPrintHtml({
        excludeFields: ['node'],
        dataFilterMethod: (data: { row: any }) => {
            map.set(data.row.id, data.row.childDetailList)
            data.row.childDetailList = []
            return true
        }
    }).then(res => {
        const headHtml = `
        <h1 style="text-align: center;">
        ${budgetInfo.value?.year}年度化工区专项发展资金预算编制明细表（一上）
        </h1>
        <div style="text-align: right;margin-bottom: 5px;">${budgetInfo.value?.unit}</div>`
        console.log(res.html)
        const html = res.html
        tableRef.value?.print({
            html: headHtml + html,
        })
    }).finally(() => {
        dataList.value.forEach((item) => {
            if (map.has(item.id!)) {
                item.childDetailList = map.get(item.id!)
            }
        })
    })
}

const handleDownloadExcel = () => {
    proxy.download('/fund/budgetAdjust/export', {
        id: budgetInfo.value?.id,
    }, `${budgetInfo.value?.year}年度化工区专项发展资金预算编制明细表（调整）${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}

</script>

<style scoped>
:deep(.project-header-custom) {
    background: linear-gradient(to bottom, #fafdff, #deeefb) !important;
    /* 深蓝渐变 */
    text-align: center;
    border-right: 1px solid #d9e0eb !important;
}
</style>
