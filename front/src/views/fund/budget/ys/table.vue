<template>
    <div class="app-container relative" v-loading="loading">
        <h1 class="text-center cursor-pointer py-2 mt-0 mb-1" :class="{ 'hover:bg-blue': budgetInfo?.status == '0' }"
            @click="handleChangeTableTitle">
            {{ budgetInfo?.title }}
        </h1>
        <div class="flex justify-end">
            <div class="flex items-center gap-2">
                <span class="cursor-pointer" :class="{ 'hover:bg-blue': budgetInfo?.status == '0' }"
                    @click="handleChangeTableUnit">{{ budgetInfo?.unit }}</span>
                <!-- <right-toolbar :search="false" :columns="[]" @queryTable="handleSearch"></right-toolbar> -->
            </div>
        </div>
        <div class="mt-2">
            <vxe-table ref="tableRef" :data="dataList" :height="tableHeight" :tree-config="bindTreeConfig" border
                header-cell-class-name="project-header-custom"
                :row-drag-config="{ isCrossDrag: true, visibleMethod: ({ row }) => row.typeCode, dragEndMethod: handleRowDragEnd }"
                :row-config="{ drag: true }" @cell-click="handleCellClick" @row-dragend="handleRowDragEnd1"
                :virtual-y-config="{ enabled: true, gt: 0 }">
                <vxe-column field="node" width="120px" align="center" tree-node drag-sort>
                    <template #default="{ row, $rowIndex }">
                        <template v-if="!row.typeCode">
                            <el-dropdown v-if="$rowIndex != 0" placement="bottom" @command="handleCommand">
                                <el-button type="primary" icon="Plus" text plain size="small"></el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item
                                            :command="{ fun: handleAddBlankProject, args: { typeCode: categorySortMap[row.categorySort as 1 | 2 | 3] } }">空项目</el-dropdown-item>
                                        <el-dropdown-item
                                            :command="{ fun: handleAddExistProject, args: { typeCode: categorySortMap[row.categorySort as 1 | 2 | 3] } }">已有项目</el-dropdown-item>

                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </template>
                        <template v-else>
                            <el-button type="danger" icon="close" text plain size="small"
                                @click="handleRemove(row)"></el-button>
                        </template>

                    </template>

                </vxe-column>
                <vxe-column title="序号" width="60px" align="center">
                    <template #default="{ row }">
                        {{ row.typeCode ? row.index : '' }}
                    </template>
                </vxe-column>
                <vxe-column field="projName" title="项目名称" width="300px">
                    <template #default="{ row }">
                        <span :class="{ 'font-bold': !row.typeCode, 'cursor-pointer': !row.projId && row.typeCode }">{{
                            row.projName }}</span>
                    </template>
                </vxe-column>
                <vxe-column field="khorgName" title="考核主体" align="center"></vxe-column>
                <vxe-colgroup title="年度用款计划" align="center">
                    <vxe-column field="scheduleAmount" :title="`${year}年一上用款计划`" header-align="center" align="right"
                        :formatter="t => numberFormat(t.row.scheduleAmount, 2)"></vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="一上预算" align="center">
                    <vxe-column field="budgetAmount" title="小计" header-align="center" align="right">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.budgetAmount" :row="row" prop="budgetAmount">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                    <vxe-column field="yearAmount" :title="`${year}年资金预算`" header-align="center" align="right">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.yearAmount" :row="row" prop="yearAmount">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                    <vxe-column field="yearPurchase" :title="`${year}年政府采购预算`" header-align="center" align="right"
                        :formatter="t => numberFormat(t.row.yearPurchase, 2)">

                    </vxe-column>
                    <vxe-column field="yearService" :title="`其中：${year}年购买服务预算`" header-align="center" align="right">
                        <template #default="{ row }">
                            <InputNumberComponent v-model="row.yearService" :row="row" prop="yearService">
                            </InputNumberComponent>
                        </template>
                    </vxe-column>
                </vxe-colgroup>

                <vxe-column field="remark" title="备注" align="center">
                    <template #default="{ row }">
                        <template v-if="row.typeCode && budgetInfo?.status == '0'">
                            <el-input v-model="row.remark" type="textarea" :rows="1" placeholder="请输入" class="w-full"
                                maxlength="1000" @change="handleChange(row, 'remark')" />
                        </template>
                        <span v-show="isPrint">{{ row.remark }}</span>

                    </template>
                </vxe-column>
            </vxe-table>

        </div>

        <div class="fixed bottom-0 z-10 bg-white w-full p-2">

            <el-button type="primary" v-if="budgetInfo?.status == '0'" @click="handleCreateSnap">生成快照</el-button>
            <el-button type="primary" @click="handleRefreshAmount">刷新用款计划金额</el-button>
            <el-button type="primary" @click="handlePrint">打印预览</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <el-button type="danger" @click="$router.back()">返回</el-button>
        </div>

        <el-dialog title="空项目" v-model="blankProjectShow" width="400px" align-center :close-on-click-modal="false"
            destroy-on-close>
            <AddBlankProject :budget-id="budgetInfo?.id!" :type-code="currentTypeCode" :data="currentRow"
                @close="blankProjectShow = false; handleSearch()" @cancel="blankProjectShow = false">
            </AddBlankProject>
        </el-dialog>
        <el-dialog title="项目选择" v-model="existProjectShow" width="800px" align-center :close-on-click-modal="false"
            destroy-on-close>
            <AddExistProject :budget-id="budgetInfo?.id!" :year="year" :type-code="currentTypeCode" :data="currentRow"
                @close="existProjectShow = false; handleSearch()" @cancel="existProjectShow = false">
            </AddExistProject>
        </el-dialog>
    </div>
</template>
<script lang="tsx" setup>
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs, ElMessage, ElMessageBox, type TabsPaneContext } from 'element-plus'
import { checkIsNumber, dateFormat, numberFormat, sumAmount } from '@/utils/common';
// import old20202022 from './component/old2020-2022.vue';
import { getBreakDownList } from '@/api/lib/project/breakDown';
import { createBudgetOneSnap, editBudgeOne, editBudgeOneFormulate, formulateOneFlushAmount, formulateOneResort, getBudgetOnedetailByYear, getBudgetOneList, removeFormulate } from '@/api/fund/budgetOne';
import { cloneDeep } from 'lodash';
import { v4 as uuidv4 } from 'uuid'
import AddBlankProject from './component/addBlankProject.vue';
import AddExistProject from './component/addExistProject.vue';

// @ts-ignore
VxeUI.use(VxePcUI)

const tableHeight = ref(window.innerHeight - 250)

const { proxy } = getCurrentInstance() as { proxy: any };

const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId',
    childrenField: 'childDetailList'
}
const categorySortMap = {
    1: 'A0',
    2: 'B0',
    3: 'C0',
}
const year = useRoute().query.year as string
const loading = ref(false);
const budgetInfo = ref<FundBudget>()
const dataList = ref<(FundBudgetFormulateVo & { index?: number, parentId?: string, categorySort?: number })[]>([])
const handleSearch = () => {
    loading.value = true
    getBudgetOnedetailByYear(year, "").then(res => {
        budgetInfo.value = res.data?.budget
        const list: typeof dataList.value = []
        res.data?.budgetDetailList?.forEach(item => {
            let index = 1
            item.id = item.id ?? uuidv4()
            list.push({ ...item, childDetailList: [] })
            item.childDetailList?.forEach(t => {
                list.push({ ...t, index: index++, id: t.id ?? uuidv4(), parentId: item.id })
            })
        })
        dataList.value = list.sort((a, b) => a.categorySort! - b.categorySort!)
        dataList.value.unshift({ ...res.data?.total!, id: uuidv4() })
        nextTick(() => tableRef.value?.setAllTreeExpand(true))
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()

const handleCreateSnap = () => {
    if (budgetInfo.value?.status != '0') return
    ElMessageBox.prompt('请输入快照名称', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: budgetInfo.value?.title,
        inputValidator(value) {
            if (value.length > 200) {
                return '文字超长'
            }
            return true
        },
        type: 'info'
    }).then((res) => {
        loading.value = true
        createBudgetOneSnap({ id: budgetInfo.value?.id!, title: res.value }).then(() => {
            ElMessage.success('快照创建成功')
        }).finally(() => loading.value = false)
    })
}

const handleChangeTableTitle = () => {
    if (budgetInfo.value?.status != '0') return
    ElMessageBox.prompt('请输入', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: budgetInfo.value?.title,
        inputValidator(value) {
            if (value.length > 200) {
                return '文字超长'
            }
            return true
        },
        type: 'info'
    }).then((res) => {
        loading.value = true
        editBudgeOne({ id: budgetInfo.value?.id, title: res.value }).then(() => {
            budgetInfo.value!.title = res.value
            ElMessage.success('修改成功')
        }).finally(() => loading.value = false)
    })
}

const handleChangeTableUnit = () => {
    if (budgetInfo.value?.status != '0') return
    ElMessageBox.prompt('请输入', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: budgetInfo.value?.unit,
        inputValidator(value) {
            if (value.length > 200) {
                return '文字超长'
            }
            return true
        },
        type: 'info'
    }).then((res) => {
        loading.value = true
        editBudgeOne({ id: budgetInfo.value?.id, unit: res.value }).then(() => {
            budgetInfo.value!.unit = res.value
            ElMessage.success('修改成功')
        }).finally(() => loading.value = false)
    })
};

const handleRemove = (row: FundBudgetFormulateVo) => {
    ElMessageBox.confirm('确定要删除吗？', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        loading.value = true
        removeFormulate(row.id!).then(() => {
            ElMessage.success('删除成功')
            handleSearch()
        }).finally(() => loading.value = false)
    })
}
const updateFileds = [
    'scheduleAmount', 'budgetAmount', 'yearAmount', 'yearPurchase', 'yearService', 'remark'
]
const handleChange = (row: FundBudgetFormulateVo, prop: keyof FundBudgetFormulateVo) => {
    loading.value = true
    editBudgeOneFormulate({ id: row.id!, columnName: prop, value: row[prop] }).then(res => {
        ElMessage.success('修改成功')
        const tableData = tableRef.value?.getFullData()
        // 只更新总计行的数据
        getBudgetOnedetailByYear(year, "").then(res => {
            Object.keys(res.data!.total).forEach((k) => {
                if (updateFileds.includes(k)) {
                    tableData!.at(0)[k] = res.data!.total[k as keyof FundBudgetFormulateVo]
                }
            })
            res.data?.budgetDetailList.forEach((t, i) => {
                Object.keys(t).forEach((k, j) => {
                    if (updateFileds.includes(k)) {
                        tableData!.at(i + 1)[k] = t[k as keyof FundBudgetFormulateVo]
                    }

                })
            })
        })
    }).finally(() => loading.value = false)
}

const handleRefreshAmount = () => {
    ElMessageBox.confirm('是否确认刷新用款计划金额？', '提示', {
        type: 'warning'
    }).then(() => {
        formulateOneFlushAmount(year).then(() => {
            ElMessage.success('刷新成功')
            handleSearch()
        })
    })
}

const categorySortMap1: any = {
    'A0': 1,
    'B0': 2,
    'C0': 3,
}
const handleRowDragEnd = (data: { newRow: FundBudgetFormulateVo, oldRow: FundBudgetFormulateVo, dragRow: FundBudgetFormulateVo, offsetIndex: number }) => {
    if (!data.dragRow.typeCode || data.dragRow.typeCode != data.newRow.typeCode) {
        return false
    }
    return true

}

const handleRowDragEnd1 = (data: { newRow: FundBudgetFormulateVo, oldRow: FundBudgetFormulateVo, dragRow: FundBudgetFormulateVo, offsetIndex: number }) => {
    const sortList = dataList.value.find(t => t.categorySort == categorySortMap1[data.dragRow.typeCode])?.childDetailList?.map((item, index) => {
        return {
            id: item.id!,
            seqNo: index + 1
        }
    })

    if (sortList && sortList.length > 0) {
        formulateOneResort(sortList).then(res => {
            ElMessage.success('保存成功')
            handleSearch()
        })
    }
}

const handleCellClick = (data: { row: FundBudgetFormulateVo, rowIndex: number, column: any }) => {
    if (data.column.field == "projName" && !data.row.projId && data.row.typeCode) {
        blankProjectShow.value = true
        currentRow.value = data.row
    }

}

const handleCommand = (command: any) => {
    if (command.fun) {
        command.fun(command.args);
    }
}

const currentTypeCode = ref('')
const currentRow = ref<FundBudgetFormulateVo>()
const blankProjectShow = ref(false)
const handleAddBlankProject = (args: any) => {
    blankProjectShow.value = true
    currentTypeCode.value = args.typeCode
};

const existProjectShow = ref(false)
const handleAddExistProject = (args: any) => {
    existProjectShow.value = true
    currentTypeCode.value = args.typeCode
};


// 因为有输入框，导致数值无法打印，因此加这个让打印时直接把数字显示出来
const isPrint = ref(false)
const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    isPrint.value = true
    nextTick(() => {
        // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
        const map = new Map<string, any[]>()
        tableRef.value?.getPrintHtml({
            excludeFields: ['node'],
            dataFilterMethod: (data: { row: any }) => {
                map.set(data.row.id, data.row.childDetailList)
                data.row.childDetailList = []
                return true
            }
        }).then(res => {
            const headHtml = `
        <h1 style="text-align: center;">
        ${year}年度化工区专项发展资金预算编制明细表（一上）
        </h1>
        <div style="text-align: right;margin-bottom: 5px;">${budgetInfo.value?.unit}</div>`
            const html = res.html
            tableRef.value?.print({
                html: headHtml + html,
            })
        }).finally(() => {
            dataList.value.forEach((item) => {
                if (map.has(item.id!)) {
                    item.childDetailList = map.get(item.id!)
                }
            })
        }).finally(() => {
            isPrint.value = false
        })
    })

}

const handleDownloadExcel = () => {
    proxy.download('/fund/budget/export', {
        id: budgetInfo.value?.id,
    }, `年度化工区专项发展资金预算编制明细表（一上）${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}


const InputNumberComponent = defineComponent({
    props: {
        modelValue: {
            type: [Number, null] as PropType<number | null>,
            required: true,
        },
        row: {
            type: Object as () => FundBudgetFormulateVo,
            required: true

        },
        prop: {
            type: String as () => keyof FundBudgetFormulateVo,
            required: true
        }
    },
    setup(props, { emit }) {
        const handleUpdate = (val: number) => {
            emit('update:modelValue', val)
        }

        return () => {
            if (props.row.typeCode && budgetInfo.value?.status == '0') {
                return (
                    <div>
                        <el-input-number modelValue={props.modelValue} onUpdate:modelValue={handleUpdate} precision={2} min={0} controls={false} placeholder="请输入" class="w-full"
                            onChange={() => handleChange(props.row, props.prop)} />
                        {
                            isPrint.value ? <span>{numberFormat(props.modelValue, 2)}</span> : ''
                        }
                    </div>
                )
            } else {
                return (<span>{numberFormat(props.modelValue, 2)}</span>)
            }
        }


    }
})
</script>

<style scoped>
:deep(.project-header-custom) {
    background: linear-gradient(to bottom, #fafdff, #deeefb) !important;
    /* 深蓝渐变 */
    text-align: center;
    border-right: 1px solid #d9e0eb !important;
}
</style>
