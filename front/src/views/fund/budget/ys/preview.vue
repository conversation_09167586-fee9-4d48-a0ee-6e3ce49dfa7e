<template>
    <div class="app-container relative">
        <h1 class="text-center py-2 mt-0 mb-1">
            {{ budgetInfo?.title }}
        </h1>
        <div class="flex justify-end">
            <div class="flex items-center gap-2">
                <span>{{ budgetInfo?.unit }}</span>
                <!-- <right-toolbar :search="false" :columns="[]" @queryTable="handleSearch"></right-toolbar> -->
            </div>
        </div>
        <div class="mt-2" v-loading="loading">
            <vxe-table ref="tableRef" :data="dataList" :height="tableHeight" :tree-config="bindTreeConfig" border
                header-cell-class-name="project-header-custom">
                <vxe-column field="node" width="60px" align="center" tree-node></vxe-column>
                <vxe-column title="序号" width="60px" align="center">
                    <template #default="{ row }">
                        {{ row.typeCode ? row.index : '' }}
                    </template>
                </vxe-column>
                <vxe-column field="projName" title="项目名称" width="300px">
                    <template #default="{ row }">
                        <span :class="{ 'font-bold': !row.typeCode }">{{ row.projName }}</span>
                    </template>
                </vxe-column>
                <vxe-column field="khorgName" title="考核主体" align="center"></vxe-column>
                <vxe-colgroup title="年度用款计划" align="center">
                    <vxe-column field="scheduleAmount" :title="`${budgetInfo?.year}年一上用款计划`" header-align="center" align="right"
                        :formatter="t => numberFormat(t.row.scheduleAmount)"></vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="一上预算" align="center">
                    <vxe-column field="budgetAmount" title="小计" header-align="center" align="right"
                        :formatter="t => numberFormat(t.row.budgetAmount)"></vxe-column>
                    <vxe-column field="yearAmount" :title="`${budgetInfo?.year}年资金预算`" header-align="center" align="right"
                        :formatter="t => numberFormat(t.row.yearAmount)"></vxe-column>
                    <vxe-column field="yearPurchase" :title="`${budgetInfo?.year}年政府采购预算`" header-align="center" align="right"
                        :formatter="t => numberFormat(t.row.yearPurchase)"></vxe-column>
                    <vxe-column field="yearService" :title="`其中：${budgetInfo?.year}年购买服务预算`" header-align="center" align="right"
                        :formatter="t => numberFormat(t.row.yearService)"></vxe-column>
                </vxe-colgroup>

                <vxe-column field="remark" title="备注" align="center"></vxe-column>
            </vxe-table>
        </div>

        <div class="fixed bottom-0 z-10 bg-white w-full p-2">
            
            <el-button type="primary" @click="handlePrint">打印预览</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <el-button type="danger" @click="$router.back()">返回</el-button>
        </div>
    </div>
</template>
<script lang="ts" setup>
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs, ElMessage, ElMessageBox, type TabsPaneContext } from 'element-plus'
import { checkIsNumber, dateFormat, numberFormat, sumAmount } from '@/utils/common';
import { createBudgetOneSnap, editBudgeOne, editBudgeOneFormulate, getBudgetOnedetailById, getBudgetOnedetailByYear, getBudgetOneList } from '@/api/fund/budgetOne';
import { v4 as uuidv4 } from 'uuid'

// @ts-ignore
VxeUI.use(VxePcUI)

const props = defineProps<{
    snapId: string
}>()

const tableHeight = ref(window.innerHeight - 200)

const { proxy } = getCurrentInstance() as { proxy: any };

const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId',
    childrenField: 'childDetailList'
}


const loading = ref(true);
const budgetInfo = ref<FundBudget>()
const dataList = ref<(FundBudgetFormulateVo & { index?: number, parentId?: string, categorySort?: number })[]>([])
const handleSearch = () => {
    getBudgetOnedetailById(props.snapId).then(res => {
        budgetInfo.value = res.data?.budget
        const list: typeof dataList.value = []
        res.data?.budgetDetailList?.forEach(item => {
            let index = 1
            item.id = item.id ?? uuidv4()
            list.push({ ...item, childDetailList: [] })
            item.childDetailList?.forEach(t => {
                list.push({ ...t, index: index++, id: t.id ?? uuidv4(), parentId: item.id })
            })
        })
        dataList.value = list.sort((a,b) => a.categorySort! - b.categorySort!)
        dataList.value.unshift(res.data?.total!)
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()


const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
    const map = new Map<string, any[]>()
    tableRef.value?.getPrintHtml({
        excludeFields: ['node'],
        dataFilterMethod: (data: { row: any }) => {
            map.set(data.row.id, data.row.childDetailList)
            data.row.childDetailList = []
            return true
        }
    }).then(res => {
        const headHtml = `
        <h1 style="text-align: center;">
        ${budgetInfo.value?.year}年度化工区专项发展资金预算编制明细表（一上）
        </h1>
        <div style="text-align: right;margin-bottom: 5px;">${budgetInfo.value?.unit}</div>`
        console.log(res.html)
        const html = res.html
        tableRef.value?.print({
            html: headHtml + html,
        })
    }).finally(() => {
        dataList.value.forEach((item) => {
            if (map.has(item.id!)) {
                item.childDetailList = map.get(item.id!)
            }
        })
    })
}

const handleDownloadExcel = () => {
    proxy.download('/fund/budget/export', {
        id: budgetInfo.value?.id,
    }, `年度化工区专项发展资金预算编制明细表（一上）${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}

</script>

<style scoped>
:deep(.project-header-custom) {
    background: linear-gradient(to bottom, #fafdff, #deeefb) !important;
    /* 深蓝渐变 */
    text-align: center;
    border-right: 1px solid #d9e0eb !important;
}
</style>
