<template>
    <div class="app-container relative">
        <h1 class="text-center py-2 mt-0 mb-1">
            {{ budgetInfo?.title }}
        </h1>
        <div class="flex justify-end">
            <div class="flex items-center gap-2">
                <span>{{ budgetInfo?.unit }}</span>
                <!-- <right-toolbar :search="false" :columns="[]" @queryTable="handleSearch"></right-toolbar> -->
            </div>
        </div>
        <div class="mt-2" v-loading="loading">
            <vxe-table ref="tableRef" :data="dataList" :height="tableHeight" :tree-config="bindTreeConfig" border
                header-cell-class-name="project-header-custom"
                :row-drag-config="{ isCrossDrag: true, visibleMethod: ({ row }) => row.typeCode }"
                :row-config="{ drag: true }" :virtual-y-config="{ enabled: true, gt: 0 }"
                :virtual-x-config="{ enabled: true, gt: 0 }">
                <vxe-column field="node" width="60px" align="center" tree-node fixed="left"></vxe-column>
                <vxe-column title="序号" width="60px" align="center" fixed="left">
                    <template #default="{ row }">
                        {{ row.typeCode ? row.index : '' }}
                    </template>
                </vxe-column>
                <vxe-column field="projName" title="项目名称" width="300px" fixed="left">
                    <template #default="{ row }">
                        <span :class="{ 'font-bold': !row.typeCode }">{{ row.projName }}</span>
                    </template>
                </vxe-column>
                <vxe-column field="khorgName" title="考核主体" align="center" width="140px" fixed="left"
                    :visible="columnMap.get('khorgName')?.visible"></vxe-column>
                <vxe-colgroup title="上年结转" align="center"
                    :visible="columnMap.get('prevAmount')?.visible || columnMap.get('prevBalance')?.visible || columnMap.get('prevPurchase')?.visible">
                    <vxe-column field="prevAmount" :title="columnMap.get('prevAmount')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.prevAmount)"
                        :visible="columnMap.get('prevAmount')?.visible"></vxe-column>
                    <vxe-column field="prevBalance" :title="columnMap.get('prevBalance')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.prevBalance)"
                        :visible="columnMap.get('prevBalance')?.visible">
                    </vxe-column>
                    <vxe-column field="prevPurchase" :title="columnMap.get('prevPurchase')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.prevPurchase)"
                        :visible="columnMap.get('prevPurchase')?.visible">
                    </vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="调整后预算" align="center"
                    :visible="columnMap.get('adjustAmount')?.visible || columnMap.get('adjustFund')?.visible || columnMap.get('adjustPurchase')?.visible || columnMap.get('adjustService')?.visible">
                    <vxe-column field="adjustAmount" :title="columnMap.get('adjustAmount')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.adjustAmount)"
                        :visible="columnMap.get('adjustAmount')?.visible"></vxe-column>
                    <vxe-column field="adjustFund" :title="columnMap.get('adjustFund')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.adjustFund)"
                        :visible="columnMap.get('adjustFund')?.visible"></vxe-column>
                    <vxe-column field="adjustPurchase" :title="columnMap.get('adjustPurchase')?.label"
                        header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.adjustPurchase)"
                        :visible="columnMap.get('adjustPurchase')?.visible"></vxe-column>
                    <vxe-column field="adjustService" :title="columnMap.get('adjustService')?.label"
                        header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.adjustService)"
                        :visible="columnMap.get('adjustService')?.visible"></vxe-column>
                </vxe-colgroup>
                <vxe-column field="receivedAmount" :title="columnMap.get('receivedAmount')?.label" header-align="center"
                    align="right" width="140px" :formatter="t => numberFormat(t.row.receivedAmount)"
                    :visible="columnMap.get('receivedAmount')?.visible"></vxe-column>
                <vxe-colgroup title="资金拨付" align="center"
                    :visible="columnMap.get('payedAmount')?.visible || columnMap.get('payedSpecial')?.visible || columnMap.get('payedPurchase')?.visible">
                    <vxe-column field="payedAmount" :title="columnMap.get('payedAmount')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.payedAmount)"
                        :visible="columnMap.get('payedAmount')?.visible"></vxe-column>
                    <vxe-column field="payedSpecial" :title="columnMap.get('payedSpecial')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.payedSpecial)"
                        :visible="columnMap.get('payedSpecial')?.visible"></vxe-column>
                    <vxe-column field="payedPurchase" :title="columnMap.get('payedPurchase')?.label"
                        header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.payedPurchase)"
                        :visible="columnMap.get('payedPurchase')?.visible">
                    </vxe-column>
                </vxe-colgroup>
                <vxe-colgroup title="预算结转结余" align="center"
                    :visible="columnMap.get('budgetAmount')?.visible || columnMap.get('budgetOutstand')?.visible || columnMap.get('budgetPurchase')?.visible || columnMap.get('budgetSave')?.visible">
                    <vxe-column field="budgetAmount" :title="columnMap.get('budgetAmount')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.budgetAmount)"
                        :visible="columnMap.get('budgetAmount')?.visible"></vxe-column>
                    <vxe-column field="budgetOutstand" :title="columnMap.get('budgetOutstand')?.label"
                        header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.budgetOutstand)"
                        :visible="columnMap.get('budgetOutstand')?.visible"></vxe-column>
                    <vxe-column field="budgetPurchase" :title="columnMap.get('budgetPurchase')?.label"
                        header-align="center" align="right" width="140px"
                        :formatter="t => numberFormat(t.row.budgetPurchase)"
                        :visible="columnMap.get('budgetPurchase')?.visible">
                    </vxe-column>
                    <vxe-column field="budgetSave" :title="columnMap.get('budgetSave')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.budgetSave)"
                        :visible="columnMap.get('budgetSave')?.visible"></vxe-column>
                </vxe-colgroup>
                <vxe-colgroup :title="`${budgetInfo?.year}年资金结转结余`" align="center"
                    :visible="columnMap.get('fundAmount')?.visible || columnMap.get('fundBalance1')?.visible || columnMap.get('fundBalance2')?.visible">
                    <vxe-column field="fundAmount" :title="columnMap.get('fundAmount')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.fundAmount)"
                        :visible="columnMap.get('fundAmount')?.visible"></vxe-column>
                    <vxe-column field="fundBalance1" :title="columnMap.get('fundBalance1')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.fundBalance1)"
                        :visible="columnMap.get('fundBalance1')?.visible"></vxe-column>
                    <vxe-column field="fundBalance2" :title="columnMap.get('fundBalance2')?.label" header-align="center"
                        align="right" width="140px" :formatter="t => numberFormat(t.row.fundBalance2)"
                        :visible="columnMap.get('fundBalance2')?.visible">
                    </vxe-column>
                </vxe-colgroup>

                <vxe-column field="remark" :title="columnMap.get('remark')?.label" align="center" width="140px"
                    :visible="columnMap.get('remark')?.visible">
                </vxe-column>
            </vxe-table>
        </div>

        <div class="fixed bottom-0 z-10 bg-white w-full p-2">
            
            <el-button type="primary" @click="handlePrint">打印预览</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <el-button type="danger" @click="$router.back()">返回</el-button>
        </div>
    </div>
</template>
<script lang="ts" setup>
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs, ElMessage, ElMessageBox, type TabsPaneContext } from 'element-plus'
import { v4 as uuidv4 } from 'uuid'
import { getBudgetCleardetailById } from '@/api/fund/budgetClear';
import { numberFormat } from '@/utils/common';

// @ts-ignore
VxeUI.use(VxePcUI)

const props = defineProps<{
    snapId: string
}>()

const tableHeight = ref(window.innerHeight - 200)

const { proxy } = getCurrentInstance() as { proxy: any };

const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId',
    childrenField: 'childDetailList'
}

const columnMap = ref<Map<string, any>>(new Map())
const loading = ref(true);
const budgetInfo = ref<FundBudget>()
const dataList = ref<(FundBudgetAdjust & { index?: number, parentId?: string, categorySort?: number })[]>([])
const handleSearch = () => {
    getBudgetCleardetailById(props.snapId).then(res => {
        budgetInfo.value = res.data?.budget
        const list: typeof dataList.value = []
        res.data?.budgetDetailList?.forEach(item => {
            let index = 1
            item.id = item.id ?? uuidv4()
            list.push({ ...item, childDetailList: [] })
            item.childDetailList?.forEach(t => {
                list.push({ ...t, index: index++, id: t.id ?? uuidv4(), parentId: item.id })
            })
        })
        dataList.value = list.sort((a, b) => a.categorySort! - b.categorySort!)
        dataList.value.unshift(res.data?.total!)
        JSON.parse(budgetInfo.value?.columns ?? '[]').forEach((col: any) => {
            columnMap.value.set(col.prop, col)
        });
    }).finally(() => {
        loading.value = false
    })
}
handleSearch()


const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
    const map = new Map<string, any[]>()
    tableRef.value?.getPrintHtml({
        excludeFields: ['node'],
        dataFilterMethod: (data: { row: any }) => {
            map.set(data.row.id, data.row.childDetailList)
            data.row.childDetailList = []
            return true
        }
    }).then(res => {
        const headHtml = `
        <h1 style="text-align: center;">
        ${budgetInfo.value?.year}年度化工区专项发展资金结转结余明细表
        </h1>
        <div style="text-align: right;margin-bottom: 5px;">${budgetInfo.value?.unit}</div>`

        const html = res.html
        tableRef.value?.print({
            html: headHtml + html,
        })
    }).finally(() => {
        dataList.value.forEach((item) => {
            if (map.has(item.id!)) {
                item.childDetailList = map.get(item.id!)
            }
        })
    })
}

const handleDownloadExcel = () => {
    proxy.download('/fund/budgetBalance/export', {
        id: budgetInfo.value?.id,
    }, `${budgetInfo.value?.year}年度化工区专项发展资金结转结余明细表${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}

</script>

<style scoped>
:deep(.project-header-custom) {
    background: linear-gradient(to bottom, #fafdff, #deeefb) !important;
    /* 深蓝渐变 */
    text-align: center;
    border-right: 1px solid #d9e0eb !important;
}
</style>
