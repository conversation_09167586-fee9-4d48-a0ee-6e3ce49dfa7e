<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="公告标题" prop="noticeTitle">
            <el-input v-model="queryParams.noticeTitle" placeholder="请输入公告标题" clearable style="width: 200px"
               maxlength="500" @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="操作人员" prop="createBy">
            <el-input v-model="queryParams.createBy" placeholder="请输入操作人员" clearable style="width: 200px"
               @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="类型" prop="noticeType">
            <el-select v-model="queryParams.noticeType" placeholder="公告类型" clearable style="width: 200px">
               <el-option v-for="dict in sys_notice_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd"
               v-hasPermi="['system:notice:add']">新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
               v-hasPermi="['system:notice:edit']">修改</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
               v-hasPermi="['system:notice:remove']">删除</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table border v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column type="index" label="序号" align="center" prop="noticeId" width="100" />
         <el-table-column label="公告标题" align="center" prop="noticeTitle" :show-overflow-tooltip="true" />
         <el-table-column label="公告类型" align="center" prop="noticeType" width="100">
            <template #default="scope">
               <dict-tag :options="sys_notice_type" :value="scope.row.noticeType" />
            </template>
         </el-table-column>
         <el-table-column prop="releaseFlag" label="发布状态" align="center" width="100">
            <template #default="{ row }">
               <el-switch v-model="row.releaseFlag" active-value="1" inactive-value="0"
                  @change="handleReleaseChange(row)"></el-switch>
            </template>
         </el-table-column>
         <el-table-column label="状态" align="center" prop="status" width="100">
            <template #default="scope">
               <dict-tag :options="sys_notice_status" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="创建者" align="center" prop="createBy" width="100" />
         <el-table-column label="创建时间" align="center" prop="createTime" width="200">
            <template #default="scope">
               <span>{{ dateFormat(scope.row.createTime, 'YYYY-MM-DD HH:mm:ss') }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:notice:edit']">修改</el-button>
               <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['system:notice:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize" @pagination="getList" />

      <!-- 添加或修改公告对话框 -->
      <el-dialog :title="title" v-model="open" width="900px" append-to-body destroy-on-close>
         <el-scrollbar height="600px">
            <el-form ref="noticeRef" :model="form" :rules="rules" label-width="80px">
               <el-row>
                  <el-col :span="12">
                     <el-form-item label="公告标题" prop="noticeTitle">
                        <el-input v-model="form.noticeTitle" placeholder="请输入公告标题" />
                     </el-form-item>
                  </el-col>
                  <el-col :span="12">
                     <el-form-item label="公告类型" prop="noticeType">
                        <el-select v-model="form.noticeType" placeholder="请选择">
                           <el-option v-for="dict in sys_notice_type" :key="dict.value" :label="dict.label"
                              :value="dict.value"></el-option>
                        </el-select>
                     </el-form-item>
                  </el-col>
                  <el-col :span="24">
                     <el-form-item label="状态">
                        <el-radio-group v-model="form.status">
                           <el-radio v-for="dict in sys_notice_status" :key="dict.value" :value="dict.value">{{
                              dict.label
                              }}</el-radio>
                        </el-radio-group>
                     </el-form-item>
                  </el-col>
                  <el-col :span="24">
                     <el-form-item label="内容">
                        <editor v-model="form.noticeContent" :height="500" />
                     </el-form-item>
                  </el-col>
                  <el-col :span="24">
                     <el-form-item label="附件">
                        <my-file-upload :accept="fileType" :data="{
                                sourceId: form.noticeId,
                                sourceType: EFileSoureType.NOTICE,
                                primaryType: EFileSoureType.NOTICE,
                                    }" @upload-success="fileTableRef?.updateTable"></my-file-upload>
                         <FileTable class="mt-1" ref="fileTableRef" :source-id="form.noticeId"></FileTable>
                     </el-form-item>
                  </el-col>
               </el-row>
            </el-form>
         </el-scrollbar>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Notice">
import { listNotice, getNotice, delNotice, addNotice, updateNotice, release } from "@/api/system/notice";
import { dateFormat, genSnowId } from "@/utils/common";
import { dayjs } from "element-plus";
import FileTable from '@/components/Table/FileTable.vue';
import { EFileSoureType } from '@/utils/constants'

const { proxy } = getCurrentInstance();
const { sys_notice_status, sys_notice_type } = proxy.useDict("sys_notice_status", "sys_notice_type");
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const fileTableRef = ref(null)
const noticeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
   form: {},
   queryParams: {
      pageNum: 1,
      pageSize: 10,
      noticeTitle: undefined,
      createBy: undefined,
      status: undefined
   },
   rules: {
      noticeTitle: [{ required: true, message: "公告标题不能为空", trigger: "blur" }],
      noticeType: [{ required: true, message: "公告类型不能为空", trigger: "change" }]
   },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
   loading.value = true;
   listNotice(queryParams.value).then(response => {
      noticeList.value = response.rows;
      total.value = response.total;
      loading.value = false;
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 表单重置 */
function reset() {
   form.value = {
      noticeId: genSnowId(),
      noticeTitle: undefined,
      noticeType: undefined,
      noticeContent: undefined,
      status: "0"
   };
   proxy.resetForm("noticeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.pageNum = 1;
   getList();
}

/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.noticeId);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "添加公告";
}

/**修改按钮操作 */
function handleUpdate(row) {
   reset();
   const noticeId = row.noticeId || ids.value;
   getNotice(noticeId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改公告";
   });
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["noticeRef"].validate(valid => {
      if (valid) {
         // if (form.value.noticeId != undefined) {
         //    updateNotice(form.value).then(response => {
         //       proxy.$modal.msgSuccess("修改成功");
         //       open.value = false;
         //       getList();
         //    });
         // } else {
            addNotice(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         // }
      }
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const noticeIds = row.noticeId || ids.value
   proxy.$modal.confirm('是否确认删除公告编号为"' + noticeIds + '"的数据项？').then(function () {
      return delNotice(noticeIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

getList();

const handleReleaseChange = (row) => {
   release(row.noticeId).then(res => {
      proxy.$modal.msgSuccess("操作成功");
   })
}
</script>
