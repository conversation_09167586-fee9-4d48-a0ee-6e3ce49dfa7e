<template>
    <div class="app-container">
        <div>
            <el-form :inline="true" :model="queryForm" class="mb-4" label-width="120px">
                <el-form-item label="项目名称">
                    <el-input v-model="queryForm.projectName" placeholder="请输入项目名称" class="w-50" />
                </el-form-item>
                <el-form-item label="申报单位">
                    <el-select v-model="queryForm.applyOrg" placeholder="--全部--" class="w-50">
                        <el-option label="全部" value="" />
                    </el-select>
                </el-form-item>
                <el-form-item label="配合单位">
                    <el-input v-model="queryForm.partnerUnit" placeholder="输入单位名" class="w-50" />
                </el-form-item>

                <el-form-item label="项目性质">
                    <el-select v-model="queryForm.natureCode" placeholder="请选择项目性质" class="w-50">
                        <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目类别">
                    <ProjectTypeSelect v-model="queryForm.typeId" placeholder="请选择项目类别" class="w-50" clearable>
                    </ProjectTypeSelect>
                </el-form-item>
                <el-form-item label="项目用途">
                    <el-select v-model="queryForm.purposeIds" multiple placeholder="请选择项目用途" class="w-50">
                        <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目状态">
                    <el-select v-model="queryForm.status" placeholder="--全部--" class="w-50">
                        <el-option label="全部" value="" />
                        <el-option label="待操作" value="待操作" />
                        <el-option label="已入库" value="已入库" />
                    </el-select>
                </el-form-item>
                <el-form-item label="执行年度">
                    <el-date-picker v-model="queryForm.year" type="year" value-format="YYYY" class="!w-25" />
                    <el-date-picker v-model="queryForm.year" type="year" value-format="YYYY" class="!w-25" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button type="success" @click="handleExport">导出</el-button>
                </el-form-item>
            </el-form>
        </div>

        <el-table :data="dataList" border v-loading="loading" show-summary :summary-method="getSummaries">
            <el-table-column prop="index" label="序号" width="60" align="center">
                <template #default="{ row, $index }">
                    <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                </template>
            </el-table-column>
            <el-table-column label="项目名称" align="center">
                <template #default="{ row }">
                    <div>
                        <div>{{ row.name }}</div>
                        <div class="text-xs text-gray-500">编号：{{ row.code }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="unit" label="申报单位" width="180" align="center" />
            <el-table-column prop="amount" label="项目金额（估算）" width="150" align="center" />
            <el-table-column prop="period" label="项目周期" width="180" align="center" />
            <el-table-column prop="status" label="状态" width="120" align="center" />
            <el-table-column prop="status" label="分送方向" width="200" align="center" />
            <el-table-column label="操作" width="300" align="center">
                <template #default>
                    <el-button type="primary" text icon="view" class="px-1">查看</el-button>
                    <el-button type="primary" text icon="guide" class="!ml-1 px-1">受理</el-button>
                    <el-button type="primary" text icon="guide" class="!ml-1 px-1">回退</el-button>
                    <el-button type="primary" text icon="guide" class="!ml-1 px-1">分篮子</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" />
            </template>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="search" />
    </div>

</template>

<script lang="ts" setup>
import { TableColumnCtx } from 'element-plus';


const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_basket_type');


const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const queryForm = reactive({
    projectName: '',
    projectType: '',
    basketCode: '',
    natureCode: '',
    typeId: '',
    expenseType: '',
    purposeIds: '',
    status: '',
    year: ''
})
const loading = ref(false)
const total = ref(0)
const dataList = ref([{}])
const search = () => {

}

const handleExport = () => {

}

interface SummaryMethodProps<T = typeof dataList.value[number]> {
    columns: TableColumnCtx<T>[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {

        if (index == 3) {
            sums[index] = 100
        }
    })
    return sums
}
</script>