<template>
  <div class="app-container" v-loading="loading">
    <div class="flex gap-3">
      <el-form :inline="true" class="demo-form-inline h-[45px] ">
        <el-form-item label="项目年度" label-width="90px"><el-input-number v-model="searchForm.year" :min="2015" :max="2099"
            width="300">
            <template #suffix>
              <span>年</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="考核主体" label-width="70px">
          <DeptSelect v-model="searchForm.assessOrgid" class="w-45" clearable></DeptSelect>
        </el-form-item>
        <el-form-item label-width="70px">
          <el-checkbox v-model="searchForm.hasEarlyAmount" :true-value="'1'" :false-value="''">已下达年初计划</el-checkbox>
          <el-checkbox v-model="searchForm.hasAdjustAmount" :true-value="'1'" :false-value="''">已下达调整计划</el-checkbox>
        </el-form-item>
      </el-form>


      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="search">
        <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
          <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="top">
          <el-button class="ml-3" circle icon="Connection" @click="handleXmjzgl" />
          <template #content>
            1、结转项目：截止上年底有过资金执行，且未执行完成的项目；<br />
            2、经常性项目：无资金执行记录，且项目周期在当前年度的经常性项目；<br />
            3、新增项目：无资金执行记录，且项目周期在当前年度的其它项目（非经常性项目）。
          </template>
        </el-tooltip>

      </right-toolbar>
      <el-drawer v-model="showSearch" title="查询" size="400px">
        <el-form label-width="70px">
          <el-form-item label="项目名称">
            <el-input v-model="searchForm.name" placeholder="请输入项目名称" class="w-full" clearable></el-input>
          </el-form-item>
          <el-form-item label="项目性质">
            <el-select v-model="searchForm.natureCode" placeholder="请选择项目性质" class="w-full" clearable>
              <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目类别">
            <ProjectTypeSelect v-model="searchForm.typeId" placeholder="请选择项目类别" class="w-full" clearable>
            </ProjectTypeSelect>
          </el-form-item>
          <el-form-item label="项目用途">
            <el-select v-model="searchForm.purposeIds" multiple collapse-tags placeholder="请选择项目类别" class="w-full"
              clearable>
              <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目篮子">
            <el-select v-model="searchForm.basketCode" placeholder="请选择项目篮子" class="w-full" clearable>
              <el-option v-for="item in project_basket_type" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isSzhyq" :true-value="'1'" :false-value="''">涉及数字化园区</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isJxhgl" :true-value="'1'" :false-value="''">涉及精细化管理支出</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isZfgm" :true-value="'1'" :false-value="''">涉及政府购买服务</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.libState" :true-value="'1'" :false-value="''">是否入库</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.checkState" :true-value="'1'" :false-value="''">是否核定</el-checkbox>
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="text-left">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="handleSearchReset">重置</el-button>
          </div>
        </template>
      </el-drawer>
    </div>
    <div style="table-outer">
      <el-table ref="tableRef" :data="dataList" fit row-key="id" border show-summary :expand-row-keys="[1, 2, 3, 4]"
        :height="tableHeight" :header-cell-style="{ 'text-align': 'center' }" :row-class-name="tableRowClassName"
        :summary-method="getSummaries" :indent="0" preserve-expanded-content>
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
        <el-table-column label="序号" align="center" width="60px" fixed="left">
          <template #default="{ row, $index }">
            <span>{{ row.index }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="项目名称" width="auto" min-width="300px" fixed="left">
          <template #default="{ row }">
            <ProjNameComponent :row="row"></ProjNameComponent>
          </template>

        </el-table-column>
        <el-table-column prop="assessOrgname" label="考核主体" align="center" width="100" v-if="columns[0].visible">
          <template #default="{ row }">
            <el-tooltip placement="top">
              <template #content>
                申报单位：{{ row.applyOrgname ?? '无' }}<br />
                配合单位：{{ row.cooperateOrgname ?? '无' }}<br />
                上报单位：{{ row.submitOrgname ?? '无' }}<br />
                考核主体：{{ row.assessOrgname ?? '无' }}
              </template>
              {{ row.assessOrgname }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="handler" label="经办人" align="center" width="100px" v-if="columns[1].visible">
          <template #default="{ row }">
            <span>{{ row.handler }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="estAmount" label="项目估算" align="right" width="100px" v-if="columns[2].visible">
          <template #default="{ row }">
            <span>{{ row.estAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="(columns[3].visible || columns[4].visible || columns[5].visible) ? '项目库' : ''">
          <el-table-column prop="basket_name" label="项目状态" align="center" width="100px" v-if="columns[3].visible">
            <template #default="{ row }">
              <BasketNameComponent :row="row"></BasketNameComponent>
            </template>
          </el-table-column>
          <el-table-column prop="libAmount" label="入库金额" align="right" width="100px" v-if="columns[4].visible">
            <template #default="{ row }">
              <LibAmountComponent :row="row"></LibAmountComponent>

            </template>
          </el-table-column>
          <el-table-column prop="checkAmount" label="核定金额" align="right" width="100px" v-if="columns[5].visible">
            <template #default="{ row }">
              <CheckAmountComponent :row="row"></CheckAmountComponent>

            </template>
          </el-table-column>
          <!-- <el-table-column prop="ht_amount" label="合同金额" align="right" width="auto" min-width="10%"
            v-if="columns[6].visible" /> -->
        </el-table-column>
        <el-table-column :label="(columns[7].visible || columns[8].visible || columns[9].visible) ? '已执行' : ''">
          <el-table-column prop="prevAmount" label="截至上年底" align="right" width="100px" v-if="columns[7].visible"
            :formatter="(row: any) => roundNumber2(row.prevAmount)?.toString()">
          </el-table-column>
          <el-table-column prop="currPayed" label="当年" align="right" width="100px" v-if="columns[8].visible"
            :formatter="(row: any) => roundNumber2(row.currPayed)?.toString()">
          </el-table-column>
          <el-table-column prop="payedTotal" label="合计" align="right" width="100px" v-if="columns[9].visible"
            :formatter="(row: any) => roundNumber2(row.payedTotal)?.toString()">
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="(columns[10].visible || columns[11].visible || columns[12].visible || columns[13].visible) ? '资金计划' : ''">
          <el-table-column prop="planSb" label="上报计划" align="right" width="100px" v-if="columns[10].visible">
            <template #default="{ row }">
              <PlanSbComponent v-show="columns[10].visible" :row="row"></PlanSbComponent>

            </template>
          </el-table-column>
          <el-table-column prop="planXd" label="下达计划" align="right" width="120" v-if="columns[11].visible">
            <template #default="{ row }">
              <PlanXdComponent v-show="columns[11].visible" :row="row" :key="row.id"></PlanXdComponent>
            </template>

          </el-table-column>
          <el-table-column prop="planTzjh" label="调整计划" align="right" width="100px" v-if="columns[12].visible">
            <template #default="{ row }">
              <PlanTzjhComponent v-show="columns[12].visible" :row="row"></PlanTzjhComponent>
            </template>
          </el-table-column>
          <el-table-column prop="planTzxd" label="调整下达" align="right" width="120" v-if="columns[13].visible">
            <template #default="{ row }">
              <PlanTzxdComponent v-show="columns[13].visible" :row="row"></PlanTzxdComponent>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column prop="date" label="操作" width="auto" min-width="10%" /> -->
      </el-table>
    </div>

    <el-dialog title="项目查看" v-model="formViewShow" width="1200px" :close-on-click-modal="false" destroy-on-close>
      <FormView @close="formViewShow = false; search()"></FormView>
    </el-dialog>
    <el-dialog title="设置入库金额" v-model="sbFormShow" width="300px" align-center :close-on-click-modal="false"
      destroy-on-close>
      <sbForm :project-id="currentProjectId" @close="sbFormShow = false; search()" @cancel="sbFormShow = false">
      </sbForm>
    </el-dialog>
    <el-dialog title="设置入库金额" v-model="cbFormShow" width="300px" align-center :close-on-click-modal="false"
      destroy-on-close>
      <cbForm :project-id="currentProjectId" @close="cbFormShow = false; search()" @cancel="cbFormShow = false">
      </cbForm>
    </el-dialog>
    <el-dialog title="设置入库金额" v-model="ssFormShow" width="300px" align-center :close-on-click-modal="false"
      destroy-on-close>
      <SsForm :project-id="currentProjectId" @close="ssFormShow = false; search()" @cancel="ssFormShow = false">
      </SsForm>
    </el-dialog>
    <el-dialog title="入库" v-model="rkFormShow" width="800px" align-center :close-on-click-modal="false"
      destroy-on-close>
      <RkForm :project-id="currentProjectId" @close="rkFormShow = false; search()" @cancel="rkFormShow = false">
      </RkForm>
    </el-dialog>
    <el-dialog title="核定" v-model="hdFormShow" width="800px" align-center :close-on-click-modal="false"
      destroy-on-close>
      <HdForm :project-id="currentProjectId" @close="hdFormShow = false; search()" @cancel="hdFormShow = false">
      </HdForm>
    </el-dialog>

  </div>
</template>

<script lang="tsx" setup name="ManageIndex">
import FormView from '../apply/components/formView.vue';
import sbForm from './components/sbForm.vue'
import cbForm from './components/cbForm.vue'
import SsForm from './components/ssForm.vue'
import RkForm from './components/rkForm.vue'
import HdForm from './components/hdForm.vue'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { dayjs, ElDropdown, ElMessage, ElMessageBox, TableColumnCtx, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { addImportantProject, adjustPlan, cancelAdjust, cancelCheck, cancelImportantProject, cancelLib, cancelRelease, carryOver, doubleClickAdjust, doubleClickRelease, getList, releasePlan } from '@/api/lib/project/manage'
import { useFormStore } from '../apply/components/store/formStore'
// @ts-ignore
import useUserStore from '@/store/modules/user'
import { EProjectState, ELibState, ECheckState, EFundPlanState, EApplyAdjustState } from '@/utils/constants';
import { sumAmount, checkIsNumber, roundNumber2 } from '@/utils/common';
import { BigNumber } from 'bignumber.js';
import { useMouse } from '@vueuse/core';
import { StarFilled, Star, Promotion } from '@element-plus/icons-vue'
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import { useConfig } from '@/utils/config';

const {
  ["sys.click.releasePlan"]: clickReleasePlan
} = useConfig('sys.click.releasePlan')


const ProjNameComponent = defineComponent({
  name: 'projNameComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {

    return () => {
      if (props.row.projType) {
        if (props.row.projType == 1) {
          return (<div class="inline-flex items-center">
            <span>一、结转项目</span>
            <span>({props.row.children.length})</span>
          </div>)
        }
        if (props.row.projType == 2) {
          return (<div class="inline-flex items-center">
            <span>二、经常性项目</span>
            <span>({props.row.children.length})</span>
          </div>)
        }
        if (props.row.projType == 3) {
          return (<div class="inline-flex items-center">
            <span>三、新增项目</span>
            <span>({props.row.children.length})</span>
          </div>)
        }
      } else {
        return (<div class="grid grid-cols-[1fr_30px] items-center">
          <div>
            <el-link type="primary" onClick={() => handleFormViewShow(props.row)}>{props.row.name}</el-link>
            <div>编号：{props.row.projectSn?.formalSn ?? props.row.projectSn?.tempSn}</div>
          </div>
          {
            props.row.currentYearRelation?.impProj == '1' ?
              <el-tooltip placement="top" content="取消重点项目">
                <i class="iconfont icon-star-fill text-6 text-orange" onClick={() => handleCancelImpProject(props.row)}></i>
              </el-tooltip> :
              <el-tooltip placement="top" content="设置重点项目">
                <i class="iconfont icon-star text-6 " onClick={() => handleImpProject(props.row)}></i>
              </el-tooltip>
          }

        </div >)
      }
    }
  }
})

const BasketNameComponent = defineComponent({
  name: 'basketNameComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    const status = ref(false)
    return () => {
      if (props.row.projType) {
        return ''
      }
      if (status.value == false) {
        return (<div onMouseover={() => status.value = true}>
          {props.row.basketCode ?
            <dict-tag options={project_basket_type.value} value={props.row.basketCode} />
            : (props.row.state == EProjectState.reported ? <div>已上报</div> : '')
          }
        </div>)
      }
      return <div class="flex items-center justify-center cursor-pointer">
        <ElDropdown placement="bottom" onCommand={handleCommand} onVisible-change={val => status.value = val} persistent={false} v-slots={{
          dropdown: () => (
            <ElDropdownMenu>
              <ElDropdownItem command={{ fun: handleOpenSb, args: { id: props.row.id } }}>申报库</ElDropdownItem>
              <ElDropdownItem command={{ fun: handleOpenCb, args: { id: props.row.id } }}>储备库</ElDropdownItem>
              <ElDropdownItem command={{ fun: handleOpenSs, args: { id: props.row.id } }}>实施库</ElDropdownItem>
            </ElDropdownMenu>
          )
        }}>
          <div>
            {props.row.basketCode ?
              <dict-tag options={project_basket_type.value} value={props.row.basketCode} />
              : (props.row.state == EProjectState.reported ? <div>已上报</div> : '')
            }
          </div>
        </ElDropdown>
      </div>
    }
  }

})

const LibAmountComponent = defineComponent({
  name: 'libAmountComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    const status = ref(false)

    const flag = computed(() =>
      props.row.libState === ELibState.yrk
        ? <div class="absolute left-2 w-2 h-2 bg-green rounded-full"></div>
        : null
    )

    const amountContent = computed(() =>
      checkIsNumber(props.row.libAmount)
        ? <el-link type="primary">{props.row.libAmount}</el-link>
        : <div class="h-10 flex-1"></div>
    )

    const dropdown = computed(() => (
      <ElDropdown
        class="flex-1 justify-right"
        placement="bottom"
        onCommand={handleCommand}
        onVisible-change={val => status.value = val}
        persistent={false}
        v-slots={{
          dropdown: () => (
            <ElDropdownMenu>
              <ElDropdownItem command={{ fun: handleOpenRk, args: { id: props.row.id } }}>
                入库
              </ElDropdownItem>
              {props.row.libState === ELibState.yrk && (
                <ElDropdownItem command={{ fun: handleCancelRk, args: { id: props.row.id } }}>
                  取消
                </ElDropdownItem>
              )}
            </ElDropdownMenu>
          )
        }}
      >
        {amountContent.value}
      </ElDropdown>
    ))

    return () => {
      // 有 projType 直接返回
      if (props.row.projType) {
        return <span>{props.row.libAmount}</span>
      }

      // 初始状态
      if (!status.value) {
        return (
          <div
            class="flex items-center justify-right cursor-pointer"
            onMouseover={() => (status.value = true)}
          >
            {flag.value}
            {amountContent.value}
          </div>
        )
      }

      // 展开 dropdown
      return (
        <div class="flex items-center justify-right cursor-pointer">
          {flag.value}
          {dropdown.value}
        </div>
      )
    }
  }
})

const CheckAmountComponent = defineComponent({
  name: 'CheckAmountComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    const dropdownVisible = ref(false)

    // 显示的金额内容
    const amountContent = computed(() => {
      if (checkIsNumber(props.row.checkAmount)) {
        return <el-link type="primary">{props.row.checkAmount}</el-link>
      }
      return <div class="h-10 flex-1"></div>
    })

    // 下拉菜单
    const dropdownMenu = (
      <ElDropdownMenu>
        <ElDropdownItem command={{ fun: handleOpenHd, args: { id: props.row.id } }}>
          核定
        </ElDropdownItem>
        {props.row.checkState === ECheckState.yhd && (
          <ElDropdownItem command={{ fun: handleCancelHd, args: { id: props.row.id } }}>
            取消
          </ElDropdownItem>
        )}
      </ElDropdownMenu>
    )

    return () => {
      // 如果有 projType，直接显示数值
      if (props.row.projType) {
        return <span>{roundNumber2(props.row.checkAmount)}</span>
      }

      // 初始态：仅显示金额，hover 后展开
      if (!dropdownVisible.value) {
        return (
          <div
            class="flex items-center justify-right cursor-pointer"
            onMouseover={() => (dropdownVisible.value = true)}
          >
            {amountContent.value}
          </div>
        )
      }

      // 展开态：显示下拉
      return (
        <div class="flex items-center justify-right cursor-pointer">
          <ElDropdown
            class="flex-1 justify-right"
            placement="bottom"
            onCommand={handleCommand}
            onVisible-change={val => (dropdownVisible.value = val)}
            persistent={false}
            v-slots={{ dropdown: () => dropdownMenu }}
          >
            {amountContent.value}
          </ElDropdown>
        </div>
      )
    }
  }
})

const PlanSbComponent = defineComponent({
  name: 'planSbComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (!props.row.projType) {
        if (!props.row?.fundPlan || [EFundPlanState.reject, EFundPlanState.draft].includes(props.row?.fundPlan?.planState)) {
          return (
            <el-link type="danger">未上报</el-link>
          )
        } else {
          return (<span>{props.row.fundPlan?.declareAmount}</span>)
        }
      } else {
        return (<span>{roundNumber2(props.row.planSb)}</span>)
      }
    }
  }
})

/** 调整计划 */
const PlanTzjhComponent = defineComponent({
  name: 'PlanTzjhComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (props.row.projType) {
        return (<div>
          <span>{roundNumber2(props.row.planTzjh)}</span>
        </div>)
      }
      if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(props.row.fundPlan?.applyState) || props.row.fundPlan?.applyState == null) {
        return (<span class="text-[var(--el-color-info-light-3)]">{props.row.fundPlan?.formalAmount}</span>)
      }
      if (EApplyAdjustState.qrtz == props.row.fundPlan?.applyState) {
        return (<span>{props.row.fundPlan?.applyAdjust ?? props.row.fundPlan?.formalAmount}</span>)
      }
      return (<span>{props.row.fundPlan?.applyAdjust}</span>)
    }
  }
})

/** 下达 */
const PlanXdComponent = defineComponent({
  name: 'PlanXdComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    const status = ref(false)
    const amount = ref<number>(props.row.plan_formalAmount)
    return () => {
      if (props.row.projType) {
        return (<div>
          <span>{roundNumber2(props.row.planXd)}</span><br />
          <span class="text-red">{roundNumber2(props.row.planXd_red)}</span>
        </div>)
      }
      if (props.row?.fundPlan && ![EFundPlanState.reject, EFundPlanState.draft].includes(props.row?.fundPlan?.planState)) {
        if (props.row?.fundPlan?.planState == EFundPlanState.yfb) {
          if (status.value == false) {
            return <el-link type="primary" class="leading-5" onMouseover={() => status.value = true}>{props.row.fundPlan?.formalAmount}</el-link>

          }
          return (
            <ElDropdown placement="bottom" onCommand={handleCommand} onVisible-change={val => status.value = val} persistent={false} v-slots={{
              dropdown: () => (
                <ElDropdownMenu>
                  <el-dropdown-item
                    command={{ fun: handleCancelPublish, args: { id: props.row.id } }}>取消</el-dropdown-item>
                </ElDropdownMenu>
              )
            }}>
              <el-link type="primary" class="leading-5">{props.row.fundPlan?.formalAmount}</el-link>
            </ElDropdown>)
        } else {
          return (<div class="flex items-center rounded-r-sm">
            <el-input-number modelValue={amount.value}
              onUpdate:modelValue={(val: number) => amount.value = val} min={0} controls={false} class="w-full"
              onDblclick={() => handleDbClickXdjh(props.row, amount.value)} />
            <div class="px-1 cursor-pointer hover:text-red">
              <el-tooltip class="item" effect="dark" placement="top" content="发布">
                <el-icon onClick={() => handlePublishXdjh(props.row, amount.value)}>
                  <Promotion></Promotion>
                </el-icon>
              </el-tooltip>

            </div>
          </div>)
        }
      }
    }



  }
})

/** 调整下达 */
const PlanTzxdComponent = defineComponent({
  name: 'PlanTzxdComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    const status = ref(false)
    const amount = ref<number>(props.row.plan_adjustAmount)
    return () => {
      if (props.row.projType) {
        return (<div >
          <span>{roundNumber2(props.row.planTzxd)}</span><br />
          <span class="text-red">{roundNumber2(props.row.planTzxd_red)}</span>
        </div>)
      }
      if ([EApplyAdjustState.draft, EApplyAdjustState.reject, EApplyAdjustState.qrtz].includes(props.row.fundPlan?.applyState ?? 0)) {
        if (status.value == false) {
          return (<div class="flex items-center justify-right cursor-pointer" onMouseover={() => status.value = true}>
            {
              EApplyAdjustState.qrtz == props.row.fundPlan?.applyState ?
                <span class="text-3.5">
                  {props.row.fundPlan?.adjustAmount}
                </span> :
                <span class="text-[var(--el-color-info-light-3)]">{props.row.fundPlan?.formalAmount}</span>
            }
          </div>)
        }
        return (<div class="flex items-center justify-right cursor-pointer">
          <ElDropdown class="flex-1 justify-right" placement="bottom" onCommand={handleCommand} onVisible-change={val => status.value = val}
            persistent={false} v-slots={{
              dropdown: () => (
                <ElDropdownMenu>
                  {
                    EApplyAdjustState.qrtz != props.row.fundPlan?.applyState ?
                      <el-dropdown-item
                        command={{ fun: handleOpenTzxd, args: { row: props.row } }}>调整下达</el-dropdown-item> :
                      <el-dropdown-item
                        command={{ fun: handleCancelTzxd, args: { id: props.row.id } }}>取消</el-dropdown-item>
                  }

                </ElDropdownMenu>
              )
            }}>
            {
              EApplyAdjustState.qrtz == props.row.fundPlan?.applyState ?
                <span class="text-3.5">
                  {props.row.fundPlan?.adjustAmount}
                </span> :
                <span class="text-[var(--el-color-info-light-3)] text-3.5">{props.row.fundPlan?.formalAmount}</span>
            }
          </ElDropdown>
        </div>)
      } else {
        if (props.row.fundPlan?.applyState === EApplyAdjustState.ysb) {
          return (<div class="flex items-center rounded-r-sm">
            <el-input-number modelValue={amount.value}
              onUpdate:modelValue={(val: number) => (amount.value = val)} min={0} controls={false} class="w-full"
              onDblclick={() => handleDbClickTzxd(props.row, amount.value)} />
            <div class="px-1 cursor-pointer hover:text-red">
              <el-tooltip class="item" effect="dark" placement="top" content="发布">
                <el-icon onClick={() => handlePublishTzxd(props.row, amount.value)}>
                  <Promotion></Promotion>
                </el-icon>
              </el-tooltip>

            </div>
          </div>)
        }
      }
    }
  }
})



const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_basket_type');


const showSearch = ref(false)
const userStore = useUserStore()
const formStore = useFormStore()

type rowType = IProjectInfoVo & { index: number; assessOrgnameTitle: string, plan_formalAmount?: number | null; plan_adjustAmount?: number, payedTotal?: number | null }
const dataList = ref([
  {
    id: 1,
    /** 结转项目 */
    projType: 1,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    prevAmount: 0,
    currPayed: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planXd_red: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0
  },
  {
    id: 2,
    /** 经常性项目 */
    projType: 2,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    prevAmount: 0,
    currPayed: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planXd_red: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0
  },
  {
    id: 3,
    /** 新增项目 */
    projType: 3,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    prevAmount: 0,
    currPayed: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planXd_red: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0
  },
])
const searchForm = reactive({
  year: dayjs().isAfter(dayjs().year() + '-08-31', 'day') ? dayjs().year() + 1 : dayjs().year(),
  projectUnits: userStore.deptId,
  name: '',
  natureCode: '',
  basketCode: '',
  typeId: '',
  purposeIds: [],
  isSzhyq: '',
  isJxhgl: '',
  isZfgm: '',
  libState: '',
  checkState: '',
  assessOrgid: '',
  hasEarlyAmount: '',
  hasAdjustAmount: ''
})
const loading = ref(false)
const tableRef = ref<InstanceType<typeof ElTable>>()
const search = () => {
  loading.value = true

  getList(searchForm).then(async res => {
    console.time()
    dataList.value.at(0)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 1) as any
    dataList.value.at(1)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 2) as any
    dataList.value.at(2)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 3) as any

    // 对数据进行合计
    dataList.value.forEach(row => {
      row.children.forEach((t: rowType, index: number) => {
        t.index = index + 1
        t.payedTotal = sumAmount([t.currPayed, t.prevAmount])

        // 如果资金计划是已上报状态，取值方式：先根据配置初步还是正式决定取哪个值
        if (t.fundPlan?.planState == EFundPlanState.ysb) {
          if (clickReleasePlan.value == '0') {
            if (checkIsNumber(t.usagePlanItem?.currEarly)) {
              t.plan_formalAmount = t.usagePlanItem?.currEarly
            }
          }
          if (clickReleasePlan.value == '1') {
            if (checkIsNumber(t.usagePlanItem?.currFormal)) {
              t.plan_formalAmount = t.usagePlanItem?.currFormal
            }
          }
        }
        if (t.fundPlan?.applyState === EApplyAdjustState.ysb) {
          if (checkIsNumber(t.usagePlanItem?.currAdjust)) {
            t.plan_adjustAmount = t.usagePlanItem?.currAdjust
          } else {
            t.plan_adjustAmount = t.fundPlan?.applyAdjust
          }

        }
      })
      row.estAmount = sumAmount(row.children.map(t => t.estAmount)) ?? 0
      row.libAmount = sumAmount(row.children.map(t => t.libAmount)) ?? 0
      row.checkAmount = sumAmount(row.children.map(t => t.checkAmount)) ?? 0
      row.prevAmount = sumAmount(row.children.map(t => t.prevAmount)) ?? 0
      row.currPayed = sumAmount(row.children.map(t => t.currPayed)) ?? 0
      row.payedTotal = sumAmount(row.children.map(t => t.payedTotal)) ?? 0

      row.planSb = sumAmount(
        row.children.map(t => {
          if ([EFundPlanState.ysb, EFundPlanState.yfb].includes(t.fundPlan?.planState)) {
            return t.fundPlan?.declareAmount
          }
          return 0
        })
      ) ?? 0
      row.planXd = sumAmount(
        row.children.map(t => t.fundPlan?.formalAmount)
      ) ?? 0
      row.planXd_red = sumAmount([
        ...row.children.map(t => t.fundPlan?.formalAmount ?? t.plan_formalAmount),
      ]) ?? 0
      row.planTzjh = sumAmount(
        row.children.map(t => {
          if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(t.fundPlan?.applyState) || t.fundPlan?.applyState == null) {
            return t.fundPlan?.formalAmount
          }
          if (EApplyAdjustState.qrtz == t.fundPlan?.applyState) {
            return t.fundPlan?.applyAdjust ?? t.fundPlan?.formalAmount
          }
          return t.fundPlan?.applyAdjust
        })
      ) ?? 0
      row.planTzxd = sumAmount(
        row.children.map(t => {
          if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(t.fundPlan?.applyState) || t.fundPlan?.applyState == null) {
            return t.fundPlan?.formalAmount
          }
          if (t.fundPlan?.applyState == EApplyAdjustState.qrtz) {
            return t.fundPlan?.adjustAmount
          }
          return 0
        })
      ) ?? 0
      row.planTzxd_red = sumAmount([
        ...row.children.map(t => t.plan_adjustAmount),
        row.planTzxd
      ]) ?? 0
    })
  }).finally(() => {
    loading.value = false
    // window.tableRef = tableRef.value
    console.timeEnd()
  })
}
search()


watch(() => [
  searchForm.year,
  searchForm.assessOrgid,
  searchForm.hasEarlyAmount,
  searchForm.hasAdjustAmount], () => {
    search()
  })

const handleCommand = (command: { fun: (args: any) => void, args?: any }) => {
  command.fun(command.args)
}

const currentProjectId = ref('')

// 打开申报库
const sbFormShow = ref(false)
const handleOpenSb = (data: { id: string }) => {
  sbFormShow.value = true
  currentProjectId.value = data.id
}

// 打开储备库
const cbFormShow = ref(false)
const handleOpenCb = (data: { id: string }) => {
  cbFormShow.value = true
  currentProjectId.value = data.id
}

// 打开实施库
const ssFormShow = ref(false)
const handleOpenSs = (data: { id: string }) => {
  ssFormShow.value = true
  currentProjectId.value = data.id
}

// 打开入库
const rkFormShow = ref(false)
const handleOpenRk = (data: { id: string }) => {
  // 打开入库金额设置
  rkFormShow.value = true
  currentProjectId.value = data.id
}

const handleImpProject = (row: any) => {
  addImportantProject(row.id, searchForm.year).then(res => {
    row.currentYearRelation.impProj = '1'
    ElMessage.success('操作成功')
  })
}
const handleCancelImpProject = (row: any) => {
  cancelImportantProject(row.id, searchForm.year).then(res => {
    row.currentYearRelation.impProj = '0'
    ElMessage.success('操作成功')
  })
}


const handleCancelRk = (data: { id: string }) => {
  ElMessageBox.confirm('是否确认取消入库（不影响分篮子）？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 取消入库逻辑
    cancelLib(data.id).then(res => {
      ElMessage.success('取消入库成功')
      search()
    })
  })
}


// 打开核定
const hdFormShow = ref(false)
const handleOpenHd = (data: { id: string }) => {
  // 打开核定金额设置
  hdFormShow.value = true
  currentProjectId.value = data.id
}
const handleCancelHd = (data: { id: string }) => {
  ElMessageBox.confirm('是否确认取消核定（去除核定金额）？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 取消核定逻辑
    cancelCheck(data.id).then(res => {
      ElMessage.success('取消核定成功')
      search()
    })
  }).catch(() => {
    // 用户点击取消
  });
}

// 发布下达计划
const handlePublishXdjh = (row: any, newAmount: number) => {
  if (checkIsNumber(newAmount)) {
    const amount = row.checkAmount ?? row.libAmount
    if (checkIsNumber(amount) && newAmount > amount) {
      ElMessage.warning(`历年已执行+当年计划已达${newAmount}万元，超过项目核定/入库金额`)
      return
    }
    releasePlan({ pid: row.id, year: searchForm.year, amount: newAmount }).then(res => {
      ElMessage.success('发布成功')
      search()
    })
  } else {
    ElMessage.warning('请输入金额')
  }

}

const handlePublishTzxd = (row: any, newAmount: number) => {
  if (checkIsNumber(newAmount)) {
    const amount = row.checkAmount ?? row.libAmount
    if (checkIsNumber(amount) && newAmount > amount) {
      ElMessage.warning(`历年已执行+当年计划已达${newAmount}万元，超过项目核定/入库金额`)
      return
    }
    adjustPlan({ pid: row.id, year: searchForm.year, amount: newAmount }).then(res => {
      ElMessage.success('发布成功')
      search()
    })
  } else {
    ElMessage.warning('请输入金额')
  }
}

// 取消下达金额
const handleCancelPublish = (data: { id: string }) => {
  cancelRelease({ pid: data.id, year: searchForm.year }).then(res => {
    ElMessage.success('取消成功')
    search()
  })

}
// 双击下达计划
const handleDbClickXdjh = (row: any, newAmount: number) => {

  if (checkIsNumber(newAmount)) {
    doubleClickRelease({ pid: row.id, year: searchForm.year, amount: newAmount }).then(res => {
      if (res.code == 202) {
        ElMessageBox.confirm(`用款计划表中未查询到该项目，无法设置“${clickReleasePlan.value == '0' ? '初步计划' : '正式计划'}”！`, '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        })
      } else {
        ElMessage.success(`${clickReleasePlan.value == '0' ? '初步使用计划' : '正式使用计划'}设置成功`)
      }

    })
  }



}

// 双击调整下达
const handleDbClickTzxd = (row: any, newAmount: number) => {
  if (checkIsNumber(newAmount)) {
    doubleClickAdjust({ pid: row.id, year: searchForm.year, amount: newAmount }).then(res => {
      if (res.code == 202) {
        ElMessageBox.confirm('用款计划表中未查询到该项目，无法设置“初步调整计划”！', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        })
      } else {
        ElMessage.success('调整下达金额成功')
      }
    })
  }

}



const handleOpenTzxd = (data: { row: IProjectInfoVo }) => {
  ElMessageBox.prompt('请输入调整金额', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: String(data.row.fundPlan?.formalAmount ?? 0),
    type: 'warning'
  }).then((res) => {
    const num = BigNumber(res.value).toNumber()
    const amount = data.row.checkAmount ?? data.row.libAmount
    if (checkIsNumber(amount) && num > amount) {
      ElMessage.warning(`历年已执行+当年计划已达${num}万元，超过项目核定/入库金额`)
      return
    }
    adjustPlan({ pid: data.row.id!, year: searchForm.year, amount: num }).then(res => {
      ElMessage.success('调整成功')
      search()
    })
  })
}


const handleCancelTzxd = (data: { id: string }) => {
  cancelAdjust({ pid: data.id, year: searchForm.year }).then(res => {
    ElMessage.success('取消成功')
    search()
  })
}

const formViewShow = ref(false)
const handleFormViewShow = (row: IProjectInfoVo) => {
  formViewShow.value = true
  formStore.formView.id = row.id

  formStore.projectYear = searchForm.year
}

// 项目结转归类
const handleXmjzgl = () => {
  ElMessageBox.confirm(`是否确认对${searchForm.year}年项目进行结转/归类？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里可以添加项目结转/归类的逻辑
    carryOver(searchForm.year).then(res => {
      ElMessage.success('结转/归类成功')
      search()
    })
  })
}

const handleSearchReset = () => {
  searchForm.name = ''
  searchForm.natureCode = ''
  searchForm.typeId = ''
  searchForm.purposeIds = []
  searchForm.basketCode = ''
  searchForm.isSzhyq = ''
  searchForm.isJxhgl = ''
  searchForm.isZfgm = ''
  searchForm.libState = ''
  searchForm.checkState = ''
}

//
const num = ref(2025)
const tableHeight = ref(window.innerHeight - 180)

// 列显隐信息
const columns = ref([
  { key: 0, label: `考核主体`, visible: true },
  { key: 1, label: `经办人`, visible: true },
  { key: 2, label: `项目估算`, visible: true },
  { key: 3, label: `项目状态`, visible: true },
  { key: 4, label: `入库金额`, visible: true },
  { key: 5, label: `核定金额`, visible: true },
  { key: 6, label: `合同金额`, visible: true },
  { key: 7, label: `截至上年底`, visible: true },
  { key: 8, label: `当年`, visible: true },
  { key: 9, label: `合计`, visible: true },
  { key: 10, label: `上报计划`, visible: true },
  { key: 11, label: `下达计划`, visible: true },
  { key: 12, label: `调整计划`, visible: true },
  { key: 13, label: `调整下达`, visible: true }
]);

var groupCount = 0
const indexMethod = (index: number) => {
  console.log('indexMethod:', index)
  return index + 1 - groupCount
}


const tableRowClassName = ({
  row,
  rowIndex
}: {
  row: any,
  rowIndex: number
}) => {
  if (row.projType) {
    return 'warning-row'
  }
  return ''
}

// 设置底部合计行样式
onMounted(() => {
  setTimeout(() => {
    const tfoot = proxy.$el.querySelector('.el-table__footer tfoot')
    tfoot.firstChild.children[0].colSpan = 2
    tfoot.firstChild.children[1].style.display = 'none'
  }, 0);
})
interface SummaryMethodProps<T = typeof dataList.value[number] & { children?: IProjectInfoVo[] }> {
  columns: TableColumnCtx[]
  data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns: tableColumns, data } = param
  const sums: (string | VNode | number | null)[] = []
  tableColumns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = (
        <div class="text-left">
          {`合计（${data.flatMap(t => t.children).length}个）`}
        </div>
      )
    }
    if (column.property == 'estAmount') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[2].visible }}>{roundNumber2(sumAmount(data.map(t => t.estAmount)) ?? 0)}</span>
      )
    }
    if (column.property == 'libAmount') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[4].visible }}>{roundNumber2(sumAmount(data.map(t => t.libAmount)) ?? 0)}</span>
      )
    }
    if (column.property == 'checkAmount') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[5].visible }}>{roundNumber2(sumAmount(data.map(t => t.checkAmount)) ?? 0)}</span>
      )
    }
    if (column.property == 'prevAmount') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[7].visible }}>{roundNumber2(sumAmount(data.map(t => t.prevAmount)) ?? 0)}</span>
      )
    }
    if (column.property == 'currPayed') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[8].visible }}>{roundNumber2(sumAmount(data.map(t => t.currPayed)) ?? 0)}</span>
      )
    }
    if (column.property == 'payedTotal') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[9].visible }}>{roundNumber2(sumAmount(data.map(t => t.payedTotal)) ?? 0)}</span>
      )
    }
    if (column.property == 'planSb') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[10].visible }}>{roundNumber2(sumAmount(data.map(t => t.planSb)) ?? 0)}</span>
      )
    }
    if (column.property == 'planXd') {
      sums[index] = (
        <div class={{ 'hidden': !columns.value[11].visible }}>
          <span>{roundNumber2(sumAmount(data.map(t => t.planXd)) ?? 0)}</span><br />
          <span class="text-red">{roundNumber2(sumAmount(data.map(t => t.planXd_red)) ?? 0)}</span>
        </div>
      )
    }
    if (column.property == 'planTzjh') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[12].visible }}>{roundNumber2(sumAmount(data.map(t => t.planTzjh)) ?? 0)}</span>
      )
    }
    if (column.property == 'planTzxd') {
      sums[index] = (
        <div class={{ 'hidden': !columns.value[13].visible }}>
          <span>{roundNumber2(sumAmount(data.map(t => t.planTzxd)) ?? 0)}</span><br />
          <span class="text-red">{roundNumber2(sumAmount(data.map(t => t.planTzxd_red)) ?? 0)}</span>
        </div>
      )
    }

  })
  return sums as any
}


const handleDownloadExcel = () => {
  proxy.download('/project/manage/download', {
    ...searchForm
  }, `项目(计划)管理${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>

<style scoped>
:deep(.el-table__placeholder) {
  display: none;
}

.el-input-group__append {
  padding: 0 8px;
}

:deep(.el-table .warning-row) {
  /*--el-table-tr-bg-color: var(--el-color-warning-light-9);*/
  background-image: linear-gradient(to bottom, #ffffff, #f2f2f2) !important;
  font-weight: 500;
  color: #444;
}
</style>