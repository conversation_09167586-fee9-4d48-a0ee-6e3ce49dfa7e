<template>
  <div class="app-container" v-loading="loading">
    <div class="flex gap-3">
      <el-form :inline="true" class="demo-form-inline h-[45px] ">
        <el-form-item label="项目年度" label-width="90px"><el-input-number v-model="searchForm.year" :min="2015" :max="2099"
            width="300">
            <template #suffix>
              <span>年</span>
            </template>
          </el-input-number>
        </el-form-item>
        <el-form-item label="考核主体" label-width="70px">
          <DeptSelect v-model="searchForm.assessOrgid" class="w-45" clearable></DeptSelect>
        </el-form-item>
        <!-- <el-form-item label-width="70px">
          <el-checkbox v-model="searchForm.hasEarlyAmount" :true-value="'1'" :false-value="''">已下达年初计划</el-checkbox>
          <el-checkbox v-model="searchForm.hasAdjustAmount" :true-value="'1'" :false-value="''">已下达调整计划</el-checkbox>
        </el-form-item> -->
      </el-form>


      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="search">
        <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
          <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="top">
          <el-button class="ml-3" circle icon="Connection" @click="handleXmjzgl" />
          <template #content>
            1、结转项目：截止上年底有过资金执行，且未执行完成的项目；<br />
            2、经常性项目：无资金执行记录，且项目周期在当前年度的经常性项目；<br />
            3、新增项目：无资金执行记录，且项目周期在当前年度的其它项目（非经常性项目）。
          </template>
        </el-tooltip>

      </right-toolbar>
      <el-drawer v-model="showSearch" title="查询">
        <el-form label-width="70px">
          <el-form-item label="项目名称">
            <el-input v-model="searchForm.name" placeholder="请输入项目名称" class="w-full" clearable></el-input>
          </el-form-item>
          <el-form-item label="项目性质">
            <el-select v-model="searchForm.natureCode" placeholder="请选择项目性质" class="w-full" clearable>
              <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目类别">
            <ProjectTypeSelect v-model="searchForm.typeId" placeholder="请选择项目类别" class="w-full" clearable>
            </ProjectTypeSelect>
          </el-form-item>
          <el-form-item label="项目用途">
            <el-select v-model="searchForm.purposeIds" multiple collapse-tags placeholder="请选择项目类别" class="w-full" clearable>
              <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目篮子">
            <el-select v-model="searchForm.basketCode" placeholder="请选择项目篮子" class="w-full" clearable>
              <el-option v-for="item in project_basket_type" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isSzhyq" :true-value="'1'" :false-value="''">涉及数字化园区</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isJxhgl" :true-value="'1'" :false-value="''">涉及精细化管理支出</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.isZfgm" :true-value="'1'" :false-value="''">涉及政府购买服务</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.libState" :true-value="'1'" :false-value="''">是否入库</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="searchForm.checkState" :true-value="'1'" :false-value="''">是否核定</el-checkbox>
          </el-form-item>
        </el-form>

        <template #footer>
          <div class="text-left">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="handleSearchReset">重置</el-button>
          </div>
        </template>
      </el-drawer>
    </div>
    <div style="table-outer">
      <el-table ref="tableRef" :data="dataList" fit row-key="id" border show-summary :expand-row-keys="[1, 2, 3, 4]"
        :height="tableHeight" :header-cell-style="{ 'text-align': 'center' }" :row-class-name="tableRowClassName"
        :summary-method="getSummaries" :indent="0" preserve-expanded-content>
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
        <el-table-column label="序号" align="center" width="60px" fixed="left">
          <template #default="{ row, $index }">
            <span>{{ row.index }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="项目名称" width="auto" min-width="300px" fixed="left">
          <template #default="{ row }">
            <ProjNameComponent :row="row"></ProjNameComponent>
          </template>

        </el-table-column>
        <el-table-column prop="assessOrgname" label="考核主体" align="center" width="100" v-if="columns[0].visible">
          <template #default="{ row }">
            <el-tooltip placement="top">
              <template #content>
                申报单位：{{ row.applyOrgname ?? '无' }}<br />
                配合单位：{{ row.cooperateOrgname ?? '无' }}<br />
                上报单位：{{ row.submitOrgname ?? '无' }}<br />
                考核主体：{{ row.assessOrgname ?? '无' }}
              </template>
              {{ row.assessOrgname }}
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="handler" label="经办人" align="center" width="100px" v-if="columns[1].visible">
          <template #default="{ row }">
            <span>{{ row.handler }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="estAmount" label="项目估算" align="right" width="100px" v-if="columns[2].visible">
          <template #default="{ row }">
            <span>{{ row.estAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="(columns[3].visible || columns[4].visible || columns[5].visible) ? '项目库' : ''">
          <el-table-column prop="basket_name" label="项目状态" align="center" width="100px" v-if="columns[3].visible">
            <template #default="{ row }">
              <BasketNameComponent :row="row"></BasketNameComponent>
            </template>
          </el-table-column>
          <el-table-column prop="libAmount" label="入库金额" align="right" width="100px" v-if="columns[4].visible">
            <template #default="{ row }">
              <LibAmountComponent :row="row"></LibAmountComponent>

            </template>
          </el-table-column>
          <el-table-column prop="checkAmount" label="核定金额" align="right" width="100px" v-if="columns[5].visible">
            <template #default="{ row }">
              <CheckAmountComponent :row="row"></CheckAmountComponent>

            </template>
          </el-table-column>
          <!-- <el-table-column prop="ht_amount" label="合同金额" align="right" width="auto" min-width="10%"
            v-if="columns[6].visible" /> -->
        </el-table-column>
        <el-table-column :label="(columns[7].visible || columns[8].visible || columns[9].visible) ? '已执行' : ''">
          <el-table-column prop="prevAmount" label="截至上年底" align="right" width="100px" v-if="columns[7].visible"
            :formatter="(row: any) => roundNumber2(row.prevAmount)?.toString()">
          </el-table-column>
          <el-table-column prop="currPayed" label="当年" align="right" width="100px" v-if="columns[8].visible"
            :formatter="(row: any) => roundNumber2(row.currPayed)?.toString()">
          </el-table-column>
          <el-table-column prop="payedTotal" label="合计" align="right" width="100px" v-if="columns[9].visible"
            :formatter="(row: any) => roundNumber2(row.payedTotal)?.toString()">
          </el-table-column>
        </el-table-column>
        <el-table-column
          :label="(columns[10].visible || columns[11].visible || columns[12].visible || columns[13].visible) ? '资金计划' : ''">
          <el-table-column prop="planSb" label="上报计划" align="right" width="100px" v-if="columns[10].visible">
            <template #default="{ row }">
              <PlanSbComponent v-show="columns[10].visible" :row="row"></PlanSbComponent>

            </template>
          </el-table-column>
          <el-table-column prop="planXd" label="下达计划" align="right" width="100px" v-if="columns[11].visible">
            <template #default="{ row }">
              <PlanXdComponent v-show="columns[11].visible" :row="row" :key="row.id"></PlanXdComponent>
            </template>

          </el-table-column>
          <el-table-column prop="planTzjh" label="调整计划" align="right" width="100px" v-if="columns[12].visible">
            <template #default="{ row }">
              <PlanTzjhComponent v-show="columns[12].visible" :row="row"></PlanTzjhComponent>
            </template>
          </el-table-column>
          <el-table-column prop="planTzxd" label="调整下达" align="right" width="100px" v-if="columns[13].visible">
            <template #default="{ row }">
              <PlanTzxdComponent v-show="columns[13].visible" :row="row"></PlanTzxdComponent>
            </template>
          </el-table-column>
        </el-table-column>
        <!-- <el-table-column prop="date" label="操作" width="auto" min-width="10%" /> -->
      </el-table>
    </div>

    <el-dialog title="项目查看" v-model="formViewShow" width="1200px" :close-on-click-modal="false" destroy-on-close>
      <FormView @close="formViewShow = false; search()"></FormView>
    </el-dialog>

  </div>
</template>

<script lang="tsx" setup name="ManageIndex">
import FormView from '../apply/components/formView.vue';
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { dayjs, ElDropdown, ElMessage, ElMessageBox, TableColumnCtx, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { addImportantProject, adjustPlan, cancelAdjust, cancelCheck, cancelImportantProject, cancelLib, cancelRelease, carryOver, doubleClickAdjust, doubleClickRelease, getList, releasePlan } from '@/api/lib/project/manage'
import { useFormStore } from '../apply/components/store/formStore'
// @ts-ignore
import useUserStore from '@/store/modules/user'
import { EProjectState, ELibState, ECheckState, EFundPlanState, EApplyAdjustState } from '@/utils/constants';
import { sumAmount, checkIsNumber, roundNumber2 } from '@/utils/common';
import { BigNumber } from 'bignumber.js';
import { useMouse } from '@vueuse/core';
import { StarFilled, Star, Promotion } from '@element-plus/icons-vue'
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import { useConfig } from '@/utils/config';

const {
  ["sys.click.releasePlan"]: clickReleasePlan
} = useConfig('sys.click.releasePlan')


const ProjNameComponent = defineComponent({
  name: 'projNameComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {

    return () => {
      if (props.row.projType) {
        if (props.row.projType == 1) {
          return (<div class="inline-flex items-center">
            <span>一、结转项目</span>
            <span>({props.row.children.length})</span>
          </div>)
        }
        if (props.row.projType == 2) {
          return (<div class="inline-flex items-center">
            <span>二、经常性项目</span>
            <span>({props.row.children.length})</span>
          </div>)
        }
        if (props.row.projType == 3) {
          return (<div class="inline-flex items-center">
            <span>三、新增项目</span>
            <span>({props.row.children.length})</span>
          </div>)
        }
      } else {
        return (<div class="grid grid-cols-[1fr_30px] items-center">
          <div>
            <el-link type="primary" onClick={() => handleFormViewShow(props.row)}>{props.row.name}</el-link>
            <div>编号：{props.row.projectSn?.formalSn ?? props.row.projectSn?.tempSn}</div>
          </div>
          {
            props.row.currentYearRelation?.impProj == '1' ?
              <i class="iconfont icon-star-fill text-6 text-orange"></i> :
              <i class="iconfont icon-star text-6 "></i>

          }

        </div >)
      }
    }
  }
})

const BasketNameComponent = defineComponent({
  name: 'basketNameComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      return (<div class="flex items-center justify-center cursor-pointer">
        {
          !props.row.projType ?
            <div>
              {props.row.basketCode ?
                <div>
                  <dict-tag options={project_basket_type.value} value={props.row.basketCode} />
                </div>
                : (props.row.state == EProjectState.reported ? <div>已上报</div> : '')
              }
            </div> : ""
        }
      </div>)
    }
  }
})

const LibAmountComponent = defineComponent({
  name: 'libAmountComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      return (<div class="flex items-center justify-right cursor-pointer">
        {props.row.libState == ELibState.yrk ? <div class=" absolute left-2 w-2 h-2 bg-green rounded-full"></div> : ''}
        {!props.row.projType ? <div>
          {
            checkIsNumber(props.row.libAmount) ?
              <el-link type="primary">{props.row.libAmount}</el-link> :
              <div class="h-10 flex-1"></div>
          }
        </div> : <span>{props.row.libAmount}</span>}
      </div>)
    }
  }
})

const CheckAmountComponent = defineComponent({
  name: 'CheckAmountComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(prop) {
    return () => {
      if (!prop.row.projType) {
        return (<div class="flex items-center justify-right cursor-pointer">

          <div>
            {
              checkIsNumber(prop.row.checkAmount) ?
                <el-link type="primary">{prop.row.checkAmount}</el-link> :
                <div class="h-10 flex-1"></div>
            }
          </div>
        </div>)
      } else {
        return (<span>{roundNumber2(prop.row.checkAmount)}</span>)
      }
    }
  }
})

const PlanSbComponent = defineComponent({
  name: 'planSbComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (!props.row.projType) {
        if (!props.row?.fundPlan || [EFundPlanState.reject, EFundPlanState.draft].includes(props.row?.fundPlan?.planState)) {
          return (
            <el-link type="danger">未上报</el-link>
          )
        } else {
          return (<span>{props.row.fundPlan?.declareAmount}</span>)
        }
      } else {
        return (<span>{roundNumber2(props.row.planSb)}</span>)
      }
    }
  }
})

/** 调整计划 */
const PlanTzjhComponent = defineComponent({
  name: 'PlanTzjhComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    return () => {
      if (props.row.projType) {
        return (<div>
          <span>{roundNumber2(props.row.planTzjh)}</span>
        </div>)
      }
      if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(props.row.fundPlan?.applyState) || props.row.fundPlan?.applyState == null) {
        return (<span class="text-[var(--el-color-info-light-3)]">{props.row.fundPlan?.formalAmount}</span>)
      }
      if (EApplyAdjustState.qrtz == props.row.fundPlan?.applyState) {
        return (<span>{props.row.fundPlan?.applyAdjust ?? props.row.fundPlan?.formalAmount}</span>)
      }
      return (<span>{props.row.fundPlan?.applyAdjust}</span>)
    }
  }
})

/** 下达 */
const PlanXdComponent = defineComponent({
  name: 'PlanXdComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    const amount = ref<number>(props.row.plan_formalAmount)
    return () => {

      if (!props.row.projType) {
        if (props.row?.fundPlan && ![EFundPlanState.reject, EFundPlanState.draft].includes(props.row?.fundPlan?.planState)) {
          if (props.row?.fundPlan?.planState == EFundPlanState.yfb) {
            return (<el-link type="primary" class="leading-5">{props.row.fundPlan?.formalAmount}</el-link>)
          } else {
            return (<div class="flex items-center rounded-r-sm">
              <el-input-number modelValue={amount.value} min={0} controls={false} class="w-full" />
            </div>)
          }
        }
      }
      return (<div>
        <span>{roundNumber2(props.row.planXd)}</span><br />
        <span class="text-red">{roundNumber2(props.row.planXd_red)}</span>
      </div>)
    }



  }
})

/** 调整下达 */
const PlanTzxdComponent = defineComponent({
  name: 'PlanTzxdComponent',
  props: {
    row: {
      type: Object as () => any,
      required: true
    }
  },
  setup(props) {
    const amount = ref<number>(props.row.plan_adjustAmount)
    return () => {
      if (!props.row.projType) {
        if ([EApplyAdjustState.draft, EApplyAdjustState.reject, EApplyAdjustState.qrtz].includes(props.row.fundPlan?.applyState ?? 0)) {
          return (<div class="flex items-center justify-right cursor-pointer">
            <div>
              {
                EApplyAdjustState.qrtz == props.row.fundPlan?.applyState ?
                  <span class="text-3.5">
                    {props.row.fundPlan?.adjustAmount}
                  </span> :
                  <span class="text-[var(--el-color-info-light-3)]">{props.row.fundPlan?.formalAmount}</span>
              }
            </div>
          </div>)
        } else {
          if (props.row.fundPlan?.applyState === EApplyAdjustState.ysb) {
            return (<div class="flex items-center rounded-r-sm">
              <el-input-number modelValue={amount.value} min={0} controls={false} class="w-full"
              />
            </div>)
          }
        }
      } else {
        return (<div >
          <span>{roundNumber2(props.row.planTzxd)}</span><br />
          <span class="text-red">{roundNumber2(props.row.planTzxd_red)}</span>
        </div>)
      }
    }
  }
})



const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_basket_type');


const showSearch = ref(false)
const userStore = useUserStore()
const formStore = useFormStore()

type rowType = IProjectInfoVo & { index: number; assessOrgnameTitle: string, plan_formalAmount?: number | null; plan_adjustAmount?: number, payedTotal?: number | null }
const dataList = ref([
  {
    id: 1,
    /** 结转项目 */
    projType: 1,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    prevAmount: 0,
    currPayed: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planXd_red: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0
  },
  {
    id: 2,
    /** 经常性项目 */
    projType: 2,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    prevAmount: 0,
    currPayed: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planXd_red: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0
  },
  {
    id: 3,
    /** 新增项目 */
    projType: 3,
    children: [] as rowType[],
    estAmount: 0,
    basketCode: 0,
    basketName: '',
    libAmount: 0,
    checkAmount: 0,
    htAmount: 0,
    prevAmount: 0,
    currPayed: 0,
    payedTotal: 0,
    planSb: 0,
    planXd: 0,
    planXd_red: 0,
    planTzjh: 0,
    planTzxd: 0,
    planTzxd_red: 0
  },
])
const searchForm = reactive({
  year: dayjs().isAfter(dayjs().year() + '-08-31', 'day') ? dayjs().year() + 1 : dayjs().year(),
  projectUnits: userStore.deptId,
  name: '',
  natureCode: '',
  basketCode: '',
  typeId: '',
  purposeIds: [],
  isSzhyq: '',
  isJxhgl: '',
  isZfgm: '',
  libState: '',
  checkState: '',
  assessOrgid: '',
  hasEarlyAmount: '',
  hasAdjustAmount: ''
})
const loading = ref(false)
const tableRef = ref<InstanceType<typeof ElTable>>()
const search = () => {
  loading.value = true
  getList(searchForm).then(async res => {
    dataList.value.at(0)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 1) as any
    dataList.value.at(1)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 2) as any
    dataList.value.at(2)!.children = res.data?.filter(t => t.currentYearRelation?.projType == 3) as any

    // 对数据进行合计
    dataList.value.forEach(row => {
      row.children.forEach((t: rowType, index: number) => {
        t.index = index + 1
        t.payedTotal = sumAmount([t.currPayed, t.prevAmount])

        // 如果资金计划是已上报状态，取值方式：先根据配置初步还是正式决定取哪个值
        if (t.fundPlan?.planState == EFundPlanState.ysb) {
          if (clickReleasePlan.value == '0') {
            if (checkIsNumber(t.usagePlanItem?.currEarly)) {
              t.plan_formalAmount = t.usagePlanItem?.currEarly
            }
          }
          if (clickReleasePlan.value == '1') {
            if (checkIsNumber(t.usagePlanItem?.currFormal)) {
              t.plan_formalAmount = t.usagePlanItem?.currFormal
            }
          }
        }
        if (t.fundPlan?.applyState === EApplyAdjustState.ysb) {
          if (checkIsNumber(t.usagePlanItem?.currAdjust)) {
            t.plan_adjustAmount = t.usagePlanItem?.currAdjust
          } else {
            t.plan_adjustAmount = t.fundPlan?.applyAdjust
          }

        }
      })
      row.estAmount = sumAmount(row.children.map(t => t.estAmount)) ?? 0
      row.libAmount = sumAmount(row.children.map(t => t.libAmount)) ?? 0
      row.checkAmount = sumAmount(row.children.map(t => t.checkAmount)) ?? 0
      row.prevAmount = sumAmount(row.children.map(t => t.prevAmount)) ?? 0
      row.currPayed = sumAmount(row.children.map(t => t.currPayed)) ?? 0
      row.payedTotal = sumAmount(row.children.map(t => t.payedTotal)) ?? 0

      row.planSb = sumAmount(
        row.children.map(t => {
          if ([EFundPlanState.ysb, EFundPlanState.yfb].includes(t.fundPlan?.planState)) {
            return t.fundPlan?.declareAmount
          }
          return 0
        })
      ) ?? 0
      row.planXd = sumAmount(
        row.children.map(t => t.fundPlan?.formalAmount)
      ) ?? 0
      row.planXd_red = sumAmount([
        ...row.children.map(t => t.fundPlan?.formalAmount ?? t.plan_formalAmount),
      ]) ?? 0
      row.planTzjh = sumAmount(
        row.children.map(t => {
          if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(t.fundPlan?.applyState) || t.fundPlan?.applyState == null) {
            return t.fundPlan?.formalAmount
          }
          if (EApplyAdjustState.qrtz == t.fundPlan?.applyState) {
            return t.fundPlan?.applyAdjust ?? t.fundPlan?.formalAmount
          }
          return t.fundPlan?.applyAdjust
        })
      ) ?? 0
      row.planTzxd = sumAmount(
        row.children.map(t => {
          if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(t.fundPlan?.applyState) || t.fundPlan?.applyState == null) {
            return t.fundPlan?.formalAmount
          }
          if (t.fundPlan?.applyState == EApplyAdjustState.qrtz) {
            return t.fundPlan?.adjustAmount
          }
          return 0
        })
      ) ?? 0
      row.planTzxd_red = sumAmount([
        ...row.children.map(t => t.plan_adjustAmount),
        row.planTzxd
      ]) ?? 0
    })
  }).finally(() => {
    loading.value = false
    // window.tableRef = tableRef.value
  })
}
search()


watch(() => [
  searchForm.year,
  searchForm.assessOrgid,
  searchForm.hasEarlyAmount,
  searchForm.hasAdjustAmount], () => {
    search()
  })


const formViewShow = ref(false)
const handleFormViewShow = (row: IProjectInfoVo) => {
  formViewShow.value = true
  formStore.formView.id = row.id

  formStore.projectYear = searchForm.year
}

// 项目结转归类
const handleXmjzgl = () => {
  ElMessageBox.confirm(`是否确认对${searchForm.year}年项目进行结转/归类？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里可以添加项目结转/归类的逻辑
    carryOver(searchForm.year).then(res => {
      ElMessage.success('结转/归类成功')
    })
  })
}

const handleSearchReset = () => {
  searchForm.name = ''
  searchForm.natureCode = ''
  searchForm.typeId = ''
  searchForm.purposeIds = []
  searchForm.basketCode = ''
  searchForm.isSzhyq = ''
  searchForm.isJxhgl = ''
  searchForm.isZfgm = ''
  searchForm.libState = ''
  searchForm.checkState = ''
}

//
const num = ref(2025)
const tableHeight = ref(window.innerHeight - 180)

// 列显隐信息
const columns = ref([
  { key: 0, label: `考核主体`, visible: true },
  { key: 1, label: `经办人`, visible: true },
  { key: 2, label: `项目估算`, visible: true },
  { key: 3, label: `项目状态`, visible: true },
  { key: 4, label: `入库金额`, visible: true },
  { key: 5, label: `核定金额`, visible: true },
  { key: 6, label: `合同金额`, visible: true },
  { key: 7, label: `截至上年底`, visible: true },
  { key: 8, label: `当年`, visible: true },
  { key: 9, label: `合计`, visible: true },
  { key: 10, label: `上报计划`, visible: true },
  { key: 11, label: `下达计划`, visible: true },
  { key: 12, label: `调整计划`, visible: true },
  { key: 13, label: `调整下达`, visible: true }
]);

var groupCount = 0


const tableRowClassName = ({
  row,
  rowIndex
}: {
  row: any,
  rowIndex: number
}) => {
  if (row.projType) {
    return 'warning-row'
  }
  return ''
}

// 设置底部合计行样式
onMounted(() => {
  setTimeout(() => {
    const tfoot = proxy.$el.querySelector('.el-table__footer tfoot')
    tfoot.firstChild.children[0].colSpan = 2
    tfoot.firstChild.children[1].style.display = 'none'
  }, 0);
})
interface SummaryMethodProps<T = typeof dataList.value[number] & { children?: IProjectInfoVo[] }> {
  columns: TableColumnCtx[]
  data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
  const { columns: tableColumns, data } = param
  const sums: (string | VNode | number | null)[] = []
  tableColumns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = (
        <div class="text-left">
          {`合计（${data.flatMap(t => t.children).length}个）`}
        </div>
      )
    }
    if (column.property == 'estAmount') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[2].visible }}>{roundNumber2(sumAmount(data.map(t => t.estAmount)) ?? 0)}</span>
      )
    }
    if (column.property == 'libAmount') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[4].visible }}>{roundNumber2(sumAmount(data.map(t => t.libAmount)) ?? 0)}</span>
      )
    }
    if (column.property == 'checkAmount') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[5].visible }}>{roundNumber2(sumAmount(data.map(t => t.checkAmount)) ?? 0)}</span>
      )
    }
    if (column.property == 'prevAmount') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[7].visible }}>{roundNumber2(sumAmount(data.map(t => t.prevAmount)) ?? 0)}</span>
      )
    }
    if (column.property == 'currPayed') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[8].visible }}>{roundNumber2(sumAmount(data.map(t => t.currPayed)) ?? 0)}</span>
      )
    }
    if (column.property == 'payedTotal') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[9].visible }}>{roundNumber2(sumAmount(data.map(t => t.payedTotal)) ?? 0)}</span>
      )
    }
    if (column.property == 'planSb') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[10].visible }}>{roundNumber2(sumAmount(data.map(t => t.planSb)) ?? 0)}</span>
      )
    }
    if (column.property == 'planXd') {
      sums[index] = (
        <div class={{ 'hidden': !columns.value[11].visible }}>
          <span>{roundNumber2(sumAmount(data.map(t => t.planXd)) ?? 0)}</span><br />
          <span class="text-red">{roundNumber2(sumAmount(data.map(t => t.planXd_red)) ?? 0)}</span>
        </div>
      )
    }
    if (column.property == 'planTzjh') {
      sums[index] = (
        <span class={{ 'hidden': !columns.value[12].visible }}>{roundNumber2(sumAmount(data.map(t => t.planTzjh)) ?? 0)}</span>
      )
    }
    if (column.property == 'planTzxd') {
      sums[index] = (
        <div class={{ 'hidden': !columns.value[13].visible }}>
          <span>{roundNumber2(sumAmount(data.map(t => t.planTzxd)) ?? 0)}</span><br />
          <span class="text-red">{roundNumber2(sumAmount(data.map(t => t.planTzxd_red)) ?? 0)}</span>
        </div>
      )
    }

  })
  return sums as any
}


const handleDownloadExcel = () => {
  proxy.download('/project/manage/download', {
    ...searchForm
  }, `项目(计划)管理${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>

<style scoped>
:deep(.el-table__placeholder) {
  display: none;
}

.el-input-group__append {
  padding: 0 8px;
}
:deep(.el-table .warning-row) {
  /*--el-table-tr-bg-color: var(--el-color-warning-light-9);*/
  background-image: linear-gradient(to bottom, #ffffff, #f2f2f2) !important;
  font-weight: 500;
  color: #444;
}
</style>