<template>
    <div>
        <el-form label-width="auto" class="flex gap-3 flex-wrap">
            <el-form-item>
                <el-input v-model="searchForm.name" placeholder="请输入项目名称" clearable class="w-50"></el-input>
            </el-form-item>

            <el-form-item>
                <ProjectTypeSelect v-model="searchForm.typeId" placeholder="请选择项目类别" class="!w-40" clearable>
                </ProjectTypeSelect>
            </el-form-item>
            <el-form-item>
                <el-select v-model="searchForm.purposeIds" multiple collapse-tags placeholder="请选择项目用途" clearable class="w-40">
                    <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-select v-model="searchForm.state" placeholder="请选择状态" clearable class="w-40">
                    <el-option v-for="item in stateOptions" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <DateRangePicker v-model:begin-date="searchForm.beginYear" v-model:end-date="searchForm.endYear"
                    type="yearrange" start-placeholder="开始年度" end-placeholder="结束年度" range-separator="至" format="YYYY"
                    value-format="YYYY" class="!w-50" clearable>
                </DateRangePicker>
            </el-form-item>
            <el-form-item>
                <el-checkbox v-model="searchForm.isSzhyq" :true-value="'1'" :false-value="''">涉及数字化园区</el-checkbox>
            </el-form-item>
            <el-form-item>
                <el-checkbox v-model="searchForm.isJxhgl" :true-value="'1'" :false-value="''">涉及精细化管理支出</el-checkbox>
            </el-form-item>
            <el-form-item>
                <el-checkbox v-model="searchForm.isZfgm" :true-value="'1'" :false-value="''">涉及政府购买服务</el-checkbox>
            </el-form-item>
        </el-form>
    </div>
    <div>
        <el-table :data="dataList" border v-loading="loading">
            <el-table-column type="index" label="序号" align="center" width="60px">
                <template #default="{ row, $index }">
                    <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="name" label="项目名称" header-align="center" align="left"></el-table-column>
            <el-table-column prop="applyOrgname" label="申报单位" align="center" width="100px"></el-table-column>
            <el-table-column label="项目周期" align="center" width="150px">
                <template #default="{ row }">
                    起:{{ row.beginDate }} <br />
                    止:{{ row.endDate }}
                </template>
            </el-table-column>
            <el-table-column prop="state" label="状态" align="center" width="100px">
                <template #default="{ row }">
                    {{stateOptions.find(item => item.value == row.state)?.label}}
                </template>
            </el-table-column>
            <el-table-column label="项目类型" align="center" width="180px">
                <template #default="{ row }">
                    <ProjectTypeSelect v-model="row.typeId" placeholder="请选择项目类别" class="w-full"
                        @change="(val: string) => handleChange({ ...row, typeId: val, typeName: project_type.find((t: any) => t.value == val)?.label })">
                    </ProjectTypeSelect>
                </template>
            </el-table-column>
            <el-table-column label="项目用途" align="center" width="180px">
                <template #default="{ row }">
                    <el-select v-model="row.purposeIds" multiple placeholder="请选择项目用途"
                        @change="handleChange({ ...row, purpose: formatPurposeData(row) })">
                        <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column label="涉及数字化园区支出" align="center" width="150px">
                <template #default="{ row }">
                    <el-checkbox v-model="row.isSzhyq" :true-value="'1'" :false-value="'0'"
                        @change="handleChange({ ...row, isSzhyq: row.isSzhyq })"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column label="涉及精细化管理支出" align="center" width="150px">
                <template #default="{ row }">
                    <el-checkbox v-model="row.isJxhgl" :true-value="'1'" :false-value="'0'"
                        @change="handleChange({ ...row, isJxhgl: row.isJxhgl })"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column label="涉及政府购买服务" align="center" width="150px">
                <template #default="{ row }">
                    <el-checkbox v-model="row.isZfgm" :true-value="'1'" :false-value="'0'"
                        @change="handleChange({ ...row, isZfgm: row.isZfgm })"></el-checkbox>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" />
            </template>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="search" />
    </div>
</template>

<script setup lang="ts">
import { editSetting, geSettingList } from '@/api/lib/project/setting';
import { dayjs, ElMessage } from 'element-plus';
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_type } = proxy.useDict("project_purpose", 'project_type');

const stateOptions = [
    {
        label: '退回申报人',
        value: -1
    },
    {
        label: '草稿',
        value: 0
    },
    {
        label: '已上报',
        value: 1
    },
]

const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const searchForm = reactive<ProjectSetQuery>({})
const loading = ref(false)
const total = ref(0)
const dataList = ref<ProjectSet[]>([])
const search = () => {
    loading.value = true
    geSettingList(searchForm, page).then(res => {
        dataList.value = res.rows?.map(item => {
            return {
                ...item,
                purposeIds: item.purpose ? JSON.parse(item.purpose).map((item: any) => item.value) : [],
            }
        }) ?? []
        total.value = res.total ?? 0
    }).finally(() => {
        loading.value = false
    })
}
watch(searchForm, () => {
    search()
}, { immediate: true })

const formatPurposeData = (row: any) => {
    if (row.purposeIds && row.purposeIds.length > 0) {
        const datas = row.purposeIds.map((item: string) => {
            return {
                value: item,
                label: project_purpose.value.find((item: any) => item.value == item)?.label,
            }
        })
        return JSON.stringify(datas)
    }
    return ''
}


const handleChange = (data: any) => {
    editSetting(data).then(() => {
        ElMessage.success('修改成功')
        search()
    })
}
</script>