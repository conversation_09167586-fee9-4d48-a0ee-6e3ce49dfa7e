<template>
    <div class="app-container">
        <div>
            <el-form :inline="true" :model="queryForm" class="mb-4" label-width="120px">
                <el-form-item label="项目名称">
                    <el-input v-model="queryForm.projectName" placeholder="请输入项目名称" class="w-50" />
                </el-form-item>
                <el-form-item label="申报单位">
                    <el-select v-model="queryForm.applyOrg" placeholder="--全部--" class="w-50">
                        <el-option label="全部" value="" />
                    </el-select>
                </el-form-item>
                <el-form-item label="配合单位">
                    <el-input v-model="queryForm.partnerUnit" placeholder="输入单位名" class="w-50" />
                </el-form-item>

                <el-form-item label="项目性质">
                    <el-select v-model="queryForm.natureCode" placeholder="请选择项目性质" class="w-50">
                        <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目类别">
                    <ProjectTypeSelect v-model="queryForm.typeId" placeholder="请选择项目类别" class="w-50" clearable>
                    </ProjectTypeSelect>
                </el-form-item>
                <el-form-item label="项目用途">
                    <el-select v-model="queryForm.purposeIds" multiple collapse-tags placeholder="请选择项目用途" class="w-50">
                        <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目状态">
                    <el-select v-model="queryForm.status" placeholder="--全部--" class="w-50">
                        <el-option label="全部" value="" />
                        <el-option label="待操作" value="待操作" />
                        <el-option label="已入库" value="已入库" />
                    </el-select>
                </el-form-item>
                <el-form-item label="执行年度">
                    <el-date-picker v-model="queryForm.year" type="year" value-format="YYYY" class="!w-25" />
                    <el-date-picker v-model="queryForm.year" type="year" value-format="YYYY" class="!w-25" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search">查询</el-button>
                    <el-button type="success" @click="handleExport">导出</el-button>
                </el-form-item>
            </el-form>
        </div>

        <el-table :data="dataList" border v-loading="loading" show-summary :summary-method="getSummaries">
            <el-table-column prop="index" label="序号" width="60" align="center">
                <template #default="{ row, $index }">
                    <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                </template>
            </el-table-column>
            <el-table-column label="项目名称" align="center">
                <template #default="{ row }">
                    <div>
                        <div>{{ row.name }}</div>
                        <div class="text-xs text-gray-500">编号：{{ row.code }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column prop="unit" label="申报单位" width="180" align="center" />
            <el-table-column prop="amount" label="核定/合同金额" width="120" align="center" />
            <el-table-column prop="depositAmount" label="年度" width="120" align="center" />
            <el-table-column prop="finalAmount" label="当年计划" width="120" align="center" />
            <el-table-column prop="period" label="计划调整" width="120" align="center" />
            <el-table-column prop="period" label="当年执行" width="120" align="center" />
            <el-table-column prop="period" label="截止上年已执行" width="150" align="center" />
            <el-table-column prop="status" label="状态" width="120" align="center" />
            <el-table-column prop="status" label="分送方向" width="200" align="center" />
            <el-table-column label="操作" width="160" align="center">
                <template #default>
                    <el-button type="primary" link icon="view">查看</el-button>
                    <el-button type="primary" link icon="guide">同意</el-button>
                    <el-button type="primary" link icon="guide">不同意</el-button>
                </template>
            </el-table-column>
            <template #empty>
                <el-empty description="暂无数据" />
            </template>
        </el-table>
        <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
            @pagination="search" />
    </div>

</template>

<script lang="ts" setup>
import { TableColumnCtx } from 'element-plus';


const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');


const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const queryForm = reactive({
    projectName: '',
    projectType: '',
    basketCode: '',
    natureCode: '',
    typeId: '',
    expenseType: '',
    purposeIds: '',
    status: '',
    year: ''
})
const loading = ref(false)
const total = ref(0)
const dataList = ref([{}])
const search = () => {

}

const handleExport = () => {

}

interface SummaryMethodProps<T = typeof dataList.value[number]> {
    columns: TableColumnCtx<T>[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {

        if (index == 3) {
            sums[index] = 100
        }
        if (index == 5) {
            sums[index] = 100
        }
        if (index == 6) {
            sums[index] = 100
        }
        if (index == 7) {
            sums[index] = 100
        }
        if (index == 8) {
            sums[index] = 100
        }
    })
    return sums
}
</script>