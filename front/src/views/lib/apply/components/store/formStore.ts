import { dayjs } from "element-plus"

export const useFormStore = defineStore('form', {
  state: () => {
    return {
      form: {} as IProjectInfoVo,
      formView: {} as IProjectInfoVo,
      /** 项目年度 */
      projectYear: dayjs().year(),
      /** 是否为信息补正 */
      isXxbz: false,
    }
  },
//   getters: {
//     getForm: (state) => state.form
//   },
  actions: {
    setForm(form: IProjectInfoVo) {
      this.form = form
    },
  }
})