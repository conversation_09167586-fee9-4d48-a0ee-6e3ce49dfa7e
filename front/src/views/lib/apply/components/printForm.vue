<template>
    <div ref="printRef" style="width:900px; margin:0 auto;">
        <h2>上海化工区专项发展资金项目申请表</h2>
        <table class="table-outer" cellpadding="0" cellspacing="0">
            <tr>
                <td class="no-border align-left" colspan="2">申请单位名称(盖章)：{{ formData.applyOrgname }}</td>
                <td class="no-border align-right" colspan="2">{{ dateFormat(formData.applyTime, 'YYYY年MM月DD日')
                    }}</td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">一、项目基本信息</td>
            </tr>
            <tr>
                <td>项目名称</td>
                <td class="align-left" colspan="3">{{ formData.name }}</td>
            </tr>
            <tr>
                <td>使用单位</td>
                <td class="align-left" colspan="3">{{ formData.useOrgname }}</td>
            </tr>
            <tr>
                <td>项目类型</td>
                <td class="align-left">{{ formData.typeName?.replaceAll('-', '')?.trim() ?? '' }}</td>
                <td>涉及政府购买服务</td>
                <td class="align-left">{{ formData.isZfgm == '1' ? '是' : '否' }}</td>
            </tr>
            <tr>
                <td width="15%">项目用途</td>
                <td width="35%" class="align-left">{{ getPurpose }}</td>
                <td width="15%">项目性质</td>
                <td width="35%" class="align-left">{{ formData.natureName }}</td>
            </tr>
            <tr>
                <td>项目负责人</td>
                <td class="align-left">{{ formData.leader }}</td>
                <td>手机</td>
                <td class="align-left">{{ formData.leaderTel }}</td>
            </tr>
            <tr>
                <td>经办人</td>
                <td class="align-left">{{ formData.handler }}</td>
                <td>手机</td>
                <td class="align-left">{{ formData.handlerTel }}</td>
            </tr>
            <tr>
                <td>项目金额估算</td>
                <td class="align-left">{{ formData.estAmount }} 万元</td>
                <td>执行期限</td>
                <td class="align-left">{{ getDateRange.minDate }}—{{ getDateRange.maxDate }}</td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">二、项目概述</td>
            </tr>
            <tr>
                <td class="no-border align-left" colspan="4">1、必要性</td>
            </tr>
            <tr style="height: 75px;">
                <td colspan="4" class="align-left">
                    {{ formData.necessity }}
                </td>
            </tr>
            <tr>
                <td class="no-border align-left" colspan="4">2、项目依据</td>
            </tr>
            <tr style="height: 75px;">
                <td colspan="4" class="align-left">
                    {{ formData.basis }}
                </td>
            </tr>
            <tr>
                <td class="no-border align-left" colspan="4">3、主要内容</td>
            </tr>
            <tr style="height: 75px;">
                <td colspan="4" class="align-left">
                    {{ formData.mainCnt }}
                </td>
            </tr>
            <tr>
                <td class="no-border align-left" colspan="4">4、项目实施计划 (投资进度)</td>
            </tr>
            <tr style="height: 75px;">
                <td colspan="4" class="align-left">
                    {{ formData.actionPlan }}
                </td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">三、绩效目标</td>
            </tr>
            <tr>
                <td class="no-border" colspan="4">
                    <table class="table-inner" cellpadding="0" cellspacing="0">
                        <tr>
                            <td>项目总目标</td>
                            <td class="align-left" colspan="3">
                                {{ formData.goal }}
                            </td>
                        </tr>
                        <tr>
                            <td width="20%" rowspan="2">一级指标</td>
                            <td colspan="3">分解目标</td>
                        </tr>
                        <tr>
                            <td width="20%">二级指标</td>
                            <td width="30%">三级指标</td>
                            <td width="30%">指标目标值</td>
                        </tr>
                        <template v-if="formData.performance && formData.performance.length > 0">
                            <tr v-for="item in formData.performance" :key="item.id">
                                <td>{{ item.indexLevel1 }}</td>
                                <td>{{ item.indexLevel2 }}</td>
                                <td>{{ item.indexLevel3 }}</td>
                                <td>{{ item.indexTarget }}</td>
                            </tr>
                        </template>
                        <tr v-else>
                            <td colspan="4" class="align-center">没有绩效目标</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">四、附件</td>
            </tr>
            <tr>
                <td colspan="4" class="no-border">
                    <table class="table-inner" cellpadding="0" cellspacing="0">
                        <tr>
                            <td width="30%">文件类型</td>
                            <td width="70%">文件名称</td>
                        </tr>
                        <template
                            v-if="formData.sysAttachments && formData.sysAttachments.length > 0">
                            <tr v-for="item in formData.sysAttachments">
                                <td>{{ item.primaryType }}</td>
                                <td>{{ item.fileName }}</td>
                            </tr>
                        </template>
                        <tr v-else>
                            <td colspan="2" class="align-center">没有附件</td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">五、项目单位意见（盖章）</td>
            </tr>
            <tr>
                <td colspan="4"><br /><br /><br /></td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">六、上级主管单位（部门）意见（盖章）</td>
            </tr>
            <tr>
                <td colspan="4"><br /><br /><br /></td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">七、公共事务中心预审意见（盖章）（涉及国资更新和维修项目）</td>
            </tr>
            <tr>
                <td colspan="4"><br /><br /><br /></td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">八、管委会相关业务处室初核（预审）意见（盖章）</td>
            </tr>
            <tr>
                <td colspan="4"><br /><br /><br /></td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">九、专题评审会意见</td>
            </tr>
            <tr>
                <td class="align-left" colspan="4">1、评审意见</td>
            </tr>
            <tr>
                <td colspan="4"><br /><br /><br /></td>
            </tr>
            <tr>
                <td class="align-left" colspan="4">2、评审结论</td>
            </tr>
            <tr>
                <td colspan="4"><br /><br /><br /></td>
            </tr>
            <tr>
                <td class="no-border align-left td-title" colspan="4">十、备注</td>
            </tr>
            <tr>
                <td colspan="4"><br /><br /><br /></td>
            </tr>
        </table>
    </div>
</template>

<script lang="tsx" setup>
import { dateFormat, getMinMaxDate, jsonStrParseToJson } from '@/utils/common';
import { useFormStore } from './store/formStore';

const props = defineProps<{
    formData: any
}>()

const getPurpose = computed(() => {
    if (props.formData.purpose) {
        return jsonStrParseToJson<INameValue[]>(props.formData.purpose)?.map((item: any) => item.name).join('，')
    }
    return ''
})
const getDateRange = computed(() => {
    return getMinMaxDate([
        props.formData.buildBegin,
        props.formData.buildEnd,
        props.formData.fundBegin,
        props.formData.fundEnd,
    ])
})

const printRef = ref<HTMLDivElement>()

defineExpose({
    print() {
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        document.body.appendChild(iframe);
        const iframeDoc = iframe.contentDocument || iframe.contentWindow!.document;
        const newStyleTag = iframeDoc.createElement('style');
        newStyleTag.type = 'text/css';
        newStyleTag.appendChild(document.createTextNode(
            `
        body{ margin:0; padding:0; font-family:微软雅黑; font-size:14px;}

        .table-outer{ border:0; width:100%; border-collapse:collapse;}
        .table-outer td{ border:1px solid #000; padding:7px 5px; line-height:20px; min-height:20px;}        

        .table-inner{ border:0; width: 100%; border-collapse:collapse; }
        .table-inner td{ border:1px solid #000; padding:5px; line-height:20px; }

        table td{ text-align:center;}
        .align-right{ text-align:right;}
        .align-left{ text-align:left;}
        .align-center{ text-align:center;}

        h2{ font-family:黑体; font-size: 24px; line-height:48px; font-weight:bold; text-align:center;}
        .td-title{ font-weight:bold; font-size:16px; padding:15px 0!important;}        
        .no-border{ border:0!important; padding-left:0!important; padding-right:0!important; }
        `
        ))
        iframeDoc.head.appendChild(newStyleTag)

        iframeDoc!.body.innerHTML = printRef.value?.outerHTML ?? '';
        iframe.contentWindow?.print();
        document.body.removeChild(iframe);
    }
})
</script>

<style type="text/css" scoped>
body {
    margin: 0;
    padding: 0;
    font-family: 微软雅黑;
    font-size: 14px;
}

.table-outer {
    border: 0;
    width: 100%;
    border-collapse: collapse;
}

.table-outer td {
    border: 1px solid #000;
    padding: 7px 5px;
    line-height: 20px;
    min-height: 20px;
}

.table-inner {
    border: 0;
    width: 100%;
    border-collapse: collapse;
}

.table-inner td {
    border: 1px solid #000;
    padding: 5px;
    line-height: 20px;
}

table td {
    text-align: center;
}

.align-right {
    text-align: right;
}

.align-left {
    text-align: left;
}

.align-center {
    text-align: center;
}

h2 {
    font-family: 黑体;
    font-size: 24px;
    line-height: 48px;
    font-weight: bold;
    text-align: center;
}

.td-title {
    font-weight: bold;
    font-size: 16px;
    padding: 15px 0 !important;
}

.no-border {
    border: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}
</style>