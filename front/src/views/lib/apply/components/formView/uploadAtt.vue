<template>
    <div class="relative">
        <div class="flex flex-col gap-3">
            <div class="h-[620px]">
                <div class="mb-2">
                    <span class="text-#303133 text-4 font-bold">附件列表</span>
                </div>
                <FileTable :source-id="formStore.formView.id!" :billFileList="billFileList" :hidden-columns="['del']"
                    height="600px" :column-option="{ primaryType: { width: '200px' }, fileName: { width: 'auto', align: 'left' } }">
                </FileTable>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
// 上传附件
import { dateFormat } from '@/utils/common';
import { useFormStore } from '../store/formStore';
import FileTable from '@/components/Table/FileTable.vue';
import { getBillAttachmentsByProjId } from '@/api/fund/paybill';
import DownloadA from '@/components/FileUpload/DownloadA.vue';
const formStore = useFormStore()

const billFileList = ref<(ISysAttachment & { serialNumber: string, })[]>([])
getBillAttachmentsByProjId(formStore.formView.id!).then(res => {
    billFileList.value = res.data ?? []
})
</script>