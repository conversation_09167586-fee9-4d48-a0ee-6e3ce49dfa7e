<template>
    <div class="flex flex-col gap-3">
        <el-descriptions :column="2" label-width="150px" border>
            <el-descriptions-item title="项目总目标" >
                <template #label>
                    项目总目标
                </template>
                <span>{{ formStore.formView.goal }}</span>
            </el-descriptions-item>
        </el-descriptions>

        <el-table :data="formStore.formView.performance" :span-method="objectSpanMethod" border class="h-[540px]">
            <el-table-column label="一级指标" prop="indexLevel1" align="center" width="150px"></el-table-column>
            <el-table-column label="分解目标" align="center">
                <el-table-column label="二级指标" prop="indexLevel2" align="center"></el-table-column>
                <el-table-column label="三级指标" prop="indexLevel3" align="center"></el-table-column>
                <el-table-column label="指标目标值" prop="indexTarget" align="center"></el-table-column>
            </el-table-column>

        </el-table>
    </div>
</template>

<script lang="ts" setup>
// 绩效目标
import { useFormStore } from '../store/formStore';
const formStore = useFormStore()
/**
 * 由chatgpt生成
 * @param param0 
 */
const objectSpanMethod = ({
    row,
    column,
    rowIndex,
    columnIndex,
}: {
    row: IProjectPerformance
    column: any
    rowIndex: number
    columnIndex: number
}) => {
    // 仅对 indexLevel1 列（第 0 列）进行合并
    if (columnIndex !== 0) return;

    const spanMap = new Map<string, { startIndex: number; count: number }>();
    const valueCount: Record<string, number> = {};

    // 遍历一次，记录每种 indexLevel1 出现的位置和次数
    formStore.formView.performance?.forEach((item, idx) => {
        const key = item.indexLevel1 as any;
        if (!valueCount[key]) {
            valueCount[key] = 1;
            spanMap.set(key, { startIndex: idx, count: 1 });
        } else {
            valueCount[key]++;
            const spanInfo = spanMap.get(key);
            if (spanInfo) {
                spanInfo.count++;
            }
        }
    });

    const key = row.indexLevel1;
    const spanInfo = spanMap.get(key as any);

    if (spanInfo?.startIndex === rowIndex) {
        return {
            rowspan: spanInfo.count,
            colspan: 1,
        };
    } else {
        return {
            rowspan: 0,
            colspan: 0,
        };
    }
};

</script>