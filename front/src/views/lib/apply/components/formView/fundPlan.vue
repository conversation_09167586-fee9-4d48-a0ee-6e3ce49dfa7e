<template>
    <div class="flex flex-col gap-3">
        <el-descriptions :column="2" label-width="15%" border>
            <el-descriptions-item label="申请单位名称" width="35%">
                <span>{{ formStore.formView.applyOrgname }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="计划年度" width="35%">
                <div class="relative">
                    {{ formStore.projectYear }}
                    <span class="absolute right-0 top-0">
                        {{ formStore.formView?.fundPlan?.planType }}
                    </span>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="项目名称">
                <span>{{ formStore.formView.name }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目估算">
                <span>{{ formStore.formView.estAmount }} 万元</span>
            </el-descriptions-item>
            <el-descriptions-item label="核定金额">
                <span v-if="checkIsNumber(formStore.formView.checkAmount)" class="text-red"> {{ formStore.formView.checkAmount }} 万元</span>
                <span v-else>{{ '<无>' }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="`截至${formStore.projectYear - 1}年底前已执行`">
                <span v-if="checkIsNumber(formStore.formView.prevPayed)" class="text-red"> {{ formStore.formView.prevPayed }} 万元</span>
                <span v-else>{{ '<无>' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目类别">
                <span>{{ formStore.formView.typeName }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="是否涉及政府购买服务">
                <el-checkbox :model-value="formStore.formView.isZfgm" :true-value="'1'"
                        :false-value="'0'" disabled>涉及政府购买服务</el-checkbox>
            </el-descriptions-item>
            <el-descriptions-item label="项目用途">
                <span>{{ getProjectPurpose }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目性质">
                <span>{{ formStore.formView.natureName }}</span>
            </el-descriptions-item>
        </el-descriptions>

        <div>
            <!-- <div class="mb-2">
                <span class="text-#303133 text-4 font-bold">二、项目计划明细</span>
            </div> -->
            <el-table :data="getFundPlanDetailList" border class="h-[400px]">
                <el-table-column type="index" label="序号" align="center" width="80px">
                    <template #default="{ row, $index }">
                        <span v-if="$index == 0">合计</span>
                        <span v-else>项目{{ $index }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="项目内容" prop="title" align="center" width="250px"></el-table-column>
                <el-table-column label="估算金额" prop="estAmount" align="center" ></el-table-column>
                <el-table-column :label="`截至${formStore.projectYear - 1}年底已执行资金`" prop="executedAmount" align="center"></el-table-column>
                <el-table-column :label="`${formStore.projectYear}年上报计划`" prop="planAmount" align="center"></el-table-column>
                <el-table-column label="项目建设周期及进度说明" prop="describe" align="center" width="250px"></el-table-column>
                <el-table-column label="合同支付比例" align="center" width="150px">
                    <template #default="{ row }">
                        <el-slider :model-value="row.payPercent" range />
                    </template>
                </el-table-column>

            </el-table>
        </div>
    </div>
</template>

<script lang="ts" setup>
// 资金计划
import { checkIsNumber } from '@/utils/common';
import { useFormStore } from '../store/formStore';
import { ElMessage } from 'element-plus';

const formStore = useFormStore()

const getProjectPurpose = computed(() => {
    if (formStore.formView.purpose) {
        return JSON.parse(formStore.formView.purpose)?.map((item: any) => {
            return item.name
        }).join(',')
    }
})

const getFundPlanDetailList = computed(() => {
    if (formStore.formView.fundPlan?.planDetail) {
        try {
            return JSON.parse(formStore.formView.fundPlan?.planDetail)
        } catch(error) {
            ElMessage.error('资金计划 planDetail JSON转换错误')
            return []
        }
    }
})
</script>