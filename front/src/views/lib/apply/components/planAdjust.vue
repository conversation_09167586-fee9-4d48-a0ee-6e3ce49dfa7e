<template>
    <div ref="divRef">
        <el-form ref="formRef" :model="form" :rules="formRules" class="flex flex-col gap-3 " :inline-message="true">
            <el-descriptions :column="2" label-width="20%" border>
                <el-descriptions-item label="项目名称" :span="2" width="30%">
                    {{ formStore.formView.name }}
                </el-descriptions-item>
                <el-descriptions-item label="计划年度" width="35%">
                    {{ formStore.projectYear }}
                </el-descriptions-item>
                <el-descriptions-item label="项目金额">
                    {{ formStore.formView.estAmount }}（万元）
                </el-descriptions-item>
                <el-descriptions-item label="原计划金额">
                    {{ formStore.formView.fundPlan?.formalAmount }}（万元）
                </el-descriptions-item>
                <el-descriptions-item label="调整金额">
                    <el-form-item prop="applyAdjust" class="!mb-0">
                        <el-input-number v-model="form.applyAdjust" :min="0" controls-position="right"></el-input-number>
                    </el-form-item>

                </el-descriptions-item>
                <el-descriptions-item label="调整理由及本年度进度计划" :span="2">
                    <el-form-item prop="applyReason" class="!mb-0">
                        <el-input v-model="form.applyReason" type="textarea" :rows="5" maxlength="200"></el-input>
                    </el-form-item>
                    <p class="text-red">
                        注：以上所有金额单位均为“万元”！<br>
                        本年度进度计划节点参考：<br>
                        投资建设类：项目开工-工程过半-完成验收-审价-竣工决算审计；<br>
                        运行维护类：完成采购（签订合同）-按合同本年度付款进度（比例）及预计支付时间。
                    </p>
                </el-descriptions-item>
            </el-descriptions>
        </el-form>
        <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
            <el-button type="primary" @click="save">调整</el-button>
            <el-button @click="$emit('close')">取消</el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { adjustPlan } from '@/api/lib/project/info';
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { useFormStore } from './store/formStore';

const emit = defineEmits(['close']);
const formStore = useFormStore()

const divRef = ref<HTMLDivElement>()
onMounted(() => {
    // 设置最后一个el-descriptions-item占据整行
    nextTick(() => {
        const tr = divRef.value?.querySelector('tbody')?.querySelectorAll('tr')[3]
        if (tr) {
            tr.querySelectorAll('td')[1].colSpan = 3
        }
    })
})
// const props = defineProps<{
//     /** 1 信息补正 */
//     projectId: string
//     year?: number;
// }>();

const form = reactive({
    applyAdjust: formStore.formView.fundPlan?.applyAdjust ?? 0,
    applyReason: formStore.formView.fundPlan?.applyReason ?? ''
})

const formRules = reactive<FormRules<typeof form>>({})
const formRef = ref<FormInstance>()

const save = () => {
    formRef.value?.validate().then((res) => {
        adjustPlan({
            projId: formStore.formView.id,
            year: formStore.projectYear,
            ...form
        }).then(res => {
            ElMessage.success('调整成功');
            emit('close')
        })
    })

}
</script>