<template>
    <el-tabs v-model="activeName" type="card" class="demo-tabs" v-loading="loading">
        <el-tab-pane label="基本信息" :name="1" :disabled="panelDisabled">
            <el-scrollbar height="700px">
                <BaseInfo ref="baseInfoRef" />
                <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
                    <el-button type="success" :disabled="saveDisabled" @click="baseInfoSave()">保存</el-button>
                    <el-button type="primary" :loading="nextStepLoading" @click="handleNextStep">下一步</el-button>
                    <el-button type="warning" :disabled="!formIdCheck" @click="handlePrint">打印</el-button>
                    <!-- <el-button type="danger" :disabled="!formIdCheck" @click="handleExport">导出</el-button> -->
                    <el-button type="primary" :disabled="reportDisabled" @click="handleReport">上报</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>
        </el-tab-pane>
        <el-tab-pane label="项目概述" :name="2" :disabled="!formIdCheck || panelDisabled">
            <el-scrollbar height="700px">
                <ProjectDesc ref="projectDescRef" />

                <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
                    <el-button type="info" @click="handlePrevStep">上一步</el-button>
                    <el-button type="success" :disabled="saveDisabled" @click="projectDescSave()">保存</el-button>
                    <el-button type="primary" :loading="nextStepLoading" @click="handleNextStep">下一步</el-button>
                    <el-button type="warning" @click="handlePrint">打印</el-button>
                    <!-- <el-button type="danger" @click="handleExport">导出</el-button> -->
                    <el-button type="primary" :disabled="reportDisabled" @click="handleReport">上报</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>

        </el-tab-pane>
        <el-tab-pane label="绩效目标" :name="3" :disabled="!formIdCheck || panelDisabled">
            <el-scrollbar height="700px">
                <KpiTarget ref="kpiTargetRef" />

                <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
                    <el-button type="info" @click="handlePrevStep">上一步</el-button>
                    <el-button type="success" :disabled="saveDisabled" @click="kpiTargetSave()">保存</el-button>
                    <el-button type="primary" :loading="nextStepLoading" @click="handleNextStep">下一步</el-button>
                    <el-button type="warning" @click="handlePrint">打印</el-button>
                    <!-- <el-button type="danger" @click="handleExport">导出</el-button> -->
                    <el-button type="primary" :disabled="reportDisabled" @click="handleReport">上报</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>

        </el-tab-pane>
        <el-tab-pane label="附件" :name="4" :disabled="!formIdCheck || panelDisabled">
            <el-scrollbar height="700px">
                <UploadAtt ref="uploadAttRef" />

                <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
                    <el-button type="info" @click="handlePrevStep">上一步</el-button>
                    <el-button type="success" :disabled="saveDisabled" @click="uploadAttSave()">保存</el-button>
                    <el-button type="primary" :loading="nextStepLoading" @click="handleNextStep">下一步</el-button>
                    <el-button type="warning" @click="handlePrint">打印</el-button>
                    <!-- <el-button type="danger" @click="handleExport">导出</el-button> -->
                    <el-button type="primary" :disabled="reportDisabled" @click="handleReport">上报</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>

        </el-tab-pane>
        <el-tab-pane label="资金计划" :name="5" :disabled="!formIdCheck">
            <el-scrollbar height="700px">
                <FundPlan ref="fundPlanRef" />

                <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
                    <el-button type="info" @click="handlePrevStep">上一步</el-button>
                    <el-button type="success" :disabled="fundPlanSaveDisabled" @click="fundPlanSave()">保存</el-button>
                    <el-button type="primary" :disabled="reportDisabled" @click="handleReport">上报</el-button>
                    <el-button type="info" plain @click="emit('close')">关闭</el-button>
                </div>
            </el-scrollbar>

        </el-tab-pane>
    </el-tabs>

    <PrintForm :form-data="formStore.form" class="hidden" ref="pringRef"></PrintForm>
</template>

<script lang="ts" setup>
// 编辑表单
import BaseInfo from './formEdit/baseInfo.vue';
import ProjectDesc from './formEdit/projectDesc.vue';
import KpiTarget from './formEdit/kpiTarget.vue';
import UploadAtt from './formEdit/uploadAtt.vue';
import FundPlan from './formEdit/fundPlan.vue';
import { add, getProjectDetail, onlyReportProject, reportProject, summary, update } from '@/api/lib/project/info';
import { dayjs, ElMessage, ElMessageBox } from 'element-plus';
import { useFormStore } from './store/formStore';
import { addPerformance } from '@/api/lib/project/performance';
import { addPlan, reportPlan } from '@/api/lib/project/plan';
import { EFundPlanState, EProjectState } from '@/utils/constants';
import PrintForm from './printForm.vue';


const emit = defineEmits(['close']);

const props = defineProps<{
    /** 1 信息补正 */
    openType?: 1
    activateName?: number;
}>();
const activeName = ref(props.activateName || 1);
const loading = ref(false)
const formStore = useFormStore()
const searchDetail = async (id: string) => {
    return getProjectDetail(id, formStore.projectYear).then(res => {
        formStore.form = res.data ?? {};
    })
}
if (formStore.form.id) {
    searchDetail(formStore.form.id);
} else {
    formStore.form.applyTime = dayjs().format('YYYY-MM-DD')
    formStore.form.natureCode = 'JC'
    formStore.form.typeId = 'A0'
    formStore.form.estAmount = 0
}
const formIdCheck = computed(() => {
    return formStore.form.id
})
// 面板控制
const panelDisabled = computed(() => {
    if (props.activateName == 5 && formStore.form.state == EProjectState.reported) {
        return true
    }
})
// 保存按钮控制
const saveDisabled = computed(() => {
    if (!formStore.form.id) {
        return false
    }
    if (formStore.isXxbz) {
        return false
    }
    if ([EProjectState.reject, EProjectState.draft].includes(formStore.form.state)) {
        return false
    }
    return true
})
// 资金计划按钮控制
const fundPlanSaveDisabled = computed(() => {
    if (!formStore.form?.fundPlan?.planState) {
        return false
    }
    if ([EFundPlanState.reject, EFundPlanState.draft].includes(formStore.form?.fundPlan?.planState)) {
        return false
    }
    return true
})

const reportDisabled = computed(() => {
    if (!formStore.form.id) {
        return true
    }
    if (!formStore.form?.fundPlan?.planState) {
        return false
    }
    if ([EFundPlanState.reject, EFundPlanState.draft].includes(formStore.form?.fundPlan?.planState)) {
        return false
    }
    return true
})
type formRefType = {
    getData: () => Promise<any>;
    getDataAndCheck: () => Promise<any>;

}
const baseInfoRef = ref<formRefType>();
const projectDescRef = ref<formRefType & { checkIsFill: () => boolean }>();
const kpiTargetRef = ref<formRefType & { checkIsFill: () => boolean }>();
const uploadAttRef = ref<formRefType>();
const fundPlanRef = ref<formRefType>();

const handlePrevStep = async () => {
    if (activeName.value == 2) {
        await projectDescSave()
    }
    if (activeName.value == 3) {
        await kpiTargetSave()
    }
    if (activeName.value == 5) {
        await fundPlanSave()
    }
    activeName.value--
}

const nextStepLoading = ref(false)
const handleNextStep = async () => {
    if (activeName.value == 1) {
        await baseInfoSave()
    }
    if (activeName.value == 2) {
        await projectDescSave()
    }
    if (activeName.value == 3) {
        await kpiTargetSave()
    }
    activeName.value++
}

const baseInfoSave = async () => {
    const data = await baseInfoRef.value?.getData()
    let save = add
    if (formStore.form.id) {
        save = update
    }
    loading.value = true
    return save({ ...data }).then(res => {
        ElMessage.success('保存成功');
        if (res.data?.id) {
            searchDetail(res.data?.id)
        }
    }).finally(() => {
        loading.value = false
    })
}
const projectDescSave = async () => {
    const data = await projectDescRef.value?.getData()
    loading.value = true
    return summary({
        projId: formStore.form.id,
        ...data
    }).then(res => {
        ElMessage.success('保存成功');
        searchDetail(formStore.form.id!)
    }).finally(() => {
        loading.value = false
    })
}
const kpiTargetSave = async () => {
    const data = await kpiTargetRef.value?.getData()
    loading.value = true
    return addPerformance({
        projId: formStore.form.id,
        goal: data.goal,
        performanceList: [...data.list]
    }).then(res => {
        ElMessage.success('保存成功');
        searchDetail(formStore.form.id!)
    }).finally(() => {
        loading.value = false
    })
}
const uploadAttSave = async () => {
    // await uploadAttRef.value?.checkForm()
}
const fundPlanSave = async () => {
    const form = await fundPlanRef.value?.getData()
    loading.value = true
    return addPlan({ ...form }).then(res => {
        ElMessage.success('保存成功');
        searchDetail(formStore.form.id!)
        // formStore.form.fundPlan = res.data
    }).finally(() => {
        loading.value = false
    })
}

const handleReport = async () => {
    if (activeName.value == 1) {
        const data = await baseInfoRef.value?.getDataAndCheck()
        loading.value = true
        await update({ ...data }).then(res => {
            if (res.data?.id) {
                return searchDetail(res.data?.id)
            }
        }).finally(() => {
            loading.value = false
        })
    }
    if (activeName.value == 2) {
        const data = await projectDescRef.value?.getData()
        loading.value = true
        await summary({
            projId: formStore.form.id,
            ...data
        }).then(res => {
            return searchDetail(formStore.form.id!)
        }).finally(() => {
            loading.value = false
        })
    }
    if (activeName.value == 3) {
        const data = await kpiTargetRef.value?.getData()
        loading.value = true
        await addPerformance({
            projId: formStore.form.id,
            goal: data.goal,
            performanceList: [...data.list]
        }).then(res => {
            return searchDetail(formStore.form.id!)
        }).finally(() => {
            loading.value = false
        })
    }
    if (activeName.value == 5) {
        // 先保存资金计划
        const form = await fundPlanRef.value?.getData()
        loading.value = true
        await addPlan({ ...form }).then(() => {
            return searchDetail(formStore.form.id!)
        }).finally(() => {
            loading.value = false
        })
    }


    ElMessageBox.confirm('是否确认上报？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
    }).then(async () => {

        // if (!formStore.form.necessity) {
        //     ElMessage.warning('[项目概述]必要性及主要内容必须填写');
        //     return
        // }
        // 如果大于等于100万，校验是否填写
        if (formStore.form.estAmount! >= 100) {
            if (!formStore.form.goal) {
                ElMessage.warning('[绩效目标]项目总目标必须填写')
                return
            }
            const ifFill = formStore.form.performance?.some(t => {
                return !!t.indexLevel1 && !!t.indexLevel2 && !!t.indexLevel3
            })
            if (!ifFill) {
                ElMessage.warning('[绩效目标]绩效分解目标必须填写')
                return
            }
        }
        // await fundPlanRef.value?.getData()
        // if (!formStore.form.fundPlan?.planDetail) {
        //     ElMessage.warning('计划明细请填写完整，当年上报计划必须填写')
        //     return
        // }
        reportProject(formStore.form.id!, formStore.projectYear).then(res => {
            ElMessage.success('上报成功');
            emit('close')
        })


    })
}

const handleExport = () => {

}

const pringRef = ref()
const handlePrint = () => {
    pringRef.value.print()
}

</script>