<template>
    <el-form ref="formRef" :model="formStore.form" :rules="formRules" class="flex flex-col gap-3"
        :inline-message="true">
        <!-- <el-descriptions :column="2" label-width="15%" border>
            <el-descriptions-item width="35%">
                <template #label>
                    申请单位名称 <span class="text-red">*</span>
                </template>
<el-input :modelValue="userStore.deptName" disabled></el-input>
</el-descriptions-item>
<el-descriptions-item label="申报日期" width="35%">
    <template #label>
                    申报时间 <span class="text-red">*</span>
                </template>
    <el-form-item prop="applyTime" class="!mb-0">
        <el-date-picker v-model="formStore.form.applyTime" :clearable="false" type="date" placeholder=""
            format="YYYY/MM/DD" value-format="YYYY-MM-DD" class="!w-full" disabled></el-date-picker>
    </el-form-item>

</el-descriptions-item>
</el-descriptions> -->
        <el-descriptions :column="2" label-width="15%" border>
            <el-descriptions-item width="35%">
                <template #label>
                    申请单位名称 <span class="text-red">*</span>
                </template>
                <el-input :modelValue="userStore.deptName" disabled></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="申报日期" width="35%">
                <template #label>
                    申报时间 <span class="text-red">*</span>
                </template>
                <el-form-item prop="applyTime" class="!mb-0">
                    <el-date-picker v-model="formStore.form.applyTime" :clearable="false" type="date" placeholder=""
                        format="YYYY/MM/DD" value-format="YYYY-MM-DD" class="!w-full" disabled></el-date-picker>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item :span="2" width="35%">
                <template #label>
                    项目名称 <span class="text-red">*</span>
                </template>
                <el-form-item prop="name" class="!mb-0">
                    <el-input v-model="formStore.form.name" placeholder="" maxlength="200"></el-input>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item width="35%">
                <template #label>
                    项目使用单位 <span class="text-red">*</span>
                </template>
                <el-form-item prop="useOrgname" class="!mb-0">
                    <el-input v-model="formStore.form.useOrgname" placeholder="" maxlength="200"></el-input>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item width="35%">
                <template #label>
                    项目金额估算（万元） <span class="text-red">*</span>
                </template>
                <el-form-item prop="estAmount" class="!mb-0">
                    <el-input-number v-model="formStore.form.estAmount" :min="0" placeholder=""
                        controls-position="right"></el-input-number>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    项目类型 <span class="text-red">*</span>
                </template>
                <el-form-item prop="typeId" class="!mb-0">
                    <ProjectTypeSelect v-model="formStore.form.typeId" class="w-full"></ProjectTypeSelect>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :rowspan="2">
                <template #label>
                    项目用途 <span class="text-red">*</span>
                </template>
                <el-form-item prop="purpose" class="!mb-0" :rules="formRules.purpose">
                    <el-checkbox-group v-model="purpose" class="flex flex-col gap-2">
                        <el-checkbox v-for="item in project_purpose" :value="item.value">{{ item.label }}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    <div>支出类型</div>
                    <div>涉及政府购买</div>
                </template>
                <el-form-item prop="test" class="!mb-0">
                    <div class="flex flex-col">
                        <el-checkbox v-model="formStore.form.isSzhyq" :true-value="'1'"
                            :false-value="'0'">涉及数字化园区支出</el-checkbox>
                        <el-checkbox v-model="formStore.form.isJxhgl" :true-value="'1'"
                            :false-value="'0'">涉及精细化管理支出</el-checkbox>
                        <el-checkbox v-model="formStore.form.isZfgm" :true-value="'1'"
                            :false-value="'0'">涉及政府购买服务</el-checkbox>
                    </div>

                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="项目性质">
                <el-form-item prop="test" class="!mb-0">
                    <el-form-item prop="natureCode" class="!mb-0">
                        <el-radio-group v-model="formStore.form.natureCode" class="flex items-baseline gap-2">
                            <el-radio v-for="item in project_nature" :value="item.value">{{ item.label }}</el-radio>
                        </el-radio-group>
                    </el-form-item>

                </el-form-item>

            </el-descriptions-item>
            <el-descriptions-item label="配合单位">
                <el-form-item prop="cooperateOrgid" class="!mb-0">
                    <DeptSelect ref="deptRef" v-model="formStore.form.cooperateOrgid" clearable></DeptSelect>

                    <el-checkbox v-model="formStore.form.isPk" true-value="1" false-value="0">配合单位为考核主体</el-checkbox>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="项目负责人">
                <el-form-item prop="leader" class="!mb-0">
                    <el-input v-model="formStore.form.leader" placeholder="" maxlength="50"></el-input>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item label="手机">
                <el-form-item prop="leaderTel" class="!mb-0">
                    <el-input v-model="formStore.form.leaderTel" placeholder=""></el-input>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    经办人 <span class="text-red">*</span>
                </template>
                <el-form-item prop="handler" class="!mb-0">
                    <el-input v-model="formStore.form.handler" placeholder="" maxlength="50"></el-input>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    手机 <span class="text-red">*</span>
                </template>
                <el-form-item prop="handlerTel" class="!mb-0">
                    <el-input v-model="formStore.form.handlerTel" placeholder=""></el-input>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    项目建设周期 <span class="text-red">*</span>
                </template>

                <el-form-item prop="buildBegin" class="!mb-0">
                    <DateRangePicker v-model:begin-date="formStore.form.buildBegin"
                        v-model:end-date="formStore.form.buildEnd" type="daterange" placeholder="" format="YYYY/MM/DD"
                        value-format="YYYY-MM-DD" class="!w-full">
                    </DateRangePicker>
                </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
                <template #label>
                    资金使用周期 <span class="text-red">*</span>
                </template>
                <el-form-item prop="fundBegin" class="!mb-0">
                    <DateRangePicker v-model:begin-date="formStore.form.fundBegin"
                        v-model:end-date="formStore.form.fundEnd" type="daterange" placeholder="" format="YYYY/MM/DD"
                        value-format="YYYY-MM-DD" class="!w-full">
                    </DateRangePicker>
                </el-form-item>
            </el-descriptions-item>
        </el-descriptions>
    </el-form>

</template>

<script lang="ts" setup>
import { FormInstance, FormItemInstance, FormRules } from 'element-plus';
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
// @ts-ignore
import useUserStore from '@/store/modules/user'
import { useFormStore } from '../store/formStore';
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { getLabelFromDicts } from '@/utils/common';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
// 基本信息

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type } = proxy.useDict("project_purpose", "project_nature", 'project_type');

const userStore = useUserStore()
const purpose = ref([])
const formStore = useFormStore()
watch(() => formStore.form.purpose, (value) => {
    if (value) {
        purpose.value = JSON.parse(value).map((item: any) => item.value)
    }
}, { immediate: true })

const deptRef = ref()
// const deptList = ref([])
// watch(() => formStore.form.cooperateOrgid, (value) => {
//     if (value) {
//         let sortValue = JSON.parse(value).sort((a: any, b: any) => a.seqNo - b.seqNo)
//         deptList.value = sortValue.map((item: any) => item.id)
//     }
// }, { immediate: true })

const getProjectCode = computed(() => {
    const code = formStore.form.projectSn?.formalSn ?? formStore.form.projectSn?.tempSn
    return code ? `（项目编号：${code}）` : ''
})

const formRules = reactive<FormRules<typeof formStore.form>>({
    applyTime: [
        { required: true, message: '请选择申报时间', trigger: 'blur' }
    ],
    name: [
        { required: true, message: '请输入项目名称', trigger: 'blur' }
    ],
    useOrgname: [
        { required: true, message: '请输入项目使用单位', trigger: 'blur' }
    ],
    estAmount: [
        { required: true, message: '请输入项目金额估算', trigger: 'blur' }
    ],
    typeId: [
        { required: true, message: '请选择项目类型', trigger: 'blur' }
    ],
    cooperateOrgid: [
        {
            validator: (rule, value, callback) => {
                if (formStore.form.isPk == '1' && !formStore.form.cooperateOrgid) {
                    callback(new Error('请选择配合单位'))
                } else {
                    callback()
                }
            }
        }
    ],
    purpose: [
        {
            validator: (rule, value, callback) => {
                if (!purpose.value || purpose.value.length == 0) {
                    callback(new Error('请选择项目用途'))
                } else {
                    callback()
                }
            }
        }
    ],
    handler: [
        { required: true, message: '请输入经办人', trigger: 'blur' }
    ],
    handlerTel: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入合法的手机号',
            trigger: ['blur', 'change']
        }
    ],
    leaderTel: [
        {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入合法的手机号',
            trigger: ['blur', 'change']
        }
    ],
    buildBegin: [
        { required: true, message: '请选择项目建设周期', trigger: 'blur' }
    ],
    fundBegin: [
        { required: true, message: '请选择资金使用周期', trigger: 'blur' }
    ],
})

const formRef = ref<FormInstance>()

defineExpose({
    async getData() {
        await formRef.value?.validateField('name')
        await formRef.value?.validateField('estAmount')
        formStore.form.purpose = JSON.stringify(purpose.value.map(t => {
            const d = project_purpose.value.find((t2: any) => t2.value == t)
            if (d) {
                return {
                    name: d.label,
                    value: d.value,
                }
            }
            return null

        }).filter(t => t))

        return {
            id: formStore.form.id,
            year: formStore.form.year,
            projType: formStore.form.projType,
            applyOrgid: userStore.deptId,
            name: formStore.form.name,
            applyTime: formStore.form.applyTime,
            useOrgname: formStore.form.useOrgname,
            estAmount: formStore.form.estAmount,
            typeId: formStore.form.typeId,
            purpose: formStore.form.purpose,
            isSzhyq: formStore.form.isSzhyq,
            isJxhgl: formStore.form.isJxhgl,
            isZfgm: formStore.form.isZfgm,
            natureCode: formStore.form.natureCode,
            cooperateOrgid: formStore.form.cooperateOrgid ?? null,
            cooperateOrgname: deptRef.value.getDeptList().find((item: any) => item.deptId === formStore.form.cooperateOrgid)?.deptName,
            isPk: formStore.form.isPk,
            leader: formStore.form.leader,
            leaderTel: formStore.form.leaderTel,
            handler: formStore.form.handler,
            handlerTel: formStore.form.handlerTel,
            buildBegin: formStore.form.buildBegin,
            buildEnd: formStore.form.buildEnd,
            fundBegin: formStore.form.fundBegin,
            fundEnd: formStore.form.fundEnd,
            typeName: project_type.value.find((t: any) => t.value == formStore.form.typeId)?.label,
            natureName: project_nature.value.find((t: any) => t.value == formStore.form.natureCode)?.label
        }


    },
    async getDataAndCheck() {
        await formRef.value?.validate().then((res) => { })

        return this.getData()
    }
})
</script>
