<template>
    <div class="flex flex-col gap-3">
        <el-descriptions :column="2" label-width="15%" border>
            <el-descriptions-item label="申请单位名称" width="35%">
                <span>{{ formStore.form.applyOrgname }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="计划年度" width="35%">
                <div class="relative">
                    {{ formStore.projectYear }}
                    <span class="absolute right-0 top-0">
                        {{ getPlanType }}
                    </span>
                </div>
            </el-descriptions-item>
            <el-descriptions-item label="项目名称">
                <span>{{ formStore.form.name }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目估算">
                <span>{{ formStore.form.estAmount }} 万元</span>
            </el-descriptions-item>
            <el-descriptions-item label="核定金额">
                <span v-if="checkIsNumber(formStore.form.checkAmount)" class="text-red">{{ formStore.form.checkAmount }}
                    万元</span>
                <span v-else>{{ '<无>' }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="`截至${formStore.projectYear - 1}年底前已执行`">
                <span v-if="checkIsNumber(formStore.form.prevPayed)" class="text-red">
                    {{ formStore.form.prevPayed }} 万元
                </span>
                <span v-else>{{ '<无>' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目类别">
                <span>{{ formStore.form.typeName }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="是否涉及政府购买服务">
                <el-checkbox :model-value="formStore.form.isZfgm" :true-value="'1'" :false-value="'0'"
                    disabled>涉及政府购买服务</el-checkbox>
            </el-descriptions-item>
            <el-descriptions-item label="项目用途">
                <span>{{ getProjectPurpose }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="项目性质">
                <span>{{ formStore.form.natureName }}</span>
            </el-descriptions-item>
        </el-descriptions>

        <div>
            <!-- <div class="mb-2">
                <span class="text-#303133 text-4 font-bold">二、项目计划明细</span>
            </div> -->
            <el-table :data="dataList" border class="h-[400px]">
                <el-table-column align="center" width="60px">
                    <template #header>
                        <el-button type="primary" text plain icon="plus" @click="addItem"></el-button>
                    </template>
                    <template #default="{ row, $index }">
                        <el-button v-if="$index > 0" type="danger" size="small" plain text icon="minus"
                            @click="dataList.splice($index, 1)"></el-button>
                    </template>
                </el-table-column>
                <el-table-column type="index" label="序号" align="center" width="80px">
                    <template #default="{ row, $index }">
                        <span v-if="$index == 0">合计</span>
                        <span v-else>项目{{ $index }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="项目内容" prop="z1" align="center" width="200px">
                    <template #default="{ row }">
                        <el-input v-model="row.title" />
                    </template>
                </el-table-column>
                <el-table-column label="估算金额" prop="z1" align="center">
                    <template #default="{ row }">
                        <el-input-number v-model="row.estAmount" :min="0" class="w-100%" :precision="2"
                            controls-position="right" />
                    </template>
                </el-table-column>
                <el-table-column :label="`截至${formStore.projectYear - 1}年底已执行资金`" prop="z2" align="center">
                    <template #default="{ row }">
                        <el-input-number v-model="row.executedAmount" :min="0" class="w-100%" :precision="2"
                            controls-position="right" />
                    </template>
                </el-table-column>
                <el-table-column :label="`${formStore.projectYear}年上报计划`" prop="z3" align="center">
                    <template #default="{ row }">
                        <el-input-number v-model="row.planAmount" :min="0" class="w-100%" :precision="2"
                            controls-position="right" />
                    </template>
                </el-table-column>
                <el-table-column label="项目建设周期及进度说明" prop="z3" align="center" width="200px">
                    <template #default="{ row }">
                        <el-input v-model="row.describe" />
                    </template>
                </el-table-column>
                <el-table-column label="合同支付比例" align="center" width="150px">
                    <template #default="{ row }">
                        <el-slider v-model="row.payPercent" range :step="5" />
                    </template>
                </el-table-column>

            </el-table>
        </div>
    </div>
</template>

<script lang="ts" setup>
// 资金计划
import { checkIsNumber, getLabelFromDicts, isDateWithinRange } from '@/utils/common';
import { useFormStore } from '../store/formStore';
import { dayjs, ElMessage } from 'element-plus';
import { BigNumber } from 'bignumber.js';
import { isNaN } from 'lodash';
import { useConfig } from '@/utils/config';
import { EProjectState } from '@/utils/constants';

const { proxy } = getCurrentInstance() as { proxy: any };
const {
    ["sys.plan.submit.period"]: planSubmitPeriod,
} = useConfig('sys.plan.submit.period')
const getPlanType = computed(() => {
    // if ([EProjectState.adjust, EProjectState.reported].includes(formStore.form.state)) {
    //    return formStore.form.fundPlan?.planType
    // }
    if (planSubmitPeriod.value) {
        let startDate = ''
        let endDate = ''
        const arr = planSubmitPeriod.value.split(',')
        if (arr[0].at(0) == 'p') {
            startDate = dayjs().subtract(1, 'year').year() + '-' + arr[0].split(':')[1]
        } else {
            startDate = dayjs().year() + '-' + arr[0].split(':')[1]
        }

        if (arr[1].at(0) == 'p') {
            endDate = dayjs().subtract(1, 'year').year() + '-' + arr[1].split(':')[1]
        } else {
            endDate = dayjs().year() + '-' + arr[1].split(':')[1]
        }

        return isDateWithinRange(dayjs(), startDate, endDate) ? '年初计划' : '调整计划'
    }
})


const formStore = useFormStore()
watch(() => formStore.form.fundPlan, (value) => {
    if (value && value?.planDetail) {
        try {
            dataList.value = JSON.parse(value?.planDetail)
        } catch(error) {
            ElMessage.error('资金计划 planDetail JSON转换错误')
        }
    } else {
        dataList.value.at(0)!.executedAmount = formStore.form.prevPayed
    }
})

const getProjectPurpose = computed(() => {
    if (formStore.form.purpose) {
        return JSON.parse(formStore.form.purpose)?.map((item: any) => {
            return item.name
        }).join(',')
    }
})

const dataList = ref<IProjectPlanItem[]>([{
    title: '',
    sort: 1
} as IProjectPlanItem])
const addItem = () => {
    dataList.value.push({
        // estAmount: formStore.form.estAmount,
        sort: dataList.value.length + 1,
    } as IProjectPlanItem)
}

defineExpose({
    async getData() {
        if (!dataList.value.every(t => checkIsNumber(t.planAmount))) {
            ElMessage.error('计划明细请填写完整，当年上报计划必须填写')
            throw new Error('计划明细请填写完整，当年上报计划必须填写')
        }
        return {
            id: formStore.form.fundPlan?.id,
            projId: formStore.form.id,
            year: formStore.projectYear,
            planDetail: JSON.stringify(dataList.value),
            planType: getPlanType.value,
            declareAmount: dataList.value.at(0)?.planAmount
        }
    },
})
</script>