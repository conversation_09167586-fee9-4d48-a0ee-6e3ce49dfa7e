<template>
    <div class="relative">
        <div class="flex flex-col gap-3">
            <el-descriptions :column="1" label-width="15%" border>
                <el-descriptions-item label="附件上传" width="85%">
                    <div class="flex gap-2">
                        <el-select v-model="type" class="!w-50" placeholder="请选择文件类型">
                            <el-option v-for="item in project_file_type" :label="item.label"
                                :value="item.label"></el-option>
                        </el-select>
                        <div class="flex items-center gap-1">
                            <el-button v-if="!type" type="primary" @click="ElMessage.warning('请选择上传类型')">选择文件</el-button>
                            <my-file-upload v-else :accept="fileType" :data="{
                                sourceId: formStore.form.id,
                                sourceType: EFileSoureType.PROJECT,
                                primaryType: type,
                                primaryTypeName: getUploadFileType?.label
                                    }" @upload-success="fileTableRef?.updateTable"></my-file-upload>
                            <span>{{ getUploadFileType?.remark }}</span>
                        </div>
                    </div>
                </el-descriptions-item>
            </el-descriptions>

            <div class="h-[580px]">
                <div class="mb-2">
                    <span class="text-#303133 text-4 font-bold">附件列表</span>
                </div>
                <FileTable ref="fileTableRef" :source-id="formStore.form.id??'null'"></FileTable>
            </div>
        </div>

        <!-- <div class="absolute right-0 top-20">
            <div class="p-2 w-20 bg-light-900 text-center cursor-pointer">
                <p class="text-#409EFF">oa文件导入</p>

            </div>
        </div> -->
    </div>

</template>

<script lang="ts" setup>
// 上传附件
import { ElMessage, ElMessageBox } from 'element-plus';
import { useFormStore } from '../store/formStore';
import { EFileSoureType } from '@/utils/constants';
import { dateFormat } from '@/utils/common';
import FileTable from '@/components/Table/FileTable.vue';
import { getBillAttachmentsByProjId } from '@/api/fund/paybill';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_file_type } = proxy.useDict("project_file_type");
const { ["sys.upload.fileType"]: fileType } = proxy.useConfig("sys.upload.fileType");

const fileTableRef = ref<FileTableExposeType | null>(null)
// const fileList = ref<ISysAttachment[]>([]);
const formStore = useFormStore()
// watch(() => formStore.form.sysAttachments, (value) => {
//     if (formStore.form.sysAttachments) {
//         fileList.value = formStore.form.sysAttachments
//     }
// })

const type = ref("");
const getUploadFileType = computed(() => {
    return project_file_type.value.find((item: any) => item.value === type.value);
});
// const handleBeforeUpload = () => {
//     if (!type.value) {
//         ElMessage.warning('请选择上传类型');
//         return false;
//     }
// }
</script>