<template>
    <el-form ref="formRef" :model="form" class="flex flex-col gap-3"
        :inline-message="true">

        <div class="flex flex-col gap-3">
            <el-descriptions :column="2" label-width="150px" border>
                <el-descriptions-item title="项目总目标">
                    <template #label>
                        项目总目标
                    </template>
                    <el-form-item prop="goal" class="!mb-0">
                        <el-input v-model="form.goal" />
                    </el-form-item>

                </el-descriptions-item>
            </el-descriptions>

            <!-- <div class="">
            <span class="text-#303133 text-4 font-bold">分解目标</span>
        </div> -->
            <el-table :data="dataList" :span-method="objectSpanMethod" border class="h-[550px]">
                <el-table-column label="一级指标" prop="indexLevel1" align="center" width="150px"></el-table-column>
                <el-table-column label="分解目标" align="center">
                    <el-table-column label="二级指标" prop="indexLevel2" align="center">
                        <template #default="{ row }">
                            <template v-if="row.indexLevel1 == '成本指标'">
                                <el-select v-model="row.indexLevel2" class="w-full">
                                    <el-option label="经济成本指标" value="经济成本指标"></el-option>
                                    <el-option label="社会成本指标" value="社会成本指标"></el-option>
                                    <el-option label="生态环境成本指标" value="生态环境成本指标"></el-option>
                                </el-select>
                            </template>
                            <template v-if="row.indexLevel1 == '产出指标'">
                                <el-select v-model="row.indexLevel2" class="w-full">
                                    <el-option label="数量指标" value="数量指标"></el-option>
                                    <el-option label="质量指标" value="质量指标"></el-option>
                                    <el-option label="时效指标" value="时效指标"></el-option>
                                </el-select>
                            </template>
                            <template v-if="row.indexLevel1 == '效益指标'">
                                <el-select v-model="row.indexLevel2" class="w-full">
                                    <el-option label="经济效益指标" value="经济效益指标"></el-option>
                                    <el-option label="社会效益指标" value="社会效益指标"></el-option>
                                    <el-option label="生态效益指标" value="生态效益指标"></el-option>
                                    <el-option label="可持续影响指标" value="可持续影响指标"></el-option>
                                </el-select>
                            </template>
                            <template v-if="row.indexLevel1 == '满意度指标'">
                                <el-select v-model="row.indexLevel2" class="w-full">
                                    <el-option label="服务对象满意度指标" value="服务对象满意度指标"></el-option>
                                </el-select>
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column label="三级指标" prop="z3" align="center">
                        <template #default="{ row }">
                            <template v-if="row.indexLevel1 == '成本指标'">
                                <el-input v-model="row.indexLevel3" maxlength="1000" />
                            </template>
                            <template v-else>
                                <el-select v-model="row.indexLevel3" allow-create filterable>
                                    <el-option
                                        v-for="item in baseList.filter(t => t.indexLevel1 == row.indexLevel1 && t.indexLevel2 == row.indexLevel2)"
                                        :label="item.indexLevel3" :value="item.indexLevel3!" />
                                </el-select>
                            </template>
                        </template>
                    </el-table-column>
                    <el-table-column label="指标目标值" prop="z4" align="center">
                        <template #default="{ row }">
                            <el-input v-model="row.indexTarget" maxlength="1000" />
                        </template>
                    </el-table-column>
                    <el-table-column align="center" width="100px">
                        <template #default="{ row, $index }">
                            <el-button type="primary" size="small" plain text icon="plus"
                                @click="addItem(row, $index)"></el-button>
                            <el-button v-if="(() => {
                                const group = dataList.filter(t => t.indexLevel1 === row.indexLevel1);
                                return group.length > 1 && group.findIndex(t => t === row) > 0;
                            })()" class="!ml-0" type="danger" size="small" plain text icon="Minus"
                                @click="removeItem($index)"></el-button>
                        </template>
                    </el-table-column>
                </el-table-column>

            </el-table>
        </div>
    </el-form>
</template>

<script lang="ts" setup>
// 绩效目标
import { ElMessage, FormInstance, FormRules } from 'element-plus';
import { useFormStore } from '../store/formStore';
import { gePerformanceBaseList } from '@/api/lib/project/performanceBase';

const formRef = ref<FormInstance>()
const form  =reactive<IProjectInfoVo>({
    goal: '',
})
const formRules = reactive<FormRules<typeof form>>({
    goal: [
        { required: true, message: '请输入项目总目标', trigger: 'blur' }
    ],
})
const formStore = useFormStore()
const dataList = ref<IProjectPerformance[]>([
    {
        projId: formStore.form.id,
        indexLevel1: '成本指标',
        indexLevel2: '经济成本指标',
        indexSort: 1
    },
    {
        projId: formStore.form.id,
        indexLevel1: '成本指标',
        indexLevel2: '社会成本指标',
        indexSort: 2
    },
    {
        projId: formStore.form.id,
        indexLevel1: '成本指标',
        indexLevel2: '生态环境成本指标',
        indexSort: 3
    },
    {
        projId: formStore.form.id,
        indexLevel1: '产出指标',
        indexLevel2: '数量指标',
        indexSort: 10000
    },
    {
        projId: formStore.form.id,
        indexLevel1: '产出指标',
        indexLevel2: '质量指标',
        indexSort: 10001
    },
    {
        projId: formStore.form.id,
        indexLevel1: '产出指标',
        indexLevel2: '时效指标',
        indexSort: 10002
    },
    {
        projId: formStore.form.id,
        indexLevel1: '效益指标',
        indexLevel2: '经济效益指标',
        indexSort: 20000
    },
    {
        projId: formStore.form.id,
        indexLevel1: '效益指标',
        indexLevel2: '社会效益指标',
        indexSort: 20001
    },
    {
        projId: formStore.form.id,
        indexLevel1: '效益指标',
        indexLevel2: '生态效益指标',
        indexSort: 20002
    },
    {
        projId: formStore.form.id,
        indexLevel1: '效益指标',
        indexLevel2: '可持续影响指标',
        indexSort: 20003
    },
    {
        projId: formStore.form.id,
        indexLevel1: '满意度指标',
        indexLevel2: '服务对象满意度指标',
        indexSort: 30000
    },
]);

const baseList = ref<IProjectPerformanceBase[]>([])
gePerformanceBaseList().then(res => {
    baseList.value = res.data ?? []
})

watchEffect(() => {
    if (formStore.form.performance && formStore.form.performance.length > 0) {
        dataList.value = JSON.parse(JSON.stringify(formStore.form.performance)) 
    }
    form.goal = formStore.form.goal
})

const addItem = (row: IProjectPerformance, index: number) => {
    dataList.value.splice(index + 1, 0, {
        projId: formStore.form.id,
        indexLevel1: row.indexLevel1,
        indexLevel2: row.indexLevel2,
        indexSort: row.indexSort + 1
    })
}

const removeItem = (index: number) => {
    dataList.value.splice(index, 1)
}
/**
 * 由chatgpt生成
 * @param param0 
 */
const objectSpanMethod = ({
    row,
    column,
    rowIndex,
    columnIndex,
}: {
    row: IProjectPerformance
    column: any
    rowIndex: number
    columnIndex: number
}) => {
    // 仅对 indexLevel1 列（第 0 列）进行合并
    if (columnIndex !== 0) return;

    const spanMap = new Map<string, { startIndex: number; count: number }>();
    const valueCount: Record<string, number> = {};

    // 遍历一次，记录每种 indexLevel1 出现的位置和次数
    dataList.value.forEach((item, idx) => {
        const key = item.indexLevel1 as any;
        if (!valueCount[key]) {
            valueCount[key] = 1;
            spanMap.set(key, { startIndex: idx, count: 1 });
        } else {
            valueCount[key]++;
            const spanInfo = spanMap.get(key);
            if (spanInfo) {
                spanInfo.count++;
            }
        }
    });

    const key = row.indexLevel1;
    const spanInfo = spanMap.get(key as any);

    if (spanInfo?.startIndex === rowIndex) {
        return {
            rowspan: spanInfo.count,
            colspan: 1,
        };
    } else {
        return {
            rowspan: 0,
            colspan: 0,
        };
    }
};


defineExpose({
    async getData() {
        // return formRef.value?.validate().then((res) => {
            return {
                goal: form.goal,
                list: dataList.value
            }
        // })
    },
})
</script>