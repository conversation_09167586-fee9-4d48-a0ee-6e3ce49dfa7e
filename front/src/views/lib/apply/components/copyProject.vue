<template>
    <div>
        <el-form label-width="70px">
            <el-row>
                <el-col :span="7">
                    <el-form-item label="项目类型">
                        <el-select v-model="searchForm.natureCode" placeholder="请选择项目性质" class="w-40">
                            <el-option v-for="item in project_nature" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="7">
                    <el-form-item label="年度">
                        <el-input-number v-model="searchForm.year" :min="2015" :max="2099">
                            <template #suffix>
                                <span>年</span>
                            </template>
                        </el-input-number>
                    </el-form-item>
                </el-col>
                <el-col :span="4">
                    <el-form-item label-width="40px">
                        <div class="flex justify-center">
                            <el-button type="primary" @click="search">查询</el-button>
                            <!-- <el-button @click="searchForm.natureCode = '';">重置</el-button> -->
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>



        </el-form>

        <div>
            <el-table :data="dataList" border height="600px" @row-click="handleRowClick">
                <el-table-column label="序号" type="index" align="center" width="80"></el-table-column>
                <el-table-column label="项目名称" prop="name" align="left"></el-table-column>
                <el-table-column label="项目(资金)周期" align="center" width="150">
                    <template #default="{ row }">
                        起：{{ dateFormat(row.beginDate) }}<br>
                        止：{{ dateFormat(row.endDate) }}
                    </template>
                </el-table-column>
                <el-table-column label="项目金额(万元)" prop="estAmount" align="right" width="150"></el-table-column>
                <el-table-column label="经办人" prop="handler" align="center" width="100"></el-table-column>
                <el-table-column label="选择" align="center" width="100">
                    <template #default="{ row }">
                        <el-icon v-if="selectIds.includes(row.id)" class="text-green text-8"><Select /></el-icon>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
    <div class="absolute bottom-0 left-0 w-full h-[60px] flex justify-center items-center">
        <el-button type="primary" @click="save">确认复制</el-button>
        <el-button @click="$emit('close')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { copyProject, getReproducibleProject } from '@/api/lib/project/info';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox } from 'element-plus';

const emit = defineEmits(['close'])
const props = defineProps<{
    year: number;
}>()

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_nature } = proxy.useDict('project_nature');

const selectIds = ref<string[]>([])
const searchForm = reactive({
    natureCode: 'JC',
    year: props.year - 1
})
const dataList = ref<IProjectInfoVo[]>([])
const search = () => {
    getReproducibleProject(searchForm).then(res => {
        dataList.value = res.data ?? []
    })
}
search()
const handleRowClick = (row: IProjectInfoVo) => {
    const index = selectIds.value.indexOf(row.id!)
    if (index !== -1) {
        selectIds.value.splice(index, 1)
    } else {
        selectIds.value.push(row.id!)
    }
}

const save = () => {
    if (selectIds.value.length == 0) {
        ElMessage.error('请选择项目')
        return
    }
    copyProject(selectIds.value, props.year).then(res => {
        ElMessage.success('复制成功')
        emit('close')
    })
}
</script>