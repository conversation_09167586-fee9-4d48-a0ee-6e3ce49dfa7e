<template>
    <div class="app-container">
        <div>
            <el-form :inline="true" :model="queryForm" class="mb-4" label-width="auto">
                <el-form-item label="项目名称">
                    <el-input v-model="queryForm.name" placeholder="请输入项目名称" class="w-50" clearable />
                </el-form-item>
                <el-form-item label="申报单位">
                    <DeptSelect v-model="queryForm.applyOrgid" clearable class="w-50"></DeptSelect>
                </el-form-item>
                <el-form-item label="配合单位">
                    <el-input v-model="queryForm.cooperateOrgname" placeholder="输入单位名" class="w-50" clearable />
                </el-form-item>

                <el-form-item label="项目性质">
                    <el-select v-model="queryForm.natureCode" placeholder="请选择项目性质" class="w-50" clearable>
                        <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目类别">
                    <ProjectTypeSelect v-model="queryForm.typeId" placeholder="请选择项目类别" class="!w-50" clearable>
                    </ProjectTypeSelect>
                </el-form-item>
                <el-form-item label="项目用途">
                    <el-select v-model="queryForm.purposeIds" multiple collapse-tags placeholder="请选择项目用途" class="w-50" clearable>
                        <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审批状态">
                    <el-select v-model="queryForm.status" placeholder="审批状态" class="w-50" clearable>
                        <el-option v-for="item in project_plan_status" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="执行年度">
                    <DateRangePicker v-model:begin-date="queryForm.beginYear" v-model:end-date="queryForm.endYear"
                        type="yearrange" placeholder="" format="YYYY" value-format="YYYY" class="!w-50"
                        :clearable="false">
                    </DateRangePicker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                </el-form-item>
            </el-form>
        </div>


        <div v-loading="loading">
            <el-table :data="tableData" border v-loading="loading">
                <el-table-column prop="index" label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="项目名称" header-align="center" align="left" min-width="300px" fixed="left">
                    <template #default="{ row }">
                        <div>
                            {{ row.name }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="orgName" label="申报单位" align="center" />
                <el-table-column prop="purpose" label="项目用途" align="center" width="130px">
                    <template #default="{ row }">
                        <span>{{jsonStrParseToJson<INameValue[]>(row.purpose)?.map(t => t.name).join('、')}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="year" label="年度" width="120" align="center" />
                <el-table-column prop="currPlan" label="当年计划" header-align="center" align="right" width="130px"
                    :formatter="(row) => numberFormat(row.currPlan, 2)" />
                <el-table-column prop="applyAdjust" label="计划调整" header-align="center" align="right" width="130px"
                    :formatter="(row) => numberFormat(row.applyAdjust, 2)" />
                <el-table-column prop="applyReason" label="调整理由" header-align="center" align="left" min-width="200px" />
                <el-table-column prop="applyRefused" label="回退理由" header-align="center" align="left"
                    min-width="200px" />
                <el-table-column prop="status" label="状态" align="center" width="130px">
                    <template #default="{ row }">
                        <dict-tag :options="project_plan_status" :value="row.status" />
                    </template>

                </el-table-column>
                <el-table-column label="操作" width="100" align="center" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link icon="view" @click="handleView(row)">查看</el-button>
                        <!-- <el-button type="primary" link icon="guide">流程状态</el-button> -->
                    </template>
                </el-table-column>
                <template #empty>
                    <el-empty description="暂无数据" />
                </template>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>



        <el-dialog title="项目查看" v-model="formViewShow" width="1200px" :close-on-click-modal="false" destroy-on-close
            class="relative">
            <FormView :only-view="true" @close="formViewShow = false"></FormView>
        </el-dialog>
    </div>

</template>

<script lang="ts" setup>
import { dayjs, TableColumnCtx } from 'element-plus';
import DeptSelect from '@/components/Select/DeptSelect.vue';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
import { getAdjustList, getAdjustTotal, getProjectList, getProjectTotal } from '@/api/lib/project/search';
import { jsonStrParseToJson, numberFormat } from '@/utils/common';
import FormView from '../apply/components/formView.vue';
import { useFormStore } from '../apply/components/store/formStore';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_plan_status, project_status } = proxy.useDict("project_purpose", "project_nature", 'project_plan_status', 'project_status');


const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const queryForm = reactive<ProjectAdjustVo>({
    beginYear: dayjs().format('YYYY'),
    endYear: dayjs().format('YYYY'),
})

const total = ref(0)

const tableData = ref<PlanAdjustQueryEntity[]>([])
const tableAmount = ref<PlanAdjustQueryEntity>()
const loading = ref(false)
const handleSearch = () => {
    loading.value = true
    Promise.all([
        getAdjustList(queryForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getAdjustTotal(queryForm).then(res => {
            tableAmount.value = res.data
        })
    ]).finally(() => {
        loading.value = false
    })
}
handleSearch()

const formStore = useFormStore()
const formViewShow = ref(false)
const handleView = (row: PlanAdjustQueryEntity) => {
    formViewShow.value = true
    formStore.formView = row
}

</script>