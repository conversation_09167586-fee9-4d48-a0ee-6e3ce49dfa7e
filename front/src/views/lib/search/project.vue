<template>
    <div class="app-container">
        <div>
            <el-form :inline="true" :model="queryForm" class="mb-4" label-width="auto">
                <el-form-item label="项目名称">
                    <el-input v-model="queryForm.name" placeholder="请输入项目名称" class="w-50" clearable />
                </el-form-item>
                <el-form-item label="申报单位">
                    <DeptSelect v-model="queryForm.applyOrgid" clearable class="w-50"></DeptSelect>
                </el-form-item>
                <el-form-item label="配合单位">
                    <el-input v-model="queryForm.cooperateOrgname" placeholder="输入单位名" class="w-50" clearable />
                </el-form-item>

                <el-form-item label="项目性质">
                    <el-select v-model="queryForm.natureCode" placeholder="请选择项目性质" class="w-50" clearable>
                        <el-option v-for="item in project_nature" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目类别">
                    <ProjectTypeSelect v-model="queryForm.typeId" placeholder="请选择项目类别" class="!w-50" clearable>
                    </ProjectTypeSelect>
                </el-form-item>
                <el-form-item label="项目用途">
                    <el-select v-model="queryForm.purposeIds" multiple collapse-tags placeholder="请选择项目用途" class="w-50" clearable>
                        <el-option v-for="item in project_purpose" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="项目状态">
                    <el-select v-model="queryForm.status" placeholder="项目状态" class="w-50" clearable>
                        <el-option v-for="item in project_status" :label="item.label" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="执行年度">
                    <DateRangePicker v-model:begin-date="queryForm.beginYear" v-model:end-date="queryForm.endYear"
                        type="yearrange" placeholder="" format="YYYY" value-format="YYYY" class="!w-50"
                        :clearable="false">
                    </DateRangePicker>
                </el-form-item>
                <el-form-item label="分篮子">
                    <el-select v-model="queryForm.basketCode" placeholder="请选择项目篮子" class="w-50" clearable>
                        <el-option v-for="item in project_basket_type" :label="item.label"
                            :value="item.value"></el-option>
                    </el-select>
                </el-form-item>


                <el-form-item label="支出类型">
                    <el-select v-model="queryForm.zfType" placeholder="支出类型" class="w-50" clearable>
                        <el-option label="数字化园区支出" value="0" />
                        <el-option label="精细化管理支出" value="1" />
                        <el-option label="政府购买服务" value="2" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <!-- <el-button type="success" @click="handleExport">导出</el-button> -->
                </el-form-item>
            </el-form>
        </div>

        <div v-loading="loading">
            <el-table :data="tableData" border v-loading="loading" show-summary :summary-method="getSummaries">
                <el-table-column prop="index" label="序号" width="60" align="center" fixed="left">
                    <template #default="{ row, $index }">
                        <span>{{ (page.pageNum - 1) * page.pageSize + $index + 1 }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="项目名称" header-align="center" align="left" fixed="left" min-width="300px">
                    <template #default="{ row }">
                        <div>
                            <div>{{ row.name }}</div>
                            <div class="text-xs text-gray-500">编号：{{ row.sn }}</div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="orgName" label="申报单位" align="center" min-width="130px" />
                <el-table-column prop="estAmount" label="项目金额（估算）" header-align="center" align="right" min-width="130px"
                    :formatter="(row) => numberFormat(row.estAmount, 2)" />
                <el-table-column prop="libAmount" label="入库金额" header-align="center" align="right" min-width="130px"
                    :formatter="(row) => numberFormat(row.libAmount, 2)" />
                <el-table-column prop="checkAmount" label="核定金额" header-align="center" align="right" min-width="130px"
                    :formatter="(row) => numberFormat(row.checkAmount, 2)" />
                <el-table-column prop="period" label="项目周期" align="center" min-width="150px">
                    <template #default="{ row }">
                        <div>起：{{ row.beginDate }}</div>
                        <div>止：{{ row.endDate }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" align="center" min-width="130px">
                    <template #default="{ row }">
                        <dict-tag :options="project_status" :value="row.status" />
                    </template>

                </el-table-column>
                <el-table-column prop="basket" label="分篮子" align="center" min-width="130px">
                    <template #default="{ row }">
                        <dict-tag :options="project_basket_type" :value="row.basketCode" />
                    </template>

                </el-table-column>
                <el-table-column label="操作" width="100" align="center" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link icon="view" @click="handleView(row)">查看</el-button>
                        <!-- <el-button type="primary" link icon="guide">流程状态</el-button> -->
                    </template>
                </el-table-column>
                <template #empty>
                    <el-empty description="暂无数据" />
                </template>
            </el-table>
            <pagination v-show="total > 0" :total="total" v-model:page="page.pageNum" v-model:limit="page.pageSize"
                @pagination="handleSearch" />
        </div>


        <el-dialog title="项目查看" v-model="formViewShow" width="1200px" :close-on-click-modal="false" destroy-on-close
            class="relative">
            <FormView :only-view="true" @close="formViewShow = false"></FormView>
        </el-dialog>
    </div>

</template>

<script lang="ts" setup>
import { dayjs, TableColumnCtx } from 'element-plus';
import DeptSelect from '@/components/Select/DeptSelect.vue';
import ProjectTypeSelect from '@/components/Select/ProjectTypeSelect.vue';
import DateRangePicker from '@/components/DatePicker/DateRangePicker.vue';
import { getProjectList, getProjectTotal } from '@/api/lib/project/search';
import { numberFormat } from '@/utils/common';
import FormView from '../apply/components/formView.vue';
import { useFormStore } from '../apply/components/store/formStore';

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_basket_type, project_status } = proxy.useDict("project_purpose", "project_nature", 'project_basket_type', 'project_status');


const page = reactive({
    pageNum: 1,
    pageSize: 10,
})
const queryForm = reactive<ProjectQueryVo>({
    beginYear: dayjs().format('YYYY'),
    endYear: dayjs().format('YYYY'),
})

const total = ref(0)

const tableData = ref<ProjectQueryEntity[]>([])
const tableAmount = ref<ProjectQueryEntity>()
const loading = ref(false)
const handleSearch = () => {
    loading.value = true
    Promise.all([
        getProjectList(queryForm, page).then(res => {
            tableData.value = res.rows ?? []
            total.value = res.total ?? 0
        }),
        getProjectTotal(queryForm).then(res => {
            tableAmount.value = res.data
        })
    ]).finally(() => {
        loading.value = false
    })
}
handleSearch()

const formStore = useFormStore()
const formViewShow = ref(false)
const handleView = (row: ProjectQueryEntity) => {
    formViewShow.value = true
    formStore.formView = row
}

const handleExport = () => {

}

interface SummaryMethodProps<T = typeof tableData.value[number]> {
    columns: TableColumnCtx[]
    data: T[]
}
const getSummaries = (param: SummaryMethodProps) => {
    const { columns, data } = param
    const sums: (string | VNode | number | null)[] = []
    columns.forEach((column, index) => {

        if (index === 0) {
            sums[index] = '合计'
        }
        if (column.property == 'estAmount') {
            sums[index] = numberFormat(tableAmount.value?.estAmount ?? 0, 2)
        }
        if (column.property == 'libAmount') {
            sums[index] = numberFormat(tableAmount.value?.libAmount ?? 0, 2)
        }
        if (column.property == 'checkAmount') {
            sums[index] = numberFormat(tableAmount.value?.checkAmount ?? 0, 2)
        }
    })
    return sums as any
}
</script>