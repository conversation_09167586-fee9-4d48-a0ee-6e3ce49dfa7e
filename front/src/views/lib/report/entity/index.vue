<template>
    <div class="app-container relative">
        <h1 class="text-center leading-9 relative ">
            <el-date-picker v-model="searchForm.year" type="year" class="!w-25 time" :clearable="false"
                value-format="YYYY"></el-date-picker>
            年资金用款计划分部门汇总表
        </h1>
        <div class="absolute right-[20px] top-12">
            <el-button @click="handleDownloadExcel">导出</el-button>
            <el-button type="primary" @click="handlePrint">打印</el-button>
        </div>
        <div v-loading="loading">
            <vxe-table ref="tableRef" :data="tableData" :height="tableHeight" show-footer :footer-data="getFooterData"
                border>
                <vxe-column type="seq" title="序号" align="center" width="50"></vxe-column>
                <vxe-column field="assessOrgname" title="考核主体" align="center"></vxe-column>
                <vxe-column field="earlyAmount" title="年初计划金额" header-align="center" align="right"></vxe-column>
                <vxe-column field="adjustAmount" title="调整计划金额" header-align="center" align="right"></vxe-column>
                <vxe-column field="releasedAmount" title="已下达资金计划" header-align="center" align="right"></vxe-column>
                <vxe-column field="toBeReleasedAmount" title="待下达资金计划" header-align="center" align="right"></vxe-column>
                <vxe-column field="executedAmount" title="已执行" header-align="center" align="right"></vxe-column>
                <template #empty>
                    <el-empty description="暂无数据" />
                </template>
            </vxe-table>
        </div>

    </div>
</template>

<script setup lang="ts">
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs } from 'element-plus';
import { debounce, get, throttle } from 'lodash';
import { getAssessmentList } from '@/api/lib/project/assessment';
import { sumAmount } from '@/utils/common';
// @ts-ignore
VxeUI.use(VxePcUI)

const { proxy } = getCurrentInstance() as { proxy: any };
const tableHeight = ref(window.innerHeight - 210)

const searchForm = reactive({
    year: String(dayjs().year()),
})

const getNumbericalYear = computed(() => {
    return parseInt(searchForm.year)
})

const getFooterData = computed<VxeTablePropTypes.FooterData>(() => {
    const earlyAmount = sumAmount(tableData.value.map(t => t.earlyAmount))
    const adjustAmount = sumAmount(tableData.value.map(t => t.adjustAmount))
    const executedAmount = sumAmount(tableData.value.map(t => t.executedAmount))
    const releasedAmount = sumAmount(tableData.value.map(t => t.releasedAmount))
    const toBeReleasedAmount = sumAmount(tableData.value.map(t => t.toBeReleasedAmount))
    return [
        {
            assessOrgname: '合计',
            earlyAmount: earlyAmount ?? 0,
            adjustAmount: adjustAmount ?? 0,
            executedAmount: executedAmount ?? 0,
            releasedAmount: releasedAmount ?? 0,
            toBeReleasedAmount: toBeReleasedAmount ?? 0,
        }
    ]
})
const loading = ref(false)
const tableData = ref<any[]>([])
watch(() => searchForm.year, throttle(async () => {
    loading.value = true
    try {
        const res = await getAssessmentList(getNumbericalYear.value)
        tableData.value = res.data
    } finally {
        loading.value = false
    }



}, 1000, { leading: true }), { immediate: true })

const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
    tableRef.value?.getPrintHtml().then(res => {
        const headHtml = `
        <h1 style="text-align: center;">
        ${getNumbericalYear.value}年资金用款计划分部门汇总表
        </h1>`

        const html = res.html
        tableRef.value?.print({
            html: headHtml + html,
        })
    })
}

const handleDownloadExcel = () => {
    proxy.download('/project/assessment/download', {
        year: getNumbericalYear.value,
    }, `资金用款计划分部门汇总表${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>

<style scoped>

:deep(.time .el-input__inner){color: #000;font-size: 24px;}
</style>