<template>
  <h1 class="text-center leading-9 relative">
    <div class="cursor-pointer inline-block" @click="type = type == 1 ? 2 : 1">
      <el-date-picker :modelValue="String(year)" @update:modelValue="(val: string) => year = parseInt(val)" type="year"
        class="!w-25 time" :clearable="false" value-format="YYYY"></el-date-picker>
      {{ type == 1 ? `年专项发展资金计划及执行汇总表` : `年专项发展资金计划（调整）及执行汇总表` }}
      <br />
      （截至{{ year }}.12.31）
    </div>
  </h1>
  <div class="absolute right-[20px] top-12">
    <!-- <el-button type="primary" @click="handleDownloadExcel">导出</el-button> -->
    <el-button type="primary" @click="handlePrint">打印</el-button>
  </div>
  <vxe-table ref="tableRef" :data="getTbaleData" style="width: 100%" border :cell-class-name="bindCellClassName"
    :header-cell-class-name="bindHeaderCellClassName">
    <vxe-column field="name" title="项目分类" header-align="center"></vxe-column>
    <vxe-colgroup title="用款计划(万元)" align="center">
      <vxe-column field="jcssjswh" title="基础设施建设维护" align="center" width="110"></vxe-column>
      <vxe-column field="aq" title="安全" align="center" width="150"></vxe-column>
      <vxe-column field="lshb" title="绿色环保" align="center" width="150"></vxe-column>
      <vxe-column field="zskc" title="招商科创" align="center" width="150"></vxe-column>
      <vxe-column field="zhyq" title="智慧园区" align="center" width="150"></vxe-column>
      <vxe-column field="qtgl" title="其他管理" align="center" width="150"></vxe-column>
      <vxe-column field="byj" title="备用金" align="center" width="150"></vxe-column>
      <vxe-column field="zj" title="总计" align="center" width="150"></vxe-column>
    </vxe-colgroup>
    <vxe-column field="yzx" title="已执行" align="center" width="150"></vxe-column>
    <vxe-column field="zxl" title="执行率" align="center" width="150"></vxe-column>
  </vxe-table>
</template>

<script setup lang="tsx">
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'

// @ts-ignore
VxeUI.use(VxePcUI)

const year = defineModel('year')

const type = ref(1)

const getTbaleData = computed(() => {
  if (year.value == 2020) {
    if (type.value == 1) {
      return [
        { name: '投资建设项目', jcssjswh: '8531', aq: '604', lshb: '54', zskc: '', zhyq: '', qtgl: '2701', byj: '', zj: '11890', yzx: '12010', zxl: '101.0%' },
        { name: '采购项目', jcssjswh: '4921', aq: '8903', lshb: '1288', zskc: '120', zhyq: '38', qtgl: '3043', byj: '', zj: '18313', yzx: '15463', zxl: '84.4%' },
        { name: '公共管理补贴', jcssjswh: '163', aq: '8155', lshb: '163', zskc: '', zhyq: '', qtgl: '2967', byj: '', zj: '11448', yzx: '11999', zxl: '104.8%' },
        { name: '功能性补贴', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '1750', byj: '', zj: '1750', yzx: '1716', zxl: '98.0%' },
        { name: '政策性扶持', jcssjswh: '', aq: '200', lshb: '3600', zskc: '5000', zhyq: '1500', qtgl: '', byj: '', zj: '10300', yzx: '7348', zxl: '71.3%' },
        { name: '预备金', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '', byj: '293', zj: '293', yzx: '0', zxl: '0.0%' },
        { name: '总计', jcssjswh: '13615', aq: '17862', lshb: '5105', zskc: '5120', zhyq: '1538', qtgl: '10460', byj: '293', zj: '54000', yzx: '--', zxl: '--' },
        { name: '已执行', jcssjswh: '14211', aq: '16561', lshb: '5151', zskc: '1672', zhyq: '1797', qtgl: '9145', byj: '0', zj: '--', yzx: '48543', zxl: '--' },
        { name: '执行率', jcssjswh: '104.4%', aq: '92.7%', lshb: '100.9%', zskc: '32.6%', zhyq: '116.8%', qtgl: '87.4%', byj: '0.0%', zj: '--', yzx: '--', zxl: '89.9%' },
      ]
    }
    if (type.value == 2) {
      return [
        { name: '投资建设项目', jcssjswh: '10414', aq: '685', lshb: '54', zskc: '', zhyq: '29', qtgl: '2354', byj: '', zj: '13535', yzx: '12010', zxl: '88.7%' },
        { name: '采购项目', jcssjswh: '4670', aq: '8552', lshb: '1063', zskc: '85', zhyq: '46', qtgl: '2878', byj: '', zj: '17294', yzx: '15463', zxl: '89.4%' },
        { name: '公共管理补贴', jcssjswh: '163', aq: '8424', lshb: '273', zskc: '', zhyq: '', qtgl: '2996', byj: '', zj: '11856', yzx: '11999', zxl: '101.2%' },
        { name: '功能性补贴', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '1728', byj: '', zj: '1728', yzx: '1716', zxl: '99.3%' },
        { name: '政策性扶持', jcssjswh: '', aq: '190', lshb: '3834', zskc: '1637', zhyq: '1957', qtgl: '', byj: '', zj: '7617', yzx: '7348', zxl: '96.5%' },
        { name: '预备金', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '', byj: '195', zj: '195', yzx: '0', zxl: '0.0%' },
        { name: '总计', jcssjswh: '15247', aq: '17851', lshb: '5223', zskc: '1722', zhyq: '2031', qtgl: '9955', byj: '195', zj: '52400', yzx: '--', zxl: '--' },
        { name: '已执行', jcssjswh: '14211', aq: '16561', lshb: '5151', zskc: '1672', zhyq: '1797', qtgl: '9145', byj: '0', zj: '--', yzx: '48543', zxl: '--' },
        { name: '执行率', jcssjswh: '93.2%', aq: '92.8%', lshb: '98.6%', zskc: '97.1%', zhyq: '88.5%', qtgl: '91.9%', byj: '0.0%', zj: '--', yzx: '--', zxl: '92.6%' },
      ]
    }
  }
  if (year.value == 2021) {
    if (type.value == 1) {
      return [
        { name: '投资建设项目', jcssjswh: '6881', aq: '240', lshb: '', zskc: '', zhyq: '556', qtgl: '505', byj: '', zj: '8182', yzx: '9278', zxl: '113.4%' },
        { name: '采购项目', jcssjswh: '5385', aq: '8345', lshb: '613', zskc: '216', zhyq: '843', qtgl: '1748', byj: '', zj: '17150', yzx: '14913', zxl: '87.0%' },
        { name: '公共管理补贴', jcssjswh: '235', aq: '4107', lshb: '277', zskc: '', zhyq: '', qtgl: '2806', byj: '', zj: '7425', yzx: '7020', zxl: '94.5%' },
        { name: '功能性补贴', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '1920', byj: '', zj: '1920', yzx: '1514', zxl: '78.8%' },
        { name: '政策性扶持', jcssjswh: '', aq: '200', lshb: '7000', zskc: '2000', zhyq: '800', qtgl: '', byj: '', zj: '10000', yzx: '8595', zxl: '85.9%' },
        { name: '预备金', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '', byj: '5322', zj: '5322', yzx: '0', zxl: '0.0%' },
        { name: '总计', jcssjswh: '12502', aq: '12893', lshb: '7890', zskc: '2216', zhyq: '2199', qtgl: '6978', byj: '5322', zj: '50000', yzx: '--', zxl: '--' },
        { name: '已执行', jcssjswh: '12347', aq: '10463', lshb: '8201', zskc: '454', zhyq: '3474', qtgl: '6380', byj: '0', zj: '--', yzx: '41320', zxl: '--' },
        { name: '执行率', jcssjswh: '98.8%', aq: '81.2%', lshb: '103.9%', zskc: '20.5%', zhyq: '158%', qtgl: '91.4%', byj: '0.0%', zj: '--', yzx: '--', zxl: '82.6%' },
      ]
    }
    if (type.value == 2) {
      return [
        { name: '投资建设项目', jcssjswh: '6476', aq: '65', lshb: '', zskc: '', zhyq: '1136', qtgl: '505', byj: '', zj: '8182', yzx: '9278', zxl: '113.4%' },
        { name: '采购项目', jcssjswh: '5313', aq: '6859', lshb: '702', zskc: '159', zhyq: '878', qtgl: '1679', byj: '', zj: '15591', yzx: '14913', zxl: '95.7%' },
        { name: '公共管理补贴', jcssjswh: '212', aq: '3804', lshb: '240', zskc: '', zhyq: '', qtgl: '2846', byj: '', zj: '7103', yzx: '7020', zxl: '98.8%' },
        { name: '功能性补贴', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '1716', byj: '', zj: '1716', yzx: '1514', zxl: '88.2%' },
        { name: '政策性扶持', jcssjswh: '', aq: '258', lshb: '7000', zskc: '773', zhyq: '915', qtgl: '', byj: '', zj: '8946', yzx: '8595', zxl: '96.1%' },
        { name: '预备金', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '', byj: '5322', zj: '5322', yzx: '0', zxl: '0.0%' },
        { name: '总计', jcssjswh: '12001', aq: '10986', lshb: '7943', zskc: '932', zhyq: '2929', qtgl: '6746', byj: '5322', zj: '46877', yzx: '--', zxl: '--' },
        { name: '已执行', jcssjswh: '12347', aq: '10463', lshb: '8201', zskc: '454', zhyq: '3474', qtgl: '6380', byj: '0', zj: '--', yzx: '41320', zxl: '--' },
        { name: '执行率', jcssjswh: '102.9%', aq: '95.2%', lshb: '103.3%', zskc: '48.7%', zhyq: '118.6%', qtgl: '94.6%', byj: '0.0%', zj: '--', yzx: '--', zxl: '88.1%' },
      ]
    }
  }
  if (year.value == 2022) {
    if (type.value == 1) {
      return [
        { name: '投资建设项目', jcssjswh: '8826', aq: '1774', lshb: '2500', zskc: '2000', zhyq: '3230', qtgl: '295', byj: '', zj: '18625', yzx: '12081', zxl: '64.86%' },
        { name: '采购项目', jcssjswh: '5664', aq: '8472', lshb: '768', zskc: '43', zhyq: '2565', qtgl: '2434', byj: '', zj: '19946', yzx: '15113', zxl: '75.77%' },
        { name: '公共管理补贴', jcssjswh: '', aq: '3570', lshb: '7', zskc: '', zhyq: '', qtgl: '2825', byj: '', zj: '6402', yzx: '5757', zxl: '89.93%' },
        { name: '功能性补贴', jcssjswh: '', aq: '', lshb: '', zskc: '50', zhyq: '', qtgl: '2332', byj: '', zj: '2382', yzx: '2237', zxl: '93.91%' },
        { name: '政策性扶持', jcssjswh: '', aq: '200', lshb: '2000', zskc: '4000', zhyq: '1000', qtgl: '', byj: '', zj: '7200', yzx: '6468', zxl: '89.83%' },
        { name: '预备金', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '', byj: '946', zj: '946', yzx: '0', zxl: '0.0%' },
        { name: '总计', jcssjswh: '14490', aq: '14016', lshb: '5275', zskc: '6093', zhyq: '6795', qtgl: '7886', byj: '946', zj: '55500', yzx: '--', zxl: '--' },
        { name: '已执行', jcssjswh: '10646', aq: '10668', lshb: '6428', zskc: '2604', zhyq: '4419', qtgl: '4419', byj: '0', zj: '--', yzx: '41657', zxl: '--' },
        { name: '执行率', jcssjswh: '73.47%', aq: '76.11%', lshb: '121.86%', zskc: '42.74%', zhyq: '65.03%', qtgl: '65.03%', byj: '0.0%', zj: '--', yzx: '--', zxl: '75.10%' },
      ]
    }
    if (type.value == 2) {
      return [
        { name: '投资建设项目', jcssjswh: '6532', aq: '2275', lshb: '1198', zskc: '1986', zhyq: '2961', qtgl: '350', byj: '', zj: '15303', yzx: '12081', zxl: '78.95%' },
        { name: '采购项目', jcssjswh: '5466', aq: '5529', lshb: '699', zskc: '23', zhyq: '2064', qtgl: '1719', byj: '', zj: '15499', yzx: '15113', zxl: '97.51%' },
        { name: '公共管理补贴', jcssjswh: '', aq: '3048', lshb: '7', zskc: '', zhyq: '', qtgl: '2822', byj: '', zj: '5876', yzx: '5757', zxl: '97.97%' },
        { name: '功能性补贴', jcssjswh: '', aq: '', lshb: '', zskc: '7', zhyq: '', qtgl: '2240', byj: '', zj: '2247', yzx: '2237', zxl: '99.55%' },
        { name: '政策性扶持', jcssjswh: '', aq: '329', lshb: '4488', zskc: '1670', zhyq: '241', qtgl: '', byj: '', zj: '6728', yzx: '6468', zxl: '96.14%' },
        { name: '预备金', jcssjswh: '', aq: '', lshb: '', zskc: '', zhyq: '', qtgl: '', byj: '0', zj: '0', yzx: '0', zxl: '--' },
        { name: '总计', jcssjswh: '11999', aq: '11181', lshb: '6392', zskc: '3686', zhyq: '5266', qtgl: '7131', byj: '0', zj: '45653', yzx: '--', zxl: '--' },
        { name: '已执行', jcssjswh: '10646', aq: '10668', lshb: '6257', zskc: '6257', zhyq: '4590', qtgl: '6892', byj: '0', zj: '--', yzx: '41657', zxl: '--' },
        { name: '执行率', jcssjswh: '88.72%', aq: '95.41%', lshb: '97.89%', zskc: '97.89%', zhyq: '87.16%', qtgl: '96.65%', byj: '0.0%', zj: '--', yzx: '--', zxl: '91.25%' },
      ]
    }
  }
})

//为第七列添加背景色
const bindCellClassName: VxeTablePropTypes.CellClassName<any> = ({ row, column, rowIndex, $columnIndex }) => {
  if ($columnIndex == 8) {
    if (rowIndex <= 6) {
      return 'cell-zj-bg'
    }
  }
  if (rowIndex == 6) {
    if ($columnIndex == 0) {
      return 'cell-zj-bg cell-align-center'
    }
    if ($columnIndex <= 7) {
      return 'cell-zj-bg'
    }
  }
  if (rowIndex > 6) {
    if ($columnIndex == 0) {
      return 'cell-align-center'
    }
  }
}
const bindHeaderCellClassName: VxeTablePropTypes.HeaderCellClassName<any> = ({ column, $rowIndex, $columnIndex }) => {
  if ($rowIndex == 1 && $columnIndex == 7) {
    return 'header-cell-zj-bg'
  }
  return 'project-header-custom';
}

const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
  tableRef.value?.getPrintHtml().then(res => {
    const headHtml = `
        <h1 style="text-align: center;">
        ${type.value == 1 ? `年专项发展资金计划及执行汇总表` : `年专项发展资金计划（调整）及执行汇总表`}
        <br />
        （截至${year.value}.12.31）
        </h1>`

    const html = res.html
    tableRef.value?.print({
      html: headHtml + html,
    })
  })
}

const handleDownloadExcel = () => {
}
</script>

<style scoped>
:deep(.cell-zj-bg) {
  background-color: #f0f9eb;
  color: black;
  font-size: 16px;
  font-weight: 600;
}

:deep(.header-cell-zj-bg) {
  background-color: #f0f9eb;
}

:deep(.cell-align-center) {
  text-align: center;
}

:deep(.project-header-custom) {
  background: linear-gradient(to bottom, #fafdff, #deeefb) !important;
  /* 深蓝渐变 */
  text-align: center;
  border-right: 1px solid #d9e0eb !important;
}
:deep(.time .el-input__inner){color: #000;font-size: 24px;}
</style>