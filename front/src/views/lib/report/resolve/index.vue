<template>
  <div class="app-container relative" v-loading="loading">
    <template v-if="getNumbericalYear >= 2020 && getNumbericalYear <= 2022">
      <old20202022 v-model:year="getNumbericalYear"></old20202022>
    </template>
    <template v-else>
      <h1 class="text-center leading-9 relative ">
        <el-date-picker v-model="searchForm.year" type="year" class="!w-25 time" :clearable="false" value-format="YYYY"
          :disabled-date="(date: any) => dayjs(date).isBefore(dayjs('2020-01-01'), 'year')"></el-date-picker>
        年专项发展资金计划及执行汇总表
        <br />
        {{ getNumbericalYear >= dayjs().year() ? `（截至${getNumbericalYear}.${dayjs().format('M.D')}）` :
          `（截至${getNumbericalYear}.12.31）` }}
      </h1>

      <div class="custom-style flex justify-between mb-[10px]">
        <el-radio-group v-model="value">
          <el-radio-button v-for="item in options" :value="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group>
        <!-- <el-segmented v-model="value" :options="options" size="middle" /> -->
        <div>
          <el-button @click="handleDownloadExcel">导出</el-button>
          <el-button type="primary" @click="handlePrint">打印</el-button>
        </div>
      </div>
      <div>
        <vxe-table ref="tableRef" :data="getTableData" style="width: 100%" border :cell-class-name="bindCellClassName"
          :header-cell-class-name="bindHeaderCellClassName">
          <vxe-column field="项目分类" title="项目分类" min-width="140">
            <template #default="{ row }">
              <div v-if="row.项目分类 == '一、投资建设类'">
                <span class="text-4 font-bold">{{ row.项目分类 }}</span>
              </div>
              <div v-else-if="row.项目分类 == '二、运行维护类'">
                <span class="text-4 font-bold">{{ row.项目分类 }}</span>
              </div>
              <div v-else-if="row.项目分类 == '（一）公共设施运行维护'" class="pl-2">
                <span>{{ row.项目分类 }}</span>
              </div>
              <div v-else-if="row.项目分类 == '（二）公共管理'" class="pl-2">
                <span>{{ row.项目分类 }}</span>
              </div>
              <div v-else-if="row.项目分类 == '（三）功能性补贴'" class="pl-2">
                <span>{{ row.项目分类 }}</span>
              </div>
              <div v-else-if="row.项目分类 == '三、政策性扶持'">
                <span class="text-4 font-bold">{{ row.项目分类 }}</span>
              </div>
              <div v-else>
                {{ row.项目分类 }}
              </div>
            </template>
          </vxe-column>
          <vxe-colgroup title="用款计划(万元)" align="center">

            <vxe-column v-for="item in project_purpose" :field="item.value" :title="item.label" align="center"
              width="120"></vxe-column>
            <vxe-column field="类别总计" title="类别总计" align="center" width="120"></vxe-column>
          </vxe-colgroup>
          <vxe-column field="占比" title="占比" align="center" width="120"></vxe-column>
          <vxe-column field="已执行" title="已执行" align="center" width="120"></vxe-column>
          <vxe-column field="执行率" title="执行率" align="center" width="120"></vxe-column>
          <vxe-column field="涉及数字化园区支出" title="涉及数字化园区支出" align="center" width="120"></vxe-column>
          <vxe-column field="数字化已执行" title="已执行" align="center" width="120"></vxe-column>
          <vxe-column field="数字化执行率" title="执行率" align="center" width="120"></vxe-column>
        </vxe-table>
      </div>

    </template>


  </div>
</template>
<script lang="ts" setup>
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import { dayjs, type TabsPaneContext } from 'element-plus'
import { checkIsNumber, dateFormat } from '@/utils/common';
import old20202022 from './component/old2020-2022.vue';
import { getBreakDownList } from '@/api/lib/project/breakDown';

// @ts-ignore
VxeUI.use(VxePcUI)

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_type, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_type', 'project_basket_type');


const value = ref('formal')
const options = [
  { label: '初步计划', value: 'early' },
  { label: '正式计划', value: 'formal' },
  { label: '调整计划', value: 'adjust' },
]

const searchForm = reactive({
  year: String(dayjs().year()),
})

const getNumbericalYear = computed<number>({
  set(val) {
    searchForm.year = String(val)
  },
  get() {
    return parseInt(searchForm.year)
  }
})



// 定义数据
const tableData = ref<{
  early: (string | number)[]
  formal: (string | number)[]
  adjust: (string | number)[]
}>({
  early: [],
  formal: [],
  adjust: []
});
const getTableData = computed(() => {
  if (value.value == 'early') {
    return tableData.value.early
  }
  if (value.value == 'formal') {
    return tableData.value.formal
  }
  if (value.value == 'adjust') {
    return tableData.value.adjust
  }
  return []
})
const loading = ref(false)
watch(() => getNumbericalYear.value, (val) => {
  if (val >= 2023) {
    loading.value = true
    getBreakDownList(getNumbericalYear.value).then(res => {
      tableData.value = res.data ?? { early: [], formal: [], adjust: [] }
    }).finally(() => {
      loading.value = false
    })
  }
}, { immediate: true })


//为第七列添加背景色
const bindCellClassName: VxeTablePropTypes.CellClassName<any> = ({ row, column, rowIndex, $columnIndex }) => {
  if ($columnIndex == 1 + project_purpose.value.length) {
    if (rowIndex <= project_type.value.length) {
      return 'cell-zj-bg'
    }
  }
  if (rowIndex == 1 + project_purpose.value.length) {
    if ($columnIndex <= project_type.value.length) {
      return 'cell-zj-bg cell-align-center'
    }
  }
  if (rowIndex > project_purpose.value.length) {
    if ($columnIndex == 0) {
      return 'cell-align-center'
    }
  }
}
const bindHeaderCellClassName: VxeTablePropTypes.HeaderCellClassName<any> = ({ column, $rowIndex, $columnIndex }) => {
  if ($rowIndex == 1 && $columnIndex == project_purpose.value.length) {
    return 'header-cell-zj-bg'
  }
  return 'project-header-custom';
}

const tableRef = ref<VxeTableInstance>()
const handlePrint = () => {
  tableRef.value?.getPrintHtml().then(res => {
    const subTitle = getNumbericalYear.value >= dayjs().year() ? `（截至${getNumbericalYear.value}.${dayjs().format('M.D')}）` : `（截至${getNumbericalYear.value}.12.31）`
    const headHtml = `
        <h1 style="text-align: center;">
        ${getNumbericalYear.value}年专项发展资金计划及执行汇总表
        <br />
  ${subTitle}
        </h1>`

    const html = res.html
    // 使用 DOMParser 解析 HTML 字符串
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    // 获取解析后的 DOM 元素
    const tableEl = doc.body.firstElementChild! as HTMLTableElement;
    const tableTrs = Array.from(tableEl.querySelectorAll('tbody tr')) as HTMLTableRowElement[];

    tableTrs.forEach((tr, index) => {
      if ([0, 1 ,5].includes(index)) {
        const td = tr.querySelector('td')
        td?.querySelector('div')?.setAttribute('style', 'font-weight: 700;')
      }
      if ([2, 3 ,4].includes(index)) {
        const td = tr.querySelector('td')
        td?.querySelector('div')?.setAttribute('style', 'padding-left: 8px;')
      }
      if (index >= 6) {
        const td = tr.querySelector('td')
        td?.querySelector('div')?.setAttribute('style', 'text-align: center;')
      }

    })
    tableRef.value?.print({
      html: headHtml + tableEl.outerHTML,
    })
  })
}

const handleDownloadExcel = () => {
  proxy.download('/project/breakDown/download', {
    year: getNumbericalYear.value,
    type: value.value
  }, `资金计划分解表${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}

</script>

<style scoped>
.custom-style .el-segmented {
  --el-segmented-item-selected-color: white;
  --el-segmented-item-selected-bg-color: #007aff;
  --el-border-radius-base: 8px;
}

:deep(.cell-zj-bg) {
  background-color: #f0f9eb;
  color: black;
  font-size: 16px;
  font-weight: 600;
}

:deep(.header-cell-zj-bg) {
  background-color: #f0f9eb;
}

:deep(.cell-align-center) {
  text-align: center;
}

:deep(.project-header-custom) {
  background: linear-gradient(to bottom, #fafdff, #deeefb) !important;
  /* 深蓝渐变 */
  text-align: center;
  border-right: 1px solid #d9e0eb !important;
}

:deep(.time .el-input__inner) {
  color: #000;
  font-size: 24px;
}
</style>
