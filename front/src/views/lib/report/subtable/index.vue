<template>
    <div class="app-container relative" v-loading="loading">
        <h1 class="text-center leading-9 relative ">
            <el-date-picker v-model="searchForm.year" type="year" class="!w-25 time" :clearable="false"
                value-format="YYYY"
                :disabled-date="(date: Date) => !getYearList.includes(dayjs(date).year())"></el-date-picker>
            年度化工区专项发展资金用款计划
        </h1>
        <div>
            <div class="flex items-center gap-2">
                <!-- <el-select v-model="unit" clearable class="w-45"
                    :disabled="!auth.hasPermi('fund:usagePlan:org:change')">
                    <el-option v-for="item in deptList" :label="item.label" :value="item.value"></el-option>
                </el-select> -->
                <DeptSelect v-model="unit" clearable class="!w-45"
                    :disabled="!auth.hasPermi('fund:usagePlan:org:change')"></DeptSelect>
                <el-radio-group v-model="unitType">
                    <el-radio-button value="1">考核主体</el-radio-button>
                    <el-radio-button value="2">上报单位</el-radio-button>
                </el-radio-group>
                <right-toolbar :search="false" :columns="columns" @queryTable="search">
                    <el-tooltip class="item" effect="dark" content="打印" placement="top">
                        <el-button class="ml-3" circle icon="Printer" @click="handlePrint" />
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
                        <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
                    </el-tooltip>
                </right-toolbar>
            </div>
            <div class="mt-2">
                <vxe-table ref="tableRef" :data="tableData" :height="tableHeight" show-header show-footer
                    :footer-data="footerData" :tree-config="{ parentField: 'parentId', childrenField: 'child' }"
                    style="width: 100%" border>
                    <vxe-column field="index" title="序号" align="center" width="50"></vxe-column>
                    <template v-for="col in columns" :key="col.prop">
                        <vxe-column :title="col.label" :field="col.prop" header-align="center"
                            :align="col.prop === 'projName' ? 'left' : (amountProps.includes(col.prop) ? 'right' : 'center')"
                            :min-width="handleWidth(col).width" :visible="col.visible">
                            <template #default="{ row }">
                                <template v-if="col.prop == 'projName'">
                                    <div>
                                        {{ row.projName }}
                                    </div>
                                    <div>
                                        {{ row?.projectInfo?.projectSn?.formalSn ?? row?.projectInfo?.projectSn?.tempSn }}
                                    </div>
                                </template>
                                <template v-else-if="amountProps.includes(col.prop)">
                                    <span v-if="col.prop == 'currPayed'">{{ row[col.prop] }}</span>
                                    <span v-else>{{ roundNumber2(row[col.prop]) }}</span>
                                </template>
                                <template v-else>
                                    {{ row[col.prop] }}
                                </template>

                            </template>
                        </vxe-column>
                    </template>
                    <template #empty>
                        <el-empty description="暂无数据" />
                    </template>
                </vxe-table>
            </div>
        </div>

    </div>
</template>

<script setup lang="tsx">
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import DeptSelect from '@/components/Select/DeptSelect.vue'
import { dayjs } from 'element-plus';
import { flatMap, template, throttle } from 'lodash';
import { getUsagePlanDept, getUsagePlanList } from '@/api/lib/project/usagePlan';
import { getList } from '@/api/lib/report/usagePlan';
import auth from '@/plugins/auth';
// @ts-ignore
import useUserStore from '@/store/modules/user'
import { numberFormat, roundNumber2, sumAmount } from '@/utils/common';
import { useDeptStore } from '@/components/Select/deptStore';
// @ts-ignore
VxeUI.use(VxePcUI)

const { proxy } = getCurrentInstance() as { proxy: any };
const tableHeight = ref(window.innerHeight - 250)

const userStore = useUserStore()

const loading = ref(false)
const unit = ref([100, 396].includes(userStore.deptId) ? '' : userStore.deptId)
const unitType = ref('1')
const searchForm = reactive({
    year: String(dayjs().year()),
    assessOrgid: '',
    submitOrgid: '',
})
const getNumbericalYear = computed(() => {
    return parseInt(searchForm.year)
})

const columns = ref<any[]>([]);
const amountProps = [
    'estAmount',
    'libAmount',
    'checkAmount',
    'contractAmount',
    'prevAmount',
    'prevAdjust',
    'prevPayed',
    'currAmount',
    'currEarly',
    'currFormal',
    'currAdjust',
    'currPayed'
]
const usagPlanList = ref<IUsagePlan[]>([])
const getYearList = computed(() => {
    return usagPlanList.value.map(item => item.year)
})
const tableData = ref<IUsagePlanItem[]>([])
await getList().then(res => {
    usagPlanList.value = res.data ?? [];
    const columns1 = usagPlanList.value.find(t => t.year === getNumbericalYear.value)?.columns
    if (columns1 && columns.value.length < 1) {
        columns.value = JSON.parse(columns1)
    }
})
// const deptList = ref<{ label: string, value: string }[]>([])


const searchDeptList = async () => {
    // const deptRes = await getUsagePlanDept({ year: searchForm.year })
    // deptList.value = Object.keys(deptRes.data).map(key => ({ label: deptRes.data[key], value: key }))
    // if (deptList.value.find(t => t.value == '5479')) {
    //     unit.value = '5479'
    // } else {
    //     unit.value = deptList.value[0].value
    // }
}
// await searchDeptList()
const search = async () => {
    const data: any = {
        year: getNumbericalYear.value
    }
    if (unitType.value == '1') {
        data.assessOrgid = unit.value
    } else {
        data.submitOrgid = unit.value
    }
    loading.value = true
    try {
        const columns1 = usagPlanList.value.find(t => t.year === getNumbericalYear.value)?.columns
        if (columns1 && columns.value.length < 1) {
            columns.value = JSON.parse(columns1)
        }
        const res = await getUsagePlanList(data)
        tableData.value = res.data?.map((item, index) => {
            return {
                ...item,
                index: index + 1
            }
        }) ?? []
    } finally {
        loading.value = false
    }
}
search()


watch(() => searchForm.year, throttle(async () => {
    await searchDeptList()

    await search()
}, 1000, { leading: true }))

watch(() => [unit.value, unitType.value], throttle(async () => {
    await search()
}, 1000, { leading: true }), { immediate: true })


const tableRef = ref<VxeTableInstance>()

const footerData = computed(() => {
    if (columns.value.length > 0 && tableData.value.length > 0) {
        const data = columns.value.filter(t => t.visible && amountProps.includes(t.prop)).map(col => {
            if (col.prop == 'currPayed') {
                return {
                    prop: col.prop,
                    sum: sumAmount(tableData.value.map(t => t[col.prop]))
                }
            } else {
                return {
                    prop: col.prop,
                    sum: roundNumber2(sumAmount(tableData.value.map(t => t[col.prop])) ?? 0)
                }
            }

        }).reduce((acc, { prop, sum }) => {
            acc[prop] = sum;
            return acc;
        }, {} as any)
        return [
            {
                ...data,
                projName: '总计',

            }
        ]
    }
    return [{ projName: '总计' }]
})

const handleWidth = (col: typeof columns.value[0]) => {
    if (col.prop == 'projName') {
        return {
            width: '300px',
        }
    } else if (col.prop == 'remark1' || col.prop == 'remark2') {
        return {
            width: '200px'
        }
    } else {
        return {
            width: '120px',
        }
    }
}

const handlePrint = () => {
    tableRef.value?.getPrintHtml().then(res => {
        const headHtml = `
        <h1 style="text-align: center;">
        ${getNumbericalYear.value}年度化工区专项发展资金用款计划
        </h1>`

        const html = res.html
        tableRef.value?.print({
            html: headHtml + html,
        })
    })
}

const handleDownloadExcel = () => {
    const data: any = {
        year: getNumbericalYear.value
    }
    if (unitType.value == '1') {
        data.assessOrgid = unit.value
    } else {
        data.submitOrgid = unit.value
    }
    proxy.download('/project/usagePlan/bmDownload', {
        ...data
        // type: value.value
    }, `资金计划分解表${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>
<style scoped>
:deep(.time .el-input__inner) {
    color: #000;
    font-size: 24px;
}
</style>