<template>
    <div>
        <div>
            <el-form-item label="按部门筛选" class="flex-wrap">
                <el-radio-group v-model="dept" class="gap-[5px_0]" @change="stepNo = undefined; search()">
                    <el-radio-button v-for="item in deptList" :label="`${item.deptName}(${item.projCount})`"
                        :value="item.deptId" />
                </el-radio-group>
            </el-form-item>
            <el-form-item label="按阶段筛选">
                <el-radio-group v-model="stepNo" class="gap-[5px_0]" @change="dept = undefined; search()">
                    <el-radio-button v-for="item in project_imp_stage_type" :label="item.remark + `(${stepMap.get(item.value) ?? 0})`" :value="item.value" />
                </el-radio-group>
            </el-form-item>
        </div>
        <div class="mt-3">
            <el-table :data="dataList" border height="500px">
                <el-table-column label="项目名称" prop="projName" header-align="center" align="left"></el-table-column>
                <el-table-column label="项目编号" align="center" width="150px">
                    <template #default="{ row }">
                        <el-input v-model="row.projCode" maxlength="50"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="项目阶段" align="center" width="200px">
                    <template #default="{ row }">
                        <el-select v-model="row.stepNo" clearable>
                            <el-option v-for="item in project_imp_stage_type" :label="item.remark"
                                :value="parseInt(item.value)"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <template #empty>
                    <el-empty description="暂无数据" />
                </template>
            </el-table>
        </div>
        <div class="flex justify-center mt-5">
            <el-button type="primary" @click="save">保存</el-button>
            <el-button @click="$emit('cancel')">取消</el-button>
        </div>

    </div>
</template>

<script lang="ts" setup>
import { piList, saveOrUpdatePiput, stepList } from '@/api/lib/report/important';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['close', 'cancel'])

const props = defineProps<{
    year: number
    deptList: any[]
}>()



const { proxy } = getCurrentInstance() as { proxy: any };
const { project_imp_stage_type } = proxy.useDict("project_imp_stage_type");

const dept = ref(props.deptList.at(0).deptId)
const stepNo = ref()
const dataList = ref<ProjectImpInputVo[]>([])
const search = () => {
    piList({ year: props.year, deptId: dept.value, stepNo: stepNo.value }).then(res => {
        dataList.value = res.data ?? []
    })
}
search()

const stepMap = ref<Map<string, number>>(new Map);
watch(dept, () => {
    
    stepList({ year: props.year, deptId: dept.value }).then(res => {
        res.data?.forEach(item => {
            stepMap.value.set(String(item.stepNo), item.projCount ?? 0)
        })
    })
}, {immediate: true})

const save = () => {
    const data = dataList.value.map(item => {
        return {
            id: item.id,
            year: item.year,
            relId: item.relId,
            projId: item.projId,
            stepNo: item.stepNo,
            projCode: item.projCode,
        }
    })
    if (data.length > 0) {
        saveOrUpdatePiput(data).then(res => {
            ElMessage.success('保存成功')
            // emit('close')
        })
    }
}
</script>

<style scoped>
:deep(.el-radio-button__inner) {
    border-left: var(--el-border);

}
</style>