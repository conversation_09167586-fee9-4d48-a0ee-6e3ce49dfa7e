<template>
    <el-descriptions :column="1" label-width="20%" border>
        <el-descriptions-item label="项目名称">
            {{ data.projName }}
        </el-descriptions-item>
        <el-descriptions-item label="进展说明及需协调问题">
            <el-input v-model="form.progressIssue" type="textarea" :rows="5" maxlength="2000"></el-input>
        </el-descriptions-item>
        <template v-if="month">
            <el-descriptions-item :label="`${month}月完成内容`">
                <div>
                    <el-checkbox-group v-model="checkValues" class="grid grid-cols-2">
                        <el-checkbox v-for="item in checkboxOptions" :value="item">
                            <div>{{ item }}</div>
                        </el-checkbox>
                    </el-checkbox-group>
                    <el-input v-model="value1" type="textarea" :rows="5"></el-input>
                </div>
            </el-descriptions-item>
        </template>
    </el-descriptions>
    <div class="flex justify-center mt-5">
        <el-button type="primary" @click="save">保存</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { saveOrUpdatePjimportant } from '@/api/lib/report/important';
import { ElMessage, ElMessageBox } from 'element-plus';

const emit = defineEmits(['close', 'cancel'])

const props = defineProps<{
    year: number
    month?: string
    data: ProjectImportantVo,
}>()
// 文本框内容
const value1 = ref('')
// 复选框选中内容
const checkValues = ref<string[]>([])
// 复选框选项
const checkboxOptions = new Set<string>()
const arr: string[] = []
if (props.month) {
    // 从所有的月度管理计划里面获取复选框选项
    props.data.projectImportantJhVos?.forEach(item => {
        if (item.plan) {
            item.plan.split(/[、\n]+/).forEach(t => {
                checkboxOptions.add(t)
            })
        }
    })
    // 判断如果实施中有有月度计划的值，则选中复选框，否则添加到文本框
    props.data.projectImportantExceVos?.find(t => t.month == parseInt(props.month!))?.action?.split(/[、\n]+/)?.forEach(item => {
        if (item) {
            if (checkboxOptions.has(item)) {
                checkValues.value.push(item)
            } else {
                arr.push(item)
            }
        }
    })
    // 添加到文本框
    if (arr.length > 0) {
        value1.value = arr.join('、')
    }
}

const form = reactive({
    progressIssue: props.data.progressIssue
})

const save = () => {
    const data: any = {
        projId: props.data.projId!,
        relId: props.data.relId!,
        year: props.year,
        progressIssue: form.progressIssue
    }
    if (props.month) {
        data.projectImportantExceVo = {
            month: props.month,
            action: checkValues.value.length > 0 ? checkValues.value.join('、') + '、' + value1.value : value1.value
        }
    }
    saveOrUpdatePjimportant(data).then(res => {
        ElMessage.success('保存成功')
        emit('close')
    })
}
</script>