<template>
    <div class="app-container relative" v-loading="loading">
        <div class="grid grid-cols-[25%_1fr_25%] items-center">
            <!-- <DeptSelect v-model="searchForm.deptId" clearable class="!w-45"
                :disabled="!auth.hasPermi('project:important:org:change')"></DeptSelect> -->
            <el-select v-model="searchForm.deptId" clearable class="w-45"
                :disabled="!auth.hasPermi('project:important:org:change')">
                <el-option label="全部" value=""></el-option>
                <el-option v-for="item in deptList" :label="item.deptName" :value="item.deptId"></el-option>
            </el-select>
            <!-- <DeptSelect v-model="searchForm.deptId" class="w-45"></DeptSelect> -->
            <div class="flex items-center justify-center gap-2">
                <el-date-picker v-model="searchForm.year" type="year" class="!w-25 time" :clearable="false"
                    value-format="YYYY" :disabled-date="handleCheckDateDisabled"></el-date-picker>
                <h1 class="text-center leading-9 relative my-4">年度专项发展资金重点推进项目</h1>
            </div>
            <div class="flex justify-end">
                <right-toolbar :columns="columns" @queryTable="search" :search="false" class="!ml-3">
                    <template #left>
                        <el-tooltip v-hasPermi="['project:important:project:mgr']" class="item" effect="dark"
                            content="项目管理" placement="top">
                            <el-button type="primary" class="ml-3" circle icon="Document"
                                @click="projectManageShow = true" />
                        </el-tooltip>
                        <el-tooltip v-hasPermi="['project:important:plan:mgr']" class="item" effect="dark"
                            content="计划管理" placement="top">
                            <el-button type="primary" class="ml-3" circle icon="Timer" @click="planManageShow = true" />
                        </el-tooltip>
                    </template>
                    <el-tooltip class="item" effect="dark" content="预览打印" placement="top">
                        <el-button class="ml-3" circle icon="Printer" @click="handlePrint" />
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="导出Excel" placement="top">
                        <el-button class="ml-3" circle icon="Download" @click="handleDownloadExcel" />
                    </el-tooltip>

                </right-toolbar>
            </div>
        </div>

        <vxe-table ref="tableRef" :data="tableData" border :height="tableHeight" :cell-class-name="bindCellClassName"
            :merge-cells="mergeCells" @cell-click="handleCellClick" :header-cell-class-name="bindHeaderCellClassName">
            <vxe-column field="projCode" title="序号" fixed="left" width="80" align="center" />

            <vxe-column field="projName" title="项目名称" fixed="left" width="260" header-align="center" align="left">
                <template v-slot="{ row }">
                    <div v-if="!row.projId && row.stepNo" class="text-center">
                        {{ getProjectImpStageTypeMap.get(row.stepNo) }}
                    </div>
                    <div v-else>
                        <div v-if="!row.projId" class="text-center">
                            {{ row.projName }}
                        </div>
                        <div v-else>
                            {{ row.projName }}
                        </div>
                    </div>
                </template>
            </vxe-column>
            <vxe-column field="projAmount" title="项目金额" width="130" align="center" />
            <vxe-column field="planBegin" title="计划开工" width="130" align="center">
                <template v-slot="{ row }">
                    {{ dateFormat(row.planBegin, 'YYYY年M月') }}
                </template>
            </vxe-column>
            <vxe-column field="projEnd" title="计划完工" width="130" align="center">
                <template v-slot="{ row }">
                    {{ dateFormat(row.projEnd, 'YYYY年M月') }}
                </template>
            </vxe-column>
            <vxe-column field="yearPlan" :title="`${getNumbericalYear}年正式计划`" width="130" align="center" />
            <vxe-column field="yearAdjustPlan" :title="`${getNumbericalYear}年调整计划`" width="130" align="center"
                :visible="projImpAdjust == '1'" />
            <vxe-column field="yearExecuted" :title="`${getNumbericalYear}年已执行`" width="130" align="center" />
            <vxe-column field="progressIssue" title="进展说明及需协调问题" width="180" align="center" />
            <vxe-colgroup field="quarter1" :title="`${getNumbericalYear}年第一季度`" align="center"
                :visible="columns[0].visible || columns[1].visible || columns[2].visible">
                <vxe-column field="0" title="1月" width="150" align="center" class="111111"
                    :visible="columns[0].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(1, row.projectImportantJhVos)?.plan :
                            findByMonth(1, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
                <vxe-column field="1" title="2月" width="150" align="center" :visible="columns[1].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(2, row.projectImportantJhVos)?.plan :
                            findByMonth(2, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
                <vxe-column field="2" title="3月" width="150" align="center" :visible="columns[2].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(3, row.projectImportantJhVos)?.plan :
                            findByMonth(3, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup field="quarter2" :title="`${getNumbericalYear}年第二季度`" align="center"
                :visible="columns[3].visible || columns[4].visible || columns[5].visible">
                <vxe-column field="3" title="4月" width="150" align="center" :visible="columns[3].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(4, row.projectImportantJhVos)?.plan :
                            findByMonth(4, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
                <vxe-column field="4" title="5月" width="150" align="center" :visible="columns[4].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(5, row.projectImportantJhVos)?.plan :
                            findByMonth(5, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
                <vxe-column field="5" title="6月" width="150" align="center" :visible="columns[5].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(6, row.projectImportantJhVos)?.plan :
                            findByMonth(6, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup field="quarter3" :title="`${getNumbericalYear}年第三季度`" align="center"
                :visible="columns[6].visible || columns[7].visible || columns[8].visible">
                <vxe-column field="6" title="7月" width="150" align="center" :visible="columns[6].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(7, row.projectImportantJhVos)?.plan :
                            findByMonth(7, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
                <vxe-column field="7" title="8月" width="150" align="center" :visible="columns[7].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(8, row.projectImportantJhVos)?.plan :
                            findByMonth(8, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
                <vxe-column field="8" title="9月" width="150" align="center" :visible="columns[8].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(9, row.projectImportantJhVos)?.plan :
                            findByMonth(9, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-colgroup field="quarter4" :title="`${getNumbericalYear}年第四季度`" align="center"
                :visible="columns[9].visible || columns[10].visible || columns[11].visible">
                <vxe-column field="9" title="10月" width="150" align="center" :visible="columns[9].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(10, row.projectImportantJhVos)?.plan :
                            findByMonth(10, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
                <vxe-column field="10" title="11月" width="150" align="center" :visible="columns[10].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(11, row.projectImportantJhVos)?.plan :
                            findByMonth(11, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
                <vxe-column field="11" title="12月" width="150" align="center" :visible="columns[11].visible">
                    <template v-slot="{ row }">
                        {{ strFormt(!row.isCopy ? findByMonth(12, row.projectImportantJhVos)?.plan :
                            findByMonth(12, row.projectImportantExceVos)?.action) }}
                    </template>
                </vxe-column>
            </vxe-colgroup>
            <vxe-column field="yearGoal" title="年度目标" width="180" align="center" />
        </vxe-table>

        <el-dialog title="重点项目整理" v-model="projectManageShow" width="1000px" :align-center="true"
            :close-on-click-modal="false" destroy-on-close>
            <ProjectManage :year="getNumbericalYear" :deptList="deptList" @close="projectManageShow = false; search()"
                @cancel="projectManageShow = false"></ProjectManage>
        </el-dialog>
        <el-dialog title="重点项目计划" v-model="planManageShow" width="1300px" :align-center="true"
            :close-on-click-modal="false" destroy-on-close>
            <PlanManage :year="getNumbericalYear" :deptList="deptList" @close="planManageShow = false; search()"
                @cancel="planManageShow = false"></PlanManage>
        </el-dialog>
        <el-dialog title="进展更新" v-model="progressUpdateShow" width="900px" :align-center="true"
            :close-on-click-modal="false" destroy-on-close @closed="month = ''">
            <ProgressUpdate :year="getNumbericalYear" :data="currentRow!" :month="month"
                @close="progressUpdateShow = false; search()" @cancel="progressUpdateShow = false"></ProgressUpdate>
        </el-dialog>
    </div>
</template>
<script lang="tsx" setup>
import { debounce, range } from 'lodash';
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { dayjs } from 'element-plus';
import VxePcUI from 'vxe-pc-ui'
import { VxeTable, VxeColumn, VxeColgroup, VxeTableInstance, VxeUI, VxeTablePropTypes } from 'vxe-table';
import 'vxe-table/styles/cssvar.scss'
import ProjectManage from './component/projectManage.vue';
import PlanManage from './component/planManage.vue';
import { checkIsNumber, dateFormat } from '@/utils/common';
import { getDeptList, getList, getYear } from '@/api/lib/report/important';
import ProgressUpdate from './component/progressUpdate.vue';
import { useConfig } from '@/utils/config';
import auth from '@/plugins/auth';
// @ts-ignore
import useUserStore from '@/store/modules/user'

// @ts-ignore
VxeUI.use(VxePcUI)

const {
    ["sys.proj.imp.adjust"]: projImpAdjust
} = useConfig("sys.proj.imp.adjust")

const userStore = useUserStore()
const years = (await getYear()).data
const handleCheckDateDisabled = (date: any) => {
    if (years?.includes(dayjs(date).year())) {
        return false
    }
    return true
}

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_imp_stage_type } = proxy.useDict("project_imp_stage_type");

const getProjectImpStageTypeMap = computed(() => {
    const map = new Map<number, string>()
    if (project_imp_stage_type.value) {
        project_imp_stage_type.value.forEach((t: any) => {
            map.set(parseInt(t.value), t.remark)
        })
    }
    return map
})
const currentQuarter = Math.ceil(dayjs().month() / 3)
const tableRef = ref<VxeTableInstance>()
const tableHeight = ref(window.innerHeight - 190)
const columns = ref([
    ...range(1, 13).map(month => {
        // 计算当前季度的起始和结束月份
        const startMonth = (currentQuarter - 1) * 3 + 1; // 当前季度的第一个月
        const endMonth = currentQuarter * 3; // 当前季度的最后一个月

        // 判断传入的月份是否在当前季度范围内
        return { key: month, label: `${month}月`, visible: month >= startMonth && month <= endMonth }
    })
])
// 必须手动监听列隐藏事件并更新合并，否则无法合并
watch(columns, () => {
    updateMergeCells()
}, { deep: true })

const searchForm = reactive({
    year: String(years?.at(-1)),
    deptId: [100, 396].includes(userStore.deptId) ? '' : String(userStore.deptId)
})
const getNumbericalYear = computed(() => {
    return parseInt(searchForm.year)
})

const deptList = ref<any[]>([])
getDeptList(getNumbericalYear.value).then(res => {
    deptList.value = res.data ?? []
})


const loading = ref(false)
type dataType = (ProjectImportantVo & { isCopy?: boolean })
const tableData = ref<dataType[]>([]);
const search = () => {
    loading.value = true
    getList({ year: getNumbericalYear.value, deptId: searchForm.deptId }).then(res => {
        // tableData.value.length = 0
        const temp: dataType[] = []
        // 把项目复制一份，用于后面的行合并
        res.data?.forEach(item => {
            if (item.projId) {
                temp.push({ ...item, isCopy: false }, { ...item, isCopy: true })
            } else {
                temp.push(item)
            }
        })
        tableData.value = temp
        updateMergeCells()
    }).finally(() => {
        loading.value = false
    })
}
watch(() => [getNumbericalYear.value, searchForm.deptId], () => {
    search()
}, { immediate: true })

const projectManageShow = ref(false)

const planManageShow = ref(false)

const findByMonth = (month: number, list: any[]) => {
    return list?.find(t => t.month == month)
}

const handleDownloadExcel = () => {
    proxy.download('project/important/download ', {
        ...searchForm
    }, `${searchForm.year}年度专项发展资金重点推进项目_${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)

}

const handlePrint = () => {
    tableRef.value?.getPrintHtml().then(res => {
        const headHtml = `
        <h1 style="text-align: center;">${getNumbericalYear.value}年度专项发展资金重点推进项目</h1>
        `;
        // 使用 DOMParser 解析 HTML 字符串
        const parser = new DOMParser();
        const doc = parser.parseFromString(res.html, 'text/html');

        // 获取解析后的 DOM 元素
        const tableEl = doc.body.firstElementChild! as HTMLTableElement;
        const tableTrs = Array.from(tableEl.querySelectorAll('tbody tr')) as HTMLTableRowElement[];

        let startCount = 7
        if (projImpAdjust.value == '1') {
            startCount = 8
        }
        const monthVisableCount = columns.value.filter(t => t.visible).length
        tableData.value.forEach((item, index) => {
            if (item.projId && item.isCopy == false) {
                tableTrs.at(index)!.querySelectorAll('td').forEach((td, index) => {
                    if (index > startCount && index <= (startCount + monthVisableCount)) {
                        td.style.backgroundColor = 'rgb(177.3, 179.4, 183.6)'
                    }

                })
            }
            // 设置每个单元格里面内容的宽度
            tableTrs.at(index)!.querySelectorAll('td').forEach((td, index) => {
                const div = td.querySelector('div')!
                const newDiv = document.createElement('div')
                newDiv.innerHTML = div.innerHTML
                newDiv.style.width = '100px'
                newDiv.style.whiteSpace = 'pre-wrap'
                div.innerHTML = ''
                div.appendChild(newDiv)
                div.style.display = 'flex'
                div.style.justifyContent = 'center'
            })
        });

        const html = tableEl.outerHTML
        console.log(html)
        tableRef.value?.print({
            html: headHtml + html,
        })
    })
}


const monthMap = new Map([
    ['0', 0], ['1', 1], ['2', 2], ['3', 3], ['4', 4], ['5', 5], ['6', 6], ['7', 7],
    ['8', 8], ['9', 9], ['10', 10], ['11', 11], ['12', 12]
])
const bindCellClassName: VxeTablePropTypes.CellClassName<dataType> = ({ row, column }) => {
    if (column.field === 'progressIssue') {
        if (row.projId) {
            if (!row.progressIssue) {
                return 'progressIssue-unset'
            }
            return 'progressIssue-change'
        }
    }
    if (column.field === 'projName') {
        if (!row.projId && !row.stepName) {
            return 'stepName'
        }
    }
    if (monthMap.has(column.field)) {
        if (row.projId) {
            if (!row.isCopy) {
                return 'iscopy-false'
            } else {
                if (row.projectImportantExceVos?.at(monthMap.get(column.field)!)?.action) {
                    return 'iscopy-true-hasvalue'
                }
                return 'iscopy-true'
            }
        }

    }
}
const bindHeaderCellClassName: VxeTablePropTypes.HeaderCellClassName<any> = ({ column, $rowIndex, $columnIndex }) => {

    return 'project-header-custom';
}
const mergeCells = ref<VxeTablePropTypes.MergeCells>([])
const updateMergeCells = () => {
    mergeCells.value.length = 0
    queueMicrotask(() => {
        let startCount = 7
        if (projImpAdjust.value == '1') {
            startCount = 8
        }
        const monthVisableCount = columns.value.filter(t => t.visible).length
        tableData.value.forEach((row, index) => {
            if (row.projId && !row.isCopy) {
                mergeCells.value.push({ row: index, col: 0, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 1, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 2, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 3, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 4, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 5, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 6, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: 7, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: startCount, rowspan: 2, colspan: 1 })
                mergeCells.value.push({ row: index, col: startCount + monthVisableCount + 1, rowspan: 2, colspan: 1 })
                // if (projImpAdjust.value == '1') {
                //     mergeCells.value.push({ row: index, col: 8, rowspan: 2, colspan: 1 })
                //     mergeCells.value.push({ row: index, col: 8 + 13, rowspan: 2, colspan: 1 })
                // } else {
                //     mergeCells.value.push({ row: index, col: 7 + 13, rowspan: 2, colspan: 1 })
                // }

            }
        })
    });

}

const progressUpdateShow = ref(false)
const month = ref('')
const currentRow = ref<ProjectImportantVo>()
const handleCellClick = (data: { row: any, column: any, }) => {
    currentRow.value = data.row
    if (!currentRow.value?.projId) {
        return
    }
    if (data.column.field === 'progressIssue') {
        progressUpdateShow.value = true
    }
    if (monthMap.has(data.column.field) && data.row.isCopy) {
        progressUpdateShow.value = true
        month.value = String(monthMap.get(data.column.field)! + 1)
    }
}

const strFormt = (str?: string) => {
    return str?.replace(/[\n\t]+/g, '、').replace(/、$/, '')
}
</script>

<style scoped>
:deep(.progressIssue-unset) {
    background-color: #f5f9ec;
    cursor: pointer;
}

:deep(.progressIssue-unset:hover) {
    background-color: #409EFF;
    cursor: pointer;
}

:deep(.progressIssue-change:hover) {
    background-color: #409EFF;
    cursor: pointer;
}

:deep(.stepName) {
    font-weight: 700;
}

:deep(.iscopy-false) {
    background-color: #fafafa;
}

:deep(.iscopy-true-hasvalue:hover) {
    background-color: #b5d2ee;
    cursor: pointer;
}

:deep(.iscopy-true) {
    background-color: #f5f9ec;
    cursor: pointer;
}

:deep(.iscopy-true:hover) {
    background-color: #409EFF;
}

:deep(.project-header-custom) {
    background: linear-gradient(to bottom, #fafdff, #deeefb) !important;
    /* 深蓝渐变 */
    text-align: center;
    border-right: 1px solid #d9e0eb !important;
}

@media print {
    .proj-ori-row-bg {
        background-color: #409EFF;
    }
}

:deep(.time .el-input__inner) {
    color: #000;
    font-size: 24px;
}
</style>