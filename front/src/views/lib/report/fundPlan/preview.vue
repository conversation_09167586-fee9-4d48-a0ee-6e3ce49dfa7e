<template>
    <div id="print" class="app-container relative" v-loading="loading">
        <h1 class="text-center cursor-pointer pb-2 mt-0 mb-1">
            {{ planForm.title }}</h1>
        <div class="flex justify-end">
            <span class="cursor-pointer">{{ planForm.unit }}</span>
        </div>

        <div class="mt-2">
            <VxeTable ref="tableRef" :data="getTableData" :height="tableHeight" :tree-config="bindTreeConfig"
                :row-config="bindRowConfig" :row-drag-config="bindRowDragConfig" auto-resize border>
                <vxe-column field="index" title="序号" width="50" align="center"></vxe-column>
                <template v-for="col in columns" :key="col.prop">
                    <vxe-column :visible="col.visible" :title="col.label" :field="col.prop"
                        :align="col.prop === 'projName' ? 'left' : (amountProps.includes(col.prop) ? 'right' : 'center')"
                        header-align="center" :min-width="handleWidth(col).width">
                        <template #default="{ row }">
                            <template v-if="col.prop === 'projName'">
                                <div>
                                    <projectNameComponent :row="row"></projectNameComponent>
                                </div>

                            </template>
                            <template v-else-if="col.prop == 'basketName'">
                                <dict-tag :options="project_basket_type" :value="row.basketId" />
                            </template>
                            <template v-else-if="amountProps.includes(col.prop)">
                                <template v-if="row.id == totalRowId">
                                    <span>{{ col.prop == 'currPayed' ? row[col.prop] : roundNumber2(row[col.prop])
                                        }}</span>
                                </template>
                                <template v-else>
                                    <template v-if="row.itemType == EUsageFundPlanItemType.project">
                                        <span :style="{
                                            color: colorMap.get(row.id + col.prop)
                                        }">{{ col.prop == 'currPayed' ? row[col.prop] : roundNumber2(row[col.prop])
                                            }}</span>
                                    </template>
                                    <template v-else>
                                        <span>{{ col.prop == 'currPayed' ? row[col.prop] : roundNumber2(row[col.prop])
                                            }}</span>

                                    </template>
                                </template>
                            </template>
                            <template v-else>
                                {{ row[col.prop] }}
                            </template>

                        </template>
                    </vxe-column>
                </template>
            </VxeTable>
        </div>
        <div class="fixed bottom-5 z-10 bg-white w-full text-center p-2">
            
            <!-- <el-button type="primary" @click="handleSave">保存</el-button> -->
            <!-- <el-button type="primary" @click="handleCreateSnap">生成快照</el-button> -->
            <el-button type="primary" @click="handlePrintView">打印</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <el-button type="danger" @click="$emit('close')">关闭</el-button>
            <!-- <el-button type="primary">考核主体汇总</el-button> -->
        </div>
    </div>

</template>

<script setup lang="tsx">
import { fromArray, foreach, find, map } from 'tree-lodash'
import Bignumber from 'bignumber.js';
import { VxeTable, VxeColumn, VxeTableInstance, VxeUI } from 'vxe-table'
import VxePcUI from 'vxe-pc-ui'
import 'vxe-table/styles/cssvar.scss'
import { useResizeObserver } from '@vueuse/core';
import { EApplyAdjustState, EUsageFundPlanItemType } from '@/utils/constants';
import { checkIsNumber, roundNumber2, sumAmount } from '@/utils/common';
import { dayjs } from 'element-plus';
import { getDetailById } from '@/api/lib/report/usagePlan';
// @ts-ignore
VxeUI.use(VxePcUI)

const props = defineProps<{
    id: string;
}>()

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_purpose, project_nature, project_basket_type } = proxy.useDict("project_purpose", "project_nature", 'project_basket_type');


const projectNameComponent = defineComponent({
    props: {
        row: {
            type: Object as () => any,
            default: ''
        }
    },
    setup(props, { emit }) {
        return () => {
            if (props.row.id != totalRowId && !props.row.typeCode) {
                return (<div class="text-left">
                    <span
                        class={[{ 'font-bold': [EUsageFundPlanItemType.type, EUsageFundPlanItemType.projectGroup].includes(props.row.itemType) }]}>
                        {props.row.projName}
                        {
                            props.row.projSn ? <br /> : ''
                        }
                        {props.row.projSn}
                    </span>
                    {
                        props.row.isImp == '1' ?
                            <span class="text-red">*</span> : ''
                    }
                </div>)
            } else {
                if (props.row.id == totalRowId) {
                    return (<div class="font-bold text-center">{props.row.projName}</div>)
                }
                return (<span class={{ 'font-bold': props.row.typeCode }}>{props.row.projName}</span>)
            }
        }
    }
})

const tableRef = ref<VxeTableInstance>()
// 计算表格高度
const tableHeight = ref(window.innerHeight - 220)
// 总计行的固定id
const totalRowId = '1'
const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId'
}
const bindRowConfig = {
    drag: true
}
const bindRowDragConfig = {
    isCrossDrag: true
}
// 合计单元格字段
const amountProps = [
    'estAmount',
    'libAmount',
    'checkAmount',
    'contractAmount',
    'prevAmount',
    'prevAdjust',
    'prevPayed',
    'currAmount',
    'currEarly',
    'currFormal',
    'currAdjust',
    'currPayed'
];

const columns = ref<{
    label: string,
    prop: string,
    width: number
    align: string
    visible: boolean
}[]>([]);

const handleWidth = (col: typeof columns.value[0]) => {
    if (col.prop == 'projName') {
        return {
            width: '300px',
        }
    } else if (col.prop == 'remark1' || col.prop == 'remark2') {
        return {
            width: '200px'
        }
    } else {
        return {
            width: '120px',
        }
    }
}

const loading = ref(false)
type usagePlanItemTree = IUsagePlanItem & { index?: number, children?: usagePlanItemTree | IUsagePlanItem[] }
const tableData = reactive({
    totalRow: {
        id: totalRowId,
        parentId: '0',
        projName: '总计',
        seqNo: 0
    } as usagePlanItemTree,
    dataList: [] as usagePlanItemTree[],
})
const getTableData = computed(() => {
    return [tableData.totalRow, ...tableData.dataList]
});

const year = useRoute().query.year as string
const planForm = reactive<IUsagePlan>({
    // title: `${year}年度上海化学工业区用款计划表`
} as IUsagePlan)
// 存储哪些单元格文字设置红色，健为id+字段名, 值为颜色值
const colorMap = new Map<string, string>()
const originList = ref<IUsagePlanItem[]>([])
const search = () => {
    loading.value = true
    getDetailById(props.id).then(async res => {
        colorMap.clear()
        if (res.data) {
            planForm.id = res.data?.id
            planForm.year = res.data.year
            planForm.title = res.data.title
            planForm.unit = res.data.unit
            console.time("caculate")
            originList.value = res.data?.dataList ?? []

            tableData.dataList = JSON.parse(JSON.stringify(originList.value))
            amountProps.forEach(prop => {
                tableData.totalRow[prop] = sumAmount(tableData.dataList.filter(t => t.parentId == '0').map((t: any) => checkIsNumber(t[prop]) ? t[prop] : 0))
            })

            if (res.data?.tableColumns && res.data?.tableColumns != '[]') {
                columns.value = JSON.parse(res.data?.tableColumns) ?? []
            }
            await nextTick(() => {
                resort()
                tableRef.value?.setAllTreeExpand(true)
            })
            console.timeEnd("caculate")
        }


    }).finally(() => loading.value = false)
}
search()


/**
 * 对表格和项目进行排序
 */
const resort = () => {
    let index = 1
    const map = new Map<string, string>()
    foreach(tableRef.value?.getTableData().fullData!, (item) => {
        if (item.itemType == EUsageFundPlanItemType.projectGroup) {
            map.set(item.id, '1')
            if (!map.has(item.parentId)) {
                item.index = index
                index++
            }

        }
        if (item.itemType == EUsageFundPlanItemType.type) {
            if (map.has(item.parentId)) {
                map.set(item.id, '1')
            }
        }
        if (item.itemType == EUsageFundPlanItemType.project) {
            if (!map.has(item.parentId)) {
                item.index = index
                index++
            }

        }
        if (item.children && item.children.length > 0) {
            item.children.sort((a: any, b: any) => a.seqNo ?? 0 - b.seqNo)
        }
    })
}


const handlePrintView = () => {
    // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
    const map = new Map<string, any[]>()
    tableRef.value?.getPrintHtml({
        dataFilterMethod: (data: { row: any }) => {
            map.set(data.row.id, data.row.children)
            data.row.children = []
            return true
        }
    }).then(res => {
        const headHtml = `
        <h1 style="text-align: center;">${planForm.title}</h1>
        <div style="text-align: right;margin-bottom: 5px;">${planForm.unit}</div>
        `
        const html = res.html.replaceAll('undefined', '').replaceAll(`style="width:64px"`, `style="width:20px"`)

        tableRef.value?.print({
            html: headHtml + html,
        })
    }).finally(() => {
        tableData.dataList.forEach((item) => {
            if (map.has(item.id!)) {
                item.children = map.get(item.id!)
            }
        })
    })
}

const handleDownloadExcel = () => {
    proxy.download('/project/usagePlan/download', {
        id: props.id
    }, `用款计划表${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>

<style scoped>
:deep(.cell-change:hover) {
    background-color: #409EFF !important;
    color: white;
    cursor: pointer;
}

:deep(.vxe-cell--tree-node) {
    /* padding-left: 0!important; */
}
</style>
