<template>
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="auto">
        <el-form-item label="项目名称">
            <el-input v-model="form.projName"></el-input>
        </el-form-item>
        <el-form-item label="考核主体">
            <DeptSelect ref="deptRef" v-model="form.assessOrgid" placeholder="请选择考核主体"></DeptSelect>
        </el-form-item>
        <el-form-item label="重点项目">
            <el-select v-model="form.impName" placeholder="请选择重点项目">
                <el-option label="不是重点项目" value="不是重点项目"></el-option>
                <el-option label="重点项目" value="重点项目"></el-option>
                <!-- <el-option label="- 前期项目" value="前期项目"></el-option>
                <el-option label="- 在建项目" value="在建项目"></el-option>
                <el-option label="- 完工审价决算项目" value="完工审价决算项目"></el-option>
                <el-option label="重点智慧园区项目" value="重点智慧园区项目"></el-option>
                <el-option label="重点采购项目" value="重点采购项目"></el-option>
                <el-option label="重点采购项目" value="重点采购项目"></el-option>
                <el-option label="功能性补贴、政策性补助" value="功能性补贴、政策性补助"></el-option> -->
            </el-select>
        </el-form-item>
        <el-form-item label="排序号">
            <el-input-number v-model="form.seqNo" :controls="false"></el-input-number>
        </el-form-item>
    </el-form>
    <div class="flex justify-center mt-5">
        <el-button type="primary" @click="save">确定</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { v4 as uuidv4 } from 'uuid'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { EUsageFundPlanItemType } from '@/utils/constants';

const emit = defineEmits(['close', 'cancel', 'add']);
const props = defineProps<{
    parentId: string;
    data?: IUsagePlanItem
}>();
const form = reactive({
    id: props.data?.id,
    projName: props.data?.projName ?? '',
    assessOrgid: props.data?.assessOrgid ?? '',
    impName: props.data?.impName ?? '不是重点项目',
    seqNo: props.data?.seqNo ?? ''
});
const formRules = {
    projName: [
        { required: true, message: '请输入类别名称', trigger: 'blur' }
    ],
    assessOrgid: [
        { required: true, message: '请选择考核主体', trigger: 'change' }
    ],
    impName: [
        { required: true, message: '请选择重点项目', trigger: 'change' }
    ],
    seqNo: [
        { required: true, message: '请输入排序号', trigger: 'blur' }
    ]
};

const deptRef = ref()

const formRef = ref();
const save = () => {
    formRef.value.validate((valid: boolean) => {
        if (valid) {
            const deptName = deptRef.value?.getDeptList()?.find((item: any) => item.deptId === form.assessOrgid)?.deptName ?? ''
            const data = Object.assign({}, form) as IUsagePlanItem;
            data.isImp = data.impName === '不是重点项目' ? '0':'1';
            // 提交表单数据
            emit('add', [{
                id: data.id ?? uuidv4().replace(/-/g, ''),
                parentId: props.parentId,
                projName: form.projName,
                sbOrgid: form.assessOrgid,
                assessOrgid: form.assessOrgid,
                sbOrgname: deptName,
                assessOrgname: deptName,
                seqNo: form.seqNo,
                isImp: data.isImp,
                impName: data.impName,
                itemType: EUsageFundPlanItemType.project,
                children: [],
            }])
            // 关闭弹窗
            emit('close');
        } else {
            console.log('表单验证失败');
            return false;
        }
    });
};
</script>