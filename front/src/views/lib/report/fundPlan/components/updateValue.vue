<template>
    <el-form ref="formRef" :model="form">
        <el-form-item prop="value" v-if="type === 'number'"
            :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
            <el-input-number v-model="form.value" class="w-full" controls-position="right" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item prop="value" v-if="type === 'string'"
            :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
            <el-input v-model="form.value" maxlength="255"></el-input>
        </el-form-item>
        <el-form-item prop="value" v-if="type === 'text'"
            :rules="[{ required: true, message: '请输入', trigger: 'blur' }]">
            <el-input v-model="form.value" type="textarea" maxlength="1000" :rows="5"></el-input>
        </el-form-item>
    </el-form>
    <div class="flex justify-center mt-5">
        <el-button type="primary" @click="save">确定</el-button>
        <el-button type="danger" @click="del">删除</el-button>
        <el-button @click="$emit('cancel')">取消</el-button>
    </div>
</template>

<script setup lang="ts">
import { updateEarlyAmount, updateTableValue } from '@/api/lib/report/usagePlan';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['close', 'cancel', 'add']);
const props = defineProps<{
    id: string,
    year: number,
    type: 'number' | 'string' | 'text',
    prop: string,
    value: string
    data?: IUsagePlanItem & { children?: IUsagePlanItem[] }
}>();

const form = reactive({
    id: props.id,
    type: props.type,
    prop: props.prop,
    value: props.value
});

const formRef = ref();
const save = () => {
    formRef.value.validateField([props.prop], (valid: boolean) => {
        if (valid) {
            updateTableValue({ id: form.id, year: props.year, prop: form.prop, value: form.value }).then(async res => {
                // 更新关联资金计划的年初计划金额
                if (form.prop == 'currEarly') {
                    if (props.data?.projId) {
                        const res = await updateEarlyAmount(props.data?.projId, props.year, form.value)
                    }
                }
                ElMessage.success('更新成功');
                emit('close', { prop: props.prop, value: form.value });
            })
        }
    })
};
const del = () => {
    updateTableValue({ id: form.id, year: props.year, prop: form.prop, value: '' }).then(async res => {
        // 更新关联资金计划的年初计划金额
        if (form.prop == 'currEarly') {
            if (props.data?.projId) {
                const res = await updateEarlyAmount(props.data?.projId, props.year, '')
            }
        }
        ElMessage.success('更新成功');
        emit('close', { prop: props.prop, value: '' });
    })
};
</script>