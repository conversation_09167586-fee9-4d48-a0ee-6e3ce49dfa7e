<template>
    <div class="app-container" v-loading="loading">
        <h1 class="text-center cursor-pointer py-2 mt-0 mb-1">
            {{ planForm.title }}</h1>
        <div class="flex justify-between">
            <div class="flex items-center gap-2">
                <el-select v-model="searchForm.basketId" placeholder="请选择项目篮子" class="w-45" clearable>
                    <el-option v-for="item in project_basket_type" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <DeptSelect v-model="searchForm.sbOrgid" class="w-45" placeholder="上报单位" clearable></DeptSelect>
                <DeptSelect v-model="searchForm.assessOrgid" class="w-45" placeholder="考核主体" clearable></DeptSelect>
                <el-select v-model="searchForm.natureName" placeholder="请选择项目性质" class="w-45" clearable>
                    <el-option label="阶段性-新增" value="阶段性-新增" />
                    <el-option label="阶段性-结转" value="阶段性-结转" />
                    <el-option label="经常性" value="经常性" />
                </el-select>
            </div>
            <div class="flex items-center gap-2">
                <span class="cursor-pointer">{{ planForm.unit }}</span>
            </div>
        </div>

        <div class="mt-2">
            <VxeTable ref="tableRef" :data="getTableData" :height="tableHeight" :tree-config="bindTreeConfig"
                :row-config="bindRowConfig" auto-resize border>
                <vxe-column field="index1" width="100px" align="center" tree-node>
                    <template #default="{ row, $rowIndex }"></template>
                </vxe-column>
                <vxe-column field="index" title="序号" width="50" align="center"></vxe-column>
                <template v-for="col in columns" :key="col.prop">
                    <vxe-column v-if="col.visible" :title="col.label" :field="col.prop" header-align="center"
                        :align="col.prop === 'projName' ? 'left' : (amountProps.includes(col.prop) ? 'right' : 'center')" :min-width="handleWidth(col).width">
                        <template #default="{ row }">
                            <template v-if="col.prop === 'projName'">
                                <div>
                                    <projectNameComponent :row="row"></projectNameComponent>
                                </div>

                            </template>
                            <template v-else-if="col.prop == 'basketName'">
                                <dict-tag :options="project_basket_type" :value="row.projectInfo?.basketCode" />
                            </template>
                            <template v-else-if="amountProps.includes(col.prop)">
                                <template v-if="row.id == totalRowId">
                                    <span>{{ col.prop == 'currPayed' ? row[col.prop] : roundNumber2(row[col.prop]) }}</span>
                                </template>
                                <template v-else>
                                    <template v-if="row.itemType == EUsageFundPlanItemType.project">
                                        <span :style="{
                                            color: colorMap.get(row.id + col.prop)
                                        }">{{ col.prop == 'currPayed' ? row[col.prop] : roundNumber2(row[col.prop]) }}</span>
                                    </template>
                                    <template v-else>
                                        <span>{{ col.prop == 'currPayed' ? row[col.prop + 'Temp'] : roundNumber2(row[col.prop + 'Temp']) }}</span>

                                    </template>
                                </template>
                            </template>
                            <template v-else>
                                {{ row[col.prop] }}
                            </template>

                        </template>
                    </vxe-column>
                </template>
            </VxeTable>
        </div>
        <div class="fixed bottom-5 z-10 bg-white w-full text-center p-2">
            
            <!-- <el-button type="primary" @click="handleSave">保存</el-button> -->
            <el-button type="primary" @click="handlePrintView">打印预览</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <el-button type="danger" @click="$emit('close')">关闭</el-button>
            <!-- <el-button type="primary">考核主体汇总</el-button> -->
        </div>
    </div>

</template>

<script setup lang="tsx">
import { fromArray, foreach, find, map } from 'tree-lodash'
import { dayjs, ElMessage, ElMessageBox } from 'element-plus';
import Bignumber, { BigNumber } from 'bignumber.js';
import { VxeTable, VxeColumn, VxeTableInstance, VxeUI } from 'vxe-table'
import VxePcUI from 'vxe-pc-ui'
import 'vxe-table/styles/cssvar.scss'
import { useResizeObserver } from '@vueuse/core';
import { getDetail, save, updateTableTitle, updateTableUnit, delYearData, saveItem, updateTableValue, createSnap, resort, getDetailById } from '@/api/lib/report/usagePlan';
import { cloneDeepWith, debounce, filter, isEmpty } from 'lodash'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { EApplyAdjustState, EUsageFundPlanItemType } from '@/utils/constants';
import { checkIsNumber, numberFormat, roundNumber2, sumAmount } from '@/utils/common';
import Preview from './preview.vue';
import { WatchStopHandle } from 'vue';
import { v4 as uuidv4 } from 'uuid'

// @ts-ignore
VxeUI.use(VxePcUI)

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_basket_type, snap_type } = proxy.useDict('project_basket_type', 'snap_type');

const props = defineProps<{
    year: number;
}>()


const projectNameComponent = defineComponent({
    props: {
        row: {
            type: Object as () => any,
            default: ''
        }
    },
    setup(props, { emit }) {
        return () => {
            if (props.row.id != totalRowId && !props.row.typeCode) {
                return (<div class="text-left">
                    <span
                        class={[{ 'font-bold': [EUsageFundPlanItemType.type, EUsageFundPlanItemType.projectGroup].includes(props.row.itemType) }]}>
                        {props.row.projName}
                        {
                            props.row.projSn ? <br /> : ''
                        }
                        {props.row.projSn}
                    </span>
                    {
                        props.row.isImp == '1' ?
                            <span class="text-red">*</span> : ''
                    }
                </div>)
            } else {
                if (props.row.id == totalRowId) {
                    return (<div class="font-bold text-center">{props.row.projName}</div>)
                }
                return (<span class={{ 'font-bold': props.row.typeCode }}>{props.row.projName}</span>)
            }
        }
    }
})

const tableRef = ref<VxeTableInstance>()
// 计算表格高度
const tableHeight = ref(window.innerHeight - 250)
// 总计行的固定id
const totalRowId = '1'
const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId'
}
const bindRowConfig = {
    drag: true
}
const bindRowDragConfig = {
    isCrossDrag: true,
    visibleMethod: ({ row }: any) => !(row.typeCode || row.id === totalRowId)
}
// 合计单元格字段
const amountProps = [
    'estAmount',
    'libAmount',
    'checkAmount',
    'contractAmount',
    'prevAmount',
    'prevAdjust',
    'prevPayed',
    'currAmount',
    'currEarly',
    'currFormal',
    'currAdjust',
    'currPayed'
];


const columns = ref([
    { label: '项目名称', prop: 'projName', width: 150, align: 'center', visible: true, disabled: true },
]);

const handleWidth = (col: typeof columns.value[0]) => {
    if (col.prop == 'projName') {
        return {
            width: '300px',
        }
    } else if (col.prop == 'remark1' || col.prop == 'remark2') {
        return {
            width: '200px'
        }
    } else {
        return {
            width: '120px',
        }
    }
}

const loading = ref(false)
type usagePlanItemTree = IUsagePlanItem & { index?: number, children?: usagePlanItemTree | IUsagePlanItem[] }
const tableData = reactive({
    totalRow: {
        id: totalRowId,
        parentId: '0',
        projName: '总计',
        seqNo: 0
    } as usagePlanItemTree,
    dataList: [] as usagePlanItemTree[],
})
const getTableData = computed(() => {
    return [tableData.totalRow, ...tableData.dataList]
});


const planForm = reactive<IUsagePlan>({
    // title: `${year}年度上海化学工业区用款计划表`
} as IUsagePlan)
const searchForm = reactive({
    basketId: '',
    sbOrgid: null,
    natureName: '',
    assessOrgid: null,
})
// 存储哪些单元格文字设置红色，健为id+字段名, 值为颜色值
const colorMap = new Map<string, string>()
const originList = ref<IUsagePlanItem[]>([])
const search = () => {
    loading.value = true
    getDetail(props.year, searchForm, JSON.stringify(columns.value)).then(async res => {
        colorMap.clear()
        if (res.data) {
            planForm.id = res.data?.id
            planForm.year = res.data.year
            planForm.title = res.data.title
            planForm.unit = res.data.unit
            console.time("caculate")

            const list = (res.data?.dataList ?? []).filter(t => {
                if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(t.projectInfo?.state)) {
                    return false
                }
                return true
            })
            const func = (data: {
                id: string,
                prop: string,
                val1?: number | null,
                val2?: number,
                color?: string
            } = { id: '', prop: '', color: 'red' }) => {
                let num
                if (!checkIsNumber(data.val1)) {
                    num = data.val2
                } else {
                    num = data.val1
                    colorMap.set(`${data.id}${data.prop}`, data.color ?? 'black')
                }
                return num
            }
            list.forEach(t => {
                if (!t.projectInfo) return
                t.projName = t.projectInfo.name!
                t.projSn = t.projectInfo.projectSn?.formalSn ?? t.projectInfo.projectSn?.tempSn ?? ''
                t.sbOrgid = t.projectInfo.submitOrgid
                // t.sbOrgname = t.projectInfo.submitOrgidName
                t.assessOrgid = t.projectInfo.assessOrgid
                t.assessOrgname = t.projectInfo.assessOrgname
                t.basketId = t.projectInfo.basketCode
                t.basketName = t.projectInfo.basketName
                if (t.projectInfo?.natureCode == 'JC') {
                    t.natureName = '经常性'
                } else {
                    t.natureName = '阶段性'
                    if (t.projectInfo?.currentYearRelation?.projType == 1) {
                        t.natureName += '-结转'
                    }
                    if (t.projectInfo?.currentYearRelation?.projType == 3) {
                        t.natureName += '-新增'
                    }
                }
                t.estAmount = func({
                    id: t.id!,
                    prop: 'estAmount',
                    val1: t.estAmount,
                    val2: t.projectInfo?.estAmount
                })
                t.libAmount = func({
                    id: t.id!,
                    prop: 'libAmount',
                    val1: t.libAmount,
                    val2: t.projectInfo?.libAmount
                })
                t.checkAmount = func({
                    id: t.id!,
                    prop: 'checkAmount',
                    val1: t.checkAmount,
                    val2: t.projectInfo?.checkAmount
                })
                t.contractAmount = func({
                    id: t.id!,
                    prop: 'contractAmount',
                    val1: t.contractAmount,
                    val2: t.projectInfo?.contractAmount
                })
                t.currAmount = func({
                    id: t.id!,
                    prop: 'currAmount',
                    val1: t.currAmount,
                    val2: t.projectInfo?.fundPlan?.declareAmount
                })
                t.currFormal = func({
                    id: t.id!,
                    prop: 'currFormal',
                    val1: t.currFormal,
                    val2: t.projectInfo?.fundPlan?.formalAmount
                })
                t.prevAdjust = func({
                    id: t.id!,
                    prop: 'prevAdjust',
                    val1: t.prevAdjust,
                    val2: EApplyAdjustState.qrtz == t.projectInfo?.prevFundPlan?.applyState ?
                        t.projectInfo?.prevFundPlan?.adjustAmount :
                        t.projectInfo?.prevFundPlan?.formalAmount
                })

                t.currAdjust = func({
                    id: t.id!,
                    prop: 'currAdjust',
                    val1: t.currAdjust,
                    val2: checkIsNumber(t.projectInfo?.fundPlan?.adjustAmount) ? t.projectInfo?.fundPlan?.adjustAmount :
                        ([EApplyAdjustState.qrtz, EApplyAdjustState.ysb].includes(t.projectInfo?.fundPlan?.applyState) ?
                            t.projectInfo?.fundPlan?.applyAdjust : t.projectInfo?.fundPlan?.formalAmount
                        )
                })

                // 当年初步使用计划 当年正式计划
                // 由后台一个配置项进行配置，不同的配置会设置不同字段的金额

                // 至上年底累计执行
                // 计算拨款单今年以前的所有金额累加
                t.prevAmount = func({
                    id: t.id!,
                    prop: 'prevAmount',
                    val1: t.prevAmount,
                    val2: t.projectInfo?.prevAmount
                })

                // 上年实际执行
                // 计算上一年的所有金额累加
                t.prevPayed = func({
                    id: t.id!,
                    prop: 'prevPayed',
                    val1: t.prevPayed,
                    val2: t.projectInfo?.prevPayed
                })

                // 当年已执行
                // 计算当年的所有金额累加
                t.currPayed = func({
                    id: t.id!,
                    prop: 'currPayed',
                    val1: t.currPayed,
                    val2: t.projectInfo?.currPayed
                })
            })
            originList.value = list
            tableData.dataList = caculate(JSON.parse(JSON.stringify(originList.value)))
            if (res.data?.tableColumns && res.data?.tableColumns != '[]') {
                columns.value = JSON.parse(res.data?.tableColumns) ?? []
            }
            console.timeEnd("caculate")
            await nextTick(() => {
                handleResort()
                tableRef.value?.setAllTreeExpand(true)
            })



        }


    })
        .finally(() => loading.value = false)
}
search()
watch(searchForm, () => {
    // 多条件筛选，相当于 where and 查询
    // 实现并集查询：只要任一条件匹配即保留
    const filtered = originList.value.filter(t => {
        if ((searchForm.sbOrgid && t.sbOrgid != searchForm.sbOrgid) ||
            (searchForm.assessOrgid && t.assessOrgid != searchForm.assessOrgid) ||
            (searchForm.basketId && t.basketId != searchForm.basketId) ||
            (searchForm.natureName && t.natureName != searchForm.natureName)) {
            return false
        }
        return true
    });
    tableData.dataList = caculate(JSON.parse(JSON.stringify(filtered)))
    handleResort()
    nextTick(() => {
        tableRef.value?.setAllTreeExpand(true)
    })
})

/**
 * 统计金额
 * @param list 
 */
const caculate = (list: IUsagePlanItem[]) => {
    const tree = fromArray(list, { itemKey: 'id', parentKey: 'parentId' })
    foreach(tree, (node) => {
        amountProps.forEach(prop => {
            const tempProp = prop + 'Temp'
            if (node[prop] != null) {
                node[tempProp] = node[prop]
            } else {
                if (node.children) {
                    node[tempProp] = sumAmount(node.children.map((child: IUsagePlanItem) => {
                        if (checkIsNumber(child[tempProp]))
                            return child[tempProp]
                        return 0
                    }))
                }
            }


        })
    }, { strategy: 'post' })

    amountProps.forEach(prop => {
        tableData.totalRow[prop] = sumAmount(tree.map((t: any) => checkIsNumber(t[prop + 'Temp']) ? t[prop + 'Temp'] : 0))
    })
    return list
}

/**
 * 对表格和项目进行排序
 */
const handleResort = () => {
    let index = 1
    // 这个用来记录项目组项目，如果是项目组的话就存起来，用于判断如果下面的元素的父级是该项目组，就跳过
    const map = new Map<string, string>()
    foreach(tableRef.value?.getTableData().fullData!, (item) => {
        if (item.itemType == EUsageFundPlanItemType.projectGroup) {
            map.set(item.id, '1')
            if (!map.has(item.parentId)) {
                item.index = index
                index++
            }

        }
        if (item.itemType == EUsageFundPlanItemType.type) {
            if (map.has(item.parentId)) {
                map.set(item.id, '1')
            }
        }
        if (item.itemType == EUsageFundPlanItemType.project) {
            if (!map.has(item.parentId)) {
                item.index = index
                index++
            }

        }
        if (item.children && item.children.length > 0) {
            item.children.sort((a: any, b: any) => a.seqNo ?? 0 - b.seqNo)
        }
    })
}

const handlePrintView = () => {
    // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
    const map = new Map<string, any[]>()
    tableRef.value?.getPrintHtml({
        excludeFields: ['index1'],
        dataFilterMethod: (data: { row: any }) => {
            map.set(data.row.id, data.row.children)
            data.row.children = []
            return true
        }
    }).then(res => {
        const headHtml = `
        <h1 style="text-align: center;">${planForm.title}</h1>
        <div style="text-align: right;margin-bottom: 5px;">${planForm.unit}</div>
        `
        const html = res.html.replaceAll('undefined', '').replaceAll(`style="width:64px"`, `style="width:20px"`)
        tableRef.value?.print({
            html: headHtml + html,
        })
    }).finally(() => {
        tableData.dataList.forEach((item) => {
            if (map.has(item.id!)) {
                item.children = map.get(item.id!)
            }
        })
    })


}

const handleDownloadExcel = () => {
    proxy.download('/project/usagePlan/download', {
        id: planForm.id
    }, `用款计划表${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>

<style>
.snap-message-box .el-message-box__message {
    flex: 1;
}

.snap-message-box .el-message-box__btns {
    display: none;
}
</style>

<style scoped>
:deep(.cell-change:hover) {
    background-color: #409EFF !important;
    color: white;
    cursor: pointer;
}
</style>
