<template>
    <div>
        <div class="mx-[30px] mt-3" v-loading="loading">
            <h1 class="text-center cursor-pointer hover:bg-blue py-2 mt-0 mb-1" @click="handleChangeTableTitle">
                {{ planForm.title }}</h1>
            <div class="flex justify-between">
                <div class="flex items-center gap-2">
                    <el-select v-model="searchForm.basketId" placeholder="请选择项目篮子" class="w-45" clearable>
                        <el-option v-for="item in project_basket_type" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <DeptSelect v-model="searchForm.sbOrgid" class="w-45" placeholder="上报单位" clearable></DeptSelect>
                    <DeptSelect v-model="searchForm.assessOrgid" class="w-45" placeholder="考核主体" clearable></DeptSelect>
                    <el-select v-model="searchForm.natureName" placeholder="请选择项目性质" class="w-45" clearable>
                        <el-option label="阶段性-新增" value="阶段性-新增" />
                        <el-option label="阶段性-结转" value="阶段性-结转" />
                        <el-option label="经常性" value="经常性" />
                    </el-select>
                </div>
                <div class="flex items-center gap-2">
                    <span class="cursor-pointer hover:bg-blue" @click="handleChangeTableUnit">{{ planForm.unit }}</span>
                    <right-toolbar :search="false" :columns="columns" @queryTable="handleSearch"></right-toolbar>
                </div>
            </div>

            <div class="mt-2  mb-[80px]">
                <VxeTable ref="tableRef" :data="getTableData" :height="tableHeight" :tree-config="bindTreeConfig"
                    :row-config="bindRowConfig" :cell-class-name="bindCellClassName"
                    :row-drag-config="bindRowDragConfig" @cell-click="handleCellClick" @row-dragend="handleRowDragEnd"
                    auto-resize border :virtual-y-config="{ enabled: true, gt: 0 }">
                    <vxe-column width="180px" align="center" tree-node drag-sort>
                        <template #default="{ row, $rowIndex }">
                            <div class="inline-flex items-center">
                                <el-dropdown
                                    v-if="row.itemType != EUsageFundPlanItemType.project && row.id != totalRowId"
                                    placement="bottom" @command="handleCommand">
                                    <el-button type="primary" icon="Plus" text plain size="small"></el-button>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item
                                                :command="{ fun: handleAddType, args: { row: row } }">类型</el-dropdown-item>
                                            <el-dropdown-item divided
                                                :command="{ fun: handleAddProjectGroup, args: { row: row } }">项目组</el-dropdown-item>
                                            <el-dropdown-item divided
                                                :command="{ fun: handleAddBlankProject, args: { row: row } }">空项目</el-dropdown-item>
                                            <el-dropdown-item
                                                :command="{ fun: handleAddExistProject, args: { row: row } }">已有项目</el-dropdown-item>

                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                                <!-- 第一个总计不允许删除-->
                                <el-button v-if="row.id != totalRowId && !row.typeCode" type="danger" icon="Close" text
                                    plain size="small" @click="handleRemove(row, $rowIndex)"></el-button>
                            </div>


                        </template>
                    </vxe-column>
                    <vxe-column field="index" title="序号" width="50" align="center"></vxe-column>
                    <template v-for="col in columns" :key="col.prop">
                        <vxe-column v-if="col.visible" :title="col.label" :field="col.prop" header-align="center"
                            :align="col.prop === 'projName' ? 'left' : (amountProps.includes(col.prop) ? 'right' : 'center')"
                            :min-width="handleWidth(col).width">
                            <template #default="{ row }">
                                <template v-if="col.prop === 'projName'">
                                    <div>
                                        <projectNameComponent :row="row"></projectNameComponent>
                                    </div>

                                </template>
                                <template v-else-if="col.prop == 'basketName'">
                                    <dict-tag :options="project_basket_type" :value="row.projectInfo?.basketCode" />
                                </template>
                                <template v-else-if="amountProps.includes(col.prop)">
                                    <template v-if="row.id == totalRowId">
                                        <span>{{ col.prop == 'currPayed' ? row[col.prop] : roundNumber2(row[col.prop])
                                        }}</span>
                                    </template>
                                    <template v-else>
                                        <template v-if="row.itemType == EUsageFundPlanItemType.project">
                                            <span :style="{
                                                color: colorMap.get(row.id + col.prop)
                                            }">{{ col.prop == 'currPayed' ? row[col.prop] : roundNumber2(row[col.prop])
                                            }}</span>
                                        </template>
                                        <template v-else>
                                            <span>{{ col.prop == 'currPayed' ? row[col.prop + 'Temp'] :
                                                roundNumber2(row[col.prop + 'Temp']) }}</span>

                                        </template>
                                    </template>
                                </template>
                                <template v-else>
                                    {{ row[col.prop] }}
                                </template>

                            </template>
                        </vxe-column>
                    </template>
                </VxeTable>
            </div>
        </div>
        <div
            class="form-foot ue-clear no-print py-3 fixed bottom-0 bg-[#f6fbfd]/90 z-100 w-full border-t-solid border-[#dfeaf4] pl-[30px]">

            <!-- <el-button type="primary" @click="handleSave">保存</el-button> -->
            <el-button type="primary" @click="handleCreateSnap">生成快照</el-button>
            <el-button type="primary" @click="handlePrintView">打印预览</el-button>
            <el-button type="primary" @click="handleDownloadExcel">导出EXCEL</el-button>
            <el-button type="danger" @click="$router.back()">返回</el-button>
            <!-- <el-button type="primary">考核主体汇总</el-button> -->
        </div>

        <el-dialog title="类型" v-model="typeShow" width="400px" align-center :close-on-click-modal="false"
            destroy-on-close>
            <AddType :parent-id="parentId" @add="handleAdd" @close="handleAddTypeClose" @cancel="typeShow = false">
            </AddType>
        </el-dialog>
        <el-dialog title="空项目" v-model="blankProjectShow" width="400px" align-center :close-on-click-modal="false"
            destroy-on-close>
            <AddBlankProject :parent-id="parentId" :data="currentRow" @add="handleAdd"
                @close="handleAddBlankProjectClose" @cancel="blankProjectShow = false">
            </AddBlankProject>
        </el-dialog>
        <el-dialog title="项目选择" v-model="existProjectShow" width="800px" align-center :close-on-click-modal="false"
            destroy-on-close>
            <AddExistProject :parent-id="parentId" :data="currentRow" :planForm="planForm" @add="handleAdd"
                @close="handleAddExistProjectClose" @cancel="existProjectShow = false">
            </AddExistProject>
        </el-dialog>
        <el-dialog title="项目组" v-model="projectGroupShow" width="400px" align-center :close-on-click-modal="false"
            destroy-on-close>
            <AddProjectGroup :parent-id="parentId" @add="handleAdd" @close="handleAddProjectGroupClose"
                @cancel="projectGroupShow = false">
            </AddProjectGroup>
        </el-dialog>
        <el-dialog title="数据更新" v-model="updatePropShow" width="300px" :align-center="true"
            :close-on-click-modal="false" destroy-on-close>
            <UpdateValue :id="currentRow!.id!" :year="planForm.year" :type="updatePropType" :prop="updateProp"
                :value="currentRow?.[updateProp]" :data="currentRow" @close="handleUpdateValueClose"
                @cancel="updatePropShow = false">
            </UpdateValue>
        </el-dialog>
        <el-dialog title="快照" v-model="snapViewShow" fullscreen :close-on-click-modal="false" destroy-on-close>
            <Preview :id="snapData!.id" :data="{ ...planForm, tableData }"></Preview>
        </el-dialog>
    </div>

</template>

<script setup lang="tsx">
import { Plus } from '@element-plus/icons-vue';
import AddType from './components/addType.vue';
import AddBlankProject from './components/addBlankProject.vue';
import AddExistProject from './components/addExistProject.vue';
import AddProjectGroup from './components/addProjectGroup.vue';
import UpdateValue from './components/updateValue.vue';
import { fromArray, foreach, find, map } from 'tree-lodash'
import { dayjs, ElMessage, ElMessageBox } from 'element-plus';
import Bignumber, { BigNumber } from 'bignumber.js';
import { VxeTable, VxeColumn, VxeTableInstance, VxeUI } from 'vxe-table'
import VxePcUI from 'vxe-pc-ui'
import 'vxe-table/styles/cssvar.scss'
import { useResizeObserver } from '@vueuse/core';
import { getDetail, save, updateTableTitle, updateTableUnit, delYearData, saveItem, updateTableValue, createSnap, resort } from '@/api/lib/report/usagePlan';
import { cloneDeepWith, debounce, filter, isEmpty } from 'lodash'
import DeptSelect from '@/components/Select/DeptSelect.vue';
import { EApplyAdjustState, EUsageFundPlanItemType } from '@/utils/constants';
import { checkIsNumber, numberFormat, roundNumber2, sumAmount } from '@/utils/common';
import Preview from './preview.vue';
import { WatchStopHandle } from 'vue';
import { v4 as uuidv4 } from 'uuid'

// @ts-ignore
VxeUI.use(VxePcUI)

const { proxy } = getCurrentInstance() as { proxy: any };
const { project_basket_type, snap_type } = proxy.useDict('project_basket_type', 'snap_type');


const projectNameComponent = defineComponent({
    props: {
        row: {
            type: Object as () => any,
            default: ''
        }
    },
    setup(props, { emit }) {
        return () => {
            if (props.row.itemType == EUsageFundPlanItemType.project && !props.row.projId) {
                return (<div class="text-left">
                    <span class={{ 'font-bold': [EUsageFundPlanItemType.type, EUsageFundPlanItemType.projectGroup].includes(props.row.itemType) }}>
                        {props.row.projName}
                        {
                            props.row.projSn ? <br /> : ''
                        }
                        {props.row.projSn}
                    </span>
                    {
                        props.row.isImp == '1' ?
                            <span class="text-red text-4">*</span> : ''
                    }
                </div >)
            } else {
                if (props.row.id != totalRowId && !props.row.typeCode) {
                    return <div class="text-left">
                            <span
                                class={[{ 'font-bold': [EUsageFundPlanItemType.type, EUsageFundPlanItemType.projectGroup].includes(props.row.itemType) }]}>
                                {props.row.projName}
                                {
                                    props.row.projSn ? <br /> : ''
                                }
                                {props.row.projSn}
                            </span>
                            {
                                props.row.isImp == '1' ?
                                    <span class="text-red text-4">*</span> : ''
                            }
                        </div>
                    // return (<el-dropdown placement="bottom"
                    //     onCommand={handleCommand} persistent={false} v-slots={{
                    //         dropdown: () => (
                    //             <el-dropdown-menu>
                    //                 <el-dropdown-item
                    //                     command={{ fun: handleCancelImpProject, args: { row: props.row } }}>不是重点项目</el-dropdown-item>
                    //                 <el-dropdown-item divided
                    //                     command={{ fun: handleSetImpProject, args: { row: props.row, name: '重点项目' } }}>重点项目</el-dropdown-item>
                    //             </el-dropdown-menu>
                    //         )
                    //     }}>
                    //     <div class="text-left">
                    //         <span
                    //             class={['cursor-pointer', { 'font-bold': [EUsageFundPlanItemType.type, EUsageFundPlanItemType.projectGroup].includes(props.row.itemType) }]}>
                    //             {props.row.projName}
                    //             {
                    //                 props.row.projSn ? <br /> : ''
                    //             }
                    //             {props.row.projSn}
                    //         </span>
                    //         {
                    //             props.row.isImp == '1' ?
                    //                 <span class="text-red text-4">*</span> : ''
                    //         }
                    //     </div>
                    // </el-dropdown>)
                } else {
                    if (props.row.id == totalRowId) {
                        return (<div class="font-bold text-center">{props.row.projName}</div>)
                    }
                    return (<span class={{ 'font-bold': props.row.typeCode }}>{props.row.projName}</span>)
                }
            }
        }
    }
})

const tableRef = ref<VxeTableInstance>()
// 计算表格高度
const tableHeight = ref(window.innerHeight - 280)
// 总计行的固定id
const totalRowId = '1'
const bindTreeConfig = {
    expandAll: true,
    transform: true,
    rowField: 'id',
    parentField: 'parentId'
}
const bindRowConfig = {
    drag: true
}
const bindRowDragConfig = {
    isCrossDrag: true,
    visibleMethod: ({ row }: any) => !(row.typeCode || row.id === totalRowId)
}
// 合计单元格字段
const amountProps = [
    'estAmount',
    'libAmount',
    'checkAmount',
    'contractAmount',
    'prevAmount',
    'prevAdjust',
    'prevPayed',
    'currAmount',
    'currEarly',
    'currFormal',
    'currAdjust',
    'currPayed'
];

const year = parseInt(useRoute().query.year as string)
const columns = ref([
    { label: '项目名称', prop: 'projName', width: 150, align: 'center', visible: true, disabled: true },
    { label: '项目篮子', prop: 'basketName', width: 100, align: 'center', visible: true },
    { label: '上报单位', prop: 'sbOrgname', align: 'center', visible: true },
    { label: '考核主体', prop: 'assessOrgname', align: 'center', visible: true },
    { label: '项目性质', prop: 'natureName', align: 'center', visible: true },
    { label: '项目估算', prop: 'estAmount', align: 'center', visible: true },
    { label: '入库金额', prop: 'libAmount', align: 'center', visible: true },
    { label: '核定金额', prop: 'checkAmount', align: 'center', visible: true },
    { label: '合同金额', prop: 'contractAmount', align: 'center', visible: true },
    { label: `至${year - 1}底累计执行`, prop: 'prevAmount', align: 'center', visible: true },
    { label: `${year - 1}调整计划`, prop: 'prevAdjust', align: 'center', visible: true },
    { label: `${year - 1}实际执行`, prop: 'prevPayed', align: 'center', visible: true },
    { label: `${year}资金需求`, prop: 'currAmount', align: 'center', visible: true },
    { label: `${year}初步使用计划`, prop: 'currEarly', align: 'center', visible: true },
    { label: `${year}正式计划`, prop: 'currFormal', align: 'center', visible: true },
    { label: `${year}调整计划`, prop: 'currAdjust', align: 'center', visible: true },
    { label: `${year}已执行`, prop: 'currPayed', align: 'center', visible: true },
    { label: '开工(采购)时间', prop: 'beginDate', align: 'center', visible: true },
    { label: '竣工(完成)时间', prop: 'endDate', align: 'center', visible: true },
    { label: '备注1', prop: 'remark1', align: 'center', visible: true },
    { label: '备注2', prop: 'remark2', align: 'center', visible: true },
]);

const handleWidth = (col: typeof columns.value[0]) => {
    if (col.prop == 'projName') {
        return {
            width: '300px',
        }
    } else if (col.prop == 'remark1' || col.prop == 'remark2') {
        return {
            width: '200px'
        }
    } else {
        return {
            width: '120px',
        }
    }
}

const loading = ref(false)
type usagePlanItemTree = IUsagePlanItem & { index?: number, children?: usagePlanItemTree | IUsagePlanItem[] }
const tableData = reactive({
    totalRow: {
        id: totalRowId,
        parentId: '0',
        projName: '总计',
        seqNo: 0
    } as usagePlanItemTree,
    dataList: [] as usagePlanItemTree[],
})
const getTableData = computed(() => {
    return [tableData.totalRow, ...tableData.dataList]
});


const planForm = reactive<IUsagePlan>({
    // title: `${year}年度上海化学工业区用款计划表`
} as IUsagePlan)
const searchForm = reactive({
    basketId: '',
    sbOrgid: null,
    natureName: '',
    assessOrgid: null,
})
// 存储哪些单元格文字设置红色，健为id+字段名, 值为颜色值
const colorMap = new Map<string, string>()
const originList = ref<IUsagePlanItem[]>([])
let stopColumnChangeWatch: WatchStopHandle | null
const search = (columnsStr?: string) => {
    loading.value = true
    getDetail(year, searchForm, columnsStr).then(async res => {
        if (stopColumnChangeWatch != null) {
            stopColumnChangeWatch()
        }
        colorMap.clear()
        if (res.data) {
            planForm.id = res.data?.id
            planForm.year = res.data.year
            planForm.title = res.data.title
            planForm.unit = res.data.unit
            console.time("caculate")

            const list = (res.data?.dataList ?? []).filter(t => {
                if ([EApplyAdjustState.reject, EApplyAdjustState.draft].includes(t.projectInfo?.state)) {
                    return false
                }
                return true
            })
            const func = (data: {
                id: string,
                prop: string,
                val1?: number | null,
                val2?: number,
                color?: string
            } = { id: '', prop: '', color: 'red' }) => {
                let num
                if (!checkIsNumber(data.val1)) {
                    num = data.val2
                } else {
                    num = data.val1
                    colorMap.set(`${data.id}${data.prop}`, data.color ?? 'black')
                }
                return num
            }
            list.forEach(t => {
                if (!t.projectInfo) return
                t.projName = t.projectInfo.name!
                t.projSn = t.projectInfo.projectSn?.formalSn ?? t.projectInfo.projectSn?.tempSn ?? ''
                t.sbOrgid = t.projectInfo.submitOrgid
                // t.sbOrgname = t.projectInfo.submitOrgidName
                t.assessOrgid = t.projectInfo.assessOrgid
                t.assessOrgname = t.projectInfo.assessOrgname
                t.basketId = t.projectInfo.basketCode
                t.basketName = t.projectInfo.basketName
                if (t.projectInfo?.natureCode == 'JC') {
                    t.natureName = '经常性'
                } else {
                    t.natureName = '阶段性'
                    if (t.projectInfo?.currentYearRelation?.projType == 1) {
                        t.natureName += '-结转'
                    }
                    if (t.projectInfo?.currentYearRelation?.projType == 3) {
                        t.natureName += '-新增'
                    }
                }
                t.estAmount = func({
                    id: t.id!,
                    prop: 'estAmount',
                    val1: t.estAmount,
                    val2: t.projectInfo?.estAmount
                })
                t.libAmount = func({
                    id: t.id!,
                    prop: 'libAmount',
                    val1: t.libAmount,
                    val2: t.projectInfo?.libAmount
                })
                t.checkAmount = func({
                    id: t.id!,
                    prop: 'checkAmount',
                    val1: t.checkAmount,
                    val2: t.projectInfo?.checkAmount
                })
                t.contractAmount = func({
                    id: t.id!,
                    prop: 'contractAmount',
                    val1: t.contractAmount,
                    val2: t.projectInfo?.contractAmount
                })
                t.currAmount = func({
                    id: t.id!,
                    prop: 'currAmount',
                    val1: t.currAmount,
                    val2: t.projectInfo?.fundPlan?.declareAmount
                })
                t.currFormal = func({
                    id: t.id!,
                    prop: 'currFormal',
                    val1: t.currFormal,
                    val2: t.projectInfo?.fundPlan?.formalAmount
                })
                t.prevAdjust = func({
                    id: t.id!,
                    prop: 'prevAdjust',
                    val1: t.prevAdjust,
                    val2: EApplyAdjustState.qrtz == t.projectInfo?.prevFundPlan?.applyState ?
                        t.projectInfo?.prevFundPlan?.adjustAmount :
                        t.projectInfo?.prevFundPlan?.formalAmount
                })

                t.currAdjust = func({
                    id: t.id!,
                    prop: 'currAdjust',
                    val1: t.currAdjust,
                    val2: checkIsNumber(t.projectInfo?.fundPlan?.adjustAmount) ? t.projectInfo?.fundPlan?.adjustAmount :
                        ([EApplyAdjustState.qrtz, EApplyAdjustState.ysb].includes(t.projectInfo?.fundPlan?.applyState) ?
                            t.projectInfo?.fundPlan?.applyAdjust : t.projectInfo?.fundPlan?.formalAmount
                        )
                })

                // 当年初步使用计划 当年正式计划
                // 由后台一个配置项进行配置，不同的配置会设置不同字段的金额

                // 至上年底累计执行
                // 计算拨款单今年以前的所有金额累加
                t.prevAmount = func({
                    id: t.id!,
                    prop: 'prevAmount',
                    val1: t.prevAmount,
                    val2: t.projectInfo?.prevAmount
                })

                // 上年实际执行
                // 计算上一年的所有金额累加
                t.prevPayed = func({
                    id: t.id!,
                    prop: 'prevPayed',
                    val1: t.prevPayed,
                    val2: t.projectInfo?.prevPayed
                })

                // 当年已执行
                // 计算当年的所有金额累加
                t.currPayed = func({
                    id: t.id!,
                    prop: 'currPayed',
                    val1: t.currPayed,
                    val2: t.projectInfo?.currPayed
                })

            })
            originList.value = list
            tableData.dataList = caculate(JSON.parse(JSON.stringify(originList.value)))
            if (res.data?.tableColumns && res.data?.tableColumns != '[]') {
                columns.value = JSON.parse(res.data?.tableColumns) ?? []
            }
            console.timeEnd("caculate")
            await nextTick(() => {
                handleResort()
                tableRef.value?.setAllTreeExpand(true)
            })



        }


    }).then(() => {
        stopColumnChangeWatch = watch(columns, debounce(() => {
            // 当列发生变化时，重新计算表格数据
            save({
                year: year,
                title: planForm.title,
                unit: planForm.unit,
                tableColumns: JSON.stringify(columns.value),
            })
        }, 3000), { deep: true })
    })
        .finally(() => loading.value = false)
}
search(JSON.stringify(columns.value))
const handleSearch = () => {
    searchForm.assessOrgid = null
    searchForm.basketId = ''
    searchForm.natureName = ''
    searchForm.sbOrgid = null
    search()
}
watch(searchForm, () => {
    // 多条件筛选，相当于 where and 查询
    // 实现并集查询：只要任一条件匹配即保留
    const filtered = originList.value.filter(t => {
        if ((searchForm.sbOrgid && t.sbOrgid != searchForm.sbOrgid) ||
            (searchForm.assessOrgid && t.assessOrgid != searchForm.assessOrgid) ||
            (searchForm.basketId && t.basketId != searchForm.basketId) ||
            (searchForm.natureName && t.natureName != searchForm.natureName)) {
            return false
        }
        return true
    });
    tableData.dataList = caculate(JSON.parse(JSON.stringify(filtered)))
    handleResort()
    nextTick(() => {
        tableRef.value?.setAllTreeExpand(true)
    })
})

/**
 * 统计金额
 * @param list 
 */
const caculate = (list: IUsagePlanItem[]) => {
    const tree = fromArray(list, { itemKey: 'id', parentKey: 'parentId' })
    foreach(tree, (node) => {
        amountProps.forEach(prop => {
            const tempProp = prop + 'Temp'
            if (node[prop] != null) {
                node[tempProp] = node[prop]
            } else {
                if (node.children) {
                    node[tempProp] = sumAmount(node.children.map((child: IUsagePlanItem) => {
                        if (checkIsNumber(child[tempProp]))
                            return child[tempProp]
                        return 0
                    }))
                }
            }


        })
    }, { strategy: 'post' })

    amountProps.forEach(prop => {
        tableData.totalRow[prop] = sumAmount(tree.map((t: any) => checkIsNumber(t[prop + 'Temp']) ? t[prop + 'Temp'] : 0))
    })
    return list
}

/**
 * 对表格和项目进行排序
 */
const handleResort = () => {
    let index = 1
    // 这个用来记录项目组项目，如果是项目组的话就存起来，用于判断如果下面的元素的父级是该项目组，就跳过
    const map = new Map<string, string>()
    foreach(tableRef.value?.getTableData().fullData!, (item) => {
        if (item.itemType == EUsageFundPlanItemType.projectGroup) {
            map.set(item.id, '1')
            if (!map.has(item.parentId)) {
                item.index = index
                index++
            }

        }
        if (item.itemType == EUsageFundPlanItemType.type) {
            if (map.has(item.parentId)) {
                map.set(item.id, '1')
            }
        }
        if (item.itemType == EUsageFundPlanItemType.project) {
            if (!map.has(item.parentId)) {
                item.index = index
                index++
            }

        }
        if (item.children && item.children.length > 0) {
            item.children.sort((a: any, b: any) => a.seqNo ?? (0 - b.seqNo))
        }
    })
}

const handleRowDragEnd = (data: { newRow: usagePlanItemTree, oldRow: usagePlanItemTree, dragRow: usagePlanItemTree, offsetIndex: number }) => {

    const datas: { id: string, parentId: string, seqNo?: number }[] = []
    if (data.newRow.parentId == '0' || !data.newRow.parentId) {
        tableRef.value?.getTableData().fullData!.forEach((item, index) => {
            if (item.id != totalRowId) {
                datas.push({
                    id: item.id,
                    parentId: '0',
                    seqNo: index
                })
            }
        })
    } else {
        const row = find(tableRef.value?.getTableData().fullData!, (item) => {
            if (item.id == data.newRow.parentId) {
                return true
            }
            return false
        })
        datas.push(...row?.children?.map((t: usagePlanItemTree, index: number) => {
            return {
                id: t.id!,
                parentId: t.parentId!,
                seqNo: index + 1
            }
        }) ?? [])
    }

    resort(year, datas).then(() => {
        ElMessage.success('排序成功')
        search()
    })
}


const handleCommand = (command: any) => {
    if (command.fun) {
        command.fun(command.args);
    }
};
const typeShow = ref(false);
const blankProjectShow = ref(false);
const existProjectShow = ref(false);
const projectGroupShow = ref(false);
const parentId = ref<string>('0');
const currentRow = ref<IUsagePlanItem>();
const handleAddType = (args: any) => {
    typeShow.value = true;
    if (args.row.id == totalRowId) {
        parentId.value = '0';
    } else {
        parentId.value = args.row.id
    }
};
const handleAddBlankProject = (args: any) => {
    currentRow.value = undefined;
    blankProjectShow.value = true;
    if (args.row.id == totalRowId) {
        parentId.value = '0';
    } else {
        parentId.value = args.row.id
    }
};
const handleAddExistProject = (args: any) => {
    existProjectShow.value = true;
    if (args.row.id == totalRowId) {
        parentId.value = '0';
    } else {
        parentId.value = args.row.id
    }
};
const handleAddProjectGroup = (args: any) => {
    projectGroupShow.value = true;
    if (args.row.id == totalRowId) {
        parentId.value = '0';
    } else {
        parentId.value = args.row.id
    }
};
const handleCancelImpProject = (args: any) => {
    loading.value = true
    updateTableValue({ id: args.row.id, year: planForm.year, prop: 'isImp', value: '0' }).then(() => {
        search()
    }).finally(() => loading.value = false)
};
const handleSetImpProject = (args: any) => {
    loading.value = true
    updateTableValue({ id: args.row.id, year: planForm.year, prop: 'isImp', value: '1' }).then(() => {
        search()
    }).finally(() => loading.value = false)
};



const handleAdd = (dataList: IUsagePlanItem[]) => {
    // tableData.dataList.push(data);
    loading.value = true
    saveItem({ id: planForm.id, dataList: dataList }).then(res => {
        search()
    }).finally(() => loading.value = false)
    // nextTick(() => {
    //     tableRef.value?.setAllTreeExpand(true)
    // })
};

const handleRemove = (row: IUsagePlanItem, index: number) => {
    if (row.id == totalRowId) {
        return;
    }
    ElMessageBox.confirm('是否确认移除该项目？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const fullData = tableRef.value?.getTableData().fullData;
        if (fullData) {
            const tree = find(fullData, (row1) => {
                return row.id === row1.id;
            });
            const ids: string[] = []
            foreach(tree!, (row1) => {
                ids.push(row1.id)
            });
            loading.value = true
            delYearData(year, ids).then(() => {
                ElMessage.success('删除成功');
                search()
            }).finally(() => loading.value = false)
        }
    })
};

const updatePropShow = ref(false);
const updatePropType = ref<any>('number')
const updateProp = ref<any>('');
const handleCellClick = (data: { row: any, column: any, }) => {
    currentRow.value = data.row
    if (data.column.field == 'projName') {
        if (data.row.itemType == EUsageFundPlanItemType.project && !data.row.projId) {
            blankProjectShow.value = true
        }
    }
    if ([...amountProps, 'beginDate', 'endDate', 'remark1', 'remark2'].includes(data.column.field)) {
        if (data.row.itemType == EUsageFundPlanItemType.project || data.row.sumMode == 2) {
            if (['beginDate', 'endDate'].includes(data.column.field)) {
                updatePropType.value = 'string';
            } else if (['remark1', 'remark2'].includes(data.column.field)) {
                updatePropType.value = 'text';
            } else {
                updatePropType.value = 'number';
            }
            updateProp.value = data.column.field;
            updatePropShow.value = true;
        }
    }


};

const bindCellClassName = (data: { row: IUsagePlanItem, column: any, rowIndex: number, columnIndex: number }) => {
    if (data.row.id != totalRowId) {
        if ([...amountProps, 'beginDate', 'endDate', 'remark1', 'remark2'].includes(data.column.field)) {
            if ((data.row.itemType == EUsageFundPlanItemType.project || data.row.sumMode == 2)) {
                return 'cell-change';
            }
        }
        if (data.column.field == 'projName') {
            if (data.row.itemType == EUsageFundPlanItemType.project && !data.row.projId) {
                return 'cell-change';
            }
        }

    }
    return '';
};

const handleUpdateValueClose = (data: any) => {
    updatePropShow.value = false
    search()
}
const handleAddTypeClose = () => {
    typeShow.value = false
    currentRow.value = undefined
    // search()
}
const handleAddBlankProjectClose = () => {
    blankProjectShow.value = false
    currentRow.value = undefined
    // search()
}
const handleAddExistProjectClose = () => {
    existProjectShow.value = false
    currentRow.value = undefined
    // search()
}
const handleAddProjectGroupClose = () => {
    projectGroupShow.value = false
    currentRow.value = undefined
    // search()
}


const handleChangeTableTitle = () => {
    ElMessageBox.prompt('请输入', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: planForm.title,
        inputValidator(value) {
            if (value.length > 200) {
                return '文字超长'
            }
            return true
        },
        type: 'info'
    }).then((res) => {
        loading.value = true
        updateTableTitle(year, res.value).then(() => {
            planForm.title = res.value
            ElMessage.success('修改成功')
        }).finally(() => loading.value = false)
    })
}

const handleChangeTableUnit = () => {
    ElMessageBox.prompt('请输入', '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: planForm.unit,
        inputValidator(value) {
            if (value.length > 200) {
                return '文字超长'
            }
            return true
        },
        type: 'info'
    }).then((res) => {
        loading.value = true
        updateTableUnit(year, res.value).then(() => {
            planForm.unit = res.value
            ElMessage.success('修改成功')
        }).finally(() => loading.value = false)
    })
};

const handleSave = () => {
    ElMessage.success('保存成功')
    // save({
    //     year: year,
    //     title: planForm.title,
    //     unit: planForm.unit,
    //     tableColumns: JSON.stringify(columns.value),
    // }).then(res => {
    //     ElMessage.success('保存成功')
    // })
}

const handleCreateSnap = () => {

    ElMessageBox({
        title: '创建快照',
        customClass: 'snap-message-box',
        showConfirmButton: false,
        message: () => {
            const title = ref(planForm.title)
            const snapType = ref('')
            const handleClick = () => {
                if (!title.value) {
                    ElMessage.error('请输入快照名称')
                    return
                }
                loading.value = true
                let newList = JSON.parse(JSON.stringify(originList.value)) as IUsagePlanItem[]
                const tree = fromArray(newList, { itemKey: 'id', parentKey: 'parentId' })
                foreach(tree, (node) => {
                    amountProps.forEach(prop => {
                        if (node[prop] != null) return
                        if (node.children) {
                            node[prop] = sumAmount(node.children.map((child: IUsagePlanItem) => {
                                if (checkIsNumber(child[prop]))
                                    return child[prop]
                                return 0
                            }))
                        }
                    })
                }, { strategy: 'post' })
                newList = newList.map(t => ({ ...t, children: null }))
                createSnap(planForm.year, title.value, snapType.value, newList).then(res => {
                    ElMessage.success('快照创建成功')
                    ElMessageBox.close()

                }).finally(() => loading.value = false)

            }
            return <div >
                <el-form-item label="快照名称" >
                    <el-input v-model={title.value} maxlength="100"></el-input>
                </el-form-item>
                <el-form-item label="快照类型">
                    <el-select v-model={snapType.value} placeholder="请选择快照类型" class="!w-40" clearable>
                        {snap_type.value.map((item: any) => <el-option label={item.label} value={item.value} ></el-option>)}
                    </el-select>
                </el-form-item>
                <div class={'text-right'}>
                    <el-button type="primary" onClick={handleClick}>确定</el-button>
                </div>

            </div>
        }
    })
}

const snapData = ref<IUsagePlan>()
const snapViewShow = ref(false)
const handlePrintView = () => {
    // 打印的时候如果不把children数据删掉，会重复，这里先暂存children数据，等会再恢复
    const map = new Map<string, any[]>()
    tableRef.value?.getPrintHtml({
        dataFilterMethod: (data: { row: any }) => {
            map.set(data.row.id, data.row.children)
            data.row.children = []
            return true
        }
    }).then(res => {
        const headHtml = `
        <h1 style="text-align: center;">${planForm.title}</h1>
        <div style="text-align: right;margin-bottom: 5px;">${planForm.unit}</div>
        `
        const html = res.html.replaceAll('undefined', '').replaceAll(`style="width:64px"`, `style="width:20px"`)

        // 使用 DOMParser 解析 HTML 字符串
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 获取解析后的 DOM 元素
        const tableEl = doc.body.firstElementChild! as HTMLTableElement;
        const tableTrs = Array.from(tableEl.querySelectorAll('tbody tr')) as HTMLTableRowElement[];

        tableData.dataList.forEach((item, index) => {
            const td = tableTrs[index + 1].querySelector('td:nth-child(3)')
            if (td) {
                td.innerHTML = `<div>${item.projName}${item.projSn ? '<br />' : ''}${item.projSn ?? ''}</div>`
            }
        })
        tableRef.value?.print({
            html: headHtml + tableEl.outerHTML,
        })
    }).finally(() => {
        tableData.dataList.forEach((item) => {
            if (map.has(item.id!)) {
                item.children = map.get(item.id!)
            }
        })
    })


}

const handleDownloadExcel = () => {
    proxy.download('/project/usagePlan/download', {
        id: planForm.id
    }, `用款计划表${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
}
</script>

<style>
.snap-message-box .el-message-box__message {
    flex: 1;
}

.snap-message-box .el-message-box__btns {
    display: none;
}
</style>

<style scoped>
:deep(.cell-change:hover) {
    background-color: #409EFF !important;
    color: white;
    cursor: pointer;
}
</style>
