<template>
    <div class="app-container">
        <div v-hasPermi="['fund:usagePlan:save']" class="flex gap-2">
            <el-date-picker v-model="year" type="year" placeholder="选择年份" format="YYYY年" value-format="YYYY"
                class="mb-4" />
            <el-button type="primary" class="mb-4" @click="handleAdd">新增用款计划</el-button>
        </div>
        <el-table :data="dataList" border row-key="id">
            <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
            <el-table-column prop="year" label="年度" align="center" width="120"></el-table-column>
            <el-table-column prop="title" label="标题" align="center"></el-table-column>
            <el-table-column prop="projectCount" label="项目数量" align="center" width="120"></el-table-column>
            <el-table-column prop="snapCount" label="快照数量" align="center" width="120">
                <template #default="{ row }">
                    <template v-if="auth.hasPermi('fund:usagePlan:createSnap')">
                        <el-link type="primary" @click="handleSnapListShow(row)">{{ row.snapCount }}</el-link>
                    </template>
                    <template v-else>
                        {{ row.snapCount }}
                    </template>
                </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" align="center" width="180"
                :formatter="(row: any) => dateFormat(row.updateTime)"></el-table-column>
            <el-table-column prop="closed" label="开放状态" align="center" width="130">
                <template #default="{ row }">
                    <el-switch v-model="row.closed" :disabled="!auth.hasPermi('fund:usagePlan:save')" active-value="0"
                        inactive-value="1" @change="handleClosedChange(row.id)"></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="220">
                <template #default="{ row }">
                    <el-button v-hasPermi="['fund:usagePlan:view']" type="primary" link icon="view"
                        @click="handleTableView(row)">查看</el-button>
                    <template v-if="row.closed == '0'">
                        <el-button v-hasPermi="['fund:usagePlan:save']" type="primary" link icon="edit"
                            @click="$router.push({ path: 'table/detail', query: { year: row.year } })">编辑</el-button>
                        <el-button v-hasPermi="['fund:usagePlan:delete']" type="danger" link icon="delete"
                            @click="handleRemove(row)">删除</el-button>
                    </template>

                    <!-- <el-button v-hasPermi="['fund:usagePlan:createSnap']" type="primary" link icon="picture"
                        @click="handleSnapListShow(row)">快照</el-button> -->
                </template>
            </el-table-column>
        </el-table>

        <el-dialog title="快照" v-model="snapListShow" width="900px" :close-on-click-modal="false" destroy-on-close>
            <el-table :data="snapList" border height="700">
                <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
                <el-table-column label="快照名称" header-align="center" align="left">
                    <template #default="{ row }">
                        <el-link type="primary" @click="handleClickSnap(row)">

                            <span class="ml-3">{{ row.snapName }}</span>
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column label="快照时间" width="170" align="center">
                    <template #default="{ row }">
                        <span>{{ dateFormat(row.createTime, 'YYYY-MM-DD HH:mm:ss') }}</span>
                    </template>
                </el-table-column>

                <el-table-column label="快照类型" width="100" align="center">
                    <template v-slot="{ row }">
                        <span v-if="row.snapType">{{snap_type.find((t: any) => t.value == row.snapType)?.label}}</span>
                    </template>
                </el-table-column>

                <template #empty>
                    <el-empty description="暂无数据"></el-empty>
                </template>
            </el-table>
        </el-dialog>
        <el-dialog title="快照" v-model="snapViewShow" fullscreen :close-on-click-modal="false" destroy-on-close>
            <Preview :id="snapData!.id" @close="snapViewShow = false"></Preview>
        </el-dialog>
        <el-dialog title="查看" v-model="tableViewShow" fullscreen :close-on-click-modal="false" destroy-on-close>
            <TableView :year="snapData!.year" @close="tableViewShow = false"></TableView>
        </el-dialog>
    </div>

</template>

<script setup lang="ts">
import { getList, updateTableClose, delByYear, getListSnap } from '@/api/lib/report/usagePlan';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox, formatter } from 'element-plus';
import Preview from './preview.vue';
import TableView from './tableView.vue';
import auth from '@/plugins/auth';

const { proxy } = getCurrentInstance() as { proxy: any };
const { snap_type } = proxy.useDict('snap_type');

const year = ref<number>()
const router = useRouter()

const dataList = ref<IUsagePlan[]>([]);
const search = () => {
    getList().then(res => {
        dataList.value = res.data ?? [];
    })
}
search()
const handleAdd = () => {
    if (!year.value) {
        ElMessage.error('请选择年份');
        return
    }
    router.push({ path: 'table/detail', query: { year: year.value } });
}

const handleClosedChange = (id: string) => {
    updateTableClose(id).then(res => {
        ElMessage.success('操作成功')
    })
}

const snapListShow = ref(false)
const snapList = ref<IUsagePlan[]>([])
const handleSnapListShow = (row: IUsagePlan) => {
    snapListShow.value = true

    getListSnap(row.year).then(res => {
        snapList.value = res.data ?? []
    })
}

const snapData = ref<IUsagePlan>()
const snapViewShow = ref(false)
const handleClickSnap = (row: IUsagePlan) => {
    snapViewShow.value = true
    snapData.value = row
}

const tableViewShow = ref(false)
const handleTableView = (row: IUsagePlan) => {
    tableViewShow.value = true
    snapData.value = row
}

const handleRemove = (row: IUsagePlan) => {
    ElMessageBox.confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        delByYear(row.year).then(res => {
            ElMessage.success('操作成功')
            search()
        })
    })
}
</script>