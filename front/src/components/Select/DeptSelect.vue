<template>
    <el-tree-select v-bind="$attrs" v-model="deptId" :data="deptStore.deptTreeList" :default-expanded-keys="getExpandedKeys"
        :props="{ value: 'deptId', label: 'deptName', children: 'children', disabled: handleDisbaled }" value-key="deptId" check-strictly />
</template>

<script setup lang="ts">
import { cloneDeep } from "lodash";
import { useDeptStore } from "./deptStore";

const deptId = defineModel("deptId")

const { proxy } = getCurrentInstance() as any;
const deptStore = useDeptStore()

const getExpandedKeys = computed(() => {
    return deptStore.deptList.map((t: any) => t.deptId)
})

const handleDisbaled = (data: SysDept) => {
    if (data.deptId == 200) {
        return true
    }
    return false
}

defineExpose({
    getDeptList() {
        return deptStore.deptList
    },
});
</script>