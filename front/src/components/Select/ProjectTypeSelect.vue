<template>
    <el-tree-select v-bind="$attrs" v-model="model" :data="getProjType" default-expand-all class="w-full">
    </el-tree-select>
</template>

<script lang="ts" setup>
const { proxy } = getCurrentInstance() as { proxy: any };
const { project_type } = proxy.useDict('project_type');

const model = defineModel<string>()

const getProjType = computed(() => {
    if (project_type.value) {
        const IS0List = project_type.value.filter((t: any) => t.value.includes('0'))
        return IS0List.map((t: any) => {
            const children = project_type.value.filter((t1: any) => t1.value != t.value && t1.value.includes(t.value.at(0)))
            return {
                ...t,
                disabled: children.length > 0,
                children
            }
        })
        
    }
    return []
})
</script>