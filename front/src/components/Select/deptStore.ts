import { listDeptAll } from "@/api/system/ndept"
import { fromArray } from "tree-lodash"
// @ts-ignore
import useUserStore from '@/store/modules/user'

// 发展公司下属单位id
export const fzgxXsUnit = [5547, 5548, 5549, 5550, 5551, 5552, 5553, 5554, 5555, 5563, 5564, 5578]

export const useDeptStore = defineStore('deptStore', () => {
    const deptList = ref<SysDept[]>([])
    const deptTreeList = ref<(SysDept & { children: [] })[]>([])
    listDeptAll().then((res: IResult<SysDept[]>) => {
        const userStore = useUserStore()
        let list = []
        if (userStore.deptId == 100) {
            list = res.data ?? []
        } else {
            list = res.data?.filter(t => t.deptId != 100) ?? []
        }
        deptList.value = list.filter(t => t.status == '0')

        const data = fromArray(deptList.value as any, { childrenKey: 'children', parentKey: 'parentId', itemKey: 'deptId' })
        deptTreeList.value = data as any

    })

    return {
        deptList,
        deptTreeList
    }
})