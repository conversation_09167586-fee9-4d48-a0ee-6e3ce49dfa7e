<template>
    <el-date-picker v-model="dateRange" v-bind="$attrs" @change="handleChange" @clear="handleClear"></el-date-picker>
</template>

<script setup lang="ts">

const dateRange = ref<string[]>([])
const beginDate = defineModel('beginDate', {type: [String] as PropType<string>})
const endDate = defineModel('endDate', {type: [String] as PropType<string>})

watch([beginDate, endDate], () => {
    if (beginDate.value && endDate.value) {
        dateRange.value = [beginDate.value, endDate.value]
    } else {
        dateRange.value = []
    }
}, {immediate: true})

const handleChange = (val: any) => {
    beginDate.value = val?.[0]
    endDate.value = val?.[1]
}

const handleClear = () => {
    beginDate.value = ''
    endDate.value = ''
}
</script>