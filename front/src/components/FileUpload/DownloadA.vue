<template>
    <div v-bind="$attrs" @click="handleView">
        <slot>
            <el-link class="break-all" :type="($attrs.type as any) ?? 'primary'">{{ file.fileName }}</el-link>
        </slot>
    </div>
   <el-dialog v-model="fileViewShow" width="1000px" append-to-body>
        <template #header>
            <span class="text-[16px]">预览文件</span>
        </template>
        <iframe :src="fileUrl" class="w-full h-[700px] border-none" />
    </el-dialog>
</template>

<script setup lang="ts">
import { downloadFile } from '@/api/lib/project/file';
import { ElLoading } from 'element-plus';
import saveAs from 'file-saver';

const props = defineProps<{
    file: ISysAttachment
}>()

const fileViewShow = ref(false)
const fileUrl = ref('')
const handleView = () => {
    const loadingInstance1 = ElLoading.service({ fullscreen: true })
    downloadFile(props.file.id!).then(res => {
        const ext = props.file.fileExt!.toLocaleLowerCase()
        let mimeType = ''
        if (/(jpg|png|bmp|webp|jpeg)$/i.test(ext)) mimeType = 'image/' + props.file.fileExt
        if (/(pdf)$/i.test(ext)) mimeType = 'application/pdf'
        if (mimeType) {
            const blob = new Blob([res], { type: mimeType })
            fileUrl.value = URL.createObjectURL(blob)
            fileViewShow.value = true
        } else {
            saveAs(new Blob([res]), props.file.fileName ?? '文件')
        }

    }).finally(() => {
        loadingInstance1.close()
    })
}

</script>