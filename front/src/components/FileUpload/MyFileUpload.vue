<template>
    <el-upload v-bind="$attrs" :action="actionUrl" :headers="headers" :show-file-list="false"
        :on-success="handleOnSuccess" :before-upload="handleBeforeUpload">
        <slot>
            <el-button type="primary">选择文件</el-button>
        </slot>
    </el-upload>
</template>

<script setup lang="ts">
// @ts-ignore
import { getToken } from "@/utils/auth";
import { Awaitable } from "@vueuse/core";
import { ElMessage, UploadRawFile } from "element-plus";

const emit = defineEmits(['upload-success'])
const props = defineProps<{
    action?: string;
    beforeUpload?: (file: UploadRawFile) => Awaitable<void | undefined | null | boolean | File | Blob>;
}>()

const actionUrl = import.meta.env.VITE_APP_BASE_API + (props.action ?? '/resource/file/upload');
const headers = ref({ Authorization: "Bearer " + getToken() });

const handleBeforeUpload = (rawFile: UploadRawFile) => {
    // const isLt50M = rawFile.size / 1024 / 1024 < 50; // 50MB
    const isLt200M = rawFile.size / 1024 / 1024 < 200
    if (!isLt200M) {
        ElMessage.error('上传文件大小不能超过 200MB!');
        return false
    }
    if (props.beforeUpload) {
        return props.beforeUpload(rawFile)
    }
    return true;
}

const handleOnSuccess = (response: any) => {
    emit('upload-success', response)
}
</script>