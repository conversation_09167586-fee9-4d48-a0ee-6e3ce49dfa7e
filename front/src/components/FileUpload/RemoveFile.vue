<template>
    <slot>
        <el-button v-bind="$attrs" type="danger" text icon="delete" @click="handleRemove"></el-button>
    </slot>
</template>

<script lang="ts" setup>
import { removeFile } from '@/api/lib/project/file';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['success'])
const props = defineProps<{
    file: ISysAttachment
}>()

const handleRemove = () => {
    removeFile(props.file.id!).then(() => {
        ElMessage.success('删除成功')
        emit('success')
    })
}
</script>