<template>
    <el-table v-bind="$attrs" :data="fileList.concat(billFileList)" border v-loading="loading">
        <el-table-column v-if="!hiddenColumns.includes('index')" type="index" label="序号" align="center"
            width="60px"></el-table-column>
        <el-table-column v-if="!hiddenColumns.includes('primaryType')" label="附件类型" prop="primaryType" header-align="center" :align="columnOption?.['primaryType'].align ?? 'center'"
            :width="columnOption?.['primaryType'].width ?? 'auto'" show-overflow-tooltip></el-table-column>

        <el-table-column v-if="!hiddenColumns.includes('fileName')" label="文件名称" prop="fileName" header-align="center" :align="columnOption?.['fileName'].align ?? 'left'"
            :width="columnOption?.['fileName'].width ?? 'auto'" show-overflow-tooltip>
            <template #default="{ row }">
                <template v-if="row.serialNumber">
                    <a class="text-[var(--el-color-primary-light-3)]" @click="handleView(row)">({{ row.serialNumber }}){{
                        row.fileName }}</a>
                </template>
                <template v-else>
                    <a class="text-[var(--el-color-primary-light-3)]" @click="handleView(row)">{{ row.fileName }}</a>
                </template>

            </template>
        </el-table-column>
        <el-table-column v-if="!hiddenColumns.includes('creator')" label="上传人" prop="creator" align="center"
            width="100px" show-overflow-tooltip></el-table-column>
        <el-table-column v-if="!hiddenColumns.includes('createTime')" label="上传时间" prop="createTime" align="center"
            width="180px" :formatter="(row: any) => dateFormat(row.createTime, 'YYYY-MM-DD HH:mm:ss')"
            show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" align="center" width="120px">
            <template #default="{ row }">
                <div class="flex justify-center">
                    <el-button v-if="!hiddenColumns.includes('del')" class="px-1" type="danger" size="small"
                        icon="delete" plain text @click="handleRemove(row.id)">删除</el-button>
                    <el-button class="!ml-0 px-1" type="primary" size="small" icon="download" plain text
                        @click="handleDownload(row)">下载</el-button>
                </div>

            </template>
        </el-table-column>
        <template #empty>
            <slot name="empty"></slot>
        </template>

    </el-table>
    <el-dialog v-model="fileViewShow" width="1000px" append-to-body>
        <template #header>
            <span class="text-[16px]">预览文件</span>
        </template>
        <iframe :src="fileUrl" class="w-full h-[700px] border-none" />
    </el-dialog>
</template>

<script setup lang="ts">
import { removeFile, downloadFile, getFileList1 } from '@/api/lib/project/file';
import { dateFormat } from '@/utils/common';
import { ElMessage, ElMessageBox } from 'element-plus';
import { saveAs } from 'file-saver'

const { proxy } = getCurrentInstance() as { proxy: any };

const emit = defineEmits(['update']);
const props = withDefaults(defineProps<{
    sourceId: string|number,
    minorId?: string|number,
    primaryType?: string,
    hiddenColumns?: string[],
    columnOption?: Record<string, { width?: string, align?: 'center' | 'right' | 'left' }>,
    billFileList?: (ISysAttachment & { serialNumber: string, })[]
}>(), {
    hiddenColumns: () => [] as string[],
    billFileList: () => []
});

const fileList = ref<ISysAttachment[]>([]);
const search = () => {
    getFileList1(props.sourceId, props.primaryType, props.minorId).then(res => {
        fileList.value = res.data ?? [];
    });
}
search()

const handleDownload = (row: ISysAttachment) => {
    loading.value = true
    downloadFile(row.id!).then(res => {
        saveAs(new Blob([res]), row.fileName ?? '文件')
    }).finally(() => loading.value = false)
}

const loading = ref(false)
const fileViewShow = ref(false)
const fileUrl = ref('')
const handleView = (row: ISysAttachment) => {
    const ext = row.fileExt!.toLocaleLowerCase()
    let mimeType = ''
    if (/(jpg|png|bmp|webp|jpeg)$/i.test(ext)) mimeType = 'image/' + row.fileExt
    if (/(pdf)$/i.test(ext)) mimeType = 'application/pdf'
    if (mimeType) {
        loading.value = true
        downloadFile(row.id!).then(res => {
            const blob = new Blob([res], { type: mimeType })
            fileUrl.value = URL.createObjectURL(blob)
            fileViewShow.value = true

        }).finally(() => loading.value = false)
    }


}

const handleRemove = (id: string) => {
    ElMessageBox.confirm('是否确认删除该文件？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        loading.value = true
        removeFile(id).then(res => {
            ElMessage.success('删除成功');
            search()
            // emit('update')
        }).finally(() => loading.value = false)
    })
}

defineExpose({
    updateTable() {
        search()
    },
    getFileList() {
        return fileList.value
    }
})
</script>