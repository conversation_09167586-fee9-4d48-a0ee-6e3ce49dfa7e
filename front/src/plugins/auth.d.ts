declare module '@/plugins/auth' {
    interface Auth {
        hasPermi(permission: string): boolean;
        hasPermiOr(permissions: string[]): boolean;
        hasPermiAnd(permissions: string[]): boolean;
        hasRole(role: string): boolean;
        hasPost(post: string): boolean;
        hasRoleOr(roles: string[]): boolean;
        hasRoleAnd(roles: string[]): boolean;
    }

    const auth: Auth;
    export default auth;
}