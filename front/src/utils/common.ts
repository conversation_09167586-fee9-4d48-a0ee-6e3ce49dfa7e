import { dayjs } from "element-plus"
import Bignumber from 'bignumber.js';
import { isNumber, isString, isNaN } from "lodash";
// @ts-ignore
import Snowflake from 'snowflake-id';
import { number } from "echarts";

export const getLabelFromDicts = (dicts?: any, value?: string) => {
  return dicts?.find((t: any) => t.value == value)?.label
}

export const dateFormat = (date?: string | Date, format = 'YYYY-MM-DD') => {
  if (date && dayjs(date).isValid()) {
    return dayjs(date).format(format)
  }
  return ''
}

export const sumAmount = (nums?: (number | undefined | null)[]) => {
  if (!nums || nums.length === 0) {
    return null;
  }
  const sum = nums.reduce((acc, num) => {
    if (num === undefined || num === null || isNaN(num)) {
      return acc;
    }
    return acc.plus(new Bignumber(num));
  }, new Bignumber(0));
  return sum.isZero() ? null : sum.toNumber();
}

export const checkIsNumber = (value: any): value is number => {
  // 检查是否是数字或者是可以转换为数字的字符串
  return (isNumber(value) && !isNaN(value)) ||
    (isString(value) && /^-?\d+(\.\d+)?$/.test(value));
}


export const roundNumber2 = (num: string | number): string | number => {
  if (!checkIsNumber(num)) return num

  const b = new Bignumber(num)
  const rounded = b.decimalPlaces(2, Bignumber.ROUND_HALF_UP)

  // 判断是否是整数
  if (rounded.isInteger()) {
    return rounded.toFixed(0) // 不保留小数
  } else {
    return rounded.toNumber()
  }
}

/**
 * 从日期数组中获取最大日期和最小日期
 * @param {Array} dateArray 日期数组
 * @returns {Object} 包含最大日期和最小日期的对象
 */
export function getMinMaxDate(dateArray: (string | Date | undefined)[]) {
  // 检查输入是否为数组
  if (!Array.isArray(dateArray)) {
    throw new Error('Input must be an array');
  }
  const newDates = dateArray.filter(date => dayjs(date).isValid())

  // 检查数组是否为空
  if (newDates.length === 0) {
    throw new Error('Array cannot be empty');
  }


  // 使用 dayjs 转换日期并初始化最大和最小日期
  let maxDate = dayjs(newDates[0]);
  let minDate = dayjs(newDates[0]);

  // 遍历日期数组
  for (let i = 1; i < newDates.length; i++) {
    const currentDate = dayjs(newDates[i]);

    // 更新最大日期
    if (currentDate.isAfter(maxDate)) {
      maxDate = currentDate;
    }

    // 更新最小日期
    if (currentDate.isBefore(minDate)) {
      minDate = currentDate;
    }
  }

  // 返回最大日期和最小日期
  return {
    maxDate: maxDate.format('YYYY-MM-DD'), // 格式化为字符串返回
    minDate: minDate.format('YYYY-MM-DD')
  };
}

/**
 * 判断指定日期是否在起始日期和结束日期之间（包含边界）
 * @param date 要判断的日期
 * @param startDate 起始日期
 * @param endDate 结束日期
 * @returns 如果 date 在 startDate 和 endDate 之间（包含），返回 true，否则返回 false
 */
export const isDateWithinRange = (
  date: string | Date | dayjs.Dayjs,
  startDate: string | Date | dayjs.Dayjs,
  endDate: string | Date | dayjs.Dayjs
): boolean => {
  const d = dayjs(date);
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  if (!d.isValid() || !start.isValid() || !end.isValid()) {
    return false;
  }

  return (d.isSame(start, 'day') || d.isAfter(start, 'day')) &&
    (d.isSame(end, 'day') || d.isBefore(end, 'day'));
}

/**
 * 生成雪花id
 * @returns 
 */
export const genSnowId = () => {
  return new Snowflake().generate()
}

/**
 * 格式化数字（支持大数，保留小数位，千分位显示）
 * @param value 数字/字符串
 * @param decimal 保留的小数位数
 * @returns 格式化后的字符串
 */
export function numberFormat(num: number | string | null | undefined, decimal?: number, roundingMode: BigNumber.RoundingMode = Bignumber.ROUND_HALF_UP): string {
  if (!checkIsNumber(num)) return "";

  try {
    const num1 = new Bignumber(num);

    if (!num1.isFinite()) return "";

    if (checkIsNumber(decimal)) {
      return num1.toFormat(decimal, roundingMode, {
        groupSize: 3,
        groupSeparator: ",",
        decimalSeparator: ".",
      });
    }
    return num1.toFormat({
      groupSize: 3,
      groupSeparator: ",",
      decimalSeparator: ".",
    })
  } catch (error) {
    console.error(error)
    return "";
  }
}

/**
 * 将json字符串转换为json
 * @param jsonStr 
 * @returns 
 */
export const jsonStrParseToJson = <T>(jsonStr?: string) => {
  try {
    if (jsonStr) {
      return JSON.parse(jsonStr) as T
    }
  } catch (error) {
    console.error(error)
  }
  return null
}