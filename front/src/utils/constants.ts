/** 文件源类型枚举 */
export enum EFileSoureType {
    NOTICE = '通知公告',
    PROJECT = '项目',
    CONTRACT = '合同',
    PAYBILL = '拨款单',
    FUND_SUPERVISION = '资金监督',
    FUND_ASSETS = '资产管理',
    FUND_PURCHASE = '政府采购',
    PROJECT_ARCHIVE = '项目结项'
}

/** 文件类型枚举 */
export enum EFilePrimaryType {
    CONTRACT = '合同',
    /** 核定依据 */
    hd = '101'
}

/** 项目状态 */
export enum EProjectState {
    /** 退回 */
    reject = -1,
    /** 草稿 */
    draft = 0,
    /** 已上报 */
    reported = 1,
    /** 确认调整 */
    adjust = 2
}

/** 资金计划状态 */
export enum EFundPlanState {
    /** 退回 */
    reject = -1,
    /** 草稿 */
    draft = 0,
    /** 已上报 */
    ysb = 1,
    /** 已受理 */
    ysl = 2,
    /** 已分送 */
    yfs = 3,
    /** 已审批 */
    ysp = 4,
    /** 已发布 */
    yfb = 9
}

/**
 * 入库标记
 */
export enum ELibState {
    /** 未入库 */
    wrk = 0,
    /** 已入库 */
    yrk = 1
}

/**
 * 核定标记
 */
export enum ECheckState {
    /** 未核定 */
    whd = 0,
    /** 已核定 */
    yhd = 1
}

/** 资金计划数据项目类型 */
export enum EUsageFundPlanItemType {
    /**
     * 类型
     */
    type = 0,
    /** 项目 */
    project = 1,
    /** 项目组 */
    projectGroup = 2,
}


/**
 * 调整状态
 */
export enum EApplyAdjustState {
    /** 退回 */
    reject = -1,
    /** 草稿 */
    draft = 0,
    /** 已上报 */
    ysb = 1,
    /** 确认调整 */
    qrtz = 2
}

export enum EFlowType {
    /**
     * 拨款单申请/审批流程
     */
    F01 = 'F01',
    /**
     * 资金监督流程
     */
    F02 = 'F02',
    /**
     * 政府采购流程
     */
    F03 = 'F03',
    /**
     * 项目结项流程
     */
    F04 = 'F04',
}

/** 拨款单状态 */
export enum EBillStatus {
    /** 退回 */
    reject = -1,
    /** 草稿 */
    draft = 0,

    /** 已上报 */
    ysb = 1,
    /** 已审核 */
    ysh = 2,
    /** 已拨付 */
    ybf = 3,
}

export enum EAuditType {
    pass = 1,
    reject = 0,
}
export const getAuditTypeName = (type?: EAuditType) => {
    if (type === EAuditType.pass) {
        return '通过'
    }
    if (type === EAuditType.reject) {
        return '退回'
    }
    return ''
}

/**
 * 项目管理单位
 */
export const projectManagerDeptList = ref([
    {
        deptId: 5483,
        deptName: '发展公司',
    },
    {
        deptId: 399,
        deptName: '公共事务中心',
    },
])

/**
 * 采购主体
 */
export const purchaseDeptList = ref([
    {
        deptId: '400',
        deptName: '管委会（综合办）'
    },
    {
        deptId: '399',
        deptName: '公共事务中心'
    },
])

/**
 * 流程处理状态
 */
export enum EFlowNodeHandleStatus {
    /** 未开始 */
    notStarted = -1,
    /** 进行中 */
    inProgress = 0,
    /** 已完成 */
    completed = 1
}

export enum EPurchaseStatus {
    /** 退回 */
    reject = -1,
    /** 草稿 */
    draft = 0,
    /** 提交 */
    submit = 1,
    /** 结束 */
    finish = 2
}

export enum EFileType {
    /** 文件 */
    F = 'F',
    /** 目录 */
    D = 'D'
}