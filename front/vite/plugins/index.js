import vue from '@vitejs/plugin-vue'

import createAutoImport from './auto-import'
import createSvgIcon from './svg-icon'
import createCompression from './compression'
import createSetupExtend from './setup-extend'
import UnoCSS from 'unocss/vite'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import'

export default function createVitePlugins(viteEnv, isBuild = false) {
    const vitePlugins = [vue()]
    vitePlugins.push(createAutoImport())
    vitePlugins.push(createSetupExtend())
    vitePlugins.push(createSvgIcon(isBuild))
    vitePlugins.push(UnoCSS())
    vitePlugins.push(vueJsx())
    vitePlugins.push(lazyImport({
        resolvers: [
            VxeResolver({
                libraryName: 'vxe-table'
            })
        ]
    }))
    isBuild && vitePlugins.push(...createCompression(viteEnv))
    return vitePlugins
}
